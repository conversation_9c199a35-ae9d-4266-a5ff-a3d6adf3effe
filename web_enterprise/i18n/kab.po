# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_enterprise
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Kabyle (https://www.transifex.com/odoo/teams/41243/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "%s days"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
msgid "(Enterprise Edition)"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid ") with the instructions to follow"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "1 month"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/views/list/list_renderer_desktop.xml:0
msgid "Add Custom Field"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Add new fields and much more with"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
msgid "CLEAR"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Click here to send an email"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Close"
msgstr ""

#. module: web_enterprise
#: model:ir.model,name:web_enterprise.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: web_enterprise
#: model:ir.model,name:web_enterprise.model_res_partner
msgid "Contact"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Contact your sales representative to help you to unlink your previous database"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
msgid "Control panel toolbar"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/color_scheme/color_scheme_menu_items.js:0
msgid "Dark Mode"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
msgid "Database expiration:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Dismiss"
msgstr ""

#. module: web_enterprise
#: model:ir.actions.server,name:web_enterprise.download_contact
msgid "Download (vCard)"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Error reason:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
msgid "FILTER"
msgstr ""

#. module: web_enterprise
#: model:ir.model,name:web_enterprise.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/navbar/navbar.js:0
msgid "Home menu"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Install Odoo Studio"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Log in as an administrator to correct the issue."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Login to your Odoo.com dashboard then unlink your previous database:"
msgstr ""

#. module: web_enterprise
#: model:ir.model,name:web_enterprise.model_ir_ui_menu
msgid "Menu"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu.xml:0
msgid "No result"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
#: model_terms:ir.ui.view,arch_db:web_enterprise.res_config_settings_view_form
msgid "Odoo"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
msgid "Odoo Enterprise Edition License V1.0"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
msgid "Odoo Studio"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Odoo Support"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Paste code here"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/navbar/navbar.js:0
msgid "Previous view"
msgstr ""

#. module: web_enterprise
#: model_terms:ir.ui.view,arch_db:web_enterprise.res_config_settings_view_form
msgid "Progressive Web App"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Refresh subscription status"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Register your subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Renew your subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
msgid "SEE RESULT"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/barcode_fields.xml:0
msgid "Scan barcode"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Sending the instructions by email ..."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Something went wrong while registering your database. You can try again or contact"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Subscription Code:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu.xml:0
msgid "TIP"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Thank you, your registration was successful! Your database is valid until"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/enterprise_subscription_service.js:0
msgid "Thank you, your registration was successful! Your database is valid until %s."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "The instructions to unlink your subscription from the previous database(s) have been sent"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "This database has expired. "
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "This database will expire in %s. "
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
msgid "This demo database will expire in %s. "
msgstr ""

#. module: web_enterprise
#: model_terms:ir.ui.view,arch_db:web_enterprise.res_config_settings_view_form
msgid "This name will be used for the application when Odoo is installed through the browser."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "To unlink it you can either:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
msgid "Today"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Unable to send the instructions by email, please contact the"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Upgrade your subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
msgid "View switcher"
msgstr ""

#. module: web_enterprise
#: model:ir.model.fields,field_description:web_enterprise.field_res_config_settings__web_app_name
msgid "Web App Name"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "You have more users or more apps installed than your subscription allows."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "You will be able to register your database once you have installed your first app."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Your subscription code"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Your subscription is already linked to a database."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "Your subscription was updated and is valid until"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "buy a subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "or"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
msgid "to the subscription owner (email:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu.xml:0
msgid "— open me anywhere with"
msgstr ""
