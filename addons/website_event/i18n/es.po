# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
#
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON> G. <<EMAIL>>, 2022\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "# Registros"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr "Mostrando resultados para"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "(Ref:"
msgstr "(Ref:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "(only"
msgstr "(sólo"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
")\n"
"                                <i class=\"fa fa-long-arrow-down d-block text-muted mx-3 my-2\" style=\"font-size: 1.5rem\"/>"
msgstr ""
")\n"
"                                <i class=\"fa fa-long-arrow-down d-block text-muted mx-3 my-2\" style=\"font-size: 1.5rem\"/>"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "<b>Drag and Drop</b> this snippet below the event title."
msgstr "<b>Arrastre y suelte</b> este snippet debajo del título del evento."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr "<b>Fin</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr "<b>Empezar</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "<em>Write here a quote from one of your attendees. It gives confidence in your events.</em>"
msgstr "<em>Escriba aquí una cita de uno de sus asistentes. Da confianza en tus eventos.</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid "<font style=\"font-size: 62px;\" class=\"o_default_snippet_text\">Introduction</font>"
msgstr "<font style=\"font-size: 62px;\" class=\"o_default_snippet_text\">Introducción</font>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<i class=\"fa fa-ban me-2\"/>Sold Out"
msgstr "<i class=\"fa fa-ban me-2\"/>Agotado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>Sin publicar"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check me-2\"/>Registered"
msgstr "<i class=\"fa fa-check me-2\"/>Registrado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" title=\"Facebook\"/>"
msgstr "<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" title=\"Facebook\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-calendar\"/>Agregar a Google Calendar"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> Añadir a Calendario/Outlook"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid "<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr "<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" title=\"Configure event tickets\"/><em>Configurar boletos</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" title=\"Instagram\"/>"
msgstr "<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" title=\"Instagram\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" title=\"LinkedIn\"/>"
msgstr "<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" title=\"LinkedIn\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid ""
"<i class=\"fa fa-long-arrow-left me-2\"/>\n"
"                            <span>All Events</span>"
msgstr ""
"<i class=\"fa fa-long-arrow-left me-2\"/>\n"
"                            <span>Todos los eventos</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" title=\"Youtube\"/>"
msgstr "<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" title=\"Youtube\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "<span class=\"badge text-bg-secondary text-uppercase o_wevent_badge\">Speaker</span>"
msgstr "<span class=\"badge text-bg-secondary text-uppercase o_wevent_badge\">Ponente</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "<span class=\"navbar-brand h4 my-0 me-auto pe-sm-4\">Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid "<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span class=\"o_countdown_metric pe-1\">days</span>"
msgstr "<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span class=\"o_countdown_metric pe-1\">días</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid "<span class=\"o_countdown_remaining o_timer_hours\">00</span><span class=\"o_countdown_metric\">:</span>"
msgstr "<span class=\"o_countdown_remaining o_timer_hours\">00</span><span class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid "<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span class=\"o_countdown_metric\">:</span>"
msgstr "<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid "<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span class=\"o_countdown_metric\"/>"
msgstr "<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span class=\"o_countdown_metric\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"py-2 o_wevent_registration_title text-start\">Tickets</span>"
msgstr "<span class=\"py-2 o_wevent_registration_title text-start\">Boletos</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"text-dark fw-bold align-middle px-2\">Qty</span>"
msgstr "<span class=\"text-dark fw-bold align-middle px-2\">Cant.</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                    <i class=\"fa fa-ban me-2\"/>Sold Out\n"
"                                </span>"
msgstr ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                    <i class=\"fa fa-ban me-2\"/>Agotado\n"
"                                </span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>Eventos en línea</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span>Tickets</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"
msgstr ""
"<span>Entradas</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr "<strong> Usted solicitó más entradas que los sitios disponibles</strong>"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_2
msgid "A friend"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr "Un evento pasado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "About Us"
msgstr "Sobre Nosotros"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "Acerca de"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Add to Calendar"
msgstr "Añadir al calendario"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
msgid "All Countries"
msgstr "Todos los países"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "All Events"
msgstr "Todos los eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr "Todos los países"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_1
msgid "Allergies"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr "Permite mostrar y gestionar menús específicos para eventos en el sitio web."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__name
msgid "Answer"
msgstr ""

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Answer Breakdown"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Answers"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__once_per_order
msgid "Ask once per order"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "At just 13 years old, John DOE was already starting to develop his first business applications for customers. After mastering civil engineering, he founded TinyERP. This was the first phase of OpenERP which would later became Odoo, the most installed open-source business software worldwide."
msgstr "Con solo 13 años, John DOE ya estaba comenzando a desarrollar sus primeras aplicaciones comerciales para clientes. Después de dominar la ingeniería civil, fundó TinyERP. Esta fue la primera fase de OpenERP que más tarde se convertiría en Odoo, el software empresarial de código abierto más instalado en todo el mundo."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "Participantes"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "Autor"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_1
#: model:event.question.answer,name:website_event.event_5_question_0_answer_1
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_1
msgid "Blog Post"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Business Workshops"
msgstr "Talleres empresariales"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel"
msgstr "Cancelar"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Cards"
msgstr "Tarjetas"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "Cerrar"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "Come back once you have registrations to overview answers."
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_1
msgid "Commercials"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Community"
msgstr "Comunidad"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr "Menú de comunidad"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__company_name
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "Compañía"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Conference For Architects"
msgstr "Conferencia para arquitectos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Confirm Registration"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_0
msgid "Consumers"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Countries"
msgstr "Países"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "Propiedades de la portada"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "Creado el"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Date"
msgstr "Fecha"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr "Fecha y Hora"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Fecha (de más nuevos a más antiguos)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Fecha (de más antiguos a más nuevos)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "Descripción"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr "Mostrar un menú dedicado en el sitio web"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr "Mostrar la pestaña de comunidad en el sitio web"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "Don't forget to click <b>save</b> when you're done."
msgstr "No olvide dar clic en <b>guardar</b> cuando termine."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__email
msgid "Email"
msgstr "Correo electrónico"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr "Fin -"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__event_id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "Evento"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr "Menús de comunidad de evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr "Fecha evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "Localización del evento"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr "Menú del evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Event Name"
msgstr "Nombre del evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Event Page"
msgstr "Página del evento"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_pages_list
msgid "Event Pages"
msgstr "Páginas de evento"

#. module: website_event
#: model:ir.model,name:website_event.model_event_question
msgid "Event Question"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_question_answer
msgid "Event Question Answer"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "Registro de eventos"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration_answer
msgid "Event Registration Answer"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr "Registro eventos"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Subtitle"
msgstr "Subtítulo del evento"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr "Categoría de etiqueta de evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_dynamic_snippet_options_template
msgid "Event Tags"
msgstr "Etiquetas de eventos"

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr "Plantilla de evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Title"
msgstr "Título del evento"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_type_id
msgid "Event Type"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "Evento no encontrado"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "Evento publicado"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "Publicación de evento cancelada"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
#: model:ir.ui.menu,name:website_event.menu_event_pages
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_event.snippets
msgid "Events"
msgstr "Eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Events Page"
msgstr "Página de eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Expired"
msgstr "Expirado"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_register_cta
#: model:ir.model.fields,field_description:website_event.field_event_type__menu_register_cta
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Extra Register Button"
msgstr "Botón de registro adicional"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Find out what people see and say about this event, and join the conversation."
msgstr "Descubra lo que la gente ve y dice sobre este evento, y únase a la conversación."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Fold Tickets Details"
msgstr "Plegar detalles de boletos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Follow Us"
msgstr "Síguenos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr "El siguiente contenido aparecerá en todos los eventos."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__general_question_ids
msgid "General Questions"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Get the direction"
msgstr "Obtener la dirección"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr "Google"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Great Reno Ballon Race"
msgstr "Gran carrera de globos de Reno"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Grid"
msgstr "Cuadrícula"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Hockey Tournament"
msgstr "Torneo de hockey"

#. module: website_event
#: model:event.question,title:website_event.event_7_question_1
msgid "How did you hear about us?"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_0_question_2
#: model:event.question,title:website_event.event_1_question_0
#: model:event.question,title:website_event.event_5_question_0
#: model:event.question,title:website_event.event_type_data_sports_question_0
msgid "How did you learn about this event?"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__id
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "ID"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_question__once_per_order
msgid "If True, this question will be asked only once and its value will be propagated to every attendees.If not it will be asked for every attendee of a reservation."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
msgid "Introduction"
msgstr "Introducción"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr "Menú de introducción"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr "Menús de introducción"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr "Hecho"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "Está en marcha"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr "Está participando"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr "Juan Pérez"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Layout"
msgstr "Diseño"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "List"
msgstr "Lista"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "Live Music Festival"
msgstr "Festival de música en vivo"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Location"
msgstr "Ubicación"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr "Menú de ubicación"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr "Menús de ubicación"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "Looking great! Let's now <b>publish</b> this page so that it becomes <b>visible</b> on your website!"
msgstr "¡Se ve genial! ¡Ahora <b>publiquemos</b> esta página para que sea <b>visible</b> en su sitio web!"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Mandatory"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__is_mandatory_answer
msgid "Mandatory Answer"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_0_question_0
msgid "Meal Type"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "Menú"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Tipo de Menú"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "Menús"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_0
msgid "Mixed"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields,field_description:website_event.field_event_event__address_name
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__name
msgid "Name"
msgstr "Nombre"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_event_action_add
msgid "New Event"
msgstr "Nuevo evento"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
msgid "Next Events"
msgstr "Próximos eventos"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "No Answers yet!"
msgstr ""

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr "¡Aún no hay elementos en el menú del sitio web!"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events found."
msgstr "No se encontraron eventos."

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr "No hay registro vinculado a este visitante"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr "No se han encontrado resultados para '"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Not Published"
msgstr "Sin publicar"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Once per Order"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
msgid "OpenWood Collection Online Reveal"
msgstr "Revelación en línea de la colección OpenWood"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "Organizador"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr "Nuestras formaciones"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_0
msgid "Our website"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "Past Events"
msgstr "Eventos pasados"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__phone
msgid "Phone"
msgstr "Teléfono"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Photos"
msgstr "Fotos"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/website_event.js:0
msgid "Please select at least one ticket."
msgstr "Por favor seleccione al menos un tipo de entrada."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Published"
msgstr "Publicado"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Question"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__question_type
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_type
msgid "Question Type"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
msgid "Question cannot belong to both the event category and itself."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Questions"
msgstr "Cuestionario"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Quotes"
msgstr "Presupuestos"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_2
#: model:event.question.answer,name:website_event.event_5_question_0_answer_2
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_2
msgid "Radio Ad"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Ref:"
msgstr "Ref:"

#. module: website_event
#. odoo-python
#. odoo-javascript
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
#: model_terms:ir.ui.view,arch_db:website_event.layout
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Register"
msgstr "Registro"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Register Button"
msgstr "Botón de registro"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr "Menú de registro"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr "Menús de registro"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr "Eventos registrados"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__registration_id
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registration"
msgstr "Registro"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr "Registro confirmado!"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr "Registros"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations Closed"
msgstr "Registros cerrados"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations are <b>closed</b>"
msgstr "Los registros están <b>cerrados</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations not yet open"
msgstr "Registro aún no comenzado"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr "Restante antes de empezar"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr "Tiempo restante antes del inicio del evento (minutos)"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_2
msgid "Research"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir publicar a este sitio web."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "Regresar a la lista de eventos"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimizado para SEO"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "SHARE"
msgstr "COMPARTIR"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_1
msgid "Sales"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales end on"
msgstr "Final de las ventas en"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales start on"
msgstr "Las ventas empiezan el"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_card
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_picture
msgid "Sample"
msgstr "Muestra"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr "Encontrar un evento..."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_tree
msgid "Selected Answers"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Selected answer"
msgstr ""

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "Nombre SEO"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__sequence
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__sequence
msgid "Sequence"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr "Mostrar en el sitio web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sidebar"
msgstr "Barra lateral"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_0
#: model:event.question.answer,name:website_event.event_5_question_0_answer_0
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_0
msgid "Social Media"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "Agotado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "Disculpe, el evento solicitado no está ya disponible."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr "Inicio -"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr "Empezar hoy"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Start → End"
msgstr "Inicio → Fin"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr "Inicia <span/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Stats"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sub-menu (Specific)"
msgstr "Submenú (específico)"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Templates"
msgstr "Plantillas"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website_event
#: model:ir.model.constraint,message:website_event.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "This month"
msgstr "Este mes"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "This shortcut will bring you right back to the event form."
msgstr "Este atajo lo regresará al formulario del evento."

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr "Este menú técnico muestra todos los elementos de submenú del evento."

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
msgid "This ticket is not available for sale for this event"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr "Entrada #"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Sales starting on"
msgstr "La venta de entradas empieza el"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets for this Event are <b>Sold Out</b>"
msgstr "Las entradas para este evento están <b>agotadas</b>"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__title
msgid "Title"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
msgid "Today"
msgstr "Hoy"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Toggle navigation"
msgstr "Intercambiar navegación"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Top Bar Filter"
msgstr "Filtro de barra superior"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
msgid "Type"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:website.snippet.filter,name:website_event.website_snippet_filter_event_list
#: model_terms:ir.ui.view,arch_db:website_event.event_time
msgid "Upcoming Events"
msgstr "Próximos Eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Use the top button '<b>+ New</b>' to create an event."
msgstr "Usa el botón superior '<b>+ Nuevo</b>' para crear un evento."

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr "Utilice este <b>atajo</b> para acceder fácilmente a la página web de su evento."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragraph to write a short text about your events or company."
msgstr "Utilice este párrafo para escribir un texto corto sobre sus eventos o su empresa."

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr "Se utiliza cuando no es un menú con base en URL"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "Ver"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "Visitante"

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr "Menú de Eventos del Sitio Web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr "Menús de eventos del sitio web"

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "Página de inicio del sitio web"

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "Menú del sitio web"

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr "Menús del sitio web"

#. module: website_event
#: model:ir.model,name:website_event.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtro de fragmentos del sitio web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr "Submenú del sitio web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "Visitante del sitio web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "Meta descripción del sitio web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta palabras clave del sitio web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "Meta título del sitio web"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagen del Open Graph del sitio"

#. module: website_event
#: model:event.question,title:website_event.event_5_question_1
msgid "What's your Hockey level?"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "Si el evento ha empezado"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr "Si el evento empezará hoy si aún no está en curso"

#. module: website_event
#: model:event.question,title:website_event.event_7_question_0
msgid "Which field are you working in"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
msgid "With the Edit button, you can <b>customize</b> the web page visitors will see when registering."
msgstr "Con el botón de editar, puede <b>personalizar</b> la página web que los visitantes verán al registrarse."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
msgid "You cannot change the question type of a question that already has answers!"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
msgid "You cannot delete a question that has already been answered by attendees."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question_answer.py:0
msgid "You cannot delete an answer that has already been selected by attendees."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "available)"
msgstr "disponible)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "e.g. \"Conference for Architects\""
msgstr "p. ej. \"Conferencia de arquitectos\""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "e.g. \"Do you have any diet restrictions?\""
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr "Calendario/Outlook"
