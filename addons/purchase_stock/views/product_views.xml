<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_category_property_form" model="ir.ui.view">
        <field name="name">product.category.property.form.inherit.purchase.stock</field>
        <field name="model">product.category</field>
        <field name="inherit_id" ref="account.view_category_property_form"/>
        <field name="arch" type="xml">
            <field name="property_account_income_categ_id" position="before">
                <field name="property_account_creditor_price_difference_categ"
                    groups="account.group_account_readonly"
                    invisible="property_valuation == 'manual_periodic'"/>
            </field>
        </field>
    </record>

    <record id="product_template_form_view" model="ir.ui.view">
        <field name="name">product.normal.form.inherit.purchase.stock</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="account.product_template_form_view"/>
        <field name="arch" type="xml">
            <field name="property_account_expense_id" position="after">
                <field name="property_account_creditor_price_difference" groups="account.group_account_readonly"/>
            </field>
        </field>
    </record>

    <record id="product_view_kanban_catalog_purchase_only" model="ir.ui.view">
        <field name="name">product.view.kanban.catalog.purchase_stock</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_view_kanban_catalog"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//kanban" position="attributes">
                    <attribute name="js_class">purchase_product_kanban_catalog</attribute>
                    <attribute name="sample">1</attribute>
            </xpath>
            <field name="id" position="after">
                <field name="type"/>
                <field name="qty_available"/>
                <field name="virtual_available"/>
                <field name="monthly_demand"/>
            </field>
            <t name="qty_free" position="attributes">
                <attribute name="invisible">True</attribute>
            </t>
            <t name="qty_available" position="after">
                <t name="qty_forecasted" t-if="record.qty_available.raw_value != record.virtual_available.raw_value">
                    <span> / </span>
                    <span class="fw-bold" t-att-class="record.virtual_available.raw_value &lt; 0 ? 'text-danger' : ''" t-out="record.virtual_available.raw_value"/>
                    <span class="text-muted small"> Forecasted</span>
                </t>
            </t>
            <div name="o_kanban_qty_available_and_on_hand" position="after">
                <div t-if="record.type.raw_value === 'consu' and record.monthly_demand.raw_value != 0" name="o_kanban_monthly_demand">
                    <span class="text-muted small">Monthly Demand: </span>
                    <span class="fw-bold ms-1" t-out="record.monthly_demand.raw_value"/>
                    <field name="uom_id" class="text-muted small ms-1" groups="uom.group_uom"/>
                </div>
            </div>
        </field>
    </record>

    <record id="product_view_search_catalog" model="ir.ui.view">
        <field name="name">purchase.view.search.catalog.inherit.purchase_stock</field>
        <field name="model">product.product</field>
        <field name="mode">extension</field>
        <field name="inherit_id" ref="purchase.product_view_search_catalog"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='products_in_purchase_order']" position="after">
                <separator/>
                <filter string="To Order" name="products_with_negative_forecast" domain="[('virtual_available', '&lt;', 0)]"/>
            </xpath>
        </field>
    </record>

</odoo>
