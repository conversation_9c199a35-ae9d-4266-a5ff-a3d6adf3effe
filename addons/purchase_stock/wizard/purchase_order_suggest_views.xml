<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="purchase_order_suggest_view_form" model="ir.ui.view">
        <field name="name">purchase.order.suggest.view.form</field>
        <field name="model">purchase.order.suggest</field>
        <field name="arch" type="xml">
            <form class="o_purchase_order_suggest">
                <field name="purchase_order_id" invisible="1"/> <!-- Needed for `currency_id` related field. -->
                <field name="currency_id" invisible="1"/> <!-- Needed for `estimated_price` `currency_field` option. -->
                <field name="product_ids" invisible="1"/> <!-- Needed for `_compute_product_count`. -->
                <group>
                    <div class="text-muted">
                        <p>Get recommendations of products to purchase at <field name="partner_id" class="oe_inline fw-bold" options="{'no_open': True}"/> based on stock on hand, and expected sales volumes or actual demand.</p>
                        <p>Estimate the sales volume based on a past period or order what you need based on actual demand. Use the percentage to take into account seasonality or business growth.</p>
                    </div>
                </group>
                <group>
                    <label for="number_of_days"/>
                    <div>
                        <field name="number_of_days" class="o_small"/> days
                    </div>
                    <field name="warehouse_id" string="In" placeholder="All Warehouses" invisible="hide_warehouse"/>
                    <label for="based_on"/>
                    <div>
                        <field name="based_on" class="oe_inline me-2" widget="time_period_selection"/> x
                        <field name="percent_factor" class="o_small"/> %
                    </div>
                </group>
                <footer>
                    <button invisible="estimated_price"
                            class="btn-secondary disabled"
                            name="action_purchase_order_suggest"
                            string="Compute" type="object"/>
                    <button invisible="not estimated_price"
                            class="btn-primary"
                            name="action_purchase_order_suggest"
                            string="Compute" type="object"
                            data-hotkey="q"/>
                    <button string="Discard" class="btn btn-secondary" special="cancel" data-hotkey="x"/>
                    <div class="position-absolute end-0 pe-3 text-end text-info fw-bold">
                        <!-- <label class="d-block" for="estimated_price"/> -->
                        <div><field name="product_count" class="oe_inline"/> Products</div>
                        <field name="estimated_price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                    </div>
                </footer>
            </form>
        </field>
    </record>
</odoo>
