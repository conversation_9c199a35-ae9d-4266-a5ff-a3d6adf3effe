<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="base.user_demo" model="res.users">
            <field name="group_ids" eval="[(4, ref('hr_timesheet.group_hr_timesheet_user'))]"/>
        </record>

        <record id="base.default_user_group" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('hr_timesheet.group_timesheet_manager'))]"/>
        </record>
    </data>

    <!-- Projects -->
    <record id="project.project_project_1" model="project.project">
        <field name="allow_timesheets" eval="True"/>
    </record>

    <record id="project.project_project_2" model="project.project">
        <field name="allow_timesheets" eval="True"/>
    </record>

    <!-- Timesheet Lines -->
    <record id="working_hours_requirements" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-60.00</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="working_hours_design" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-30.00</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="working_hours_coding" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-90.00</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="working_hours_testing" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-30.00</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="working_hours_maintenance" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-30.00</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_5" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_7" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-17)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-19)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-20)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-24)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-26)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-27)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-28)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-28)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-32)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-34)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-35)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-37)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-38)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-48)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_22" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-49)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_23" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-50)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_24" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-51)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_1_account_analytic_line_25" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-55)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_6" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-19)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-20)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-23)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-25)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-28)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_12" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-30)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-31)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-34)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-35)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-36)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-38)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-39)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-42)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_20" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-46)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-49)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_22" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-53)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_23" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-53)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_2_account_analytic_line_24" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-60)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_7" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-17)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-18)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-20)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-25)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-27)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-27)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-31)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-37)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-37)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-38)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-39)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-40)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-40)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_22" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-41)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_23" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-44)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_24" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-44)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_25" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-45)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_26" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-45)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_27" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-46)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_28" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-47)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_29" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-48)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_30" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-50)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_5_account_analytic_line_31" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-58)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_5"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_9" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-16)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-16)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-21)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-22)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-23)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-26)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-29)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-36)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-39)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-40)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-41)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-43)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_22" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-48)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_23" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-54)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_24" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-58)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_25" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-59)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_6_account_analytic_line_26" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-60)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_6"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_1" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-17)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-19)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_8" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-21)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-22)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-23)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-25)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-29)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-30)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-30)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-31)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-35)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-41)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-42)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-44)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-45)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-47)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_22" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-51)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_7_account_analytic_line_23" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-57)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_2" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_6" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-16)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-18)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_10" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-22)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-24)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-26)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-33)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-33)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-34)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-36)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-42)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-43)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-43)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-46)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-47)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_22" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-50)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_23" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-51)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_24" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-52)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_25" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-52)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_26" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-52)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_27" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-55)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_28" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-56)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_29" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-56)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_30" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-57)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_31" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-58)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_8_account_analytic_line_32" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-59)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_8"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-18)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_5" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-21)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-24)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-29)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-32)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-32)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-33)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-49)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-53)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-54)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-54)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-55)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-56)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_17" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-57)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-59)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_9_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-60)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_9"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_10_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_10"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_10_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_10"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_12_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Meeting</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() - relativedelta(weeks=2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_12"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_13_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Summary Writing</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() - relativedelta(weeks=2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_13"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_1_task_14_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Preparation</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() - relativedelta(weeks=2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_1_task_14"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_7" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_8" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_14" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_21" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_22" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_1_account_analytic_line_23" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_1"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_13" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_2_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_2"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_9" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_14" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_22" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_23" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_24" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_25" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_26" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_3_account_analytic_line_27" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_3"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_3" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_7" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_14" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_15" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_18" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_4_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_4"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_6" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_8" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_10" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=0)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_18" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_19" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_5_account_analytic_line_22" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_5"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_1" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_3" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_5" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=0)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=0)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=0)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=0)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=0)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=0)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() - relativedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_22" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_23" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_24" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_6_account_analytic_line_25" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_6"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_1" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_2" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_3" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_4" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_5" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_6" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_7" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_8" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_9" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_10" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_11" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_12" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_13" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_14" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_15" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-90.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_16" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_17" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_18" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_19" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_20" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=0)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-30.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="project_2_task_7_account_analytic_line_21" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=0)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_2_task_7"/>
        <field name="amount">-60.0</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <!-- Projects -->
    <record id="project.project_1_milestone_2" model="project.milestone">
        <field name="deadline" eval="(DateTime.now() + relativedelta(years=1, months=3)).strftime('%Y-%m-15')"/>
    </record>

    <record id="project_update_1" model="project.update" context="{'default_project_id': ref('project.project_project_1')}">
        <field name="name">Weekly review</field>
        <field name="user_id" eval="ref('base.user_demo')"/>
        <field name="progress" eval="30"/>
        <field name="status">on_hold</field>
    </record>
    <record id="project_update_2" model="project.update" context="{'default_project_id': ref('project.project_project_2')}">
        <field name="name">Weekly review</field>
        <field name="user_id" eval="ref('base.user_admin')"/>
        <field name="progress" eval="30"/>
        <field name="status">off_track</field>
    </record>
    <record id="project.project_update_1" model="project.update">
        <field name="uom_id" ref="uom.product_uom_hour"/>
    </record>
    <record id="project.project_update_3" model="project.update">
        <field name="uom_id" ref="uom.product_uom_hour"/>
    </record>
    <record id="project.project_update_construction_1" model="project.update">
        <field name="uom_id" ref="uom.product_uom_hour"/>
    </record>
    <record id="project.project_update_construction_2" model="project.update">
        <field name="uom_id" ref="uom.product_uom_hour"/>
    </record>
    <record id="project.project_update_2" model="project.update">
        <field name="uom_id" ref="uom.product_uom_hour"/>
    </record>

</odoo>
