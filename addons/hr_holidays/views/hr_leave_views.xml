<?xml version='1.0' encoding='UTF-8' ?>
<odoo>

    <record id="view_evaluation_report_graph" model="ir.ui.view">
        <field name="name">hr.holidays.graph</field>
        <field name="model">hr.leave</field>
        <field name="arch" type="xml">
            <graph string="Appraisal Analysis" sample="1">
                <field name="employee_id"/>
                <field name="holiday_status_id"/>
                <field name="date_from"/>
                <field name="number_of_days" type="measure"/>
            </graph>
         </field>
    </record>

    <record id="view_hr_holidays_filter" model="ir.ui.view">
        <field name="name">hr.holidays.filter</field>
        <field name="model">hr.leave</field>
        <field name="arch" type="xml">
            <search string="Search Time Off">
                <field name="employee_id"/>
                <field name="holiday_status_id"/>
                <field name="name"/>
                <field name="activity_user_id" string="Activities of"/>
                <field name="activity_type_id" string="Activity type"/>
                <filter domain="[
                        ('state','in',['confirm']),
                        '|',
                        ('employee_id.user_id', '!=', uid),
                        '&amp;',
                        ('employee_id.user_id', '=', uid),
                        ('employee_id.leave_manager_id', '=', uid)]"
                    string="Waiting For Me"
                    name="waiting_for_me"
                    groups="hr_holidays.group_hr_holidays_responsible,!hr_holidays.group_hr_holidays_user"/>
                <filter domain="[
                        ('state','in',['confirm','validate1']),
                        '|',
                            ('employee_id.user_id', '!=', uid),
                            '|',
                                '&amp;',
                                    ('state','=','confirm'),
                                    ('holiday_status_id.leave_validation_type','=','hr'),
                                ('state','=','validate1')]"
                    string="Waiting For Me"
                    name="waiting_for_me_manager"
                    groups="hr_holidays.group_hr_holidays_user"/>
                <separator/>
                <filter domain="[('state', '=', 'confirm')]" string="First Approval" name="approve"/>
                <filter domain="[('state', '=', 'validate1')]" string="Second Approval" name="second_approval"/>
                <filter string="Approved" domain="[('state', '=', 'validate')]" name="validated"/>
                <filter name="cancelled_leaves" string="Cancelled" domain="[('state', '=', 'cancel')]"/>
                <filter name="refused_leaves" string="Refused" domain="[('state', '=', 'refuse')]"/>
                <separator/>
                <filter string="My Time Off" name="my_leaves" domain="[('employee_id.user_id', '=', uid)]"/>
                <filter string="My Team" name="my_team" domain="['|', ('employee_id.leave_manager_id', '=', uid), ('employee_id.user_id', '=', uid)]" help="Time off of people you are manager of"/>
                <filter string="My Department" name="department"
                        domain="[('employee_id.member_of_department', '=', True)]"
                        help="My Department"/>
                <separator/>
                <filter name="filter_date_from" date="date_from" default_period="year" string="Start Date"/>
                <separator/>
                <filter invisible="1" string="My Activities" name="filter_activities_my"
                    domain="[('activity_user_id', '=', uid)]"/>
                <separator invisible="1"/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                    domain="[('activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records whose next activity date is past"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))
                        ]"/>
                <group expand="0" string="Group By">
                    <filter name="group_employee" string="Employee" context="{'group_by':'employee_id'}"/>
                    <filter name="group_type" string="Type" context="{'group_by':'holiday_status_id'}"/>
                    <filter name="group_state" string="Status" context="{'group_by': 'state'}"/>
                    <separator/>
                    <filter name="group_date_from" string="Date" context="{'group_by':'date_from'}"/>
                </group>
                <searchpanel>
                    <field name="state" string="Status"/>
                    <field name="department_id" string="Department" icon="fa-users"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="hr_leave_view_kanban" model="ir.ui.view">
        <field name="name">hr.leave.view.kanban</field>
        <field name="model">hr.leave</field>
        <field name="arch" type="xml">
            <kanban sample="1" class="o_holidays_view_kanban">
                <header>
                    <button type="action"
                        name="%(hr_holidays.action_hr_leave_generate_multi_wizard)d"
                        string="New Group Time Off" class="btn btn-secondary"
                        groups="hr_holidays.group_hr_holidays_user" display="always"
                    />
                </header>
                <field name="supported_attachment_ids_count"/>
                <field name="state"/>
                <field name="employee_id"/>
                <field name="request_unit_hours"/>
                <field name="request_unit_half"/>
                <field name="request_date_from"/>
                <field name="request_hour_from"/>
                <field name="request_hour_to"/>
                <field name="request_date_to"/>
                <field name="can_approve"/>
                <field name="can_validate"/>
                <field name="can_refuse"/>
                <field name="request_date_from_period"/>
                <field name="holiday_status_requires_allocation"/>
                <templates>
                    <t t-name="card" class="g-0">
                        <div class="o_hr_holidays_kanban w-100">
                            <widget name="web_ribbon" title="To Approve" bg_color="text-bg-warning" invisible="state != 'confirm'"/>
                            <widget name="web_ribbon" title="To Validate" bg_color="text-bg-warning" invisible="state != 'validate1'"/>
                            <widget name="web_ribbon" title="Cancelled" bg_color="text-bg-danger" invisible="state != 'cancel'"/>
                            <widget name="web_ribbon" title="Refused" bg_color="bg-danger" invisible="state != 'refuse'"/>
                            <widget name="web_ribbon" title="Approved" bg_color="bg-success" invisible="state != 'validate'"/>
                            <aside>
                                <img t-att-src="'/web/image/hr.employee.public/' + record.employee_id.raw_value + '/avatar_128'"
                                    class="o_image_64_cover float-start mb-2 me-2" alt="Employee's image"/>
                            </aside>
                            <main class="ps-3">
                                <div class="o_holidays_kanban_card">
                                    <field class="fw-bold fs-3 mb-2" name="employee_id"/>
                                    <div class="d-flex flex-row">
                                        <div class="col-md-9 col-lg-9 col-10 d-flex flex-row o_hr_holidays_value">
                                            <div class="col-md-5 col-lg-7 col-6 o_hr_holidays_name">
                                                <field name="holiday_status_id" class="fw-bold"/>
                                                <div class="d-flex flex-row">
                                                    <t t-out="luxon.DateTime.fromISO(record.request_date_from.raw_value).toLocaleString({ month: 'short', day: 'numeric'})"/>
                                                    <t t-if="record.request_date_from.raw_value != record.request_date_to.raw_value">
                                                        <span class="mx-1">-</span>
                                                        <t t-out="luxon.DateTime.fromISO(record.request_date_to.raw_value).toLocaleString({ month: 'short', day: 'numeric'})"/>
                                                    </t>
                                                    <t t-if="record.request_unit_half.raw_value">
                                                        <span class="mx-1">-</span>
                                                        <t t-out="record.request_date_from_period.value"/>
                                                    </t>
                                                    <t t-if="record.request_unit_hours.raw_value">
                                                        <span class="mx-1">-</span>
                                                        <field name="request_hour_from" widget="float_time_selection"/>
                                                        <span>/</span>
                                                        <field name="request_hour_to" widget="float_time_selection"/>
                                                    </t>
                                                </div>
                                            </div>
                                            <div class="col-3 col-lg-2 o_hr_holidays_card">
                                                <div class="fw-bold d-flex me-1">
                                                    <field name="duration_display"/>
                                                </div>
                                                Amount
                                            </div>
                                            <div class="col-3 o_hr_holidays_card">
                                                <div t-if="record.holiday_status_requires_allocation.raw_value"> <!-- To keep the same space-->
                                                    <div class="fw-bold d-flex gap-1 me-1">
                                                        <field name="virtual_remaining_leaves"/>
                                                        /
                                                        <field name="max_leaves"/>
                                                    </div>
                                                    <span>
                                                        Current balance
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-3 o_hr_holidays_button flex-wrap d-flex pe-4 gap-2 justify-content-start">
                                            <button
                                                name="action_approve" type="object" class="btn btn-primary"
                                                t-if="record.can_approve.raw_value">
                                                Approve
                                            </button>
                                            <button
                                                name="action_approve" type="object" class="btn btn-primary"
                                                t-if="record.can_validate.raw_value and !record.can_approve.raw_value">
                                                Validate
                                            </button>
                                            <button
                                                name="action_refuse" type="object" class="btn btn-secondary"
                                                t-if="record.can_refuse.raw_value">
                                                Refuse
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </main>
                        </div>
                        <div class="f-flex flex-row o_hr_holidays_kanban_mobile w-100">
                            <main>
                                <div class="flex flex-grow-1 align-items-start mb-1">
                                    <div class="mb-1 d-flex align-items-center gap-2 hr_leave_kanban_title">
                                        <field name="employee_id" widget="many2one_avatar_employee"/>
                                        <field name="employee_id" class="fw-bold w-100"/>
                                        <span
                                            style="min-width: fit-content;"
                                            t-attf-class="rounded-pill badge flex-shrink-1 text-bg-{{
                                                record.state.raw_value == 'validate' ? 'success' :
                                                ['confirm', 'validate1'].includes(record.state.raw_value) ?
                                                'warning' : 'danger' }}"
                                            t-out="record.state.value"/>
                                    </div>
                                    <div class="d-flex flex-row">
                                        <div id="date_part" class="d-flex flex-row"> <!-- To be override in my_kanban-->
                                            <t t-out="luxon.DateTime.fromISO(record.request_date_from.raw_value).toLocaleString({ month: 'short', day: 'numeric'})"/>
                                            <t t-if="record.request_date_from.raw_value != record.request_date_to.raw_value">
                                                <span class="mx-1">-</span>
                                                <t t-out="luxon.DateTime.fromISO(record.request_date_to.raw_value).toLocaleString({ month: 'short', day: 'numeric'})"/>
                                            </t>
                                            <t t-if="record.request_unit_half.raw_value">
                                                <span class="mx-1">-</span>
                                                <t t-out="record.request_date_from_period.value"/>
                                            </t>
                                            <t t-if="record.request_unit_hours.raw_value">
                                                <span class="mx-1">-</span>
                                                <field name="request_hour_from" widget="float_time_selection"/>
                                                <span>/</span>
                                                <field name="request_hour_to" widget="float_time_selection"/>
                                            </t>
                                        </div>
                                    </div>
                                    <field name="holiday_status_id" class="text-muted"/>
                                    <span class="text-muted">
                                        (<field name="duration_display"/>)
                                    </span>
                                </div>
                            </main>
                            <bottom class="d-flex justify-content-end gap-2 mt-0">
                                <button t-if="record.can_approve.raw_value" name="action_approve" type="object" class="btn btn-sm btn-primary py-0"
                                    groups="hr_holidays.group_hr_holidays_user">
                                    Approve
                                </button>
                                <button t-if="record.can_validate.raw_value and !record.can_approve.raw_value" name="action_approve" type="object" class="btn btn-sm btn-primary py-0"
                                    groups="hr_holidays.group_hr_holidays_manager">
                                    Validate
                                </button>
                                <button t-if="record.can_refuse.raw_value" name="action_refuse" type="object" class="btn btn-sm btn-secondary py-0"
                                    groups="hr_holidays.group_hr_holidays_user">
                                    Refuse
                                </button>
                                <button t-if="record.supported_attachment_ids_count.raw_value > 0" name="action_documents" type="object" class="btn btn-link btn-sm ps-0 text-danger">
                                    <i class="fa fa-paperclip"> <field name="supported_attachment_ids_count" nolabel="1"/></i>
                                </button>
                            </bottom>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_leave_view_activity" model="ir.ui.view">
        <field name="name">hr.leave.view.activity</field>
        <field name="model">hr.leave</field>
        <field name="arch" type="xml">
            <activity string="Time Off Request">
                <field name="employee_id"/>
                <templates>
                    <div t-name="activity-box">
                        <img class="rounded" t-att-src="activity_image('hr.employee', 'avatar_128', record.employee_id.raw_value)" t-att-title="record.employee_id.value" t-att-alt="record.employee_id.value"/>
                        <div class="ms-2 flex-grow-1">
                            <div class="d-flex justify-content-between">
                                <field name="name" class="o_text_block o_text_bold"/>
                                <div class="text-muted text-nowrap">(<field name="number_of_days"/> days)</div>
                            </div>
                            <div class="text-muted">
                                <field name="holiday_status_id" display="full"/>
                                <field name="date_from"/> <i class="fa fa-long-arrow-right" title="to" /> <field name="date_to"/>
                            </div>
                        </div>
                    </div>
                </templates>
            </activity>
        </field>
    </record>

    <record id="hr_leave_view_form" model="ir.ui.view">
        <field name="name">hr.leave.view.form</field>
        <field name="model">hr.leave</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <form string="Time Off Request" class="o_hr_leave_form" duplicate="false">
            <field name="employee_id" invisible="1"/> <!-- for the employee's default value-->
            <header>
                <button string="Approve" name="action_approve" type="object" class="oe_highlight" invisible="not can_approve or not id"/>
                <button string="Validate" name="action_approve" invisible="not can_validate or can_approve or not id" type="object" class="oe_highlight"/>
                <button string="Refuse" name="action_refuse" type="object" invisible="not can_refuse or not id"/>
                <button string="Cancel" name="action_cancel" type="object" invisible="not can_cancel or not id" />
                <field name="state" widget="statusbar" statusbar_visible="confirm,validate,validate1" invisible="validation_type != 'both'"/>
                <field name="state" widget="statusbar" statusbar_visible="confirm,validate" invisible="validation_type == 'both'"/>
            </header>
            <sheet>
                <field name="dashboard_warning_message" class="alert alert-danger d-flex mt-2" role="alert" style="white-space: pre-wrap;" invisible="not dashboard_warning_message"/>
                <field name="leave_type_increases_duration" class="alert alert-info d-flex" role="alert" style="white-space: pre-wrap;" invisible="not leave_type_increases_duration"/>

                <div class="alert alert-info" role="alert" invisible="not request_unit_hours or not tz_mismatch or dashboard_warning_message">
                    <span>The employee has a different timezone than yours! Here dates and times are displayed in the employee's timezone</span>
                    (<field name="tz"/>).
                </div>
                <div class="o_hr_leave_content row">
                    <div class="o_hr_leave_column col_left col-md-6 col-12">
                        <widget name="web_ribbon" title="Cancelled" bg_color="text-bg-danger" invisible="state != 'cancel'"/>
                        <div name="title" class="o_hr_leave_title" invisible="1">
                            <field name="display_name" invisible="not holiday_status_id"/>
                        </div>
                        <group name="col_left" class="m-0">
                            <field name="holiday_status_id" force_save="1"
                                domain="[
                                    '|',
                                        ('requires_allocation', '=', False),
                                        '&amp;',
                                            ('has_valid_allocation', '=', True),
                                            '|',
                                                ('allows_negative', '=', True),
                                                '&amp;',
                                                    ('virtual_remaining_leaves', '&gt;', 0),
                                                    ('allows_negative', '=', False),
                                ]"
                                context="{'employee_id': employee_id, 'default_date_from': date_from, 'default_date_to': date_to}"
                                options="{'no_create': True, 'request_type': 'leave'}"
                                readonly="state in ['cancel', 'refuse', 'validate', 'validate1']"/>
                            <!-- half day or custom hours: only show one date -->
                            <label for="request_date_from" invisible="not request_unit_half and not request_unit_hours" string="Date" />
                            <label for="request_date_from" invisible="request_unit_half or request_unit_hours" string="Dates" />
                            <div class="o_row" invisible="not request_unit_half and not request_unit_hours">
                                <field name="request_date_from" class="oe_inline" string="Date" readonly="state != 'confirm'" />
                                <field name="request_date_from_period" invisible="not request_unit_half" required="request_unit_half" readonly="state != 'confirm'"/>
                                <div>
                                ( <field name="duration_display" readonly="1" class="w-auto"/> )
                                </div>
                            </div>
                            <!-- full days: show date start/end with daterange -->
                            <div id="full_date" class="o_row" invisible="request_unit_half or request_unit_hours">
                                <field
                                    name="request_date_from"
                                    widget="daterange"
                                    readonly="state != 'confirm'"
                                    required="not date_from or not date_to"
                                    options="{'end_date_field': 'request_date_to'}"/>
                                <field name="request_date_to" invisible="1"/> <!-- For Form view test-->
                                <div>
                                ( <field name="duration_display" readonly="1" class="w-auto"/> )
                                </div>
                            </div>
                            <label for="request_unit_half" string="" invisible="not leave_type_request_unit or leave_type_request_unit == 'day'"/>
                            <div class="o_row o_row_readonly oe_edit_only" style="margin-left: -2px;" invisible="not leave_type_request_unit or leave_type_request_unit == 'day'">
                                <field name="request_unit_half" class="oe_inline" invisible="leave_type_request_unit == 'day' or (not request_unit_half and state != 'confirm')" readonly="state != 'confirm'" />
                                <label for="request_unit_half" invisible="leave_type_request_unit == 'day' or (not request_unit_half and state != 'confirm')" />
                                <field name="request_unit_hours" invisible="leave_type_request_unit != 'hour' or (not request_unit_hours and state != 'confirm')" readonly="state != 'confirm'" class="ms-5" />
                                <label for="request_unit_hours" invisible="leave_type_request_unit != 'hour' or (not request_unit_hours and state != 'confirm')" />
                            </div>
                            <label for="request_hour_from" string="" invisible="not request_unit_hours"/>
                            <div class="o_row o_row_readonly" invisible="not request_unit_hours">
                                <label for="request_hour_from" string="From" />
                                <field name="request_hour_from" invisible="not request_unit_hours" readonly="state == 'validate'" required="request_unit_hours" widget="float_time_selection" placeholder="00:00"/>
                                <label for="request_hour_to" string="To" />
                                <field name="request_hour_to" invisible="not request_unit_hours" readonly="state == 'validate'" required="request_unit_hours" widget="float_time_selection"/>
                            </div>
                            <field name="name" readonly="state != 'confirm'" widget="text" nolabel="1" colspan="2" placeholder="No description provided" />
                        </group>
                        <group>
                            <field name="supported_attachment_ids" widget="many2many_binary" nolabel="1" invisible="state not in ('confirm', 'validate1')" />
                        </group>
                    </div>
                    <div class="o_hr_leave_column col_right col-md-6 col-12">
                        <widget name="hr_leave_stats"/>
                    </div>
                </div>
            </sheet>
            <div class="o_attachment_preview"/>
            <chatter reload_on_post="True" reload_on_attachment="True"/>
            </form>
        </field>
    </record>

    <record id="hr_leave_view_form_dashboard" model="ir.ui.view">
        <field name="name">hr.leave.view.form.dashboard</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form"/>
        <field name="mode">primary</field>
        <field name="priority">100</field>
        <field name="arch" type="xml">
            <xpath expr="//header" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//div[hasclass('col_right')]" position="replace"/>
            <xpath expr="//chatter" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//div[hasclass('o_attachment_preview')]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="hr_leave_view_form_dashboard_new_time_off" model="ir.ui.view">
        <field name="name">hr.leave.view.form.dashboard.new.time.off</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form_dashboard"/>
        <field name="mode">primary</field>
        <field name="priority">17</field>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='col_left']" position="attributes">
                <attribute name="colspan">5</attribute>
            </xpath>
            <xpath expr="//div[hasclass('o_hr_leave_content')]" position="attributes">
                <attribute name="class" remove="row my-n4" separator=" "/>
            </xpath>
            <xpath expr="//div[hasclass('o_hr_leave_column')]" position="attributes">
                <attribute name="class" remove="col_left col-md-6" separator=" "/>
            </xpath>
            <xpath expr="//div[hasclass('o_hr_leave_content')]" position="before">
                <widget name="web_ribbon" title="Refused" bg_color="bg-danger" invisible="state != 'refuse' or not id"/>
                <widget name="web_ribbon" title="Approved" bg_color="bg-success" invisible="state != 'validate' or not id"/>
            </xpath>
        </field>
    </record>

    <record id="hr_leave_view_form_dashboard_manager_new_time_off" model="ir.ui.view">
        <field name="name">hr.leave.view.form.dashboard.new.time.off</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form_dashboard_new_time_off"/>
        <field name="mode">primary</field>
        <field name="priority">17</field>
        <field name="arch" type="xml">
            <field name="holiday_status_id" position="before">
                <field name="user_id" invisible="1"/> <!-- Used in HolidaysFormViewDialog -->
                <field name="employee_id" widget="many2one_avatar_employee"/>
            </field>
        </field>
    </record>

    <record id="hr_leave_view_dashboard" model="ir.ui.view">
        <field name="name">hr.leave.view.dashboard</field>
        <field name="model">hr.leave</field>
        <field name="arch" type="xml">
            <calendar js_class="time_off_calendar_dashboard"
                    string="Time Off Request"
                    form_view_id="%(hr_holidays.hr_leave_view_form_dashboard_new_time_off)d"
                    event_open_popup="true"
                    date_start="date_from"
                    date_stop="date_to"
                    quick_create="0"
                    show_date_picker="0"
                    show_unusual_days="True"
                    color="color"
                    hide_time="True"
                    mode="year">
                <field name="display_name" string=""/>
                <field name="holiday_status_id" filters="1" invisible="1" color="color"/>
                <field name="state" invisible="1"/>
                <field name="is_hatched" invisible="1" />
                <field name="is_striked" invisible="1"/>
            </calendar>
        </field>
    </record>

    <record id="hr_leave_employee_view_dashboard" model="ir.ui.view">
        <field name="name">hr.leave.view.dashboard</field>
        <field name="model">hr.leave</field>
        <field name="arch" type="xml">
            <calendar string="Time Off Request"
                    js_class="time_off_calendar_dashboard"
                    form_view_id="%(hr_holidays.hr_leave_view_form_dashboard_new_time_off)d"
                    event_open_popup="true"
                    date_start="date_from"
                    date_stop="date_to"
                    mode="year"
                    quick_create="0"
                    show_date_picker="0"
                    show_unusual_days="True"
                    color="color"
                    hide_time="True">
                <field name="display_name" string=""/>
                <field name="holiday_status_id" filters="1" invisible="1" color="color"/>
                <field name="state" invisible="1"/>
                <field name="is_hatched" invisible="1" />
                <field name="is_striked" invisible="1"/>
            </calendar>
        </field>
    </record>

    <record id="hr_leave_view_form_manager" model="ir.ui.view">
        <field name="name">hr.leave.view.form.manager</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_leave_view_form"/>
        <field name="mode">primary</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='title']" position="attributes">
                <attribute name="invisible">0</attribute>
            </xpath>
            <xpath expr="//field[@name='holiday_status_id']" position="before">
                <field name="employee_id" groups="hr_holidays.group_hr_holidays_user" readonly="state in ['cancel', 'refuse', 'validate', 'validate1']" widget="many2one_avatar_employee"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="department_id" groups="hr_holidays.group_hr_holidays_user" readonly="1"/>
            </xpath>
        </field>
    </record>

    <record id="hr_leave_view_calendar" model="ir.ui.view">
        <field name="name">hr.leave.view.calendar</field>
        <field name="model">hr.leave</field>
        <field name="arch" type="xml">
            <calendar js_class="time_off_calendar"
                    string="Time Off Request"
                    form_view_id="%(hr_holidays.hr_leave_view_form_dashboard_manager_new_time_off)d"
                    event_open_popup="true"
                    date_start="date_from"
                    date_stop="date_to"
                    mode="month"
                    show_unusual_days="True"
                    quick_create="0"
                    color="color">
                <field name="holiday_status_id" color="color" filters="1" invisible="1"/>
                <field name="employee_id" filters="1" widget="many2one_avatar_employee"/>
                <field name="name"/>
                <field name="state" invisible="1"/>
                <field name="is_hatched" invisible="1" />
                <field name="is_striked" invisible="1"/>
                <field name="state" invisible="1"/>
            </calendar>
        </field>
    </record>

    <record id="hr_leave_view_tree" model="ir.ui.view">
        <field name="name">hr.holidays.view.list</field>
        <field name="model">hr.leave</field>
        <field name="arch" type="xml">
            <list string="Time Off Requests" sample="1" duplicate="false" js_class="hr_holidays_payslip_list">
                <header>
                    <button type="action"
                        name="%(hr_holidays.action_hr_leave_generate_multi_wizard)d"
                        string="New Group Time Off" class="btn btn-secondary"
                        groups="hr_holidays.group_hr_holidays_user" display="always"
                    />
                </header>
                <field name="employee_id" widget="many2one_avatar_employee" readonly="state in ['cancel', 'refuse', 'validate', 'validate1']" />
                <field name="department_id" optional="hidden" readonly="state in ['cancel', 'refuse', 'validate', 'validate1']"/>
                <field name="holiday_status_id" class="fw-bold" readonly="state in ['cancel', 'refuse', 'validate', 'validate1']"/>
                <field name="date_from" readonly="state in ['cancel', 'refuse', 'validate', 'validate1']"/>
                <field name="date_to" readonly="state in ['cancel', 'refuse', 'validate', 'validate1']"/>
                <field name="duration_display" string="Duration"/>
                <field name="name" optional="hidden"/>
                <field name="state" widget="badge" decoration-warning="state in ('confirm','validate1')" decoration-success="state == 'validate'" decoration-danger="state == 'refuse'"/>
                <field name="active_employee" column_invisible="True"/>
                <field name="user_id" column_invisible="True"/>
                <field name="message_needaction" column_invisible="True"/>
                <button string="Approve" name="action_approve" type="object"
                    icon="fa-thumbs-up"
                    invisible="not can_approve"/>
                <button string="Validate" name="action_approve" type="object"
                    icon="fa-check"
                    invisible="not can_validate or can_approve"/>
                <button string="Refuse" name="action_refuse" type="object"
                    icon="fa-times"
                    invisible="not can_refuse"/>
                <field name="company_id" optional="hidden" groups="base.group_multi_company"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
            </list>
        </field>
    </record>

    <record id="hr_leave_view_tree_my" model="ir.ui.view">
        <field name="name">hr.holidays.view.list</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_leave_view_tree"/>
        <field name="mode">primary</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <xpath expr="//list" position="attributes">
                <attribute name="default_order">date_from DESC</attribute>
            </xpath>
            <xpath expr="//field[@name='employee_id']" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='department_id']" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='%(hr_holidays.action_hr_leave_generate_multi_wizard)d']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="hr_leave_view_search_my" model="ir.ui.view">
        <field name="name">hr.holidays.view.search.my</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="view_hr_holidays_filter"/>
        <field name="mode">primary</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <xpath expr="//searchpanel" position="replace"/>
            <xpath expr="//filter[@name='department']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='my_team']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='my_leaves']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='group_employee']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='employee_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="hr_leave_view_search_manager" model="ir.ui.view">
        <field name="name">hr.holidays.view.search.manager</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="view_hr_holidays_filter"/>
        <field name="mode">primary</field>
        <field name="priority">33</field>
        <field name="arch" type="xml">
            <field name='employee_id' position="after">
                <field name="department_id"/>
            </field>
            <xpath expr="//filter[@name='my_leaves']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='group_employee']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="hr_leave_view_search_report" model="ir.ui.view">
        <field name="name">hr.holidays.view.search.report</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="view_hr_holidays_filter"/>
        <field name="mode">primary</field>
        <field name="priority">34</field>
        <field name="arch" type="xml">
            <filter name="cancelled_leaves" position="replace" />
        </field>
    </record>

    <record id="hr_leave_action_new_request" model="ir.actions.act_window">
        <field name="name">Dashboard</field>
        <field name="path">time-off</field>
        <field name="res_model">hr.leave</field>
        <field name="view_mode">calendar,list,form,activity</field>
        <field name="domain">[('user_id', '=', uid), ('employee_id.company_id', 'in', allowed_company_ids)]</field>
        <field name="context">{'short_name': 1, 'search_default_year': 1}</field>
        <field name="search_view_id" ref="hr_holidays.hr_leave_view_search_my"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Keep track of your requests.
            </p><p>
                A great way to keep track on your time off requests, sick days, and approval status.
            </p>
        </field>
    </record>

    <record id="hr_leave_action_new_request_view_calendar" model="ir.actions.act_window.view">
        <field name="sequence">1</field>
        <field name="view_mode">calendar</field>
        <field name="act_window_id" ref="hr_leave_action_new_request"/>
        <field name="view_id" ref="hr_leave_view_dashboard"/>
    </record>

    <record id="hr_leave_action_new_request_view_tree" model="ir.actions.act_window.view">
        <field name="sequence">2</field>
        <field name="view_mode">list</field>
        <field name="act_window_id" ref="hr_leave_action_new_request"/>
        <field name="view_id" ref="hr_leave_view_tree_my"/>
    </record>

    <record id="hr_leave_action_new_request_view_form" model="ir.actions.act_window.view">
        <field name="sequence">3</field>
        <field name="view_mode">form</field>
        <field name="act_window_id" ref="hr_leave_action_new_request"/>
        <field name="view_id" ref="hr_leave_view_form"/>
    </record>

    <record id="hr_leave_action_my_request" model="ir.actions.act_window">
        <field name="name">Time Off Request</field>
        <field name="res_model">hr.leave</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
    <record id="hr_leave_action_my_request_view_form" model="ir.actions.act_window.view">
        <field name="view_mode">form</field>
        <field name="act_window_id" ref="hr_leave_action_my_request"/>
        <field name="view_id" ref="hr_leave_view_form_dashboard_new_time_off"/>
    </record>

    <record id="hr_leave_view_kanban_my" model="ir.ui.view">
        <field name="name">hr.leave.view.kanban.my</field>
        <field name="mode">primary</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_kanban"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='date_part']" position="attributes">
                <attribute name="class" add="w-100 fw-bold" separator=" "/>
            </xpath>
            <xpath expr="//div[@id='date_part']" position="after">
                <span
                    style="min-width: fit-content;"
                    t-attf-class="rounded-pill badge flex-shrink-1 text-bg-{{
                    record.state.raw_value == 'validate' ? 'success' :
                    ['confirm', 'validate1'].includes(record.state.raw_value) ?
                    'warning' : 'danger' }}"
                    t-out="record.state.value"/>
            </xpath>
            <xpath expr="//div[hasclass('hr_leave_kanban_title')]" position="replace"/>
        </field>
    </record>

    <record id="hr_leave_action_my" model="ir.actions.act_window">
        <field name="name">My Time Off</field>
        <field name="res_model">hr.leave</field>
        <field name="path">my-time-off</field>
        <field name="view_mode">list,form,kanban,activity</field>
        <field name="search_view_id" ref="hr_leave_view_search_my"/>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('hr_leave_view_kanban_my')})]"/>
        <field name="domain">[('user_id', '=', uid)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Keep track of your requests.
            </p><p>
                A great way to keep track on your time off requests, sick days, and approval status.
            </p>
        </field>
    </record>

    <record id="hr_leave_action_my_view_tree" model="ir.actions.act_window.view">
        <field name="sequence">1</field>
        <field name="view_mode">list</field>
        <field name="act_window_id" ref="hr_leave_action_my"/>
        <field name="view_id" ref="hr_leave_view_tree_my"/>
    </record>

    <record id="hr_leave_action_my_view_form" model="ir.actions.act_window.view">
        <field name="sequence">2</field>
        <field name="view_mode">form</field>
        <field name="act_window_id" ref="hr_leave_action_my"/>
        <field name="view_id" ref="hr_leave_view_form"/>
    </record>

    <record id="hr_leave_action_action_approve_department" model="ir.actions.act_window">
        <field name="name">All Time Off</field>
        <field name="res_model">hr.leave</field>
        <field name="path">time-off-approval</field>
        <field name="view_mode">kanban,list,form,calendar,activity</field>
        <field name="search_view_id" ref="hr_holidays.hr_leave_view_search_manager"/>
        <field name="context">{
            'search_default_waiting_for_me': 1,
            'search_default_waiting_for_me_manager': 2,
            'search_default_current_year': 3,
            'hide_employee_name': 1,
            }
        </field>
        <field name="domain">[('employee_id.company_id', 'in', allowed_company_ids)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Meet the time off dashboard.
            </p><p>
                A great way to keep track on employee’s time off, sick days, and approval status.
            </p>
        </field>
    </record>

    <record id="hr_leave_action_holiday_allocation_id" model="ir.actions.act_window">
        <field name="name">Time Off</field>
        <field name="res_model">hr.leave</field>
        <field name="view_mode">list,kanban,form,calendar,activity</field>
        <field name="search_view_id" ref="hr_holidays.hr_leave_view_search_manager"/>
        <field name="context">{
            'hide_employee_name': 1,
            }
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Meet the time off dashboard.
            </p><p>
                A great way to keep track on employee’s PTOs, sick days, and approval status.
            </p>
        </field>
    </record>

    <record id="action_view_kanban_manager_approve" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="hr_leave_view_kanban"/>
        <field name="act_window_id" ref="hr_leave_action_action_approve_department"/>
    </record>
    <record id="action_view_tree_manager_approve" model="ir.actions.act_window.view">
        <field name="sequence" eval="2"/>
        <field name="view_mode">list</field>
        <field name="view_id" ref="hr_leave_view_tree"/>
        <field name="act_window_id" ref="hr_leave_action_action_approve_department"/>
    </record>
    <record id="action_view_form_manager_approve" model="ir.actions.act_window.view">
        <field name="sequence" eval="3"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_leave_view_form_manager"/>
        <field name="act_window_id" ref="hr_leave_action_action_approve_department"/>
    </record>
    <record id="action_view_calendar_manager_approve" model="ir.actions.act_window.view">
        <field name="sequence" eval="4"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" eval="False"/>
        <field name="act_window_id" ref="hr_leave_action_action_approve_department"/>
    </record>
    <record id="action_view_activity_manager_approve" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">activity</field>
        <field name="view_id" eval="False"/>
        <field name="act_window_id" ref="hr_leave_action_action_approve_department"/>
    </record>

    <record id="view_holiday_pivot" model="ir.ui.view">
        <field name="name">hr.holidays.report_pivot</field>
        <field name="model">hr.leave</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <pivot string="Time Off Summary" sample="1">
                <field name="request_hour_from" invisible="1"/>
                <field name="request_hour_to" invisible="1"/>
                <field name="color" invisible="1"/>
                <field name="employee_id" type="row"/>
                <field name="date_from" type="col"/>
                <field name="number_of_days" type="measure"/>
            </pivot>
        </field>
    </record>

    <record id="view_holiday_graph" model="ir.ui.view">
        <field name="name">hr.holidays.report_graph</field>
        <field name="model">hr.leave</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <graph string="Time Off Summary" sample="1">
                <field name="request_hour_from" invisible="1"/>
                <field name="request_hour_to" invisible="1"/>
                <field name="color" invisible="1"/>
                <field name="employee_id"/>
                <field name="number_of_hours" widget="float_time"/>
                <field name="number_of_days" type="measure"/>
            </graph>
        </field>
    </record>

    <record id="view_holiday_list" model="ir.ui.view">
        <field name="name">hr.holidays.report_list</field>
        <field name="model">hr.leave</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <list string="Time Off Summary" sample="1" duplicate="false">
                <field name="employee_id" widget="many2one_avatar_employee"/>
                <field name="number_of_days" string="# days" type="measure"/>
                <field name="date_from" string="Start"/>
                <field name="date_to" string="Stop"/>
                <field name="name" optional="hide"/>
                <field name="state"/>
            </list>
        </field>
    </record>

    <record id="action_hr_available_holidays_report" model="ir.actions.act_window">
        <field name="name">Time Off by Employee</field>
        <field name="res_model">hr.leave</field>
        <field name="view_mode">list,graph,pivot,calendar,form</field>
        <field name="search_view_id" ref="hr_holidays.view_hr_holidays_filter"/>
        <field name="context">{'search_default_filter_date_from': 1, 'search_default_group_employee': 1, 'search_default_group_type': 1}</field>
        <field name="domain">[('state', '!=', 'cancel')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No Time off yet!
            </p>
            <p>
                Check back later to review all your employees' time off
            </p>
        </field>
    </record>

    <record id="action_window_leave_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="4"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="view_holiday_pivot"/>
        <field name="act_window_id" ref="action_hr_available_holidays_report"/>
    </record>

    <record id="action_window_leave_list" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">list</field>
        <field name="view_id" ref="view_holiday_list"/>
        <field name="act_window_id" ref="action_hr_available_holidays_report"/>
    </record>

    <record id="action_window_leave_graph" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="view_holiday_graph"/>
        <field name="act_window_id" ref="action_hr_available_holidays_report"/>
    </record>

</odoo>
