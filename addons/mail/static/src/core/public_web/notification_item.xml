<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

<t t-name="mail.NotificationItem">
    <ActionSwiper onLeftSwipe="props.onSwipeLeft ? props.onSwipeLeft : undefined" onRightSwipe="props.onSwipeRight ? props.onSwipeRight : undefined">
        <button class="o-mail-NotificationItem list-group-item d-flex cursor-pointer align-items-center w-100 gap-2 position-relative border o-rounded-bubble o-py-1_5 shadow-sm" t-on-click="onClick" t-ref="root" t-att-class="{
            'o-important': props.muted === 0,
            'text-muted border-transparent': props.muted === 1,
            'opacity-50 border-transparent': props.muted === 2,
            'px-1 o-small': ui.isSmall,
            'border-top-0': props.first,
            'p-2': !ui.isSmall,
            'o-active': props.isActive,
        }">
            <div class="o-mail-NotificationItem-avatarContainer position-relative bg-inherit flex-shrink-0 align-self-start" t-att-class="{ 'o-small': ui.isSmall }">
                <img class="o_avatar w-100 h-100 rounded" alt="Notification Item Image" t-att-src="props.iconSrc"/>
                <t t-slot="icon"/>
            </div>
            <div class="d-flex flex-column flex-grow-1 align-self-start overflow-auto ps-1">
                <div class="d-flex text-nowrap">
                    <span class="o-mail-NotificationItem-name fw-bold" t-att-class="{ 'fw-bold': props.muted, 'fw-bolder': !props.muted }" t-att-style="props.nameMaxLine !== undefined ? webkitLineClamp(props.nameMaxLine) : ''">
                        <t t-slot="name"/>
                    </span>
                    <span class="flex-grow-1"/>
                    <div class="d-flex align-items-center">
                        <span t-if="props.counter > 0 and !rootHover.isHover" t-attf-class="o-mail-NotificationItem-badge o-discuss-badge {{props.muted === 2 ? 'o-muted' : ''}} d-flex align-items-center justify-content-center m-0 badge rounded-pill fw-bold o-mail-NotificationItem-counter shadow-sm"><t t-esc="props.counter"/></span>
                        <span t-if="props.hasMarkAsReadButton and rootHover.isHover" class="o-mail-NotificationItem-badge o-discuss-badgeShape text-success d-flex align-items-center justify-content-center m-0 rounded-3 fw-bold o-mail-NotificationItem-markAsRead fa fa-check text-600 opacity-75 opacity-100-hover cursor-pointer shadow-sm" title="Mark As Read" t-ref="markAsRead"/>
                    </div>
                </div>
                <div class="d-flex">
                    <div class="o-mail-NotificationItem-text opacity-75" t-att-class="{ 'fw-bold': !props.muted, 'text-truncate': props.textTruncate, 'text-start': !props.textTruncate  }" t-att-style="props.textMaxLine !== undefined ? webkitLineClamp(props.textMaxLine) : ''">
                        <t t-slot="body-icon"/>
                        <t t-if="props.slots?.body" name="notificationBody" t-slot="body"/>
                    </div>
                    <div class="flex-grow-1"/>
                    <small t-if="props.datetime" class="o-mail-NotificationItem-date ms-2 me-1 opacity-75 flex-shrink-0 align-self-end" t-att-class="{ 'fw-bold': !props.muted }" t-att-title="props.datetime?.toLocaleString(DateTime.DATETIME_SHORT) ?? ''">
                        <t t-esc="dateText"/>
                    </small>
                </div>
            </div>
            <t t-if="props.slots and props.slots.sideContent">
                <t t-slot="sideContent" />
            </t>
        </button>
    </ActionSwiper>
</t>

</templates>
