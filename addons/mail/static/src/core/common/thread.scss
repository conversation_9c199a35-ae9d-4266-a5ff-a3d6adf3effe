.o-mail-Thread:focus-visible {
    outline: 0;
}

.o-mail-Thread-jumpPresent {
    z-index: $o-mail-NavigableList-zIndex - 1;
    --border-opacity: .5;
}

.o-mail-Thread-newMessage {
    transition: opacity 0.5s;

    span {
        --bg-opacity: 90%;
        clip-path: polygon(-1px 50%, 15% 0%, 100% 0%, 100% 100%, 15% 100%);
    }
}

.o-mail-Thread-avatar {
    --avatar-size: 56px;
    --icon-font-size: 4em;

    &.o-mail-Thread-avatarChatWindow {
        --avatar-size: 28px;
        --icon-font-size: 1.5em;
    }

    img {
        height: var(--avatar-size);
        width: var(--avatar-size);
    }

    i::before {
        font-size: var(--icon-font-size);
    }
}

.o-mail-Thread-newMessageLine {
    --border-opacity: .5;
}

.o_mail_notification {
    & a:hover {
        text-decoration: underline;
    }
    display: inline;
}

.o-mail-NotificationMessage p {
    margin-bottom: 0;
}

.o-mail-NotificationMessage:has(.o_hide_author) {
    & .o-mail-NotificationMessage-author {
        display: none!important;
    }
}

.o-mail-Thread-banner {
    z-index: $o-mail-NavigableList-zIndex - 1;
    --border-opacity: 0.35;
}

.o-mail-Thread-bannerHover:hover {
    filter: brightness(98%);
}
