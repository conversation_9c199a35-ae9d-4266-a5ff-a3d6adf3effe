<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="html_builder.Snippet">
    <div class="o_snippet"
         t-att-class="{'o_disabled': snippet.isDisabled, 'o_to_install': snippet.isInstallable, 'o_draggable': !snippet.isInstallable and !snippet.isDisabled}"
         t-att-data-snippet-group="snippet.groupName"
         t-att-name="snippet.title"
         t-att-data-id="snippet.id"
         t-att-data-tooltip="snippet.isDisabled and props.disabledTooltip"
         t-on-mouseover="onInstallableHover"
         t-on-mouseout="onInstallableHover">
        <div class="o_snippet_thumbnail" t-att-data-snippet="snippet.name">
            <button t-if="!snippet.isInstallable" class="o_snippet_thumbnail_area" t-on-click="props.onClickHandler" title="Insert snippet"/>
            <Img t-if="snippet.isDisabled" src="'/html_builder/static/img/snippet_disabled.svg'" class="'o_snippet_undroppable'"/>
            <div class="o_snippet_thumbnail_img" t-attf-style="background-image: url({{snippet.thumbnailSrc}});"/>
            <span class="o_snippet_thumbnail_title" t-esc="snippet.title"/>
            <button t-if="snippet.isInstallable" class="o_install_btn visually-hidden-focusable btn btn-primary border-0 w-100" t-on-click="() => props.snippetModel.installSnippetModule(snippet)">Install</button>
        </div>
    </div>
</t>

</templates>
