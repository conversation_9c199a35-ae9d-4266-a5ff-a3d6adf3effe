.o_add_snippets_preview {
    overflow: hidden;

    > body {
        overflow-x: hidden;
        overflow-y: auto;
        width: 30%;
        height: 30%;
        background-color: unset;
        scrollbar-gutter: stable both-edges;
    }
    .o_snippets_preview_row {
        position: absolute; // Needed for RTL
        transform: scale(0.3);
        transform-origin: top left;
        width: 333%;
        height: 100%;

        .o_snippet_preview_wrap {
            min-height: 100px;
            box-shadow: 0 0 6rem rgba(0, 0, 0, 0.16);
            margin: map-get($spacers, 5) (map-get($spacers, 5) * .5) (map-get($spacers, 3) * 4);
            background-color: var(--body-bg);
            transform: scale(.98);
            cursor: pointer;

            [data-snippet="s_carousel"],
            [data-snippet="s_carousel_intro"],
            [data-snippet="s_carousel_cards"],
            [data-snippet="s_quotes_carousel_minimal"],
            [data-snippet="s_quotes_carousel"] {
                height: 550px;
            }
            [data-snippet="s_quotes_carousel_compact"] {
                height: 350px;
            }
            [data-snippet="s_three_columns"] .figure-img[style*="height:50vh"] {
                /* In Travel theme. */
                height: 500px !important;
            }
            [data-snippet="s_numbers_charts"] .s_chart {
                display: none;
            }
            [data-snippet="s_dynamic_category"] .s_category_container {
                grid-auto-rows: minmax(150px, auto) !important;
            }
            .o_full_screen_height, .o_half_screen_height {
                height: unset !important;
                min-height: unset !important;
            }
            .o_full_screen_height {
                aspect-ratio: 4 / 3;
            }
            .o_half_screen_height {
                aspect-ratio: 8 / 3;
            }
            > [data-snippet] {
                isolation: isolate;
                pointer-events: none;
                user-select: none;

                &[data-snippet="s_text_block"] {
                    font-size: 1.6rem;
                }

                &.s_popup {
                    min-height: 660px;

                    > .modal {
                        --black-50: rgba(0, 0, 0, 0.25) !important;
                        display: block;
                        opacity: 1;
                        position: absolute;

                        .modal-dialog {
                            transform: none;
                        }
                        .modal-content {
                            box-shadow: 0rem 3.5rem 12rem rgba(0, 0, 0, 0.6);
                        }
                    }
                }
            }
            > [data-snippet][data-preview-interaction-enabled="true"] {
                > *:first-child {
                    // Enable pointer events on the snippet when preview
                    // interaction is enabled to allow mouseenter/mouseleave
                    // events.
                    pointer-events: auto;
                }

                > *:first-child * {
                    // Disable pointer events for descendants to prevent
                    // unwanted hover effects.
                    // Descendants that require interaction can be excluded
                    // using the :not() selector.
                    pointer-events: none;
                }
            }
            &[data-label]:not([data-label=""])::before {
                content: attr(data-label);
                @include o-position-absolute(0, 0);
                z-index: 1;
                transform-origin: top right;
                transform: scale(3.4);
                font-family: $o-we-font-family;
                font-size: $o-we-sidebar-font-size;
                background-color: $o-we-toolbar-color-accent;
                color: white;
                padding: 6px 12px;
                border-bottom-left-radius: 5px;
            }
            &::after {
                content: "";
                @include o-position-absolute(0, 0, 0, 0);
                outline: 6px solid transparent;
                pointer-events: none;
                z-index: 1;
            }
            &:hover {
                box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.2);
                &::after {
                    outline: 6px solid $o-we-handles-accent-color;
                }
            }
            &.o_snippet_preview_install {
                .s_dialog_preview_image {
                    filter: saturate(0) blur(5px);
                }
                &:hover {
                    .s_dialog_preview_image {
                        opacity: 1;
                        filter: saturate(0) brightness(0.6) blur(5px);
                    }
                     > .o_snippet_preview_install_btn {
                        opacity: 1;
                     }
                }
                > .o_snippet_preview_install_btn {
                    z-index: 1;
                    @include o-position-absolute(auto, 0, 0, 0);
                    opacity: 0;
                    transform: scale(3.33);
                    width: max-content;
                    font-size: 14px;
                    font-family: $o-we-font-family;
                    background-color: $o-we-color-info;
                    transition: 0.3s;
                }
            }
            .s_dialog_preview_image {
                display: flex;
                flex-direction: column;
                img {
                    max-width: 100%;
                }
            }

            .o_animate {
                visibility: visible;
                animation-name: none;
            }
        }

        .o_custom_snippet_edit {
                > * {
                color: rgba(0, 0, 0, 0.6);
                font-size: 50px;
                font-family: $o-we-font-family;
                }
                .btn:hover {
                color: rgba(0, 0, 0, 0.8);
                }
        }
    }
}
