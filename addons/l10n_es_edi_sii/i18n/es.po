# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_edi_sii
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-13 13:18+0000\n"
"PO-Revision-Date: 2025-02-13 13:18+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_account_move_send
msgid "Account Move Send"
msgstr "Envío de movimiento de cuenta"

#. module: l10n_es_edi_sii
#: model:ir.model.fields.selection,name:l10n_es_edi_sii.selection__res_company__l10n_es_sii_tax_agency__aeat
msgid "Agencia Tributaria española"
msgstr "Agencia Tributaria española"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_bank_statement_line__l10n_es_edi_csv
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__l10n_es_edi_csv
msgid "CSV return code"
msgstr "Código de devolución CSV"

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_certificate_certificate
msgid "Certificate"
msgstr "Certificado"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__l10n_es_sii_certificate_id
msgid "Certificate (SII)"
msgstr "Certificado (SII)"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_certificate_certificate__scope
msgid "Certificate scope"
msgstr "Alcance del certificado"

#. module: l10n_es_edi_sii
#: model:ir.ui.menu,name:l10n_es_edi_sii.menu_l10n_es_edi_sii_certificates
msgid "Certificates"
msgstr "Certificados"

#. module: l10n_es_edi_sii
#: model:ir.actions.act_window,name:l10n_es_edi_sii.l10n_es_edi_sii_certificate_action
msgid "Certificates for SII EDI invoices on Spain"
msgstr "Certificados para facturas SII EDI de España"

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: l10n_es_edi_sii
#: model_terms:ir.actions.act_window,help:l10n_es_edi_sii.l10n_es_edi_sii_certificate_action
msgid "Create the first certificate"
msgstr "Crear el primer certificado"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_edi_format__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move_send__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_resequence_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_certificate_certificate__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_account_edi_format
msgid "EDI format"
msgstr "Formato EDI"

#. module: l10n_es_edi_sii
#: model:ir.model.fields.selection,name:l10n_es_edi_sii.selection__res_company__l10n_es_sii_tax_agency__bizkaia
msgid "Hacienda Foral de Bizkaia"
msgstr "Hacienda Foral de Bizkaia"

#. module: l10n_es_edi_sii
#: model:ir.model.fields.selection,name:l10n_es_edi_sii.selection__res_company__l10n_es_sii_tax_agency__gipuzkoa
msgid "Hacienda Foral de Gipuzkoa"
msgstr "Hacienda Foral de Gipuzkoa"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_edi_format__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move_send__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_resequence_wizard__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_certificate_certificate__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__id
msgid "ID"
msgstr "ID"

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid ""
"In case of a foreign customer, you need to configure the tax scope on taxes:\n"
"%s"
msgstr ""
"En el caso de un cliente extranjero, es necesario configurar el ámbito fiscal en impuestos:\n"
"%s"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_bank_statement_line__l10n_es_edi_is_required
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__l10n_es_edi_is_required
msgid "Is the Spanish EDI needed"
msgstr "¿Se necesita el EDI español?"

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__l10n_es_sii_certificate_ids
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__l10n_es_sii_certificate_ids
msgid "L10N Es Sii Certificate"
msgstr "L10N ES Certificado SII"

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one main tax."
msgstr "La línea %s solo debe tener un impuesto principal."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one no sujeto (localizations) tax."
msgstr "La línea %s solo debe tener un impuesto no sujeto (localizaciones)."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one no sujeto tax."
msgstr "La línea %s solo debe tener un impuesto no sujeto."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one recargo tax."
msgstr "La línea %s solo debe tener un impuesto de recargo."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one retention tax."
msgstr "La línea %s solo debe tener un impuesto de retención."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one sujeto tax."
msgstr "La línea %s solo debe tener un impuesto sujeto."

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "Manage certificates (SII)"
msgstr "Gestionar certificados (SII)"

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid ""
"Networking error:\n"
"%s"
msgstr ""
"Error de red:\n"
"%s"

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "No tax agency selected: SII not activated."
msgstr "No se ha seleccionado ninguna agencia tributaria: SII no activado."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Please configure the certificate for SII."
msgstr "Configure el certificado para el SII."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Please specify a tax agency on your company for SII."
msgstr "Especifique una agencia tributaria en su compañía para el SII"

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "Production mode: EDI data is sent to the official agency servers."
msgstr ""
"Modo de producción: los datos EDI se envían a los servidores de la agencia "
"oficial."

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_bank_statement_line__l10n_es_registration_date
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__l10n_es_registration_date
msgid "Registration Date"
msgstr "Fecha de registro"

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "Registro de Libros connection SII"
msgstr "Connexión con el Registro de Libros mediante SII"

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr "Haga de nuevo la secuencia de asientos contables."

#. module: l10n_es_edi_sii
#: model:ir.model.fields.selection,name:l10n_es_edi_sii.selection__certificate_certificate__scope__sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.certificate_certificate_view_search
msgid "SII"
msgstr "SII"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__l10n_es_sii_test_env
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__l10n_es_sii_test_env
msgid "SII Test Mode"
msgstr "Modo de prueba SII"

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.certificate_certificate_view_search
msgid "SII certificates"
msgstr "Certificados SII"

#. module: l10n_es_edi_sii
#: model:ir.ui.menu,name:l10n_es_edi_sii.menu_l10n_es_edi_sii_root
msgid "Spain SII"
msgstr "SII España"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__l10n_es_sii_tax_agency
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__l10n_es_sii_tax_agency
msgid "Tax Agency for SII"
msgstr "Agencia Tributaria para SII"

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "Tax agency selected: invoices will be sent by SII."
msgstr ""
"Agencia tributaria seleccionada: las facturas serán enviadas por el SII."

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid ""
"Test mode: EDI data is sent to separate test servers and is not considered "
"official."
msgstr ""
"Modo de prueba: los datos EDI se envían a servidores de prueba "
"independientes y no se consideran oficiales."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "The SSL certificate could not be validated."
msgstr "No se ha podido validar el certificado SSL"

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "The web service is not responding"
msgstr "El servicio web no responde"

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "This was accepted with errors: "
msgstr "Esto se ha aceptado con errores:"

#. module: l10n_es_edi_sii
#: model:ir.model.fields,help:l10n_es_edi_sii.field_res_company__l10n_es_sii_test_env
#: model:ir.model.fields,help:l10n_es_edi_sii.field_res_config_settings__l10n_es_sii_test_env
msgid "Use the test environment for SII"
msgstr "Utilizar el entorno de prueba para SII"

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "VAT number is missing on company %s"
msgstr "Falta el NIF de la compañía %s"

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid ""
"We saw that this invoice was sent correctly before, but we did not treat the"
" response.  Make sure it is not because of a wrong configuration."
msgstr ""
"Esta factura se ha enviada correctamente antes, pero no procesamos la "
"respuesta. Asegúrese de que no se deba a una configuración incorrecta."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "You should put a vendor reference on this vendor bill. "
msgstr ""
"Debe poner una referencia del proveedor en esta factura del proveedor."

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "[%(error_code)s] %(error_message)s"
msgstr "[%(error_code)s] %(error_message)s"
