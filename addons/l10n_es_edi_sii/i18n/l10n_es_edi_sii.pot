# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_edi_sii
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-21 14:26+0000\n"
"PO-Revision-Date: 2025-05-21 14:26+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_account_move_send
msgid "Account Move Send"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields.selection,name:l10n_es_edi_sii.selection__res_company__l10n_es_sii_tax_agency__aeat
msgid "Agencia Tributaria española"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_bank_statement_line__l10n_es_edi_csv
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__l10n_es_edi_csv
msgid "CSV return code"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_certificate_certificate
msgid "Certificate"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__l10n_es_sii_certificate_id
msgid "Certificate (SII)"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_certificate_certificate__scope
msgid "Certificate scope"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.ui.menu,name:l10n_es_edi_sii.menu_l10n_es_edi_sii_certificates
msgid "Certificates"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.actions.act_window,name:l10n_es_edi_sii.l10n_es_edi_sii_certificate_action
msgid "Certificates for SII EDI invoices on Spain"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_es_edi_sii
#: model_terms:ir.actions.act_window,help:l10n_es_edi_sii.l10n_es_edi_sii_certificate_action
msgid "Create the first certificate"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_edi_format__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move_send__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_resequence_wizard__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_certificate_certificate__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields.selection,name:l10n_es_edi_sii.selection__res_company__l10n_es_sii_tax_agency__bizkaia
msgid "Hacienda Foral de Bizkaia"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields.selection,name:l10n_es_edi_sii.selection__res_company__l10n_es_sii_tax_agency__gipuzkoa
msgid "Hacienda Foral de Gipuzkoa"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_edi_format__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move_send__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_resequence_wizard__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_certificate_certificate__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__id
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__id
msgid "ID"
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid ""
"In case of a foreign customer, you need to configure the tax scope on taxes:\n"
"%s"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_bank_statement_line__l10n_es_edi_is_required
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__l10n_es_edi_is_required
msgid "Is the Spanish EDI needed"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__l10n_es_sii_certificate_ids
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__l10n_es_sii_certificate_ids
msgid "L10N Es Sii Certificate"
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one main tax."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one no sujeto (localizations) tax."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one no sujeto tax."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one recargo tax."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one retention tax."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Line %s should only have one sujeto tax."
msgstr ""

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "Manage certificates (SII)"
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid ""
"Networking error:\n"
"%s"
msgstr ""

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "No tax agency selected: SII not activated."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Please configure the certificate for SII."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "Please specify a tax agency on your company for SII."
msgstr ""

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "Production mode: EDI data is sent to the official agency servers."
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_bank_statement_line__l10n_es_registration_date
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_account_move__l10n_es_registration_date
msgid "Registration Date"
msgstr ""

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "Registro de Libros connection SII"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model,name:l10n_es_edi_sii.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields.selection,name:l10n_es_edi_sii.selection__certificate_certificate__scope__sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.certificate_certificate_view_search
msgid "SII"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__l10n_es_sii_test_env
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__l10n_es_sii_test_env
msgid "SII Test Mode"
msgstr ""

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.certificate_certificate_view_search
msgid "SII certificates"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.ui.menu,name:l10n_es_edi_sii.menu_l10n_es_edi_sii_root
msgid "Spain SII"
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_company__l10n_es_sii_tax_agency
#: model:ir.model.fields,field_description:l10n_es_edi_sii.field_res_config_settings__l10n_es_sii_tax_agency
msgid "Tax Agency for SII"
msgstr ""

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid "Tax agency selected: invoices will be sent by SII."
msgstr ""

#. module: l10n_es_edi_sii
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_sii.res_config_settings_view_form
msgid ""
"Test mode: EDI data is sent to separate test servers and is not considered "
"official."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "The SSL certificate could not be validated."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "The web service is not responding"
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "This was accepted with errors: "
msgstr ""

#. module: l10n_es_edi_sii
#: model:ir.model.fields,help:l10n_es_edi_sii.field_res_company__l10n_es_sii_test_env
#: model:ir.model.fields,help:l10n_es_edi_sii.field_res_config_settings__l10n_es_sii_test_env
msgid "Use the test environment for SII"
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "VAT number is missing on company %s"
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid ""
"We saw that this invoice was sent correctly before, but we did not treat the"
" response.  Make sure it is not because of a wrong configuration."
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "You should put a vendor reference on this vendor bill. "
msgstr ""

#. module: l10n_es_edi_sii
#. odoo-python
#: code:addons/l10n_es_edi_sii/models/account_edi_format.py:0
msgid "[%(error_code)s] %(error_message)s"
msgstr ""
