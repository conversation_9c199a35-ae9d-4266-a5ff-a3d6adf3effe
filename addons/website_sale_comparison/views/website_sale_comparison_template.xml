<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="add_to_compare" inherit_id="website_sale.products_item" name="Comparison List" priority="22">
        <xpath expr="//div[hasclass('o_wsale_product_btn_list')]" position="inside">
            <t t-set="attrib_categories" t-value="product.valid_product_template_attribute_line_ids._prepare_categories_for_display()"/>
            <t t-set="product_variant_id" t-value="product._get_first_possible_variant_id()"/>
            <button
                type="button"
                role="button"
                t-attf-class="{{'' if product_variant_id and attrib_categories else 'invisible'}} d-none d-md-inline-block btn btn-light o_add_compare"
                title="Compare"
                aria-label="Compare"
                t-att-data-product-product-id="product_variant_id"
                data-action="o_comparelist"
            >
                <span class="fa fa-exchange"/>
            </button>
        </xpath>
        <xpath expr="//div[hasclass('o_wsale_product_btn_catalog')]" position="inside">
            <t t-set="attrib_categories" t-value="product.valid_product_template_attribute_line_ids._prepare_categories_for_display()"/>
            <t t-set="product_variant_id" t-value="product._get_first_possible_variant_id()"/>
            <button
                type="button"
                role="button"
                t-attf-class="{{'' if product_variant_id and attrib_categories else 'invisible'}} d-none d-md-inline-block btn btn-light o_add_compare"
                title="Compare"
                aria-label="Compare"
                t-att-data-product-product-id="product_variant_id"
                data-action="o_comparelist"
            >
                <span class="fa fa-exchange"/>
            </button>
        </xpath>
    </template>

    <template id="product_add_to_compare" name='Add to comparison in product page' inherit_id="website_sale.product" priority="8">
        <xpath expr="//div[@id='o_wsale_cta_wrapper']" position="after">
            <t t-set="attrib_categories" t-value="product.valid_product_template_attribute_line_ids._prepare_categories_for_display()"/>
            <t t-set="product_variant_id" t-value="product._get_first_possible_variant_id()"/>
            <button t-if="product_variant_id and attrib_categories"
                type="button"
                role="button"
                class="d-none d-md-block btn btn-link px-0 o_add_compare_dyn"
                aria-label="Compare"
                t-att-data-product-product-id="product_variant_id"
                data-action="o_comparelist">
                    <span class="fa fa-exchange me-2"/>Compare
            </button>
        </xpath>
    </template>

    <template id="product_attributes_body" inherit_id="website_sale.product" name="Product attributes table">
        <xpath expr="//div[@id='product_attributes_simple']" position="replace"/>
        <xpath expr="//div[@id='product_full_description']" position="after">
            <t t-set="attrib_categories" t-value="product.valid_product_template_attribute_line_ids._prepare_categories_for_display()"/>
            <t t-if="attrib_categories">
                <section class="pt32 pb32" id="product_full_spec">
                    <div class="container">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3 class="m-0">Specifications</h3>
                        </div>
                        <div id="product_specifications">
                            <div class="row">
                                <t t-foreach="attrib_categories" t-as="category">
                                    <div class="col-lg-6">
                                        <t t-call="website_sale_comparison.specifications_table"/>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </section>
            </t>
        </xpath>
    </template>

    <template
        id="accordion_specs_item"
        name="Specifications Accordion Item"
        inherit_id="website_sale.product_accordion"
        active="False"
    >
        <xpath expr="//div[@id='more_information_accordion_item']" position="before">
            <t t-if="product.valid_product_template_attribute_line_ids._prepare_categories_for_display()">
                <t t-foreach="attrib_categories" t-as="category">
                    <div class="accordion-item">
                        <div class="accordion-header my-0 h6">
                            <button
                                t-out="category.name"
                                class="accordion-button collapsed fw-medium"
                                type="button"
                                data-bs-toggle="collapse"
                                t-attf-data-bs-target="#category_accordion_{{category_index}}"
                                aria-expanded="false"
                                aria-controls="specifications"
                            >
                                <t t-if="category_size == 1">Specifications</t>
                                <t t-else="">Others</t>
                            </button>
                        </div>
                        <div
                            t-attf-id="category_accordion_{{category_index}}"
                            class="accordion-collapse collapse"
                            data-bs-parent="#product_accordion"
                        >
                            <div class="accordion-body pt-0">
                                <t t-call="website_sale_comparison.specifications_table">
                                    <t t-set="is_accordion" t-value="True"/>
                                </t>
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </xpath>
    </template>

    <template id="specifications_table" name="Specifications Table">
        <table t-attf-class="table {{is_accordion and 'table-sm mb-0'}}">
            <t t-if="len(attrib_categories) > 1 and not is_accordion">
                <tr>
                    <th class="text-start" colspan="2">
                        <span t-if="category" t-field="category.name"/>
                        <span t-else="">Others</span>
                    </th>
                </tr>
            </t>
            <tr
                t-foreach="attrib_categories[category].filtered(lambda l: len(l.value_ids) > 1)"
                t-as="ptal"
            >
                <t
                    t-set="hide_border_bottom_classes"
                    t-value="'border-bottom-0' if ptal_last and is_accordion else ''"
                />
                <td t-attf-class="w-25 {{hide_border_bottom_classes}} ps-0">
                    <span t-field="ptal.attribute_id.name"/>
                </td>
                <td t-attf-class="w-75 {{hide_border_bottom_classes}} pe-0 text-muted text-end">
                    <t t-foreach="ptal.value_ids" t-as="pav">
                        <span t-field="pav.name"/><t t-if="not pav_last">, </t>
                    </t>
                </td>
            </tr>
            <t
                t-set="single_value_attributes"
                t-value="attrib_categories[category]._prepare_single_value_for_display()"
            />
            <tr t-foreach="single_value_attributes" t-as="attribute">
                <t
                    t-set="hide_border_bottom_classes"
                    t-value="'border-bottom-0' if attribute_last and is_accordion else ''"
                />
                <td t-attf-class="w-25 {{hide_border_bottom_classes}} ps-0 ">
                    <span t-field="attribute.name"/>
                </td>
                <td t-attf-class="w-75 {{hide_border_bottom_classes}} pe-0 text-muted text-end">
                    <t t-foreach="single_value_attributes[attribute]" t-as="ptal">
                        <span t-field="ptal.product_template_value_ids._only_active().name"/><t t-if="not ptal_last">, </t>
                    </t>
                </td>
            </tr>
        </table>
    </template>

    <template id="product_compare" name="Comparator Page">
        <t t-call="website.layout">
            <t t-set="additional_title">Shop Comparator</t>
            <div id="wrap" class="js_sale">
                <div class="oe_structure oe_empty" id="oe_structure_website_sale_comparison_product_compare_1"/>
                <div class="container oe_website_sale pt-3">
                    <section class="container">
                        <h3>Compare Products</h3>
                        <table class="table table-bordered table-hover text-center mt16 table-comparator" id="o_comparelist_table">
                            <t t-set="attrib_categories" t-value="products._prepare_categories_for_display()"/>
                            <thead>
                                <tr>
                                    <td t-if="len(attrib_categories)" class='o_ws_compare_image td-top-left border-bottom-0'/>
                                    <td t-foreach="products" t-as="product" class="o_ws_compare_image position-relative border-bottom-0">
                                        <a href="#" t-att-data-product_product_id="product.id" class="o_comparelist_remove" t-if="len(products) &gt; 2">
                                            <strong>x</strong>
                                        </a>
                                        <a t-att-href="product.website_url">
                                            <img t-attf-src="/web/image/product.product/#{product.id}/image_256" class="img img-fluid" style="margin:auto;" alt="Product image"/>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td t-if="len(attrib_categories)" class='td-top-left border-top-0'/>
                                    <td t-foreach="products" t-as="product" class="border-top-0 align-top">
                                        <t t-set="combination_info" t-value="product._get_combination_info_variant()"/>
                                        <div class='product_summary'>
                                            <a class="o_product_comparison_table" t-att-href="product.website_url">
                                                <span t-esc="combination_info['display_name']"></span><br/>
                                            </a>
                                            <span class="o_comparison_price" t-if="not combination_info['prevent_zero_price_sale']">
                                                <strong>Price:</strong>
                                                <span t-out="combination_info['price']"
                                                      t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                                <del t-if="combination_info.get('compare_list_price') and (combination_info['compare_list_price'] &gt; combination_info['price'])"
                                                     t-attf-class="text-muted mr8"
                                                     style="white-space: nowrap;"
                                                     t-esc="combination_info['compare_list_price']"
                                                     t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" />
                                                <del t-else=""
                                                     t-attf-class="text-muted mr8 {{'' if combination_info['has_discounted_price'] else 'd-none'}}"
                                                     style="white-space: nowrap;"
                                                     t-out="combination_info['list_price']"
                                                     t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                                <small t-if="combination_info.get('base_unit_price')"
                                                       class="d-block text-muted">
                                                    <t t-call="website_sale.base_unit_price">
                                                        <t t-set="base_unit_price" t-value="combination_info['base_unit_price']"/>
                                                    </t>
                                                </small>
                                            </span>
                                            <form
                                                class="text-center o_add_cart_form_compare"
                                                t-att-data-show-quantity="is_view_active('website_sale.product_quantity')"
                                            >
                                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                                <input
                                                    name="product_template_id"
                                                    type="hidden"
                                                    t-att-value="product.product_tmpl_id.id"
                                                />
                                                <input
                                                    name="product_id"
                                                    type="hidden"
                                                    t-att-value="product.id"
                                                />
                                                <a t-if="combination_info['prevent_zero_price_sale']"
                                                   t-att-href="website.contact_us_button_url + '?subject=' + combination_info['display_name']"
                                                   class="btn btn-primary btn_cta">
                                                   Contact Us
                                                </a>
                                                <a t-else="" role="button" class="btn btn-primary a-submit" href="#">
                                                    <i class="fa fa-shopping-cart me-2"/>Add to Cart
                                                </a>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="attrib_categories" t-as="category">
                                    <t t-if="len(attrib_categories) &gt; 1">
                                        <tr class="clickable" data-bs-toggle="collapse" t-att-data-bs-target="'.o_ws_category_%d' % category.id">
                                            <th class="text-start" t-att-colspan="len(products) + 1"><i class="fa fa-chevron-circle-down o_product_comparison_collpase" role="img" aria-label="Collapse" title="Collapse"></i><span t-if="category" t-field="category.name"/><span t-else="">Uncategorized</span></th>
                                        </tr>
                                    </t>
                                    <tr t-foreach="attrib_categories[category]" t-as="attribute" t-att-class="'collapse show o_ws_category_%d' % category.id">
                                        <td><span t-field="attribute.name"/></td>
                                        <td t-foreach="attrib_categories[category][attribute]" t-as="product">
                                            <t t-foreach="attrib_categories[category][attribute][product]" t-as="ptav">
                                                <span t-field="ptav.name"/><t t-if="not ptav_last">, </t>
                                            </t>
                                        </td>
                                    </tr>
                                </t>
                                <t t-if="is_view_active('website_sale.product_tags') and any([product.all_product_tag_ids for product in products])">
                                    <tr class="clickable" data-bs-toggle="collapse" data-bs-target=".o_ws_tags">
                                        <th class="text-start" t-att-colspan="len(products) + 1">
                                            <i class="fa fa-chevron-circle-down o_product_comparison_collpase" role="img" aria-label="Collapse" title="Collapse"></i><span>Tags</span>
                                        </th>
                                    </tr>
                                    <tr class="collapse show o_ws_tags">
                                        <td><span>Tags</span></td>
                                        <td t-foreach="products" t-as="product">
                                            <div class="d-flex justify-content-center">
                                                <t t-call="website_sale.product_tags">
                                                    <t t-set="all_product_tags" t-value="product.all_product_tag_ids"/>
                                                </t>
                                            </div>

                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </section>
                </div>
                <div class="oe_structure" id="oe_structure_website_sale_comparison_product_compare_2"/>
            </div>
        </t>
    </template>

    <template id="product_product" name="Comparator - Product row in comparator popover">
        <t t-set="combination_info" t-value="product._get_combination_info_variant()"/>
        <div class="row g-0 align-items-center my-1 o_product_row" t-att-data-category_ids="product.public_categ_ids.ids">
            <div class="col-3 text-center">
                <img class="img o_image_64_max" t-att-src="website.image_url(product, 'image_128')" alt="Product image"/>
            </div>
            <div class="col-8 ps-2">
                <h6>
                    <a t-att-href="product.website_url"><t t-esc="combination_info['display_name']" /></a><br/>
                    <div t-attf-class="{{'d-none' if combination_info['prevent_zero_price_sale'] else ''}}">
                        <span t-out="combination_info['price']"
                              t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                        <del t-if="combination_info.get('compare_list_price') and (combination_info['compare_list_price'] &gt; combination_info['price'])"
                             t-attf-class="text-muted me-1 h6 mb-0 small"
                             style="white-space: nowrap;"
                             t-esc="combination_info['compare_list_price']"
                             t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" />
                        <del t-else=""
                             t-attf-class="text-muted me-1 h6 mb-0 small {{'' if combination_info['has_discounted_price'] else 'd-none'}}"
                             style="white-space: nowrap;"
                             t-esc="combination_info['list_price']"
                             t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                    </div>
                </h6>
            </div>
            <div class="col-1 text-end">
                <a href='#' class="o_remove" title="Remove" t-att-data-product_product_id="product.id">
                    <i class="fa fa-trash" role="img" aria-label="Remove"></i>
                </a>
            </div>
        </div>
    </template>

</odoo>
