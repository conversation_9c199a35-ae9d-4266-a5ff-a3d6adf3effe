{"version": "18.4.10", "sheets": [{"id": "cf4e83c3-9ba8", "name": "Dashboard", "colNumber": 28, "rowNumber": 204, "rows": {"0": {"size": 32}, "1": {"size": 103}, "2": {"size": 22}, "3": {"size": 32}, "4": {"size": 21}, "26": {"size": 41}, "27": {"size": 46}, "28": {"size": 27}, "29": {"size": 33}, "30": {"size": 33}, "31": {"size": 33}, "32": {"size": 34}, "33": {"size": 37}, "50": {"size": 32}}, "cols": {"0": {"size": 50}, "1": {"size": 50}, "2": {"size": 50}, "3": {"size": 50}, "4": {"size": 50}, "5": {"size": 50}, "6": {"size": 50}, "7": {"size": 50}, "8": {"size": 50}, "9": {"size": 50}, "10": {"size": 50}, "11": {"size": 50}, "12": {"size": 50}, "13": {"size": 50}, "14": {"size": 50}, "15": {"size": 50}, "16": {"size": 50}, "17": {"size": 50}, "18": {"size": 50}, "19": {"size": 50}, "20": {"size": 50}, "21": {"size": 50}, "22": {"size": 50}, "23": {"size": 50}, "24": {"size": 50}, "25": {"size": 50}, "26": {"size": 50}, "27": {"size": 50}}, "merges": ["A4:D4", "A33:C33", "A107:F107", "P107:U107", "A73:E73", "A52:D52", "A126:C126", "P126:S126", "A128:J128", "K128:M128", "K129:M129", "A129:J129", "P128:Y128", "Z128:AB128", "P129:Y129", "Z129:AB129", "A137:J137", "K137:M137", "A138:J138", "K138:M138", "P137:Y137", "Z137:AB137", "P138:Y138", "Z138:AB138", "A135:D135", "P135:T135", "A130:J130", "A131:J131", "A133:J133", "A132:J132", "K130:M130", "K131:M131", "K132:M132", "P130:Y130", "P131:Y131", "P132:Y132", "Z130:AB130", "Z131:AB131", "Z132:AB132", "A139:J139", "A140:J140", "A141:J141", "P139:Y139", "P140:Y140", "P141:Y141", "P142:Y142", "A142:J142", "K133:M133", "P133:Y133", "Z133:AB133", "K139:M139", "K140:M140", "K141:M141", "K142:M142", "Z139:AB139", "Z140:AB140", "Z141:AB141", "Z142:AB142", "Y34:AB34", "Y35:AB35", "Y36:AB36", "Y37:AB37", "Y38:AB38", "Y39:AB39", "Y40:AB40", "Y41:AB41", "Y42:AB42", "Y43:AB43", "Y44:AB44", "U34:X34", "U35:X35", "U36:X36", "U37:X37", "U38:X38", "U39:X39", "U40:X40", "U41:X41", "U42:X42", "U43:X43", "U44:X44", "Q34:T34", "Q35:T35", "Q36:T36", "Q37:T37", "Q38:T38", "Q39:T39", "Q40:T40", "Q41:T41", "Q42:T42", "Q43:T43", "Q44:T44", "M34:P34", "M35:P35", "M36:P36", "M37:P37", "M38:P38", "M39:P39", "M40:P40", "M41:P41", "M42:P42", "M43:P43", "M44:P44", "I34:L34", "I35:L35", "I36:L36", "I37:L37", "I38:L38", "I39:L39", "I40:L40", "I41:L41", "I42:L42", "I43:L43", "I44:L44", "A34:H34", "A35:H35", "A36:H36", "A37:H37", "A38:H38", "A39:H39", "A40:H40", "A41:H41", "A42:H42", "A43:H43", "A44:H44", "A54:H54", "A55:H55", "A56:H56", "A57:H57", "A58:H58", "A59:H59", "A60:H60", "A61:H61", "A62:H62", "A63:H63", "A64:H64", "I54:L54", "I55:L55", "M54:P54", "M55:P55", "Q54:T54", "Q55:T55", "I56:L56", "I57:L57", "I58:L58", "I59:L59", "I60:L60", "I61:L61", "I62:L62", "I63:L63", "I64:L64", "M56:P56", "M57:P57", "M58:P58", "M59:P59", "M60:P60", "M61:P61", "M62:P62", "M63:P63", "M64:P64", "Q56:T56", "Q57:T57", "Q58:T58", "Q59:T59", "Q60:T60", "Q61:T61", "Q62:T62", "Q63:T63", "Q64:T64"], "cells": {"A4": "[Sessions by Agent](odoo://view/{\"viewType\":\"graph\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_channel_action\",\"domain\":[],\"context\":{\"group_by\":[\"partner_id\"],\"graph_measure\":\"__count\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"partner_id\"],\"graph_order\":null,\"graph_stacked\":true},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sessions by Agent\"})", "A33": "[Top Agents](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_operator_action\",\"domain\":[],\"context\":{\"group_by\":[],\"pivot_measures\":[\"__count\",\"time_to_answer\",\"duration\",\"rating\",\"number_of_calls\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"partner_id\"]},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Agents\"})", "A34": "=_t(\"Agent\")", "A35": "=PIVOT.HEADER(3,\"#partner_id\", 1)", "A36": "=PIVOT.HEADER(3,\"#partner_id\",2)", "A37": "=PIVOT.HEADER(3,\"#partner_id\",3)", "A38": "=PIVOT.HEADER(3,\"#partner_id\",4)", "A39": "=PIVOT.HEADER(3,\"#partner_id\",5)", "A40": "=PIVOT.HEADER(3,\"#partner_id\",6)", "A41": "=PIVOT.HEADER(3,\"#partner_id\",7)", "A42": "=PIVOT.HEADER(3,\"#partner_id\",8)", "A43": "=PIVOT.HEADER(3,\"#partner_id\",9)", "A44": "=PIVOT.HEADER(3,\"#partner_id\",10)", "A52": "[Calls by Agents](odoo://view/{\"viewType\":\"graph\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_channel_action\",\"domain\":[],\"context\":{\"group_by\":[\"partner_id\"],\"graph_measure\":\"number_of_calls\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"partner_id\"],\"graph_order\":null,\"graph_stacked\":true},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Calls by Agent\"})", "A54": "=_t(\"Agent\")", "A55": "=PIVOT.HEADER(6,\"#partner_id\", 1)", "A56": "=PIVOT.HEADER(6,\"#partner_id\",2)", "A57": "=PIVOT.HEADER(6,\"#partner_id\",3)", "A58": "=PIVOT.HEADER(6,\"#partner_id\",4)", "A59": "=PIVOT.HEADER(6,\"#partner_id\",5)", "A60": "=PIVOT.HEADER(6,\"#partner_id\",6)", "A61": "=PIVOT.HEADER(6,\"#partner_id\",7)", "A62": "=PIVOT.HEADER(6,\"#partner_id\",8)", "A63": "=PIVOT.HEADER(6,\"#partner_id\",9)", "A64": "=PIVOT.HEADER(6,\"#partner_id\",10)", "A73": "[Sessions by Day](odoo://view/{\"viewType\":\"graph\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_channel_action\",\"domain\":[],\"context\":{\"group_by\":[\"start_date:day\"],\"graph_measure\":\"__count\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"start_date:day\"],\"graph_order\":null,\"graph_stacked\":true},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sessions by Day\"})", "A107": "[Sessions by Day of Week](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_operator_action\",\"domain\":[],\"context\":{\"group_by\":[\"day_number\"],\"pivot_measures\":[\"__count\",\"time_to_answer\",\"duration\",\"rating\",\"number_of_calls\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"partner_id\"]},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sessions by Day of Week\"})", "A126": "[Top Countries](odoo://view/{\"viewType\":\"graph\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_channel_action\",\"domain\":[],\"context\":{\"group_by\":[\"country_id\"],\"graph_measure\":\"__count\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"country_id\"],\"graph_order\":null,\"graph_stacked\":true},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Countries\"})", "A128": "=_t(\"Country\")", "A129": "=PIVOT.HEADER(9,\"#country_id\", 1)", "A130": "=PIVOT.HEADER(9,\"#country_id\",2)", "A131": "=PIVOT.HEADER(9,\"#country_id\",3)", "A132": "=PIVOT.HEADER(9,\"#country_id\",4)", "A133": "=PIVOT.HEADER(9,\"#country_id\",5)", "A135": "[Top Expertises](odoo://view/{\"viewType\":\"graph\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_channel_action\",\"domain\":[],\"context\":{\"group_by\":[\"session_expertises\"],\"graph_measure\":\"__count\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"session_expertises\"],\"graph_order\":null,\"graph_stacked\":true},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Expertises\"})", "A137": "=_t(\"Expertise\")", "A138": "=PIVOT.HEADER(11,\"#session_expertises\", 1)", "A139": "=PIVOT.HEADER(11,\"#session_expertises\",2)", "A140": "=PIVOT.HEADER(11,\"#session_expertises\",3)", "A141": "=PIVOT.HEADER(11,\"#session_expertises\",4)", "A142": "=PIVOT.HEADER(11,\"#session_expertises\",5)", "I34": "=_t(\"Sessions\")", "I35": "=PIVOT.VALUE(3,  \"__count\", \"#partner_id\", 1)", "I36": "=PIVOT.VALUE(3,\"__count\",\"#partner_id\",2)", "I37": "=PIVOT.VALUE(3,\"__count\",\"#partner_id\",3)", "I38": "=PIVOT.VALUE(3,\"__count\",\"#partner_id\",4)", "I39": "=PIVOT.VALUE(3,\"__count\",\"#partner_id\",5)", "I40": "=PIVOT.VALUE(3,\"__count\",\"#partner_id\",6)", "I41": "=PIVOT.VALUE(3,\"__count\",\"#partner_id\",7)", "I42": "=PIVOT.VALUE(3,\"__count\",\"#partner_id\",8)", "I43": "=PIVOT.VALUE(3,\"__count\",\"#partner_id\",9)", "I44": "=PIVOT.VALUE(3,\"__count\",\"#partner_id\",10)", "I54": "=_t(\"Sessions with Calls (#)\")", "I55": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\", 1)", "I56": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\",2)", "I57": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\",3)", "I58": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\",4)", "I59": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\",5)", "I60": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\",6)", "I61": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\",7)", "I62": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\",8)", "I63": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\",9)", "I64": "=PIVOT.VALUE(6,\"has_call:sum\",\"#partner_id\",10)", "K128": "=_t(\"Sessions\")", "K129": "=PIVOT.VALUE(9,\"__count\",\"#country_id\", 1)", "K130": "=PIVOT.VALUE(9,\"__count\",\"#country_id\",2)", "K131": "=PIVOT.VALUE(9,\"__count\",\"#country_id\",3)", "K132": "=PIVOT.VALUE(9,\"__count\",\"#country_id\",4)", "K133": "=PIVOT.VALUE(9,\"__count\",\"#country_id\",5)", "K137": "=_t(\"Sessions\")", "K138": "=PIVOT.VALUE(11,\"__count\",\"#session_expertises\", 1)", "K139": "=PIVOT.VALUE(11,\"__count\",\"#session_expertises\",2)", "K140": "=PIVOT.VALUE(11,\"__count\",\"#session_expertises\",3)", "K141": "=PIVOT.VALUE(11,\"__count\",\"#session_expertises\",4)", "K142": "=PIVOT.VALUE(11,\"__count\",\"#session_expertises\",5)", "M34": "=_t(\"Session Duration (min)\")", "M35": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",1)", "M36": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",2)", "M37": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",3)", "M38": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",4)", "M39": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",5)", "M40": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",6)", "M41": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",7)", "M42": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",8)", "M43": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",9)", "M44": "=PIVOT.VALUE(3,\"duration:avg\",\"#partner_id\",10)", "M54": "=_t(\"Sessions with Calls (%)\")", "M55": "=IF(I55, I55 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 1), \"\")", "M56": "=IF(I56, I56 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 2), \"\")", "M57": "=IF(I57, I57 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 3), \"\")", "M58": "=IF(I58, I58 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 4), \"\")", "M59": "=IF(I59, I59 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 5), \"\")", "M60": "=IF(I60, I60 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 6), \"\")", "M61": "=IF(I61, I61 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 7), \"\")", "M62": "=IF(I62, I62 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 8), \"\")", "M63": "=IF(I63, I63 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 9), \"\")", "M64": "=IF(I64, I64 / PIVOT.VALUE(6, \"__count\", \"#partner_id\", 10), \"\")", "P107": "[Sessions by Hour of Day](odoo://view/{\"viewType\":\"graph\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_channel_action\",\"domain\":[],\"context\":{\"group_by\":[\"start_hour\"],\"graph_measure\":\"__count\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"start_hour\"],\"graph_order\":null,\"graph_stacked\":true},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sessions by Hour of Day\"})", "P126": "[Top Languages](odoo://view/{\"viewType\":\"graph\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_channel_action\",\"domain\":[],\"context\":{\"group_by\":[\"lang_id\"],\"graph_measure\":\"__count\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"lang_id\"],\"graph_order\":null,\"graph_stacked\":true},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Languages\"})", "P128": "=_t(\"Language\")", "P129": "=PIVOT.HEADER(10,\"#lang_id\", 1)", "P130": "=PIVOT.HEADER(10,\"#lang_id\",2)", "P131": "=PIVOT.HEADER(10,\"#lang_id\",3)", "P132": "=PIVOT.HEADER(10,\"#lang_id\",4)", "P133": "=PIVOT.HEADER(10,\"#lang_id\",5)", "P135": "[Top Chatbot Answers](odoo://view/{\"viewType\":\"graph\",\"action\":{\"xmlId\":\"im_livechat.im_livechat_report_channel_action\",\"domain\":[],\"context\":{\"group_by\":[\"chatbot_answers_path\"],\"graph_measure\":\"__count\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"chatbot_answers_path\"],\"graph_order\":null,\"graph_stacked\":true},\"modelName\":\"im_livechat.report.channel\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Chatbot Answers\"})", "P137": "=_t(\"Chatbot Answer\")", "P138": "=PIVOT.HEADER(12,\"#chatbot_answers_path_str\", 1)", "P139": "=PIVOT.HEADER(12,\"#chatbot_answers_path_str\",2)", "P140": "=PIVOT.HEADER(12,\"#chatbot_answers_path_str\",3)", "P141": "=PIVOT.HEADER(12,\"#chatbot_answers_path_str\",4)", "P142": "=PIVOT.HEADER(12,\"#chatbot_answers_path_str\",5)", "Q34": "=_t(\"Time to Respond (sec)\")", "Q35": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\", 1)", "Q36": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\",2)", "Q37": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\",3)", "Q38": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\",4)", "Q39": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\",5)", "Q40": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\",6)", "Q41": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\",7)", "Q42": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\",8)", "Q43": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\",9)", "Q44": "=PIVOT.VALUE(3,\"time_to_answer:avg\",\"#partner_id\",10)", "Q54": "=_t(\"Call Duration\")", "Q55": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 1), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 1)  / 24, \"\")", "Q56": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 2), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 2)  / 24, \"\")", "Q57": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 3), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 3)  / 24, \"\")", "Q58": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 4), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 4)  / 24, \"\")", "Q59": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 5), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 5)  / 24, \"\")", "Q60": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 6), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 6)  / 24, \"\")", "Q61": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 7), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 7)  / 24, \"\")", "Q62": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 8), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 8)  / 24, \"\")", "Q63": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 9), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 9)  / 24, \"\")", "Q64": "=if(PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 10), PIVOT.VALUE(6,\"call_duration_hour:avg\",\"#partner_id\", 10)  / 24, \"\")", "U34": "=_t(\"Messages per Session\")", "U35": "=PIVOT.VALUE(3,\"nbr_message:avg\", \"#partner_id\", 1)", "U36": "=PIVOT.VALUE(3,\"nbr_message:avg\",\"#partner_id\",2)", "U37": "=PIVOT.VALUE(3,\"nbr_message:avg\",\"#partner_id\",3)", "U38": "=PIVOT.VALUE(3,\"nbr_message:avg\",\"#partner_id\",4)", "U39": "=PIVOT.VALUE(3,\"nbr_message:avg\",\"#partner_id\",5)", "U40": "=PIVOT.VALUE(3,\"nbr_message:avg\",\"#partner_id\",6)", "U41": "=PIVOT.VALUE(3,\"nbr_message:avg\",\"#partner_id\",7)", "U42": "=PIVOT.VALUE(3,\"nbr_message:avg\",\"#partner_id\",8)", "U43": "=PIVOT.VALUE(3,\"nbr_message:avg\",\"#partner_id\",9)", "U44": "=PIVOT.VALUE(3,\"nbr_message:avg\",\"#partner_id\",10)", "Y34": "=_t(\"Rating (%)\")", "Y35": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",1)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",1) - 1, 0) * 0.25, \"\")", "Y36": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",2)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",2) - 1, 0) * 0.25, \"\")", "Y37": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",3)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",3) - 1, 0) * 0.25, \"\")", "Y38": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",4)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",4) - 1, 0) * 0.25, \"\")", "Y39": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",5)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",5) - 1, 0) * 0.25, \"\")", "Y40": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",6)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",6) - 1, 0) * 0.25, \"\")", "Y41": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",7)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",7) - 1, 0) * 0.25, \"\")", "Y42": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",8)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",8) - 1, 0) * 0.25, \"\")", "Y43": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",9)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",9) - 1, 0) * 0.25, \"\")", "Y44": "=IF(ISNUMBER(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",10)), MAX(PIVOT.VALUE(3,\"rating:avg\",\"#partner_id\",10) - 1, 0) * 0.25, \"\")", "Z128": "=_t(\"Sessions\")", "Z129": "=PIVOT.VALUE(10,\"__count\",\"#lang_id\", 1)", "Z130": "=PIVOT.VALUE(10,\"__count\",\"#lang_id\",2)", "Z131": "=PIVOT.VALUE(10,\"__count\",\"#lang_id\",3)", "Z132": "=PIVOT.VALUE(10,\"__count\",\"#lang_id\",4)", "Z133": "=PIVOT.VALUE(10,\"__count\",\"#lang_id\",5)", "Z137": "=_t(\"Sessions\")", "Z138": "=PIVOT.VALUE(12,\"__count\",\"#chatbot_answers_path_str\", 1)", "Z139": "=PIVOT.VALUE(12,\"__count\",\"#chatbot_answers_path_str\",2)", "Z140": "=PIVOT.VALUE(12,\"__count\",\"#chatbot_answers_path_str\",3)", "Z141": "=PIVOT.VALUE(12,\"__count\",\"#chatbot_answers_path_str\",4)", "Z142": "=PIVOT.VALUE(12,\"__count\",\"#chatbot_answers_path_str\",5)"}, "styles": {"A1": 1, "C5": 1, "D33": 1, "E4:E5": 1, "K129:M129": 1, "K138:M138": 1, "F52:N52": 1, "Z129:AB129": 1, "Z138:AB138": 1, "A4:B5": 2, "A33:C33": 2, "A126:C126": 2, "C4:D4": 2, "A135:D135": 2, "A73:E73": 2, "A107:F107": 2, "P126:S126": 2, "P135:T135": 2, "P107:U107": 2, "A34:H34": 3, "A54:H54": 3, "B64:H64": 3, "A128:J128": 3, "A137:J137": 3, "J56:L64": 3, "P128:Y128": 3, "P137:Y137": 3, "A35": 4, "A55": 4, "B35:C44": 4, "D35:H35": 4, "B55:H63": 4, "A52:D52": 5, "E36:G44": 6, "J35:L35": 6, "N35:P35": 6, "R35:T35": 6, "V35:X35": 6, "J36:L44": 7, "K128:M128": 7, "K137:M137": 7, "N36:P44": 7, "R36:T44": 7, "I54:T54": 7, "V41:X44": 7, "I34:AB34": 7, "Z128:AB128": 7, "Z137:AB137": 7, "I55:L55": 8, "M55:P64": 8}, "formats": {"B55:B56": 1, "D55": 1, "H35": 1, "I55:I64": 1, "L35:M35": 1, "Q35": 1, "C35:C44": 2, "E35:G44": 2, "J35:K44": 2, "N35:N44": 2, "D65": 3, "L55:L64": 3, "N56:O64": 3, "Q55:Q64": 3, "R55:T55": 3, "H55:H64": 4, "J55:K55": 4, "M55:M64": 4, "N55:P55": 4, "Y35:AB44": 4}, "borders": {"A107:M107": 1, "A126:M126": 1, "A135:M135": 1, "A4:AB4": 1, "A33:AB33": 1, "A52:AB52": 1, "A73:AB73": 1, "P107:AB107": 1, "P126:AB126": 1, "P135:AB135": 1}, "conditionalFormats": [{"rule": {"type": "DataBarRule", "color": 15531509, "rangeValues": "I35:L44"}, "id": "b175f93a-1542", "ranges": ["A35:A44"]}, {"rule": {"type": "DataBarRule", "color": 15531509, "rangeValues": "I55:L64"}, "id": "2a77c4d0-e66a", "ranges": ["A55:H64"]}, {"rule": {"type": "DataBarRule", "color": 15531509, "rangeValues": "K129:M133"}, "id": "bea9774e-9ff9", "ranges": ["A129:J133"]}, {"rule": {"type": "DataBarRule", "color": 15531509, "rangeValues": "Z129:AB133"}, "id": "8bd95a2e-8387", "ranges": ["P129:Y133"]}, {"rule": {"type": "DataBarRule", "color": 15531509, "rangeValues": "K138:M142"}, "id": "391a2b88-fd28", "ranges": ["A138:J142"]}, {"rule": {"type": "DataBarRule", "color": 15531509, "rangeValues": "Z138:AB142"}, "id": "4940c66f-e877", "ranges": ["P138:Y142"]}], "dataValidationRules": [], "figures": [{"id": "b47aaa91-819c", "col": 0, "row": 1, "offset": {"x": 0, "y": 4}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": {"text": "Sessions", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!E2", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!D2", "humanize": true}}, {"id": "669caec3-bf59", "col": 0, "row": 1, "offset": {"x": 287.5, "y": 4}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": {"text": "Session Duration", "color": "#434343", "bold": true}, "type": "scorecard", "background": "#F4CCCC", "baseline": "Data!C4", "baselineDescr": {"text": "last period"}, "keyValue": "Data!B4", "humanize": true}}, {"id": "c859e1b2-98e2", "col": 0, "row": 1, "offset": {"x": 575, "y": 4}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": {"text": "Time to Respond", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#F4CCCC", "baseline": "Data!C3", "baselineDescr": {"text": "last period"}, "keyValue": "Data!B3", "humanize": true}}, {"id": "817ebe59-aa76", "col": 0, "row": 1, "offset": {"x": 862.5, "y": 4}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "percentage", "title": {"text": "Messages per session", "bold": true, "align": "left", "fontSize": 14}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C6", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!B6", "humanize": true}}, {"id": "f8004001-f384", "col": 0, "row": 1, "offset": {"x": 1150, "y": 4}, "width": 250, "height": 102, "tag": "chart", "data": {"background": "#EFF6FF", "sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "0", "rangeMax": "100", "lowerInflectionPoint": {"type": "number", "value": "20", "operator": "<="}, "upperInflectionPoint": {"type": "number", "value": "50", "operator": "<="}}, "title": {"text": "Rating (%)", "bold": true, "color": "#434343", "fontSize": 14}, "type": "gauge", "dataRange": "Data!D5"}}, {"id": "1b00e2ec-de7f", "col": 0, "row": 4, "offset": {"x": 0, "y": 15}, "width": 1400, "height": 700, "tag": "chart", "data": {"title": {"text": ""}, "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["partner_id"], "measure": "__count", "order": "DESC", "resModel": "im_livechat.report.channel", "mode": "bar"}, "searchParams": {"comparison": null, "context": {"im_livechat.hide_partner_company": true}, "domain": [["partner_id.chatbot_script_ids", "=", false]], "groupBy": ["partner_id"], "orderBy": []}, "type": "odoo_bar", "dataSets": [{}], "verticalAxisPosition": "left", "stacked": false, "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime"}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}}}}, {"id": "a8cdffbf-c922", "col": 0, "row": 45, "offset": {"x": 287.5, "y": 12}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "percentage", "title": {"text": "Sessions with Calls (#)", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C7", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!B7", "humanize": true}}, {"id": "f9d08184-6089", "col": 0, "row": 45, "offset": {"x": 575, "y": 12}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "percentage", "title": {"text": "Sessions with Calls (%)", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C8", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!B8", "humanize": true}}, {"id": "ed6c9126-a259", "col": 0, "row": 45, "offset": {"x": 862.5, "y": 12}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "percentage", "title": {"text": "Call Duration", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C9", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!B9", "humanize": false}}, {"id": "ecab6849-00d6", "col": 0, "row": 64, "offset": {"x": 0, "y": 14}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "percentage", "title": {"text": "Handled by <PERSON><PERSON>", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C10", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!B10", "humanize": true}}, {"id": "e28f8bef-9ed4", "col": 0, "row": 64, "offset": {"x": 287.5, "y": 14}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "percentage", "title": {"text": "Handled by Agent", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C11", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!B11", "humanize": true}}, {"id": "849cc753-af26", "col": 0, "row": 64, "offset": {"x": 575, "y": 14}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "percentage", "title": {"text": "Escalated", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C12", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!B12", "humanize": true}}, {"id": "8f96cf4c-942e", "col": 0, "row": 64, "offset": {"x": 862.5, "y": 14}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "percentage", "title": {"text": "Not Answered", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C13", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!B13", "humanize": true}}, {"id": "2aba95e3-9993", "col": 0, "row": 64, "offset": {"x": 1150, "y": 14}, "width": 250, "height": 102, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "percentage", "title": {"text": "No One Available", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baseline": "Data!C14", "baselineDescr": {"text": "since last period"}, "keyValue": "Data!B14", "humanize": true}}, {"id": "ead13406-6cc1", "col": 0, "row": 73, "offset": {"x": 0, "y": 11}, "width": 1400, "height": 700, "tag": "chart", "data": {"title": {"text": ""}, "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["start_date:day"], "measure": "__count", "order": null, "resModel": "im_livechat.report.channel", "mode": "bar"}, "searchParams": {"context": {"pivot_measures": ["__count", "time_to_answer", "duration", "rating", "number_of_calls"], "im_livechat.hide_partner_company": true}, "domain": [], "groupBy": ["start_date:day"], "orderBy": []}, "type": "odoo_bar", "actionXmlId": "im_livechat.im_livechat_report_channel_action", "dataSets": [{}], "verticalAxisPosition": "left", "stacked": true, "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime"}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}}}, {"id": "6433af83-2dbd", "col": 0, "row": 107, "offset": {"x": 0, "y": 10}, "width": 649, "height": 350, "tag": "chart", "data": {"title": {"text": ""}, "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["day_number"], "measure": "__count", "order": null, "resModel": "im_livechat.report.channel", "mode": "bar"}, "searchParams": {"context": {"pivot_measures": ["__count", "time_to_answer", "duration", "rating", "number_of_calls"], "im_livechat.hide_partner_company": true}, "domain": [], "groupBy": ["day_number"], "orderBy": []}, "type": "odoo_bar", "actionXmlId": "im_livechat.im_livechat_report_channel_action", "dataSets": [{}], "verticalAxisPosition": "left", "stacked": false, "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime"}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}}}, {"id": "98a0cc2d-8c14", "col": 14, "row": 107, "offset": {"x": 48, "y": 10}, "width": 652, "height": 350, "tag": "chart", "data": {"title": {"text": ""}, "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["start_hour"], "measure": "__count", "order": null, "resModel": "im_livechat.report.channel", "mode": "bar"}, "searchParams": {"context": {"pivot_measures": ["__count", "time_to_answer", "duration", "rating", "number_of_calls"], "im_livechat.hide_partner_company": true}, "domain": [], "groupBy": ["start_hour"], "orderBy": []}, "type": "odoo_bar", "actionXmlId": "im_livechat.im_livechat_report_channel_action", "dataSets": [{}], "verticalAxisPosition": "left", "stacked": false, "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime"}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}}}], "tables": [], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "16705d67-20c0-466d-ac72-3b955432e0ba", "name": "Data", "colNumber": 65, "rowNumber": 95, "rows": {}, "cols": {"0": {"size": 164}, "1": {"size": 110}, "2": {"size": 110}, "3": {"size": 110}, "4": {"size": 110}}, "merges": [], "cells": {"A1": "=_t(\"KPI\")", "A2": "=_t(\"Sessions\")", "A3": "=_t(\"Avg. time to respond\")", "A4": "=_t(\"Avg. session duration\")", "A5": "=_t(\"Avg. Rating\")", "A6": "=_t(\"Avg. number of messages\")", "A7": "# number of calls", "A8": "% number of calls", "A9": "Avg. call duration", "A10": "Sum handled by bot", "A11": "Sum handled by agent", "A12": "Sum escalated", "A13": "Sum failure no_answer", "A14": "Sum failure no_agent", "B1": "=_t(\"Current\")", "B2": "=PIVOT.VALUE(4,\"__count\")", "B3": "=PIVOT.VALUE(1, \"time_to_answer:avg\")", "B4": "=PIVOT.VALUE(1,\"duration:avg\")", "B5": "=IF(ISNUMBER(PIVOT.VALUE(1,\"rating:avg\")), MAX(PIVOT.VALUE(1,\"rating:avg\") - 1, 0) * 100 / 4, \"\")", "B6": "=PIVOT.VALUE(1,\"nbr_message:avg\")", "B7": "=PIVOT.VALUE(1, \"has_call:sum\")", "B8": "=IF(B2 > 0, B7/B2, 0)", "B9": "=PIVOT.VALUE(1, \"call_duration_hour:avg\") / 24", "B10": "=PIVOT.VALUE(1,\"handled_by_bot:sum\")", "B11": "=PIVOT.VALUE(1,\"handled_by_agent:sum\")", "B12": "=PIVOT.VALUE(7,\"__count\",\"session_outcome\",\"escalated\")", "B13": "=PIVOT.VALUE(7,\"__count\",\"session_outcome\",\"no_answer\")", "B14": "=IF(PIVOT.VALUE(7,\"__count\",\"session_outcome\", \"no_agent\"), PIVOT.VALUE(7,\"__count\",\"session_outcome\", \"no_agent\"), 0)", "C1": "=_t(\"Previous\")", "C2": "=PIVOT.VALUE(5,\"__count\")", "C3": "=PIVOT.VALUE(2,\"time_to_answer:avg\")", "C4": "=PIVOT.VALUE(2,\"duration:avg\")", "C6": "=PIVOT.VALUE(2,\"nbr_message:avg\")", "C7": "=PIVOT.VALUE(2, \"has_call:sum\")", "C8": "=IF(C2 > 0, C7/C2, 0)", "C9": "=PIVOT.VALUE(2, \"call_duration_hour:avg\") / 24", "C10": "=PIVOT.VALUE(2,\"handled_by_bot:sum\")", "C11": "=PIVOT.VALUE(2,\"handled_by_agent:sum\")", "C12": "=PIVOT.VALUE(8,\"__count\",\"session_outcome\",\"escalated\")", "C13": "=PIVOT.VALUE(8,\"__count\",\"session_outcome\",\"escalated\")", "C14": "=IF(PIVOT.VALUE(8,\"__count\",\"session_outcome\", \"no_agent\"), PIVOT.VALUE(8,\"__count\",\"session_outcome\", \"no_agent\"), 0)", "D1": "=_t(\"Current\")", "D2": "=FORMAT.LARGE.NUMBER(B2)", "D5": "=B5", "E1": "=_t(\"Previous\")", "E2": "=FORMAT.LARGE.NUMBER(C2)"}, "styles": {"F8": 1, "F12": 1}, "formats": {"B9:C9": 3, "I15": 3, "B3:C3": 5, "B4:C4": 6, "B8:C8": 7}, "borders": {}, "conditionalFormats": [], "dataValidationRules": [], "figures": [], "tables": [], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}], "styles": {"1": {"align": "right"}, "2": {"fontSize": 16, "bold": true, "textColor": "#01666B"}, "3": {"bold": true, "fontSize": 11, "textColor": "#434343"}, "4": {"textColor": "#000000"}, "5": {"bold": true, "fillColor": "#FFFFFF", "fontSize": 16, "textColor": "#01666B"}, "6": {"textColor": "#000000", "align": "right"}, "7": {"bold": true, "fontSize": 11, "align": "center", "textColor": "#434343"}, "8": {"textColor": "#434343"}}, "formats": {"1": "#,##0", "2": "0[$]", "3": "hhhh:mm:ss", "4": "0.00%", "5": "0 \"sec\"", "6": "0 \"min\"", "7": "0%"}, "borders": {"1": {"bottom": {"style": "thin", "color": "#CCCCCC"}}}, "revisionId": "cfb95d8c-0b4f-4637-8a6a-b71fa073f96f", "uniqueFigureIds": true, "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ",", "weekStart": 7}}, "pivots": {"1": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [], "id": "1", "measures": [{"id": "duration:avg", "fieldName": "duration", "aggregator": "avg"}, {"id": "time_to_answer:avg", "fieldName": "time_to_answer", "aggregator": "avg"}, {"id": "rating:avg", "fieldName": "rating", "aggregator": "avg"}, {"id": "nbr_message:avg", "fieldName": "nbr_message", "aggregator": "avg"}, {"id": "has_call:sum", "fieldName": "has_call", "aggregator": "sum"}, {"id": "call_duration_hour:avg", "fieldName": "call_duration_hour", "aggregator": "avg"}, {"id": "handled_by_bot:sum", "fieldName": "handled_by_bot", "aggregator": "sum"}, {"id": "handled_by_agent:sum", "fieldName": "handled_by_agent", "aggregator": "sum"}], "model": "im_livechat.report.channel", "name": "stats 2 - current", "sortedColumn": null, "formulaId": "1", "columns": [], "rows": []}, "2": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": -1}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [], "id": "2", "measures": [{"id": "duration:avg", "fieldName": "duration", "aggregator": "avg"}, {"id": "time_to_answer:avg", "fieldName": "time_to_answer", "aggregator": "avg"}, {"id": "rating:avg", "fieldName": "rating", "aggregator": "avg"}, {"id": "nbr_message:avg", "fieldName": "nbr_message", "aggregator": "avg"}, {"id": "has_call:sum", "fieldName": "has_call", "aggregator": "sum"}, {"id": "call_duration_hour:avg", "fieldName": "call_duration_hour", "aggregator": "avg"}, {"id": "handled_by_agent:sum", "fieldName": "handled_by_agent", "aggregator": "sum"}, {"id": "handled_by_bot:sum", "fieldName": "handled_by_bot", "aggregator": "sum"}], "model": "im_livechat.report.channel", "name": "stats 2 - previous", "sortedColumn": null, "formulaId": "2", "columns": [], "rows": []}, "3": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {"im_livechat.hide_partner_company": true}, "domain": [["partner_id.chatbot_script_ids", "=", false]], "id": "3", "measures": [{"id": "__count", "fieldName": "__count", "aggregator": "sum"}, {"id": "time_to_answer:avg", "fieldName": "time_to_answer", "aggregator": "avg"}, {"id": "duration:avg", "fieldName": "duration", "aggregator": "avg"}, {"id": "rating:avg", "fieldName": "rating", "aggregator": "avg"}, {"id": "nbr_message:avg", "fieldName": "nbr_message", "aggregator": "avg"}], "model": "im_livechat.report.channel", "name": "Livechat Support Statistics by Agent", "sortedColumn": {"domain": [], "order": "desc", "measure": "__count"}, "formulaId": "3", "columns": [], "rows": [{"fieldName": "partner_id", "order": "asc"}]}, "4": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [], "id": "4", "measures": [{"id": "__count", "fieldName": "__count"}], "model": "im_livechat.report.channel", "name": "sessions count - current", "sortedColumn": null, "formulaId": "4", "columns": [], "rows": []}, "5": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": -1}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [], "id": "5", "measures": [{"id": "__count", "fieldName": "__count"}], "model": "im_livechat.report.channel", "name": "sessions count - previous", "sortedColumn": null, "formulaId": "5", "columns": [], "rows": []}, "6": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {"im_livechat.hide_partner_company": true}, "domain": [["partner_id.chatbot_script_ids", "=", false]], "id": "6", "measures": [{"id": "has_call:sum", "fieldName": "has_call", "aggregator": "sum"}, {"id": "call_duration_hour:avg", "fieldName": "call_duration_hour", "aggregator": "avg"}, {"id": "__count", "fieldName": "__count", "aggregator": "sum", "isHidden": false}], "model": "im_livechat.report.channel", "name": "Livechat Call Statistics by Agent", "sortedColumn": {"domain": [], "order": "desc", "measure": "has_call:sum"}, "formulaId": "6", "columns": [], "rows": [{"fieldName": "partner_id", "order": ""}]}, "a96176e7-d33f": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [], "id": "1", "measures": [{"id": "__count", "fieldName": "__count", "aggregator": "sum"}], "model": "im_livechat.report.channel", "name": "current escalated/failure statistics", "sortedColumn": null, "formulaId": "7", "columns": [], "rows": [{"fieldName": "session_outcome"}]}, "a22ba739-5135": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [], "id": "1", "measures": [{"id": "__count", "fieldName": "__count", "aggregator": "sum"}], "model": "im_livechat.report.channel", "name": "previous escalated/failure statistics", "sortedColumn": null, "formulaId": "8", "columns": [], "rows": [{"fieldName": "session_outcome"}]}, "6670ff23-c9c4": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [["country_id", "!=", false]], "id": "1", "measures": [{"id": "__count", "fieldName": "__count", "aggregator": "sum"}], "model": "im_livechat.report.channel", "name": "Top countries", "sortedColumn": {"domain": [], "order": "desc", "measure": "__count"}, "formulaId": "9", "columns": [], "rows": [{"fieldName": "country_id"}], "collapsedDomains": {"COL": [], "ROW": []}}, "b3250c1f-c721": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [["lang_id", "!=", false]], "id": "1", "measures": [{"id": "__count", "fieldName": "__count", "aggregator": "sum"}], "model": "im_livechat.report.channel", "name": "Top Languages", "sortedColumn": {"domain": [], "order": "desc", "measure": "__count"}, "formulaId": "10", "columns": [], "rows": [{"fieldName": "lang_id"}], "collapsedDomains": {"COL": [], "ROW": []}}, "21eaa7c7-29bc": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [["session_expertise_ids", "!=", false]], "id": "1", "measures": [{"id": "__count", "fieldName": "__count", "aggregator": "sum"}], "model": "im_livechat.report.channel", "name": "Top Expertises", "sortedColumn": {"domain": [], "order": "desc", "measure": "__count"}, "formulaId": "11", "columns": [], "rows": [{"fieldName": "session_expertises"}], "collapsedDomains": {"COL": [], "ROW": []}}, "d11c47b1-b913": {"type": "ODOO", "fieldMatching": {"4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac": {"chain": "start_date", "type": "datetime", "offset": 0}, "5124b7b1-aa17-46cc-b574-2309708de75b": {"chain": "partner_id", "type": "many2one"}, "ee4a297f-0cc3": {"chain": "session_expertise_ids", "type": "many2many"}}, "context": {}, "domain": [["chatbot_answers_path_str", "!=", false]], "id": "1", "measures": [{"id": "__count", "fieldName": "__count", "aggregator": "sum"}], "model": "im_livechat.report.channel", "name": "Top Chatbot Answers", "sortedColumn": {"domain": [], "order": "desc", "measure": "__count"}, "formulaId": "12", "columns": [], "rows": [{"fieldName": "chatbot_answers_path_str"}], "collapsedDomains": {"COL": [], "ROW": []}}}, "pivotNextId": 13, "customTableStyles": {}, "globalFilters": [{"id": "4db3e2d2-5471-44a4-80ca-7c2fa4f1a5ac", "type": "date", "label": "Period", "defaultValue": "last_month", "rangeType": "relative"}, {"id": "5124b7b1-aa17-46cc-b574-2309708de75b", "type": "relation", "label": "Agents", "modelName": "res.partner", "domainOfAllowedValues": "[['partner_share', '=', False]]", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year"}, {"id": "5efd6191-32ae", "label": "Countries", "type": "relation", "defaultValue": [], "modelName": "res.country", "domainOfAllowedValues": [], "includeChildren": false}, {"id": "d8c5ec23-6206", "label": "Languages", "type": "relation", "defaultValue": [], "modelName": "res.lang", "domainOfAllowedValues": [], "includeChildren": false}, {"id": "5d0a8551-7ea8", "label": "Channels", "type": "relation", "defaultValue": [], "modelName": "im_livechat.channel", "domainOfAllowedValues": [], "includeChildren": false}, {"id": "ee4a297f-0cc3", "label": "Expertises", "type": "relation", "defaultValue": [], "modelName": "im_livechat.expertise", "domainOfAllowedValues": [], "includeChildren": false}], "lists": {}, "listNextId": 1, "chartOdooMenusReferences": {"b47aaa91-819c": "im_livechat.menu_reporting_livechat_channel", "669caec3-bf59": "im_livechat.menu_reporting_livechat_channel", "c859e1b2-98e2": "im_livechat.menu_reporting_livechat_channel", "f8004001-f384": "im_livechat.menu_livechat_sessions", "1b00e2ec-de7f": "im_livechat.menu_livechat_root"}}