<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="website_sale.ProductsItemOptionPlugin">
        <div t-if="this.displaySizeOption" class="o_wsale_soptions_menu_sizes">
            <BuilderRow label.translate="Size">
                <BuilderButtonGroup action="'setItemSize'">
                    <table t-ref="table" t-on-mouseenter="_onTableMouseEnter" t-on-mouseleave="_onTableMouseLeave">
                        <t t-foreach="[0, 1, 2, 3]" t-as="i" t-key="i">
                            <tr>
                                <t t-foreach="[0, 1, 2, 3]" t-as="j" t-key="j">
                                    <td t-on-mouseover="()=>this._onTableCellMouseOver(i, j)" t-on-click="()=>this._onTableCellMouseClick(i, j)">
                                        <BuilderButton preview="false" actionValue="[i, j]" />
                                    </td>
                                </t>
                            </tr>
                        </t>
                    </table>
                </BuilderButtonGroup>
            </BuilderRow>
        </div>
        <BuilderRow label.translate="Re-order" t-if="this.displayReOrder">
            <BuilderButtonGroup action="'changeSequence'">
                <BuilderButton actionValue="'top'" title.translate="Push to top" className="'fa fa-fw fa-angle-double-left'" />
                <BuilderButton actionValue="'up'" title.translate="Push up" className="'fa fa-fw fa-angle-left ms-1'" />
                <BuilderButton actionValue="'down'" title.translate="Push down" className="'fa fa-fw fa-angle-right mx-1'" />
                <BuilderButton actionValue="'bottom'" title.translate="Push to bottom" className="'fa fa-fw fa-angle-double-right'" />
            </BuilderButtonGroup>
        </BuilderRow>
        <BuilderRow label.translate="Ribbon">
            <BuilderSelect action="'setRibbon'" t-key="props.count.value" className="'o_wsale_ribbon_select'" t-on-click="()=>state.ribbonEditMode=false">
                <BuilderSelectItem actionValue="''" id="'no_ribbon_opt'">
                    None
                </BuilderSelectItem>
                <t t-foreach="this.state.ribbons" t-as="ribbon" t-key="ribbon.id">
                    <BuilderSelectItem actionValue="ribbon.id" label="ribbon.name">
                        <div>
                            <t t-out="ribbon.name" />
                            <span t-attf-class="fa fa-arrow-#{ribbon.position} ms-1"></span>
                            <span t-attf-class="o_wsale_color_preview ms-1" t-attf-style="background-color: #{ribbon.bg_color}; border: {{(ribbon.bg_color == '#FFFFFF' || ribbon.bg_color == '') ? '2px solid #CCCCCC' : ''}};" />
                            <span t-attf-class="o_wsale_color_preview ms-1" t-attf-style="background-color: #{ribbon.text_color} !important; border: {{ribbon.text_color == '#FFFFFF' ? '2px solid #CCCCCC' : ''}};" />
                        </div>
                    </BuilderSelectItem>
                </t>
            </BuilderSelect>
            <BuilderButton title.translate="Edit" className="'fa fa-edit'" t-if="!this.isActiveItem('no_ribbon_opt')" t-on-click="()=>state.ribbonEditMode = !state.ribbonEditMode"/>
            <BuilderButton action="'createRibbon'" preview="false" title.translate="Create" className="'fa fa-plus text-success'" style="'background-color: transparent !important;'" t-if="!state.ribbonEditMode" t-on-click="()=>state.ribbonEditMode = true" />
        </BuilderRow>

        <t t-if="state.ribbonEditMode">
            <BuilderContext action="'modifyRibbon'">
                <BuilderRow label.translate="Name" level="1">
                    <BuilderTextInput actionParam="'name'" />
                </BuilderRow>
                <BuilderRow label.translate="Background" level="1">
                    <BuilderColorPicker actionParam="'bg_color'" />
                </BuilderRow>
                <BuilderRow label.translate="Text" level="1">
                    <BuilderColorPicker actionParam="'text_color'" />
                </BuilderRow>
                <BuilderRow label.translate="Position" level="1">
                    <BuilderSelect actionParam="'position'">
                        <BuilderSelectItem actionValue="'left'">Left</BuilderSelectItem>
                        <BuilderSelectItem actionValue="'right'">Right</BuilderSelectItem>
                    </BuilderSelect>
                </BuilderRow>
            </BuilderContext>
            <BuilderRow label.translate=" ">
                <BuilderButton action="'deleteRibbon'" preview="false" className="'o_we_bg_danger'" t-on-click="()=>state.ribbonEditMode = false">Delete Ribbon</BuilderButton>
            </BuilderRow>
        </t>
    </t>
</templates>
