import { _t } from '@web/core/l10n/translation';
import { rpc } from '@web/core/network/rpc';
import { registry } from '@web/core/registry';
import { listenSizeChange, utils as uiUtils } from '@web/core/ui/ui_service';
import { renderToFragment } from '@web/core/utils/render';
import { Interaction } from '@web/public/interaction';

// Category size configuration constants
const CATEGORY_SIZE_CONFIG = {
    small: { span: 2, row: "10vh" },
    medium: { span: 2, row: "15vh" },
    large: { span: 4, row: "15vh" },
};

// Category alignment CSS class mappings
const CATEGORY_ALIGNMENT_CLASSES = {
    left: "justify-content-between",
    center: "align_category_center",
    right: "justify-content-between align_category_right",
};

// Template names for different category types
const CATEGORY_TEMPLATES = {
    clickable: "website_sale.dynamic_filter_template_categories_clickable_items",
    default: "website_sale.dynamic_filter_template_categories",
};

// CSS classes for interactive elements
const INTERACTIVE_CLASSES = {
    button: ["oe_unremovable", "stretched-link", "opacity-0", "p-0", "h-0"],
    overlay: ["justify-content-between", "align_category_right", "align_category_center"],
};


export class dynamicSnippetCategory extends Interaction {
    static selector = ".s_dynamic_category";

    async willStart() {
        const categoryId = this.el.dataset.categoryId;
        this.data = categoryId
            ? await this.waitFor(rpc('/shop/categories', { category_id: parseInt(categoryId) }))
            : [];
    }

    start() {
        this.registerCleanup(listenSizeChange(this.render.bind(this)));
        this.render();
    }

    render() {
        const nodeData = this.el.dataset;
        const alignmentClass = CATEGORY_ALIGNMENT_CLASSES[nodeData.alignment];
        const sizeConfig = CATEGORY_SIZE_CONFIG[nodeData.size];

        const categoryGrid = this.el.querySelector('.s_category_container');
        const categoryWrapperEl = this.el.querySelector(".s_dynamic_category_wrapper");

        // Clear existing categories
        categoryWrapperEl.querySelectorAll(".category_item").forEach(el => el.remove());

        //  Apply selected settings
        const categoryTemplate = nodeData?.isClickable
            ? CATEGORY_TEMPLATES.clickable
            : CATEGORY_TEMPLATES.default;
        categoryWrapperEl.appendChild(
            renderToFragment(categoryTemplate, {
                data: this.data,
                size: sizeConfig.span,
                alignmentClass: alignmentClass,
                buttonText: _t(nodeData.button),
            })
        );

        // Apply styling to category grid layout
        const columns = uiUtils.isSmall() ? 1 : parseInt(nodeData.columns);
        categoryGrid.style.setProperty('--DynamicCategory-columns', `${columns}`);
        categoryGrid.style.setProperty('grid-auto-rows', `minmax(${sizeConfig.row}, auto)`);

        // Setup 'All Products' item visibility and styling
        const allProducts = this.el.querySelector('.all_products');
        if (nodeData.allProducts === 'true') {
            allProducts.classList.remove('d-none');

            // Update 'All Products' item overlay alignment
            const overlay = allProducts.querySelector('.s_category_overlay');
            overlay.classList.remove(...INTERACTIVE_CLASSES.overlay);
            overlay.className += " " + alignmentClass;

            // Set heading and button text for 'All Products' item
            allProducts.querySelector('a').textContent = nodeData.button;

            // Adjust 'All Product' number of columns
            const shouldSpanTwo = columns !== 1 &&
                (['large', 'medium'].includes(nodeData.size) || columns === 5);
            allProducts.style.setProperty('grid-column', `span ${shouldSpanTwo ? 2 : 1}`);

            // Toggle related elements and styles for interactivity
            this._setupAllProductsInteractivity(allProducts, nodeData.isClickable);
        } else {
            allProducts.classList.add("d-none");
        }
    }

    /**
     * Setup interactivity for the 'All Products' item
     * @private
     * @param {Element} allProducts - The all products element
     * @param {string} isClickable - Whether the item should be clickable
     */
    _setupAllProductsInteractivity(allProducts, isClickable) {
        const elements = {
            button: allProducts.querySelector(".s_dynamic_category_button"),
            arrow: allProducts.querySelector(".s_dynamic_category_arrow"),
            image: allProducts.querySelector(".s_category_image"),
            filter: allProducts.querySelector(".s_category_filter"),
            overlay: allProducts.querySelector(".s_category_overlay"),
            title: allProducts.querySelector("h3"),
        };

        const isClickableBool = Boolean(isClickable);
        const toggleClass = (el, className, condition) => {
            el.classList.toggle(className, condition);
        };

        // Apply interactive styles
        toggleClass(allProducts, "opacity-trigger-hover", isClickableBool);
        toggleClass(elements.arrow, "d-none", !isClickableBool);
        toggleClass(elements.image, "transition-base", isClickableBool);
        toggleClass(elements.filter, "d-none", !isClickableBool);
        toggleClass(elements.overlay, "z-0", isClickableBool);
        toggleClass(elements.title, "mb-0", isClickableBool);

        // Apply button classes
        INTERACTIVE_CLASSES.button.forEach(className =>
            toggleClass(elements.button, className, isClickableBool)
        );
    }

}

registry
    .category("public.interactions")
    .add("website_sale.dynamic_snippet_category", dynamicSnippetCategory);

registry
    .category("public.interactions.edit")
    .add("website_sale.dynamic_snippet_category", {Interaction: dynamicSnippetCategory});
