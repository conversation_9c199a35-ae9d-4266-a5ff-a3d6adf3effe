<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="product_attribute_view_form" model="ir.ui.view">
        <field name="name">product.attribute.view.form</field>
        <field name="model">product.attribute</field>
        <field name="inherit_id" ref="product.product_attribute_view_form"/>
        <field name="arch" type="xml">
            <group name="main_fields" position="inside">
                <group name="ecommerce_main_fields">
                    <field
                        name="visibility"
                        string="eCommerce Filter"
                        widget="radio"
                        options="{'horizontal': True}"
                    />
                    <field
                        name="preview_variants"
                        widget="radio"
                        options="{'horizontal': True}"
                        readonly="create_variant != 'always' or display_type == 'multi'"
                    />
                    <field
                        name="is_thumbnail_visible"
                        widget="boolean_toggle"
                        readonly="create_variant != 'always' or display_type == 'multi'"
                    />
                </group>
            </group>
        </field>
    </record>

    <record id="attribute_tree_view" model="ir.ui.view">
        <field name="name">product.attribute.list</field>
        <field name="model">product.attribute</field>
        <field name="inherit_id" ref="product.attribute_tree_view"/>
        <field name="arch" type="xml">
            <field name="create_variant" position="after">
                <field name="visibility" string="eCommerce Filter Visibility"/>
            </field>
        </field>
    </record>

</odoo>
