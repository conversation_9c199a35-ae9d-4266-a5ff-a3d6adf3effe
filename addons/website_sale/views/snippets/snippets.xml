<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="snippets" inherit_id="website.snippets" name="e-commerce snippets">
    <xpath expr="//t[@id='sale_products_hook']" position="replace">
        <t t-snippet="website_sale.s_dynamic_snippet_products" string="Products" group="catalog"/>
        <t t-snippet="website_sale.s_dynamic_category" string="Category List" group="catalog"/>
    </xpath>
    <xpath expr="//t[@id='snippet_add_to_cart_hook']" position="replace">
        <t t-snippet="website_sale.s_add_to_cart" string="Add to Cart Button"  t-thumbnail="/website/static/src/img/snippets_thumbs/s_add_to_cart.svg"/>
    </xpath>
</template>

<template id="snippet_options" inherit_id="website.snippet_options" name="e-commerce snippet options">
    <xpath expr="." position="inside">
        <!-- All products page -->
        <div
            data-js="WebsiteSaleGridLayout"
            data-page-options="true"
            groups="website.group_website_designer"
            data-selector="main:has(.o_wsale_products_page)"
            data-no-check="true"
            string="Products Page"
            data-target="#o_wsale_container"
        >
            <we-button-group
                string="Content Width"
                class="o_wsale_editor_section"
            >
                <we-button
                    data-select-class="container"
                    data-img="/website/static/src/img/snippets_options/content_width_normal.svg"
                    title="Regular"
                />
                <we-button
                    data-select-class="container-fluid o_wsale_page_fluid"
                    data-customize-website-views="website_sale.shop_fullwidth"
                    data-img="/website/static/src/img/snippets_options/content_width_full.svg"
                    title="Full"
                />
            </we-button-group>

            <we-button-group class="o_wsale_editor_section" string="Layout">
                <we-button
                    title="Catalog of products"
                    data-select-class="o_wsale_layout_catalog"
                    data-customize-website-views=""
                    data-name="grid_view_opt"
                >
                    <i class="oi oi-apps"/>
                </we-button>
                <we-button
                    title="List of products"
                    data-select-class="o_wsale_layout_list"
                    data-customize-website-views="website_sale.products_list_view"
                >
                    <i class="oi oi-view-list"/>
                </we-button>
            </we-button-group>

            <we-row string="Size" class="o_we_sublevel_1">
                <we-input data-set-ppg="" data-step="1" data-no-preview="true" data-reload="/"/>
                <span class="mx-2 o_wsale_ppr_by">by</span>
                <we-select class="o_wsale_ppr_submenu" data-dependencies="grid_view_opt" data-no-preview="true" data-reload="/">
                    <we-button data-set-ppr="2">2</we-button>
                    <we-button data-set-ppr="3">3</we-button>
                    <we-button data-set-ppr="4">4</we-button>
                    <we-button data-set-ppr="5">5</we-button>
                </we-select>
            </we-row>

            <we-select
                string="Mobile"
                class="o_we_sublevel_1"
                data-dependencies="grid_view_opt"
                data-no-preview="true"
                data-reload="/"
            >
                <we-button data-customize-website-views="website_sale.products_mobile_cols_single">
                    1 Column
                </we-button>
                <we-button data-customize-website-views="">
                    2 Columns
                </we-button>
            </we-select>

            <we-range
                string="Gap" class="o_we_sublevel_1"
                data-select-style="0"
                data-set-gap=""
                data-css-property="--o-wsale-products-grid-gap"
                data-display-range-value="true"
                data-display-range-value-unit="px"
                data-unit="px"
                data-min="0"
                data-max="28"
            />

            <we-select string="Style" class="o_wsale_editor_section">
                <we-button data-select-class=""
                           data-set-default-gap="16"
                           data-customize-website-views="">
                           Default
                </we-button>
                <we-button data-select-class="o_wsale_design_cards"
                           data-set-default-gap="16"
                           data-customize-website-views="website_sale.products_design_card">
                           Cards
                </we-button>
                <we-button data-select-class="o_wsale_design_thumbs"
                           data-set-default-gap="16"
                           data-customize-website-views="website_sale.products_design_thumbs">
                           Thumbnails
                </we-button>
                <we-button data-select-class="o_wsale_design_grid"
                           data-set-default-gap="0"
                           data-customize-website-views="website_sale.products_design_grid">
                           Grid
                </we-button>
            </we-select>
            <we-collapse>
            <we-select string="Images Size" class="o_we_sublevel_1">
                <we-button data-select-class="o_wsale_context_thumb_4_3"
                           data-customize-website-views="website_sale.products_thumb_4_3">
                           Landscape (4/3)
                </we-button>
                <we-button data-select-class=""
                           data-customize-website-views="">
                           Default (1/1)
                </we-button>
                <we-button data-select-class="o_wsale_context_thumb_4_5"
                           data-customize-website-views="website_sale.products_thumb_4_5">
                           Portrait (4/5)
                </we-button>
                <we-button data-select-class="o_wsale_context_thumb_2_3"
                           data-customize-website-views="website_sale.products_thumb_2_3">
                           Vertical (2/3)
                </we-button>
            </we-select>
            <we-button-group
                string="Images Fill"
                class="o_we_sublevel_2"
                data-variable="thumb_size"
            >
                <we-button data-select-class=""
                           title="Contain within the box"
                           data-img="/website/static/src/img/snippets_options/content_width_normal.svg"
                           data-reload="/"
                           data-customize-website-views="">
                </we-button>
                <we-button data-select-class="o_wsale_context_thumb_cover"
                           title="Always Fill the box"
                           data-name="thumb_cover"
                           data-variable="thumb_cover"
                           data-img="/website/static/src/img/snippets_options/content_width_full.svg"
                           data-reload="/"
                           data-customize-website-views="website_sale.products_thumb_cover">
                </we-button>
            </we-button-group>
            </we-collapse>
            <we-checkbox
                string="Description"
                class="o_we_sublevel_1"
                data-customize-website-views="website_sale.products_description"
                data-wsale-preview-id="website_sale.editor_preview.description"
                data-wsale-preview-classes="o_wsale_previewing_description"
                data-wsale-preview-place-after=".oe_product_cart:not(.oe_product_cart_has_description) .o_wsale_products_item_title"
                data-no-preview="true"
                data-reload="/"
            />
            <we-row string="Actions" class="o_we_full_row o_we_sublevel_1">
                <we-button
                    title="Add to Cart"
                    class="fa fa-fw fa-shopping-cart o_we_add_to_cart_btn"
                    data-customize-website-views="website_sale.products_add_to_cart"
                    data-no-preview="true"
                    data-reload="/"
                />
                <div class="flex-basis-100"/>
            </we-row>
            <we-row id="o_wsale_grid_left_panel" class="o_wsale_editor_section" string="Categories">
                <we-button
                    string="Sidebar"
                    title="Show into Sidebar"
                    data-customize-website-views="website_sale.products_categories"
                    data-wsale-preview-id="website_sale.editor_preview.sidebar"
                    data-wsale-preview-place-before="#o_wsale_container:not(.o_wsale_has_sidebar) #products_grid"
                    data-wsale-preview-classes="o_wsale_previewing_sidebar, o_wsale_has_sidebar"
                    data-name="categories_opt"
                    data-no-preview="true"
                    data-reload="/"
                />
                <we-button
                    string="Top"
                    title="Show a filmstrip navigation at the Top"
                    data-customize-website-views="website_sale.products_categories_top"
                    data-wsale-preview-id="website_sale.editor_preview.filmstrip"
                    data-wsale-preview-classes="o_wsale_previewing_filmstrip"
                    data-wsale-preview-place-before="#o_wsale_container:not(.o_wsale_has_filmstrip) .products_header"
                    data-name="categories_opt_top"
                    data-no-preview="true"
                    data-reload="/"
                />
            </we-row>
            <we-checkbox
                id="collapse_category_recursive"
                string="Collapse Category Recursive"
                class="o_we_sublevel_1"
                data-customize-website-views="website_sale.option_collapse_products_categories"
                data-dependencies="categories_opt"
                data-no-preview="true"
                data-reload="/"
            />
            <we-select
                string="Style"
                class="o_we_sublevel_1"
                data-dependencies="categories_opt_top"
            >
                <we-button
                    data-select-class=""
                    data-customize-website-views=""
                >
                           Filmstrip - default
                </we-button>
            </we-select>
            <we-select
                string="Filters"
                data-name="attributes_selector"
                class="o_wsale_editor_section"
                data-no-preview="true"
                data-reload="/"
            >
                <we-button
                    string="Hide"
                    title="Hide all filters"
                    data-name="attributes_hide_opt"
                    data-customize-website-views=""
                />
                <we-button
                    string="Sidebar"
                    title="Show filters into Sidebar"
                    data-wsale-preview-id="website_sale.editor_preview.sidebar"
                    data-wsale-preview-place-before="#o_wsale_container:not(.o_wsale_has_sidebar) #products_grid"
                    data-wsale-preview-classes="o_wsale_previewing_sidebar, o_wsale_has_sidebar"
                    data-customize-website-views="website_sale.products_attributes"
                    data-name="attributes_opt"
                />
                <we-button
                    string="Off-screen Menu"
                    title="Render a button to show the Off-screen Menu"
                    data-customize-website-views="website_sale.products_attributes_top"
                    data-wsale-preview-id=""
                    data-wsale-preview-classes="o_wsale_previewing_offcanvas"
                    data-name="attributes_opt_top"
                />
            </we-select>
            <we-checkbox
                string="Price"
                class="o_we_sublevel_1"
                data-customize-website-views="website_sale.filter_products_price"
                data-dependencies="attributes_opt, attributes_opt_top"
                data-no-preview="true"
                data-reload="/"
            />
            <we-checkbox
                string="Tags"
                id="o_wsale_tags_filter_opt"
                class="o_we_sublevel_1"
                data-customize-website-views="website_sale.filter_products_tags"
                data-dependencies="attributes_opt, attributes_opt_top"
                data-no-preview="true"
                data-reload="/"
            />
            <we-checkbox
                id="collapsible_sidebar"
                string="Collapse sidebar sections"
                class="o_wsale_editor_section"
                data-customize-website-views="website_sale.products_categories_list_collapsible, website_sale.products_attributes_collapsible"
                data-dependencies="categories_opt, attributes_opt"
                data-no-preview="true"
                data-reload="/"
            />
            <we-row string="Toolbar" class="o_wsale_editor_section">
                <we-button
                    string="Sort by"
                    data-customize-website-views="website_sale.sort"
                    data-no-preview="true"
                    data-name="sort_top_opt"
                    data-reload="/"
                />
                <we-button
                    string="Search"
                    data-customize-website-views="website_sale.search"
                    data-no-preview="true"
                    data-name="search_top_opt"
                    data-reload="/"
                />
            </we-row>
            <we-checkbox
                string="Floating"
                title="Floating sticky Toolbar"
                class="o_we_sublevel_1"
                data-customize-website-views="website_sale.floating_bar"
                data-dependencies="attributes_opt_top, search_top_opt, sort_top_opt"
                data-wsale-preview-id="website_sale.editor_preview.floating_bar"
                data-wsale-preview-classes="o_wsale_previewing_floating_bar"
                data-wsale-preview-place-after="#o_wsale_container:not(.o_wsale_has_floating_bar) .o_wsale_products_main_row"
                data-no-preview="true"
                data-reload="/"
            />
            <we-select
                string="Default Sort"
                class="o_wsale_sort_submenu o_we_sublevel_1"
                data-no-preview="true"
                data-reload="/"
            >
                <t
                    t-foreach="request.env['website']._get_product_sort_mapping()"
                    t-as="query_and_label"
                >
                    <we-button t-att-data-set-default-sort="query_and_label[0]">
                        <t t-esc="query_and_label[1]"/>
                    </we-button>
                </t>
            </we-select>
        </div>
        <!-- Product -->
        <div data-js="WebsiteSaleProductsItem"
            data-selector="#products_grid .oe_product"
            data-no-check="true">
            <div class="o_wsale_soptions_menu_sizes">
                <we-row string="Size">
                    <table>
                        <tr>
                            <td/><td/><td/><td/>
                        </tr>
                        <tr>
                            <td/><td/><td/><td/>
                        </tr>
                        <tr>
                            <td/><td/><td/><td/>
                        </tr>
                        <tr>
                            <td/><td/><td/><td/>
                        </tr>
                    </table>
                </we-row>
            </div>
            <we-button-group
                data-name="o_wsale_change_sequence_widget"
                string="Re-order"
                data-no-preview="true"
            >
                <we-button title="Push to top" data-change-sequence="top" class="fa fa-fw fa-angle-double-left"/>
                <we-button title="Push up" data-change-sequence="up" class="fa fa-fw fa-angle-left ms-1"/>
                <we-button title="Push down" data-change-sequence="down" class="fa fa-fw fa-angle-right mx-1"/>
                <we-button title="Push to bottom" data-change-sequence="bottom" class="fa fa-fw fa-angle-double-right"/>
            </we-button-group>

            <we-row>
                <we-select string="Ribbon" class="o_wsale_ribbon_select">
                    <we-button data-set-ribbon="" data-name="no_ribbon_opt">None</we-button>
                    <!-- Ribbons are filled in JS -->
                </we-select>
                <we-button data-edit-ribbon="" title="Edit" class="fa fa-edit" data-no-preview="true" data-dependencies="!no_ribbon_opt"/>
                <we-button data-create-ribbon="" data-name="create_ribbon_opt" title="Create" class="fa fa-plus text-success" data-no-preview="true"/>
            </we-row>
            <div class="d-none" data-name="ribbon_customize_opt">
                <we-input string="Name" class="o_we_sublevel_1 o_we_large"
                          data-set-ribbon-name="Ribbon name" data-apply-to=".o_ribbon"/>
                <we-colorpicker string="Background" class="o_we_sublevel_1"
                                title="" data-select-style="" data-css-property="background-color" data-color-prefix="text-bg-" data-apply-to=".o_ribbon"/>
                <we-colorpicker string="Text" class="o_we_sublevel_1"
                                title="" data-select-style="" data-css-property="color" data-apply-to=".o_ribbon"/>
                <we-select string="Position" class="o_we_sublevel_1">
                    <we-button data-set-ribbon-position="left">Left</we-button>
                    <we-button data-set-ribbon-position="right">Right</we-button>
                </we-select>
                <we-row string=" ">
                    <we-button class="o_we_bg_danger" data-delete-ribbon="" data-no-preview="true">
                        Delete Ribbon
                    </we-button>
                </we-row>
            </div>
        </div>
        <div data-selector="#wrapwrap > header"
            data-no-check="true"
            groups="website.group_website_designer">
            <we-row string="Show Empty" class="o_we_full_row">
                <div class="d-flex gap-1 mb-1 w-100">
                    <we-button title="Show/hide shopping cart" class="o_btn_show_empty_cart fa fa-shopping-cart d-flex justify-content-center flex-grow-1"
                            data-customize-website-views="website_sale.header_hide_empty_cart_link|"
                            data-no-preview="true"
                            data-reload="/"/>
                </div>
            </we-row>
        </div>
        <!-- Product image -->
        <div data-js="WebsiteSaleProductAttribute" data-selector="#product_detail .o_wsale_product_attribute" data-no-check="true">
            <we-select string="Display Type" data-no-preview="true">
                <we-button data-set-display-type="radio">Radio</we-button>
                <we-button data-set-display-type="pills">Pills</we-button>
                <we-button data-set-display-type="select">Select</we-button>
                <we-button data-set-display-type="color">Color</we-button>
            </we-select>
        </div>
        <!-- Product page -->
        <div data-js="WebsiteSaleProductPage" data-selector="main:has(.o_wsale_product_page)" data-page-options="true" groups="website.group_website_designer" data-no-check="true" string="Product Page">
            <!-- Image config -->
            <we-button-group string="Images Width" data-no-preview="true" data-reload="/">
                <we-button data-set-image-width="none" data-img="/website_sale/static/src/img/snippet_options/image-width-none.svg" title="None"/>
                <we-button data-set-image-width="50_pc" data-img="/website_sale/static/src/img/snippet_options/image-width-50.svg" title="50 percent"/>
                <we-button data-set-image-width="66_pc" data-img="/website_sale/static/src/img/snippet_options/image-width-66.svg" title="66 percent"/>
                <we-button data-set-image-width="100_pc" data-img="/website_sale/static/src/img/snippet_options/image-width-100.svg" title="100 percent"/>
            </we-button-group>
            <we-select string="Layout" data-name="o_wsale_image_layout" data-no-preview="true" data-reload="/">
                <we-button data-set-image-layout="carousel">Carousel</we-button>
                <we-button data-set-image-layout="grid">Grid</we-button>
            </we-select>
            <we-select
                string="Images Ratio"
                data-name="o_wsale_image_ratio"
                class="o_we_sublevel_1"
                data-reload="/"
            >
                <we-button data-customize-website-views="">
                    Default (1x1)
                </we-button>
                <we-button data-customize-website-views="website_sale.products_carousel_4x3">
                    Landscape (4/3)
                </we-button>
                <we-button data-customize-website-views="website_sale.products_carousel_4x5">
                    Portrait (4/5)
                </we-button>
                <we-button data-customize-website-views="website_sale.products_carousel_16x9">
                    Wide (16/9)
                </we-button>
                <we-button data-customize-website-views="website_sale.products_carousel_21x9">
                    Wider (21/9)
                </we-button>
            </we-select>
            <we-select string="Image Zoom" class="o_we_sublevel_1" data-name="o_wsale_zoom_mode" data-no-preview="true" data-reload="/">
                <we-button data-name="o_wsale_zoom_hover" data-customize-website-views="website_sale.product_picture_magnify_hover">Magnifier on hover</we-button>
                <we-button data-name="o_wsale_zoom_click" data-customize-website-views="website_sale.product_picture_magnify_click">Pop-up on Click</we-button>
                <we-button data-name="o_wsale_zoom_both" data-customize-website-views="website_sale.product_picture_magnify_both">Both</we-button>
                <we-button data-name="o_wsale_zoom_none" data-customize-website-views="">None</we-button>
            </we-select>
            <!-- Carousel config -->
            <we-button-group string="Thumbnails" class="o_we_sublevel_1" data-name="o_wsale_thumbnail_pos" data-no-preview="true" data-reload="/">
                <we-button class="fa fa-fw fa-long-arrow-left" title="Left" data-customize-website-views="website_sale.carousel_product_indicators_left"/>
                <we-button class="fa fa-fw fa-long-arrow-down" title="Bottom" data-customize-website-views="website_sale.carousel_product_indicators_bottom"/>
            </we-button-group>
            <!-- Grid config -->
            <we-range string="Image Spacing" class="o_we_sublevel_1" data-name="o_wsale_grid_spacing" data-no-preview="true" data-reload="/" data-max="3" data-step="1" data-set-spacing=""/>
            <we-select string="Columns" class="o_we_sublevel_1" data-name="o_wsale_grid_columns"  data-no-preview="true" data-reload="/">
                <we-button data-set-columns="1">1</we-button>
                <we-button data-set-columns="2">2</we-button>
                <we-button data-set-columns="3">3</we-button>
            </we-select>
            <we-row string="Main Image">
                <we-button class="o_we_bg_success" data-name="o_wsale_replace_main_image" data-replace-main-image="true" data-no-preview="true">Replace</we-button>
            </we-row>
            <we-row string="Extra Media">
                <we-button class="o_we_bg_success" data-name="o_wsale_add_extra_images" data-add-images="true" data-no-preview="true">Add</we-button>
                <we-button class="o_we_bg_danger" data-name="o_wsale_clear_extra_images" data-clear-images="true" data-no-preview="true">Remove all</we-button>
            </we-row>
            <we-row string="Cart" class="o_we_full_row" data-name="o_wsale_cart_opt">
                <we-button
                    title="Buy Now"
                    class="o_we_buy_now_btn"
                    data-customize-website-views="website_sale.product_buy_now"
                    data-no-preview="true"
                    data-reload="/"
                >
                    <i class="fa fa-fw fa-bolt"/>
                    Buy Now
                </we-button>
                <we-button
                    title="Select Quantity"
                    class="o_we_buy_now_btn"
                    data-customize-website-views="website_sale.product_quantity"
                    data-no-preview="true"
                    data-reload="/"
                >
                    Quantity
                </we-button>
            </we-row>
            <div data-name="o_wsale_actions_opt" class="d-none">
                <we-row string="Actions" class="o_we_full_row" data-name="o_we_actions_opt"/>
            </div>
            <we-checkbox
                string="Tax Indication"
                data-customize-website-views="website_sale.tax_indication"
                data-no-preview="true"
                data-reload="/"
            />
            <we-checkbox
                string="Product Tags"
                data-customize-website-views="website_sale.product_tags"
                data-no-preview="true"
                data-reload="/"
            />
            <we-checkbox
                string="Terms and Conditions"
                data-customize-website-views="website_sale.product_custom_text"
                data-no-preview="true"
                data-reload="/"
            />
            <we-checkbox
                string="Reviews"
                data-customize-website-views="website_sale.product_comment"
                data-no-preview="true"
                data-reload="/"
            />
        </div>
        <!-- Checkout page  -->
        <div
            string="Checkout Pages"
            name="o_wsale_checkout_pages"
            data-js="WebsiteSaleCheckoutPage"
            data-selector="main:has(.oe_website_sale .o_wizard)"
            data-page-options="true"
            data-no-check="true"
            groups="website.group_website_designer"
        >
            <we-checkbox string="Extra Step"
                         data-customize-website-views="website_sale.extra_info"
                         data-set-extra-step="true"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Suggested Accessories"
                         data-customize-website-views="website_sale.suggested_products_list"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Promo Code"
                         data-customize-website-views="website_sale.reduction_code"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Accept Terms &amp; Conditions"
                         data-customize-website-views="website_sale.accept_terms_and_conditions"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Show B2B Fields"
                         data-customize-website-views="website_sale.address_b2b"
                         data-no-preview="true"
                         data-reload="/"/>
        </div>
    </xpath>
    <xpath expr="//div[@data-js='MegaMenuLayout']" position="inside">
        <t t-set="categories" t-value="request.env['product.public.category'].search([])"/>
        <we-row t-if="categories">
            <we-title class="o_long_title">eCommerce Categories</we-title>
            <div class="o_switch ms-4">
                <we-checkbox
                    class="o_mega_menu"
                    data-name="fetch_ecom_categories_opt"
                    data-select-class="fetchEcomCategories"
                    data-toggle-fetch-ecom-categories=""
                    data-no-preview="true"
                />
            </div>
            <we-button
                type="button"
                title="Reset Categories"
                data-refresh-mega-menu-template=""
                data-no-preview="true"
                class="ms-4"
            >
                <i class="fa fa-refresh"/>
            </we-button>
        </we-row>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']/we-button[@data-select-template='website.s_mega_menu_multi_menus']"
        position="attributes"
    >
        <attribute name="data-dependencies">!fetch_ecom_categories_opt</attribute>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']/we-button[@data-select-template='website.s_mega_menu_menu_image_menu']"
        position="attributes"
    >
        <attribute name="data-dependencies">!fetch_ecom_categories_opt</attribute>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']/we-button[@data-select-template='website.s_mega_menu_odoo_menu']"
        position="attributes"
    >
        <attribute name="data-dependencies">!fetch_ecom_categories_opt</attribute>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']/we-button[@data-select-template='website.s_mega_menu_little_icons']"
        position="attributes"
    >
        <attribute name="data-dependencies">!fetch_ecom_categories_opt</attribute>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']/we-button[@data-select-template='website.s_mega_menu_big_icons_subtitles']"
        position="attributes"
    >
        <attribute name="data-dependencies">!fetch_ecom_categories_opt</attribute>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']/we-button[@data-select-template='website.s_mega_menu_images_subtitles']"
        position="attributes"
    >
        <attribute name="data-dependencies">!fetch_ecom_categories_opt</attribute>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']/we-button[@data-select-template='website.s_mega_menu_menus_logos']"
        position="attributes"
    >
        <attribute name="data-dependencies">!fetch_ecom_categories_opt</attribute>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']/we-button[@data-select-template='website.s_mega_menu_thumbnails']"
        position="attributes"
    >
        <attribute name="data-dependencies">!fetch_ecom_categories_opt</attribute>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']/we-button[@data-select-template='website.s_mega_menu_cards']"
        position="attributes"
    >
        <attribute name="data-dependencies">!fetch_ecom_categories_opt</attribute>
    </xpath>
    <xpath
        expr="//div[@data-js='MegaMenuLayout']/we-select[@data-name='mega_menu_template_opt']"
        position="inside"
    >
        <t t-set="_label">Multi Menus</t>
        <we-button
            t-att-data-select-label="_label"
            data-dependencies="fetch_ecom_categories_opt"
            data-select-template="website_sale.s_mega_menu_multi_menus"
            data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_multi_menus.svg"
            t-out="_label"
        />
        <t t-set="_label">Image Menu</t>
        <we-button
            t-att-data-select-label="_label"
            data-dependencies="fetch_ecom_categories_opt"
            data-select-template="website_sale.s_mega_menu_menu_image_menu"
            data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_menu_image_menu.svg"
            t-out="_label"
        />
        <t t-set="_label">Odoo Menu</t>
        <we-button
            t-att-data-select-label="_label"
            data-dependencies="fetch_ecom_categories_opt"
            data-select-template="website_sale.s_mega_menu_odoo_menu"
            data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_odoo_menu.svg"
            t-out="_label"
        />
        <t t-set="_label">Little Icons</t>
        <we-button
            t-att-data-select-label="_label"
            data-dependencies="fetch_ecom_categories_opt"
            data-select-template="website_sale.s_mega_menu_little_icons"
            data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_little_icons.svg"
            t-out="_label"
        />
        <t t-set="_label">Big Icons Subtitles</t>
        <we-button
            t-att-data-select-label="_label"
            data-dependencies="fetch_ecom_categories_opt"
            data-select-template="website_sale.s_mega_menu_big_icons_subtitles"
            data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_big_icons_subtitles.svg"
            t-out="_label"
        />
        <t t-set="_label">Images Subtitles</t>
        <we-button
            t-att-data-select-label="_label"
            data-dependencies="fetch_ecom_categories_opt"
            data-select-template="website_sale.s_mega_menu_images_subtitles"
            data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_images_subtitles.svg"
            t-out="_label"
         />
        <t t-set="_label">Logos</t>
        <we-button
            t-att-data-select-label="_label"
            data-dependencies="fetch_ecom_categories_opt"
            data-select-template="website_sale.s_mega_menu_menus_logos"
            data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_menus_logos.svg"
            t-out="_label"
        />
        <t t-set="_label">Thumbnails</t>
        <we-button
            t-att-data-select-label="_label"
            data-dependencies="fetch_ecom_categories_opt"
            data-select-template="website_sale.s_mega_menu_thumbnails"
            data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_thumbnails.svg"
            t-out="_label"
        />
        <t t-set="_label">Cards</t>
        <we-button
            t-att-data-select-label="_label"
            data-dependencies="fetch_ecom_categories_opt"
            data-select-template="website_sale.s_mega_menu_cards"
            data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_cards.svg"
            t-out="_label"
        />
    </xpath>
</template>

<template id="snippets_options_web_editor" inherit_id="web_editor.snippet_options" name="e-commerce base snippet options">
    <xpath expr="//div[@data-js='ReplaceMedia']" position="inside">
        <we-row string="Re-order">
            <we-button class="fa fa-fw fa-angle-double-left" data-no-preview="true" title="Move to first" data-set-position="first" data-name="media_wsale_resequence"/>
            <we-button class="fa fa-fw fa-angle-left" data-no-preview="true" title="Move to previous" data-set-position="left" data-name="media_wsale_resequence"/>
            <we-button class="fa fa-fw fa-angle-right" data-no-preview="true" title="Move to next" data-set-position="right" data-name="media_wsale_resequence"/>
            <we-button class="fa fa-fw fa-angle-double-right" data-no-preview="true" title="Move to last" data-set-position="last" data-name="media_wsale_resequence"/>
        </we-row>
    </xpath>
    <xpath expr="//div[@data-js='ReplaceMedia']/we-row" position="inside">
        <we-button class="o_we_bg_danger" data-remove-media="true" data-no-preview="true" data-name="media_wsale_remove">Remove</we-button>
    </xpath>
</template>

<template id="product_searchbar_input_snippet_options" inherit_id="website.searchbar_input_snippet_options" name="product search bar snippet options">
    <xpath expr="//div[@data-js='SearchBar']/we-select[@data-name='scope_opt']" position="inside">
        <we-button data-set-search-type="products" data-select-data-attribute="products" data-name="search_products_opt" data-form-action="/shop">Products</we-button>
    </xpath>
    <xpath expr="//div[@data-js='SearchBar']/we-select[@data-name='order_opt']" position="inside">
        <t t-foreach="request.env['website']._get_product_sort_mapping()" t-as="query_and_label">
            <!-- name asc is already part of the general sorting methods of this snippet. -->
            <we-button t-if="query_and_label[0] != 'name asc'" t-att-data-set-order-by="query_and_label[0]" t-att-data-select-data-attribute="query_and_label[0]" data-dependencies="search_products_opt"><t t-out="query_and_label[1]"/></we-button>
        </t>
    </xpath>
    <xpath expr="//div[@data-js='SearchBar']/div[@data-dependencies='limit_opt']" position="inside">
        <we-checkbox string="Description" data-dependencies="search_products_opt" data-select-data-attribute="true" data-attribute-name="displayDescription"
            data-apply-to=".search-query"/>
        <we-checkbox string="Category" data-dependencies="search_products_opt" data-select-data-attribute="true" data-attribute-name="displayExtraLink"
            data-apply-to=".search-query"/>
        <we-checkbox string="Price" data-dependencies="search_products_opt" data-select-data-attribute="true" data-attribute-name="displayDetail"
            data-apply-to=".search-query"/>
        <we-checkbox string="Image" data-dependencies="search_products_opt" data-select-data-attribute="true" data-attribute-name="displayImage"
            data-apply-to=".search-query"/>
    </xpath>
</template>

</odoo>
