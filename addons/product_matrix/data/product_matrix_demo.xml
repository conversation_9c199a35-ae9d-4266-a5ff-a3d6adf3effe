<?xml version="1.0" encoding="UTF-8"?>
<odoo noupdate="1">

    <record id="matrix_product_template_shirt" model="product.template">
        <field name="name">My Company Tshirt (GRID)</field>
        <field name="categ_id" ref="product.product_category_goods"/>
        <field name="standard_price">7.0</field>
        <field name="list_price">15.0</field>
        <field name="type">consu</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="description_sale">Show your company love around you =).</field>
        <field name="image_1920" type="base64" file="product_matrix/static/img/matrix_mycompany_tshirt.jpeg"/>
    </record>

    <record id="product_template_attribute_line_size" model="product.template.attribute.line">
        <field name="product_tmpl_id" ref="matrix_product_template_shirt"/>
        <field name="attribute_id" ref="product.size_attribute"/>
        <field
            name="value_ids"
            eval="[Command.set([
                ref('product.size_attribute_xs'),
                ref('product.size_attribute_s'),
                ref('product.size_attribute_m'),
                ref('product.size_attribute_l'),
                ref('product.size_attribute_xl'),
                ref('product.size_attribute_2xl'),
                ref('product.size_attribute_3xl'),
                ref('product.size_attribute_4xl'),
                ref('product.size_attribute_5xl'),
            ])]"
        />
    </record>
    <record id="product_template_attribute_line_color" model="product.template.attribute.line">
        <field name="product_tmpl_id" ref="matrix_product_template_shirt"/>
        <field name="attribute_id" ref="product.product_attribute_color"/>
        <field
            name="value_ids"
            eval="[Command.set([
                ref('product.pav_blue'),
                ref('product.pav_pink'),
                ref('product.pav_yellow'),
                ref('product.pav_gold'),
            ])]"
        />
    </record>
</odoo>
