<templates>
    <t t-name="purchase.DocumentFileUploader">
        <FileUploader
            acceptedFileExtensions="props.acceptedFileExtensions"
            fileUploadClass="'document_file_uploader'"
            multiUpload="true"
            onClick.bind="onClick"
            onUploaded.bind="onFileUploaded"
            onUploadComplete.bind="onUploadComplete">
            <t t-set-slot="toggler">
                <button type="button" class="btn btn-secondary" data-hotkey="shift+i">
                    Upload Bill
                </button>
            </t>
            <t t-slot="default"/>
        </FileUploader>
    </t>
 </templates>
