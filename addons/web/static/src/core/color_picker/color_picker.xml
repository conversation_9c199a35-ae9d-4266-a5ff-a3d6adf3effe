<templates xml:space="preserve">
<t t-name="web.ColorPicker">
    <div class="o_font_color_selector user-select-none" t-on-pointerdown.stop="() => {}" data-prevent-closing-overlay="true" t-ref="root">
        <div class="my-1 d-flex">
            <button class="btn btn-sm btn-light ms-1 text-truncate btn-tab theme-tab"
                t-if="this.props.enabledTabs.includes('theme')"
                t-att-class="{active: state.activeTab === 'theme'}"
                t-on-click="() => this.setTab('theme')">
                Theme
            </button>
            <button class="btn btn-sm btn-light ms-1 text-truncate btn-tab solid-tab"
                t-if="this.props.enabledTabs.includes('solid')"
                t-att-class="{active: state.activeTab === 'solid'}"
                t-on-click="() => this.setTab('solid')">
                Solid
            </button>
            <button class="btn btn-sm btn-light text-truncate btn-tab custom-tab"
                t-if="this.props.enabledTabs.includes('custom')"
                t-att-class="{active: state.activeTab === 'custom'}"
                t-on-click="() => this.setTab('custom')">
                Custom
            </button>
            <button class="btn btn-sm btn-light text-truncate btn-tab gradient-tab"
                t-if="this.props.enabledTabs.includes('gradient')"
                t-att-class="{active: state.activeTab === 'gradient'}"
                t-on-click="() => this.setTab('gradient')">
                Gradient
            </button>
            <div class="flex-grow-1" />
            <button class="btn btn-sm btn-light fa fa-trash me-1"
                    title="Reset"
                    t-on-click="onColorApply"
                    t-on-mouseover="onColorHover"
                    t-on-mouseout="onColorHoverOut"
                    t-on-focusin="onColorFocusin"/>
        </div>
        <t t-if="state.activeTab==='theme'">
            <div class="pt-2 px-2 pb-3 d-flex flex-column gap-1 o_cc_preview_wrapper"
                t-on-click="onColorApply"
                t-on-mouseover="onColorHover"
                t-on-mouseleave="() => this.props.applyColorResetPreview()"
                t-on-focusin="onColorHover"
                t-on-focusout="onColorHoverOut"
                >
                <!-- List all Presets -->
                <t t-foreach="[1, 2, 3, 4, 5]" t-as="number" t-key="number">
                    <t t-set="className" t-value="'o_cc' + number"/>
                    <t t-set="activeClass" t-value="this.props.state.selectedColorCombination === className ? 'selected' : ''"/>
                    <button
                            type="button"
                            class="w-100 p-0 border-0 bg-transparent p-2 d-flex justify-content-between align-items-center color-combination-button"
                            t-att-class="[className, activeClass].join(' ')"
                            t-att-data-color="className"
                            t-attf-title="Preset {{number}}">
                        <h1 class="m-0 fs-4">Title</h1>
                        <p class="m-0 flex-grow-1">Text</p>
                        <span class="py-1 px-1 rounded-1 fs-6 btn btn-sm lh-1 btn btn-sm me-1 d-flex flex-column justify-content-center btn-primary"><small>Button</small></span>
                        <span class="py-1 px-1 rounded-1 fs-6 btn btn-sm lh-1 btn btn-sm d-flex flex-column justify-content-center btn-secondary"><small>Button</small></span>
                    </button>
                </t>
            </div>
        </t>
        <t t-if="state.activeTab==='solid'">
            <div class="p-1"
                t-on-click="onColorApply"
                t-on-mouseover="onColorHover"
                t-on-mouseout="onColorHoverOut"
                t-ref="solidTabRef"
                t-on-keydown="colorPickerNavigation"
                t-on-focusin="onColorFocusin">
                <div class="o_colorpicker_section">
                    <button data-color="o-color-1" t-attf-style="background-color: var(--o-color-1)" class="btn o_color_button"/>
                    <button data-color="o-color-3" t-attf-style="background-color: var(--o-color-3)" class="btn o_color_button"/>
                    <button data-color="o-color-2" t-attf-style="background-color: var(--o-color-2)" class="btn o_color_button"/>
                    <button data-color="o-color-4" t-attf-style="background-color: var(--o-color-4)" class="ms-2 btn o_color_button"/>
                    <button data-color="o-color-5" t-attf-style="background-color: var(--o-color-5)" class="btn o_color_button"/>
                </div>

                <t t-foreach="DEFAULT_COLORS" t-as="line" t-key="line_index">
                    <div class="o_color_section justify-content-center d-flex">
                        <t t-foreach="line" t-as="color" t-key="color_index">
                            <button class="o_color_button btn" t-att-class="{'selected': color === this.state.currentCustomColor.toUpperCase()}" t-att-data-color="color" t-attf-style="background-color: {{color}}"/>
                        </t>
                    </div>
                </t>
            </div>
        </t>
        <t t-if="state.activeTab==='custom'">
            <div class="p-1" t-on-keydown="colorPickerNavigation">
                <div class="o_colorpicker_section" t-on-click="onColorApply" t-on-mouseover="onColorHover" t-on-mouseout="onColorHoverOut" t-on-focusin="onColorFocusin">
                    <t t-foreach="this.usedCustomColors" t-as="color" t-key="color_index">
                        <button t-if="color !== this.state.currentCustomColor?.toLowerCase()" class="o_color_button btn" t-att-data-color="color" t-attf-style="background-color: {{color}}"/>
                    </t>
                    <button class="o_color_button btn selected" t-att-data-color="this.state.currentCustomColor" t-attf-style="background-color: {{this.state.currentCustomColor}}"/>
                </div>
                <div class="o_colorpicker_section" t-on-click="onColorApply" t-on-mouseover="onColorHover" t-on-mouseout="onColorHoverOut" t-on-focusin="onColorFocusin">
                    <button data-color="black" class="btn o_color_button bg-black"></button>
                    <button data-color="900" class="o_color_button btn" style="background-color: var(--900)"></button>
                    <button data-color="800" class="o_color_button btn" style="background-color: var(--800)"></button>
                    <button data-color="600" class="o_color_button btn" style="background-color: var(--600)"></button>
                    <button data-color="400" class="o_color_button btn" style="background-color: var(--400)"></button>
                    <button data-color="200" class="o_color_button btn" style="background-color: var(--200)"></button>
                    <button data-color="100" class="o_color_button btn" style="background-color: var(--100)"></button>
                    <button data-color="white" class="o_color_button btn bg-white"></button>
                </div>
                <CustomColorPicker
                    defaultColor="this.state.currentCustomColor"
                    onColorSelect.bind="(color) => this.applyColor(color.hex)"
                    onColorPreview.bind="onColorPreview"
                    showRgbaField="false"
                    noTransparency="props.noTransparency" />
            </div>
        </t>
        <t t-if="state.activeTab==='gradient'">
            <div class="o_colorpicker_sections p-2" t-on-click="onColorApply" t-on-mouseover="onColorHover" t-on-mouseout="onColorHoverOut" t-on-focusin="onColorFocusin">
                <t t-foreach="this.DEFAULT_GRADIENT_COLORS" t-as="gradient" t-key="gradient">
                    <button class="w-50 m-0 o_color_button o_gradient_color_button btn" t-attf-style="background-image: #{gradient};" t-att-data-color="gradient"/>
                </t>
            </div>
            <div class="px-2">
                <button t-attf-style="background-image: {{ getCurrentGradientColor() }};" class="w-50 border btn mb-2 o_custom_gradient_button" t-on-click="this.toggleGradientPicker" title="Define a custom gradient">Custom</button>
                <GradientPicker t-if="state.showGradientPicker" onGradientChange.bind="applyColor" selectedGradient="getCurrentGradientColor()"/>
            </div>
        </t>
    </div>
</t>

</templates>
