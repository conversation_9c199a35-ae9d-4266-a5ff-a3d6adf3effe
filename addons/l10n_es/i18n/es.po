# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.1alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 15:09+0000\n"
"PO-Revision-Date: 2024-11-06 15:09+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_p103
msgid "+mod390[103]"
msgstr "+mod390[103]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_p104
msgid "+mod390[104]"
msgstr "+mod390[104]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_p230
msgid "+mod390[230]"
msgstr "+mod390[230]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_p232
msgid "+mod390[232]"
msgstr "+mod390[232]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_p773
msgid "+mod390[773]"
msgstr "+mod390[773]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_p774
msgid "+mod390[774]"
msgstr "+mod390[774]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_p775
msgid "+mod390[775]"
msgstr "+mod390[775]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_p776
msgid "+mod390[776]"
msgstr "+mod390[776]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_m104
msgid "-mod390[104]"
msgstr "-mod390[104]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_m230
msgid "-mod390[230]"
msgstr "-mod390[230]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_m232
msgid "-mod390[232]"
msgstr "-mod390[232]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_m773
msgid "-mod390[773]"
msgstr "-mod390[773]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_m774
msgid "-mod390[774]"
msgstr "-mod390[774]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_m775
msgid "-mod390[775]"
msgstr "-mod390[775]"

#. module: l10n_es
#: model:account.account.tag,name:l10n_es.account_tag_mod390_m776
msgid "-mod390[776]"
msgstr "-mod390[776]"

#. module: l10n_es
#: model_terms:ir.ui.view,arch_db:l10n_es.res_config_settings_view_form
msgid "Above this limit the simplified invoice won't be made"
msgstr "Por encima de este límite no se realizará la factura simplificada"

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de Plan de Cuentas"

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_7
msgid "Actividades con Regímenes de Deducción Diferenciados"
msgstr "Actividades con Regímenes de Deducción Diferenciados"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_56
msgid "Actividades con regímenes de deducción diferenciados"
msgstr "Actividades con regímenes de deducción diferenciados"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_8
msgid "Adquisiciones intracomunitarias de bienes"
msgstr "Adquisiciones intracomunitarias de bienes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_9
msgid "Adquisiciones intracomunitarias de servicios"
msgstr "Adquisiciones intracomunitarias de servicios"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e1
msgid "Art. 20"
msgstr "Art. 20"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e2
msgid "Art. 21"
msgstr "Art. 21"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e3
msgid "Art. 22"
msgstr "Art. 22"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e4
msgid "Art. 23 y 24"
msgstr "Art. 23 y 24"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e5
msgid "Art. 25"
msgstr "Art. 25"

#. module: l10n_es
#: model:account.report.column,name:l10n_es.mod_390_column_sect1
#: model:account.report.column,name:l10n_es.mod_390_column_sect2
#: model:account.report.column,name:l10n_es.mod_390_column_sect3
#: model:account.report.column,name:l10n_es.mod_390_column_sect4
#: model:account.report.column,name:l10n_es.mod_390_column_sect5
#: model:account.report.column,name:l10n_es.mod_390_column_sect6
#: model:account.report.column,name:l10n_es.mod_390_column_sect7
msgid "Balance"
msgstr "Saldo"

#. module: l10n_es
#: model:account.report.column,name:l10n_es.mod_420_column_base
msgid "Base"
msgstr "Base"

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_tax__l10n_es_bien_inversion
msgid "Bien de Inversion"
msgstr "Bien de inversión"

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_canary_full.py:0
msgid "Canary Islands - Complete (2008)"
msgstr "Islas Canarias - Completo (2008)"

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_canary_assoc.py:0
msgid "Canary Islands - PGCE non-profit entities (2008)"
msgstr "Islas Canarias - PGCE Entidades sin ánimo de lucro (2008)"

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_canary_pymes.py:0
msgid "Canary Islands - SMEs (2008)"
msgstr "Islas Canarias - PyMEs (2008)"

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_common.py:0
msgid "Common"
msgstr "Común"

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_61
#: model:account.report.line,name:l10n_es.mod_390_title_67
#: model:account.report.line,name:l10n_es.mod_390_title_73
msgid ""
"Compensaciones en régimen especial de la agricultura, ganadería y pesca"
msgstr ""
"Compensaciones en régimen especial de la agricultura, ganadería y pesca"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_41
msgid "Compensación en régimen especial de la agricultura, ganaderia y pesca"
msgstr "Compensación en régimen especial de la agricultura, ganadería y pesca"

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_full.py:0
msgid "Complete (2008)"
msgstr "Completo (2008)"

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_coop_full.py:0
msgid "Cooperatives - Complete (2008)"
msgstr "Cooperativas - Completo (2008)"

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_coop_pymes.py:0
msgid "Cooperatives - SMEs (2008)"
msgstr "Cooperativas - PyMEs (2008)"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_42
msgid ""
"Cuotas deducibles en virtud de resolución administrativa o sentencia firmes "
"con tipos no vigentes"
msgstr ""
"Cuotas deducibles en virtud de resolución administrativa o sentencia firmes "
"con tipos no vigentes"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__dua
msgid "DUA"
msgstr "DUA"

#. module: l10n_es
#: model:product.template,name:l10n_es.product_dua_valuation_10_product_template
msgid "DUA VAT Valuation 10%"
msgstr "DUA Valoración IVA 10 %"

#. module: l10n_es
#: model:product.template,name:l10n_es.product_dua_valuation_21_product_template
msgid "DUA VAT Valuation 21%"
msgstr "DUA Valoración IVA 21 %"

#. module: l10n_es
#: model:product.template,name:l10n_es.product_dua_valuation_4_product_template
msgid "DUA VAT Valuation 4%"
msgstr "DUA Valoración IVA 4 %"

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:l10n_es.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_es.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_es.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_es.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:l10n_es.field_res_partner__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_53
msgid ""
"Exclusivamente para aquellos sujetos pasivos acogidos al régimen especial "
"del criterio de caja y para aquéllos que sean destinatarios de operaciones "
"afectadas por el mismo"
msgstr ""
"Exclusivamente para aquellos sujetos pasivos acogidos al régimen especial "
"del criterio de caja y para aquéllos que sean destinatarios de operaciones "
"afectadas por el mismo"

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_tax__l10n_es_exempt_reason
msgid "Exempt Reason (Spain)"
msgstr "Motivo de exención (España)"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__exento
msgid "Exento"
msgstr "Exento"

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_chart_template__id
#: model:ir.model.fields,field_description:l10n_es.field_account_move__id
#: model:ir.model.fields,field_description:l10n_es.field_account_tax__id
#: model:ir.model.fields,field_description:l10n_es.field_res_company__id
#: model:ir.model.fields,field_description:l10n_es.field_res_config_settings__id
#: model:ir.model.fields,field_description:l10n_es.field_res_partner__id
msgid "ID"
msgstr "ID"

#. module: l10n_es
#: model_terms:ir.ui.view,arch_db:l10n_es.report_invoice_document
msgid "ID:"
msgstr "ID:"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_title_igic_deductible
msgid "IGIC Deductible"
msgstr "IGIC deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_igic_due
msgid "IGIC Due"
msgstr "IGIC devengado"

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_2
msgid "IVA Deducible"
msgstr "IVA Deducible"

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_1
msgid "IVA Devengado"
msgstr "IVA Devengado"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_19
msgid "IVA deducible"
msgstr "IVA deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_60
#: model:account.report.line,name:l10n_es.mod_390_title_66
#: model:account.report.line,name:l10n_es.mod_390_title_72
msgid "IVA deducible en adquisiciones intracomunitarias"
msgstr "IVA deducible en adquisiciones intracomunitarias"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_35
msgid "IVA deducible en adquisiciones intracomunitarias de bienes corrientes"
msgstr "IVA deducible en adquisiciones intracomunitarias de bienes corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_37
msgid ""
"IVA deducible en adquisiciones intracomunitarias de bienes de inversión"
msgstr ""
"IVA deducible en adquisiciones intracomunitarias de bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_39
msgid "IVA deducible en adquisiciones intracomunitarias de servicios"
msgstr "IVA deducible en adquisiciones intracomunitarias de servicios"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_59
#: model:account.report.line,name:l10n_es.mod_390_title_65
#: model:account.report.line,name:l10n_es.mod_390_title_71
msgid "IVA deducible en importaciones"
msgstr "IVA deducible en importaciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_31
msgid "IVA deducible en importaciones de bienes corrientes"
msgstr "IVA deducible en importaciones de bienes corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_33
msgid "IVA deducible en importaciones de bienes de inversión"
msgstr "IVA deducible en importaciones de bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_58
#: model:account.report.line,name:l10n_es.mod_390_title_64
#: model:account.report.line,name:l10n_es.mod_390_title_70
msgid "IVA deducible en operaciones interiores"
msgstr "IVA deducible en operaciones interiores"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_26
msgid "IVA deducible en operaciones interiores de bienes de inversión"
msgstr "IVA deducible en operaciones interiores de bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_21
msgid ""
"IVA deducible en operaciones interiores de bienes y servicios corrientes"
msgstr ""
"IVA deducible en operaciones interiores de bienes y servicios corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_28
msgid "IVA deducible en operaciones intragrupo de bienes de inversión"
msgstr "IVA deducible en operaciones intragrupo de bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_23
msgid ""
"IVA deducible en operaciones intragrupo de bienes y servicios corrientes"
msgstr ""
"IVA deducible en operaciones intragrupo de bienes y servicios corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_57
msgid "IVA deducible: Grupo 1"
msgstr "IVA deducible: Grupo 1"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_63
msgid "IVA deducible: Grupo 2"
msgstr "IVA deducible: Grupo 2"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_69
msgid "IVA deducible: Grupo 3"
msgstr "IVA deducible: Grupo 3"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_2
msgid "IVA devengado"
msgstr "IVA devengado"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_10
msgid "IVA devengado en otros supuestos de inversión del sujeto pasivo"
msgstr "IVA devengado en otros supuestos de inversión del sujeto pasivo"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__ignore
msgid "Ignore even the base amount"
msgstr "Ignorar incluso el importe base"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_30
msgid "Importaciones y adquisiciones intracomunitarias de bienes y servicios"
msgstr "Importaciones y adquisiciones intracomunitarias de bienes y servicios"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_55
msgid ""
"Importe de las adquisiciones de bienes y servicios a las que sea de "
"aplicación o afecte el régimen especial del criterio de caja conforme a la "
"regla general de devengo contenida en el art. 75 LIVA"
msgstr ""
"Importe de las adquisiciones de bienes y servicios a las que sea de "
"aplicación o afecte el régimen especial del criterio de caja conforme a la "
"regla general de devengo contenida en el art. 75 LIVA"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_54
msgid ""
"Importes de las entregas de bienes y prestaciones de servicios a las que "
"habiéndoles sido aplicado el régimen especial del criterio de caja hubieran "
"resultado devengadas conforme a la regla general de devengo contenida en el "
"art. 75 LIVA"
msgstr ""
"Importes de las entregas de bienes y prestaciones de servicios a las que "
"habiéndoles sido aplicado el régimen especial del criterio de caja hubieran "
"resultado devengadas conforme a la regla general de devengo contenida en el "
"art. 75 LIVA"

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_bank_statement_line__l10n_es_is_simplified
#: model:ir.model.fields,field_description:l10n_es.field_account_move__l10n_es_is_simplified
msgid "Is Simplified"
msgstr "Es simplificado"

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_es
#: model:res.country.group,name:l10n_es.mainland_es
msgid "Mainland Spain VAT"
msgstr "España Peninsula IVA"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_11
msgid "Modificación de bases y cuotas"
msgstr "Modificación de bases y cuotas"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_12
msgid "Modificación de bases y cuotas de operaciones intragrupo"
msgstr "Modificación de bases y cuotas de operaciones intragrupo"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_13
msgid ""
"Modificación de bases y cuotas por auto de declaración de concurso de "
"acreedores"
msgstr ""
"Modificación de bases y cuotas por auto de declaración de concurso de "
"acreedores"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_16
msgid "Modificación recargo equivalencia"
msgstr "Modificación recargo equivalencia"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_17
msgid ""
"Modificación recargo equivalencia por auto de declaraciónde concurso de "
"acreedores"
msgstr ""
"Modificación recargo equivalencia por auto de declaración de concurso de "
"acreedores"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__no_deducible
msgid "No Deducible"
msgstr "No Deducible"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__no_sujeto
msgid "No Sujeto"
msgstr "No Sujeto"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__no_sujeto_loc
msgid "No Sujeto por reglas de Localization"
msgstr "No Sujeto por reglas de localización"

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_assec.py:0
msgid "Non-profit entities (2008)"
msgstr "Entidades sin ánimo de lucro (2008)"

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_6
msgid "Operaciones Específicas"
msgstr "Operaciones Específicas"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_51
msgid "Operaciones específicas"
msgstr "Operaciones específicas"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_20
msgid "Operaciones interiores corrientes"
msgstr "Operaciones interiores corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_25
msgid "Operaciones interiores de bienes de inversión"
msgstr "Operaciones interiores de bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_4
msgid "Operaciones intragrupo"
msgstr "Operaciones intragrupo"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_50
#: model:account.report.line,name:l10n_es.mod_390_title_52
msgid "Operaciones realizadas en el ejercicio"
msgstr "Operaciones realizadas en el ejercicio"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e6
msgid "Otros"
msgstr "Otros"

#. module: l10n_es
#: model:ir.model.fields,help:l10n_es.field_res_company__l10n_es_simplified_invoice_limit
#: model:ir.model.fields,help:l10n_es.field_res_config_settings__l10n_es_simplified_invoice_limit
msgid ""
"Over this amount is not legally possible to create a simplified invoice"
msgstr ""
"Por encima de este importe no es legalmente posible crear una factura "
"simplificada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_47
msgid "Períodos que no tributan en Régimen especial del grupo de entidades"
msgstr "Periodos que no tributan en Régimen especial del grupo de entidades"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_48
msgid "Períodos que tributan en Régimen especial del grupo de entidades"
msgstr "Periodos que tributan en Régimen especial del grupo de entidades"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__recargo
msgid "Recargo de Equivalencia"
msgstr "Recargo de Equivalencia"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_15
msgid "Recargo de equivalencia"
msgstr "Recargo de equivalencia"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_43
#: model:account.report.line,name:l10n_es.mod_390_title_62
#: model:account.report.line,name:l10n_es.mod_390_title_68
#: model:account.report.line,name:l10n_es.mod_390_title_74
msgid "Rectificación de deducciones"
msgstr "Rectificación de deducciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_44
msgid "Rectificación de deducciones por operaciones intragrupo"
msgstr "Rectificación de deducciones por operaciones intragrupo"

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_3
msgid "Resultado Liquidación Anual"
msgstr "Resultado Liquidación Anual"

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_4
msgid "Resultado de las Liquidaciones"
msgstr "Resultado de las Liquidaciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_46
msgid "Resultado de las liquidaciones"
msgstr "Resultado de las liquidaciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_45
msgid ""
"Resultado liquidación anual (Sólo para sujetos pasivos que tributan "
"exclusivamente en territorio común)"
msgstr ""
"Resultado liquidación anual (Sólo para sujetos pasivos que tributan "
"exclusivamente en territorio común)"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__retencion
msgid "Retencion"
msgstr "Retención"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_7
msgid "Régimen especial de agencias de viaje"
msgstr "Régimen especial de agencias de viaje"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_6
msgid ""
"Régimen especial de bienes usados, objetos de arte, antigüedades y objetos "
"de colección"
msgstr ""
"Régimen especial de bienes usados, objetos de arte, antigüedades y objetos "
"de colección"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_5
msgid "Régimen especial del criterio de caja"
msgstr "Régimen especial del criterio de caja"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_3
msgid "Régimen ordinario"
msgstr "Régimen ordinario"

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_pymes.py:0
msgid "SMEs (2008)"
msgstr "PyMEs (2008)"

#. module: l10n_es
#: model_terms:ir.ui.view,arch_db:l10n_es.res_config_settings_view_form
msgid "Simplified Invoice Limit"
msgstr "Límite de factura simplificada"

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_res_company__l10n_es_simplified_invoice_limit
#: model:ir.model.fields,field_description:l10n_es.field_res_config_settings__l10n_es_simplified_invoice_limit
msgid "Simplified Invoice limit amount"
msgstr "Monto límite de factura simplificada"

#. module: l10n_es
#: model_terms:ir.ui.view,arch_db:l10n_es.res_config_settings_view_form
msgid "Spain Localization"
msgstr "Localización Española"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__sujeto
msgid "Sujeto"
msgstr "Sujeto"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__sujeto_agricultura
msgid "Sujeto Agricultura"
msgstr "Sujeto Agricultura"

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__sujeto_isp
msgid "Sujeto ISP"
msgstr "Sujeto ISP"

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_account_tax
msgid "Tax"
msgstr "Impuesto"

#. module: l10n_es
#: model:account.report.column,name:l10n_es.mod_420_column_tax_amount
msgid "Tax Amount"
msgstr "Cuota"

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390
msgid "Tax Report (Mod 390)"
msgstr "Informe de impuestos (Mod 390)"

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_420
msgid "Tax Report (Mod 420) Canary Islands"
msgstr "Informe de impuestos Canarias (Mod 420)"

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_tax__l10n_es_type
msgid "Tax Type (Spain)"
msgstr "Tipo de impuesto (España)"

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_tax__l10n_es_type
msgid "Tax Type (Spain)"
msgstr "Tipo de impuesto (España)"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_36
msgid ""
"Total bases imponibles y cuotas deducibles en adquisiciones "
"intracomunitarias de bienes corrientes"
msgstr ""
"Total bases imponibles y cuotas deducibles en adquisiciones "
"intracomunitarias de bienes corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_38
msgid ""
"Total bases imponibles y cuotas deducibles en adquisiciones "
"intracomunitarias de bienes de inversión"
msgstr ""
"Total bases imponibles y cuotas deducibles en adquisiciones "
"intracomunitarias de bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_40
msgid ""
"Total bases imponibles y cuotas deducibles en adquisiciones "
"intracomunitarias de servicios"
msgstr ""
"Total bases imponibles y cuotas deducibles en adquisiciones "
"intracomunitarias de servicios"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_32
msgid ""
"Total bases imponibles y cuotas deducibles en importaciones de bienes "
"corrientes"
msgstr ""
"Total bases imponibles y cuotas deducibles en importaciones de bienes "
"corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_34
msgid ""
"Total bases imponibles y cuotas deducibles en importaciones de bienes de "
"inversión"
msgstr ""
"Total bases imponibles y cuotas deducibles en importaciones de bienes de "
"inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_27
msgid ""
"Total bases imponibles y cuotas deducibles en operaciones interiores de "
"bienes de inversión"
msgstr ""
"Total bases imponibles y cuotas deducibles en operaciones interiores de "
"bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_22
msgid ""
"Total bases imponibles y cuotas deducibles en operaciones interiores de "
"bienes y servicios corrientes"
msgstr ""
"Total bases imponibles y cuotas deducibles en operaciones interiores de "
"bienes y servicios corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_29
msgid ""
"Total bases imponibles y cuotas deducibles en operaciones intragrupo de "
"bienes de inversión"
msgstr ""
"Total bases imponibles y cuotas deducibles en operaciones intragrupo de "
"bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_24
msgid ""
"Total bases imponibles y cuotas deducibles en operaciones intragrupo de "
"bienes y servicios corrientes"
msgstr ""
"Total bases imponibles y cuotas deducibles en operaciones intragrupo de "
"bienes y servicios corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_14
msgid "Total bases y cuotas IVA"
msgstr "Total bases y cuotas IVA"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_18
msgid "Total cuotas IVA y recargo de equivalencia"
msgstr "Total cuotas IVA y recargo de equivalencia"

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_5
msgid "Volumen de Operaciones"
msgstr "Volumen de Operaciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_49
msgid "Volumen de operaciones"
msgstr "Volumen de operaciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_01
msgid "[01] Base imponible 4%"
msgstr "[01] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_02
msgid "[02] Cuota devengada 4%"
msgstr "[02] Cuota devengada 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_03
msgid "[03] Base imponible 10%"
msgstr "[03] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_04
msgid "[04] Cuota devengada 10%"
msgstr "[04] Cuota devengada 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_05
msgid "[05] Base imponible 21%"
msgstr "[05] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_06
msgid "[06] Cuota devengada 21%"
msgstr "[06] Cuota devengada 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_07
msgid "[07] Base imponible 4%"
msgstr "[07] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_08
msgid "[08] Cuota devengada 4%"
msgstr "[08] Cuota devengada 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_09
msgid "[09] Base imponible 10%"
msgstr "[09] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_100
msgid "[100] Operaciones en régimen simplificado"
msgstr "[100] Operaciones en régimen simplificado"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_101
msgid ""
"[101] Operaciones en régimen especial de la agricultura, ganadería y pesca"
msgstr ""
"[101] Operaciones en régimen especial de la agricultura, ganadería y pesca"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_102
msgid ""
"[102] Operaciones realizadas por sujetos pasivos acogidos al régimen "
"especial del recargo de equivalencia"
msgstr ""
"[102] Operaciones realizadas por sujetos pasivos acogidos al régimen "
"especial del recargo de equivalencia"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_103
msgid "[103] Entregas intracomunitarias exentas"
msgstr "[103] Entregas intracomunitarias exentas"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_104
msgid ""
"[104] Exportaciones y otras operaciones exentas con derecho a deducción"
msgstr ""
"[104] Exportaciones y otras operaciones exentas con derecho a deducción"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_105
msgid "[105] Operaciones exentas sin derecho a deducción"
msgstr "[105] Operaciones exentas sin derecho a deducción"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_106
msgid ""
"[106] Entregas de bienes inmuebles, operaciones fi nancieras y relativas al "
"oro de inversión no habituales"
msgstr ""
"[106] Entregas de bienes inmuebles, operaciones financieras y relativas al "
"oro de inversión no habituales"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_107
msgid "[107] Entregas de bienes de inversión"
msgstr "[107] Entregas de bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_108
msgid "[108] Total volumen de operaciones (Art. 121 Ley IVA)"
msgstr "[108] Volumen de operaciones (Art. 121 Ley IVA)"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_109
msgid "[109] Adquisiciones intracomunitarias exentas"
msgstr "[109] Adquisiciones intracomunitarias exentas"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_10
msgid "[10] Cuota devengada 10%"
msgstr "[10] Cuota devengada 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_10_12
msgid "[10]-[12] 9.5%"
msgstr "[10]-[12] 9,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_110
msgid ""
"[110] Operaciones no sujetas por reglas de localización o con inversión del "
"sujeto pasivo"
msgstr ""
"[110] Operaciones no sujetas por reglas de localización o con inversión del "
"sujeto pasivo"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_111
msgid ""
"[111] Operaciones sujetas y no exentas que originan el derecho a la "
"devolución mensual"
msgstr ""
"[111] Operaciones sujetas y no exentas que originan el derecho a la "
"devolución mensual"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_113
msgid ""
"[113] Entregas interiores de bienes devengadas por inversión del sujeto "
"pasivo como consecuencia de operaciones triangulares"
msgstr ""
"[113] Entregas interiores de bienes devengadas por inversión del sujeto "
"pasivo como consecuencia de operaciones triangulares"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_11
msgid "[11] Base imponible 21%"
msgstr "[11] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_125
msgid "[125] Operaciones sujetas con inversión del sujeto pasivo"
msgstr "[125] Operaciones sujetas con inversión del sujeto pasivo"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_126
msgid ""
"[126] OSS. Operaciones no sujetas por reglas de localización acogidas a la "
"OSS"
msgstr ""
"[126] Operaciones no sujetas por reglas de localización acogidas a los "
"regímenes especiales de ventanilla única"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_127
msgid "[127] Operaciones sujetas y acogidas a la OSS"
msgstr ""
"[127] Operaciones sujetas y acogidas a los regímenes especiales de "
"ventanilla única"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_128
msgid ""
"[128] Operaciones intragrupo valoradas conforme a lo dispuesto en los arts. "
"78 y 79 LIVA"
msgstr ""
"[128] Operaciones intragrupo valoradas conforme a lo dispuesto en los "
"artículos 78 y 79 de la LIVA"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_12
msgid "[12] Cuota devengada 21%"
msgstr "[12] Cuota devengada 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_139
msgid "[139] Bienes y servicios corrientes : base imponible"
msgstr "[139] Bienes y servicios corrientes: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_13
msgid "[13] Base imponible 21%"
msgstr "[13] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_13_15
msgid "[13]-[15] 15%"
msgstr "[13]-[15] 15 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_140
msgid "[140] Bienes y servicios corrientes: cuota deducible"
msgstr "[140] Bienes y servicios corrientes: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_141
msgid "[141] Bienes de inversión : base imponible"
msgstr "[141] Bienes de inversión: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_142
msgid "[142] Bienes de inversión: cuota deducible"
msgstr "[142] Bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_143
msgid "[143] Bienes corrientes : base imponible"
msgstr "[143] Bienes corrientes: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_144
msgid "[144] Bienes corrientes: cuota deducible"
msgstr "[144] Bienes corrientes: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_145
msgid "[145] Bienes de inversión : base imponible"
msgstr "[145] Bienes de inversión: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_146
msgid "[146] Bienes de inversión: cuota deducible"
msgstr "[146] Bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_147
msgid "[147] Bienes corrientes y servicios: base imponible"
msgstr "[147] Bienes corrientes y servicios: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_148
msgid "[148] Bienes corrientes y servicios: cuota deducible"
msgstr "[148] Bienes corrientes y servicios: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_149
msgid "[149] Bienes de inversión : base imponible"
msgstr "[149] Bienes de inversión: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_14
msgid "[14] Cuota devengada 21%"
msgstr "[14] Cuota devengada 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_150
msgid "[150] Bienes de inversión: cuota deducible"
msgstr "[150] Bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_151
msgid "[151] Base imponible"
msgstr "[151] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_152
msgid "[152] Cuota deducible"
msgstr "[152] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_153
msgid "[153] Cuota deducible"
msgstr "[153] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_154
msgid "[154] Regularización de bienes de inversión : cuota deducible"
msgstr "[154] Regularización de bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_155
msgid "[155] Suma de deducciones"
msgstr "[155] Suma de deducciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_156
msgid "[156] Bienes y servicios corrientes : base imponible"
msgstr "[156] Bienes y servicios corrientes: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_157
msgid "[157] Bienes y servicios corrientes: cuota deducible"
msgstr "[157] Bienes y servicios corrientes: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_158
msgid "[158] Bienes de inversión : base imponible"
msgstr "[158] Bienes de inversión: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_159
msgid "[159] Bienes de inversión: cuota deducible"
msgstr "[159] Bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_160
msgid "[160] Bienes corrientes : base imponible"
msgstr "[160] Bienes corrientes: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_161
msgid "[161] Bienes corrientes: cuota deducible"
msgstr "[161] Bienes corrientes: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_162
msgid "[162] Bienes de inversión : base imponible"
msgstr "[162] Bienes de inversión: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_163
msgid "[163] Bienes de inversión: cuota deducible"
msgstr "[163] Bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_164
msgid "[164] Bienes corrientes y servicios: base imponible"
msgstr "[164] Bienes corrientes y servicios: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_165
msgid "[165] Bienes corrientes y servicios: cuota deducible"
msgstr "[165] Bienes corrientes y servicios: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_166
msgid "[166] Bienes de inversión : base imponible"
msgstr "[166] Bienes de inversión: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_167
msgid "[167] Bienes de inversión: cuota deducible"
msgstr "[167] Bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_168
msgid "[168] Base imponible"
msgstr "[168] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_169
msgid "[169] Cuota deducible"
msgstr "[169] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_16_18
msgid "[16]-[18] 20%"
msgstr "[16]-[18] 20 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_170
msgid "[170] Cuota deducible"
msgstr "[170] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_171
msgid "[171] Regularización de bienes de inversión : cuota deducible"
msgstr "[171] Regularización de bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_172
msgid "[172] Suma de deducciones"
msgstr "[172] Suma de deducciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_173
msgid "[173] Bienes y servicios corrientes : base imponible"
msgstr "[173] Bienes y servicios corrientes: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_174
msgid "[174] Bienes y servicios corrientes: cuota deducible"
msgstr "[174] Bienes y servicios corrientes: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_175
msgid "[175] Bienes de inversión : base imponible"
msgstr "[175] Bienes de inversión: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_176
msgid "[176] Bienes de inversión: cuota deducible"
msgstr "[176] Bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_177
msgid "[177] Bienes corrientes : base imponible"
msgstr "[177] Bienes corrientes: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_178
msgid "[178] Bienes corrientes: cuota deducible"
msgstr "[178] Bienes corrientes: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_179
msgid "[179] Bienes de inversión : base imponible"
msgstr "[179] Bienes de inversión: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_180
msgid "[180] Bienes de inversión: cuota deducible"
msgstr "[180] Bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_181
msgid "[181] Bienes corrientes y servicios: base imponible"
msgstr "[181] Bienes corrientes y servicios: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_182
msgid "[182] Bienes corrientes y servicios: cuota deducible"
msgstr "[182] Bienes corrientes y servicios: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_183
msgid "[183] Bienes de inversión : base imponible"
msgstr "[183] Bienes de inversión: base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_184
msgid "[184] Bienes de inversión: cuota deducible"
msgstr "[184] Bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_185
msgid "[185] Base imponible"
msgstr "[185] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_186
msgid "[186] Cuota deducible"
msgstr "[186] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_187
msgid "[187] Cuota deducible"
msgstr "[187] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_188
msgid "[188] Regularización de bienes de inversión : cuota deducible"
msgstr "[188] Regularización de bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_189
msgid "[189] Suma de deducciones"
msgstr "[189] Suma de deducciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_190
msgid "[190] Base imponible 4%"
msgstr "[190] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_191
msgid "[191] Cuota deducible 4%"
msgstr "[191] Cuota deducible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_196
msgid "[196] Base imponible 4%"
msgstr "[196] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_197
msgid "[197] Cuota deducible 4%"
msgstr "[197] Cuota deducible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_isp
msgid "[19]-[20] ISP"
msgstr "[19]-[20] ISP"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_1_3
msgid "[1] 0%"
msgstr "[1] 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_202
msgid "[202] Base imponible 4%"
msgstr "[202] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_203
msgid "[203] Cuota deducible 4%"
msgstr "[203] Cuota deducible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_208
msgid "[208] Base imponible 4%"
msgstr "[208] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_209
msgid "[209] Cuota deducible 4%"
msgstr "[209] Cuota deducible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_214
msgid "[214] Base imponible 4%"
msgstr "[214] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_215
msgid "[215] Cuota deducible 4%"
msgstr "[215] Cuota deducible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_21
msgid "[21] Base imponible 4%"
msgstr "[21] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_mod_21_22
msgid "[21]-[22] Corrections"
msgstr "[21]-[22] Modificación de bases y cuotas"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_220
msgid "[220] Base imponible 4%"
msgstr "[220] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_221
msgid "[221] Cuota deducible 4%"
msgstr "[221] Cuota deducible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_227
msgid ""
"[227] Operaciones en Régimen especial de bienes usados, objetos de arte, "
"antigüedades y objetos de colección"
msgstr ""
"[227] Operaciones en Régimen especial de bienes usados, objetos de arte, "
"antigüedades y objetos de colección"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_228
msgid "[228] Operaciones en régimen especial de Agencias de Viajes"
msgstr "[228] Operaciones en régimen especial de Agencias de Viajes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_22
msgid "[22] Cuota devengada 4%"
msgstr "[22] Cuota devengada 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_230
msgid "[230] Adquisiciones interiores exentas"
msgstr "[230] Adquisiciones interiores exentas"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_231
msgid "[231] Importaciones exentas"
msgstr "[231] Importaciones exentas"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_232
msgid "[232] Bases imponibles del IVA soportado no deducible"
msgstr "[232] Bases imponibles del IVA soportado no deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_23
msgid "[23] Base imponible 10%"
msgstr "[23] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_mod_23_24
msgid "[23]-[24] REAV Returned amounts"
msgstr "[23]-[24] Cuotas devueltas en régimen de viajeros"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_24
msgid "[24] Cuota devengada 10%"
msgstr "[24] Cuota devengada 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_25
msgid "[25] Base imponible 21%"
msgstr "[25] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_total_25
msgid "[25] Total Due"
msgstr "[25] Total cuotas devengadas"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_26
msgid "[26] Cuota devengada 21%"
msgstr "[26] Cuota devengada 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_igic_deductible
msgid "[26]-[27] Internal Operations"
msgstr "[26]-[27] Operaciones interiores bienes y servicios corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_27
msgid "[27] Base imponible"
msgstr "[27] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_28
msgid "[28] Base"
msgstr "[28] Base"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_28
msgid "[28] Cuota devengada"
msgstr "[28] Cuota devengada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_igic_deductible_inv
msgid "[28]-[29] Internal Operations Investment Goods"
msgstr "[28]-[29] Operaciones interiores bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_29
msgid "[29] Base imponible"
msgstr "[29] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_30
msgid "[30] Base"
msgstr "[30] Base"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_30
msgid "[30] Cuota devengada"
msgstr "[30] Cuota devengada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_igic_deductible_imp
msgid "[30]-[31] Imports"
msgstr "[30]-[31] Importaciones de bienes corrientes"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_31
msgid "[31] Base imponible"
msgstr "[31] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_32
msgid "[32] Base"
msgstr "[32] Base"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_32
msgid "[32] Cuota devengada"
msgstr "[32] Cuota devengada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_igic_deductible_imp_inv
msgid "[32]-[33] Imports of Investment Goods"
msgstr "[32]-[33] Importaciones de bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_33
msgid "[33] Base imponible"
msgstr "[33] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_34
msgid "[34] Base"
msgstr "[34] Base"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_34
msgid "[34] Cuotas"
msgstr "[34] Cuota devengada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_deduc_modif
msgid "[34]-[35] Rectification of Deductions"
msgstr "[34]-[35] Rectificación de deducciones"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_35
msgid "[35] Base imponible 0.5%"
msgstr "[35] Base imponible 0,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_comp_agri
msgid "[36] Agricultural Compensation Regime"
msgstr "[36] Compensación régimen especial de agricultura, ganadería y pesca"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_36
msgid "[36] Base"
msgstr "[36] Base"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_36
msgid "[36] Cuota devengada 0.5%"
msgstr "[36] Cuota devengada 0,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_comp_reg_inv
msgid "[37] Regularization for Investment Goods"
msgstr "[37] Regularización de cuotas soportadas por bienes de inversión"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_38
msgid "[38] Base"
msgstr "[38] Base"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_comp_reg_start
msgid "[38] Regularization before the Start of the Activity"
msgstr ""
"[38] Regularización de cuotas soportadas antes del inicio de la actividad"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_comp_reg_def
msgid "[39] Regularization for applying the definitive prorata percentage"
msgstr ""
"[39] Regularización por aplicación del porcentaje definitivo de prorrata"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_40
msgid "[40] Base"
msgstr "[40] Base"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_total
msgid "[40] Total Deductible"
msgstr "[40] Total cuotas deducibles"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_41
msgid "[41] Base imponible 1.75%"
msgstr "[41] Base imponible 1,75 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_diff
msgid "[41] Difference"
msgstr "[41] Diferencia"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_42
msgid "[42] Cuota devengada 1.75%"
msgstr "[42] Cuota devengada 1,75 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_reg_art
msgid "[42] Regularization art. 22.8.5"
msgstr "[42] Regularización cuotas artículo 22.8.5ª Ley 20/1991"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_43
msgid "[43] Base imponible"
msgstr "[43] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_igic_comp
msgid "[43] IGIC to compensate from previous periods"
msgstr "[43] Cuotas de I.G.I.C. a compensar de períodos anteriores"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_44
msgid "[44] Cuota devengada"
msgstr "[44] Cuota devengada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_to_deduct
msgid "[44] To Deduct (only in complementary self-liquidation case)"
msgstr ""
"[44] A deducir (exclusivamente en caso de autoliquidación complementaria)"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_45
msgid "[45] Base imponible"
msgstr "[45] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_result
msgid "[45] Result of the Self-liquidation"
msgstr "[45] Resultado de la autoliquidación"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_46
msgid "[46] Cuota devengada"
msgstr "[46] Cuota devengada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_47
msgid "[47] Cuotas"
msgstr "[47] Cuota devengada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_48
msgid "[48] Bases imponibles"
msgstr "[48] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_49
msgid "[49] Cuotas deducibles"
msgstr "[49] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_4_6
msgid "[4]-[6] 3%/5%"
msgstr "[4]-[6] 3 % / 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_500
msgid "[500] Base imponible 4%"
msgstr "[500] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_501
msgid "[501] Cuota devengada 4%"
msgstr "[501] Cuota devengada 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_502
msgid "[502] Base imponible 10%"
msgstr "[502] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_503
msgid "[503] Cuota devengada 10%"
msgstr "[503] Cuota devengada 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_504
msgid "[504] Base imponible 21%"
msgstr "[504] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_505
msgid "[505] Cuota devengada 21%"
msgstr "[505] Cuota devengada 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_506
msgid "[506] Base imponible 4%"
msgstr "[506] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_507
msgid "[507] Cuota 4%"
msgstr "[507] Cuota deducible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_50
msgid "[50] Base imponible"
msgstr "[50] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_512
msgid "[512] Base imponible"
msgstr "[512] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_513
msgid "[513] Cuotas deducibles"
msgstr "[513] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_514
msgid "[514] Base imponible 4%"
msgstr "[514] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_515
msgid "[515] Cuota deducible 4%"
msgstr "[515] Cuota deducible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_51
msgid "[51] Cuotas deducibles"
msgstr "[51] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_520
msgid "[520] Base imponible"
msgstr "[520] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_521
msgid "[521] Cuotas deducibles"
msgstr "[521] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_522
msgid ""
"[522] Regularización por aplicación porcentaje definitivo de prorrata: cuota"
" deducible"
msgstr ""
"[522] Regularización por aplicación porcentaje definitivo de prorrata: cuota"
" deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_523
msgid ""
"[523] Servicios localizados en el territorio de aplicación del impuesto por "
"inversión del sujeto pasivo"
msgstr ""
"[523] Servicios localizados en el territorio de aplicación del impuesto por "
"inversión del sujeto pasivo"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_524
msgid ""
"[524] Total devoluciones solicitadas por cuotas soportadas en la adquisición"
" de elementos de transporte (Art. 30 bis RIVA)"
msgstr ""
"[524] Total devoluciones solicitadas por cuotas soportadas en la adquisición"
" de elementos de transporte (Art. 30 bis RIVA)"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_525
msgid ""
"[525] Total resultados positivos autoliquidaciones del ejercicio (modelo "
"322)"
msgstr ""
"[525] Total resultados positivos autoliquidaciones del ejercicio (modelo "
"322)"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_526
msgid ""
"[526] Total resultados negativos autoliquidaciones del ejercicio (modelo "
"322)"
msgstr ""
"[526] Total resultados negativos autoliquidaciones del ejercicio (modelo "
"322)"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_52
msgid "[52] Base imponible"
msgstr "[52] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_53
msgid "[53] Cuotas deducibles"
msgstr "[53] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_545
msgid "[545] Base imponible 4%"
msgstr "[545] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_546
msgid "[546] Cuota devengada 4%"
msgstr "[546] Cuota devengada 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_547
msgid "[547] Base imponible 10%"
msgstr "[547] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_548
msgid "[548] Cuota devengada 10%"
msgstr "[548] Cuota devengada 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_54
msgid "[54] Base imponible"
msgstr "[54] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_551
msgid "[551] Base imponible 21%"
msgstr "[551] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_552
msgid "[552] Cuota devengada 21%"
msgstr "[552] Cuota devengada 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_55
msgid "[55] Cuotas deducibles"
msgstr "[55] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_56
msgid "[56] Base imponible"
msgstr "[56] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_57
msgid "[57] Cuotas deducibles"
msgstr "[57] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_587
msgid "[587] Base imponible 4%"
msgstr "[587] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_588
msgid "[588] Cuota deducible 4%"
msgstr "[588] Cuota deducible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_58
msgid "[58] Base imponible"
msgstr "[58] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_597
msgid "[597] Base imponible"
msgstr "[597] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_598
msgid "[598] Cuotas deducibles"
msgstr "[598] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_599
msgid "[599] Base imponible 1.4%"
msgstr "[599] Base imponible 1,4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_59
msgid "[59] Cuotas deducibles"
msgstr "[59] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_600
msgid "[600] Cuota devengada 1.4%"
msgstr "[600] Cuota devengada 1,4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_601
msgid "[601] Base imponible 5.2%"
msgstr "[601] Base imponible 5,2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_602
msgid "[602] Cuota devengada 5.2%"
msgstr "[602] Cuota devengada 5,2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_603
msgid "[603] Base imponible 10%"
msgstr "[603] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_604
msgid "[604] Cuota deducible 10%"
msgstr "[604] Cuota deducible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_605
msgid "[605] Base imponible 21%"
msgstr "[605] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_606
msgid "[606] Cuota deducible 21%"
msgstr "[606] Cuota deducible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_607
msgid "[607] Base imponible 10%"
msgstr "[607] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_608
msgid "[608] Cuota 10%"
msgstr "[608] Cuota deducible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_609
msgid "[609] Base imponible 21%"
msgstr "[609] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_60
msgid "[60] Base imponible"
msgstr "[60] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_610
msgid "[610] Cuota 21%"
msgstr "[610] Cuota deducible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_611
msgid "[611] Base imponible 10%"
msgstr "[611] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_612
msgid "[612] Cuota deducible 10%"
msgstr "[612] Cuota deducible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_613
msgid "[613] Base imponible 21%"
msgstr "[613] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_614
msgid "[614] Cuota deducible 21%"
msgstr "[614] Cuota deducible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_615
msgid "[615] Base imponible 10%"
msgstr "[615] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_616
msgid "[616] Cuota deducible 10%"
msgstr "[616] Cuota deducible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_617
msgid "[617] Base imponible 21%"
msgstr "[617] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_618
msgid "[618] Cuota deducible 21%"
msgstr "[618] Cuota deducible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_619
msgid "[619] Base imponible 10%"
msgstr "[619] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_61
msgid "[61] Cuota deducible"
msgstr "[61] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_620
msgid "[620] Cuota deducible 10%"
msgstr "[620] Cuota deducible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_621
msgid "[621] Base imponible 21%"
msgstr "[621] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_622
msgid "[622] Cuota deducible 21%"
msgstr "[622] Cuota deducible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_623
msgid "[623] Base imponible 10%"
msgstr "[623] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_624
msgid "[624] Cuota deducible 10%"
msgstr "[624] Cuota deducible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_625
msgid "[625] Base imponible 21%"
msgstr "[625] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_626
msgid "[626] Cuota deducible 21%"
msgstr "[626] Cuota deducible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_627
msgid "[627] Base imponible 10%"
msgstr "[627] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_628
msgid "[628] Cuota deducible 10%"
msgstr "[628] Cuota deducible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_629
msgid "[629] Base imponible 21%"
msgstr "[629] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_62
msgid "[62] Base taxable"
msgstr "[62] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_62
msgid "[62] Cuota deducible"
msgstr "[62] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_630
msgid "[630] Cuota deducible 21%"
msgstr "[630] Cuota deducible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_631
msgid "[631] Base imponible 10%"
msgstr "[631] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_632
msgid "[632] Cuota deducible 10%"
msgstr "[632] Cuota deducible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_633
msgid "[633] Base imponible 21%"
msgstr "[633] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_634
msgid "[634] Cuota deducible 21%"
msgstr "[634] Cuota deducible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_635
msgid "[635] Base imponible 10%"
msgstr "[635] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_636
msgid "[636] Cuota deducible 10%"
msgstr "[636] Cuota deducible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_637
msgid "[637] Base imponible 21%"
msgstr "[637] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_638
msgid "[638] Cuota deducible 21%"
msgstr "[638] Cuota deducible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_639
msgid "[639] Base imponible"
msgstr "[639] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_63
msgid "[63] Regularización de bienes de inversión: cuota deducible"
msgstr "[63] Regularización de bienes de inversión: cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_640
msgid "[640] Base imponible"
msgstr "[640] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_641
msgid "[641] Base imponible"
msgstr "[641] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_642
msgid "[642] Base imponible"
msgstr "[642] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_643
msgid "[643] Base imponible 4%"
msgstr "[643] Base imponible 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_644
msgid "[644] Cuota devengada 4%"
msgstr "[644] Cuota devengada 4 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_645
msgid "[645] Base imponible 10%"
msgstr "[645] Base imponible 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_646
msgid "[646] Cuota devengada 10%"
msgstr "[646] Cuota devengada 10 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_647
msgid "[647] Base imponible 21%"
msgstr "[647] Base imponible 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_648
msgid "[648] Cuota devengada 21%"
msgstr "[648] Cuota devengada 21 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_649
msgid "[649] Base imponible"
msgstr "[649] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_64
msgid "[64] Suma de deducciones: Cuotas deducibles"
msgstr "[64] Suma de deducciones: Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_650
msgid "[650] Cuota devengada"
msgstr "[650] Cuota devengada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_651
msgid "[651] Base imponible"
msgstr "[651] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_652
msgid "[652] Cuota deducible"
msgstr "[652] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_653
msgid ""
"[653] Operaciones a las que habiéndoles sido aplicado el régimen especial "
"del criterio de caja hubieran resultado devengadas conforme a la regla "
"general de devengo contenida en el art. 75 LIVA"
msgstr ""
"[653] Operaciones a las que habiéndoles sido aplicado el régimen especial "
"del criterio de caja hubieran resultado devengadas conforme a la regla "
"general de devengo contenida en el art. 75 LIVA"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_654
msgid "[654] Base imponible"
msgstr "[654] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_655
msgid "[655] Cuota"
msgstr "[655] Cuota"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_656
msgid "[656] Base imponible"
msgstr "[656] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_657
msgid "[657] Cuota soportada"
msgstr "[657] Cuota soportada"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_658
msgid "[658] Regularización cuotas art. 80.Cinco.5ª LIVA"
msgstr "[658] Regularización cuotas art. 80.Cinco.5ª LIVA"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_659
msgid ""
"[659] IVA a la importación liquidado por la Aduana (sólo sujetos pasivos con"
" opción de diferimiento)"
msgstr ""
"[659] IVA a la importación liquidado por la Aduana (sólo sujetos pasivos con"
" opción de diferimiento)"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_65
msgid "[65] Resultado régimen general"
msgstr "[65] Resultado régimen general"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_660
msgid "[660] Base imponible"
msgstr "[660] Base imponible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_661
msgid "[661] Cuota deducible"
msgstr "[661] Cuota deducible"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_662
msgid "[662] Cuotas pendientes de compensación al término del ejercicio"
msgstr ""
"[662] Cuotas pendientes de compensación generadas en el ejercicio y "
"distintas de las incluidas en la casilla 97"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_667
msgid "[667] Base imponible 2%"
msgstr "[667] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_667
msgid "[667] Base imponible 2%"
msgstr "[667] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_668
msgid "[668] Cuota 2%"
msgstr "[668] Cuota devengada 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_668
msgid "[668] Cuota 2%"
msgstr "[668] Cuota devengada 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_669
msgid "[669] Base imponible 7.5%"
msgstr "[669] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_669
msgid "[669] Base imponible 7.5%"
msgstr "[669] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_670
msgid "[670] Cuota 7.5%"
msgstr "[670] Cuota devengada 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_671
msgid "[671] Base imponible 2%"
msgstr "[671] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_672
msgid "[672] Cuota 2%"
msgstr "[672] Cuota devengada 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_673
msgid "[673] Base imponible 7.5%"
msgstr "[673] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_674
msgid "[674] Cuota 7.5%"
msgstr "[674] Cuota devengada 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_675
msgid "[675] Base imponible 2%"
msgstr "[675] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_676
msgid "[676] Cuota 2%"
msgstr "[676] Cuota devengada 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_677
msgid "[677] Base imponible 7.5%"
msgstr "[677] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_678
msgid "[678] Cuota 7.5%"
msgstr "[678] Cuota devengada 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_679
msgid "[679] Base imponible 2%"
msgstr "[679] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_680
msgid "[680] Cuota 2%"
msgstr "[680] Cuota devengada 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_681
msgid "[681] Base imponible 7.5%"
msgstr "[681] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_682
msgid "[682] Cuota 7.5%"
msgstr "[682] Cuota devengada 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_683
msgid "[683] Base imponible 2%"
msgstr "[683] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_684
msgid "[684] Cuota 2%"
msgstr "[684] Cuota devengada 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_685
msgid "[685] Base imponible 7.5%"
msgstr "[685] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_686
msgid "[686] Cuota 7.5%"
msgstr "[686] Cuota devengada 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_687
msgid "[687] Base imponible 2%"
msgstr "[687] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_688
msgid "[688] Cuota 2%"
msgstr "[688] Cuota devengada 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_689
msgid "[689] Base imponible 7.5%"
msgstr "[689] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_690
msgid "[690] Cuota 7.5%"
msgstr "[690] Cuota devengada 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_695
msgid "[695] Base imponible 2%"
msgstr "[695] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_696
msgid "[696] Cuota deducible 2%"
msgstr "[696] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_697
msgid "[697] Base imponible 7.5"
msgstr "[697] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_698
msgid "[698] Cuota 7.5"
msgstr "[698] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_700
msgid "[700] Base imponible 0%"
msgstr "[700] Base imponible 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_701
msgid "[701] Cuota 0%"
msgstr "[701] Cuota devengada 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_702
msgid "[702] Base imponible 5%"
msgstr "[702] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_703
msgid "[703] Cuota 5%"
msgstr "[703] Cuota devengada 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_704
msgid "[704] Base imponible 0%"
msgstr "[704] Base imponible 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_705
msgid "[705] Cuota 0%"
msgstr "[705] Cuota devengada 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_706
msgid "[706] Base imponible 5%"
msgstr "[706] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_707
msgid "[707] Cuota 5%"
msgstr "[707] Cuota devengada 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_708
msgid "[708] Base imponible 0%"
msgstr "[708] Base imponible 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_709
msgid "[709] Cuota 0%"
msgstr "[709] Cuota devengada 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_710
msgid "[710] Base imponible 5%"
msgstr "[710] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_711
msgid "[711] Cuota 5%"
msgstr "[711] Cuota devengada 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_712
msgid "[712] Base imponible 0%"
msgstr "[712] Base imponible 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_713
msgid "[713] Cuota 0%"
msgstr "[713] Cuota devengada 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_714
msgid "[714] Base imponible 5%"
msgstr "[714] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_715
msgid "[715] Cuota 5%"
msgstr "[715] Cuota devengada 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_716
msgid "[716] Base imponible 0%"
msgstr "[716] Base imponible 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_717
msgid "[717] Cuota 0%"
msgstr "[717] Cuota devengada 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_718
msgid "[718] Base imponible 5%"
msgstr "[718] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_719
msgid "[719] Cuota 5%"
msgstr "[719] Cuota devengada 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_720
msgid "[720] Base imponible 0%"
msgstr "[720] Base imponible 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_721
msgid "[721] Cuota 0%"
msgstr "[721] Cuota devengada 0 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_722
msgid "[722] Base imponible 5%"
msgstr "[722] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_723
msgid "[723] Cuota 5%"
msgstr "[723] Cuota devengada 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_724
msgid "[724] Base imponible 5%"
msgstr "[724] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_725
msgid "[725] Cuota 5%"
msgstr "[725] Cuota deducible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_726
msgid "[726] Base imponible 5%"
msgstr "[726] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_727
msgid "[727] Cuota 5%"
msgstr "[727] Cuota deducible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_728
msgid "[728] Base imponible 5%"
msgstr "[728] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_729
msgid "[729] Cuota 5%"
msgstr "[729] Cuota deducible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_730
msgid "[730] Base imponible 5%"
msgstr "[730] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_731
msgid "[731] Cuota 5%"
msgstr "[731] Cuota deducible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_732
msgid "[732] Base imponible 5%"
msgstr "[732] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_733
msgid "[733] Cuota 5%"
msgstr "[733] Cuota deducible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_734
msgid "[734] Base imponible 5%"
msgstr "[734] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_735
msgid "[735] Cuota 5%"
msgstr "[735] Cuota deducible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_736
msgid "[736] Base imponible 5%"
msgstr "[736] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_737
msgid "[737] Cuota 5%"
msgstr "[737] Cuota deducible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_738
msgid "[738] Base imponible 5%"
msgstr "[738] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_739
msgid "[739] Cuota 5%"
msgstr "[739] Cuota deducible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_740
msgid "[740] Base imponible 5%"
msgstr "[740] Base imponible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_741
msgid "[741] Cuota 5%"
msgstr "[741] Cuota deducible 5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_745
msgid "[745] Base imponible 2%"
msgstr "[745] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_745
msgid "[745] Base imponible 2%"
msgstr "[745] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_746
msgid "[746] Cuota 2%"
msgstr "[746] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_746
msgid "[746] Cuota 2%"
msgstr "[746] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_747
msgid "[747] Base imponible 7.5%"
msgstr "[747] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_747
msgid "[747] Base imponible 7.5%"
msgstr "[747] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_748
msgid "[748] Cuota 7.5%"
msgstr "[748] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_748
msgid "[748] Cuota 7.5%"
msgstr "[748] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_749
msgid "[749] Base imponible 2%"
msgstr "[749] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_749
msgid "[749] Base imponible 2%"
msgstr "[749] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_750
msgid "[750] Cuota deducible 2%"
msgstr "[750] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_751
msgid "[751] Base imponible 7.5%"
msgstr "[751] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_752
msgid "[752] Cuota 7.5%"
msgstr "[752] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_753
msgid "[753] Base imponible 2%"
msgstr "[753] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_754
msgid "[754] Cuota deducible 2%"
msgstr "[754] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_755
msgid "[755] Base imponible 7.5%"
msgstr "[755] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_756
msgid "[756] Cuota 7.5%"
msgstr "[756] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_757
msgid "[757] Base imponible 2%"
msgstr "[757] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_758
msgid "[758] Cuota deducible 2%"
msgstr "[758] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_759
msgid "[759] Base imponible 7.5%"
msgstr "[759] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_760
msgid "[760] Cuota 7.5%"
msgstr "[760] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_761
msgid "[761] Base imponible 2%"
msgstr "[761] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_762
msgid "[762] Cuota deducible 2%"
msgstr "[762] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_763
msgid "[763] Base imponible 7.5%"
msgstr "[763] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_764
msgid "[764] Cuota 7.5%"
msgstr "[764] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_765
msgid "[765] Base imponible 2%"
msgstr "[765] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_766
msgid "[766] Cuota deducible 2%"
msgstr "[766] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_767
msgid "[767] Base imponible 7.5%"
msgstr "[767] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_768
msgid "[768] Cuota 7.5%"
msgstr "[768] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_769
msgid "[769] Base imponible 2%"
msgstr "[769] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_770
msgid "[770] Cuota deducible 2%"
msgstr "[770] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_771
msgid "[771] Base imponible 7.5%"
msgstr "[771] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_772
msgid "[772] Cuota deducible 7.5%"
msgstr "[772] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_773
msgid "[773] Base imponible 2%"
msgstr "[773] Base imponible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_774
msgid "[774] Cuota deducible 2%"
msgstr "[774] Cuota deducible 2 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_775
msgid "[775] Base imponible 7.5%"
msgstr "[775] Base imponible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_776
msgid "[776] Cuota 7.5%"
msgstr "[776] Cuota deducible 7,5 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_420_casilla_7_9
msgid "[7]-[9] 7%"
msgstr "[7]-[9] 7 %"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_84
msgid "[84] Suma de resultados"
msgstr "[84] Suma de resultados"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_85
msgid "[85] Compensación de cuotas del ejercicio anterior"
msgstr "[85] Compensación de cuotas del ejercicio anterior"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_86
msgid "[86] Suma de resultados"
msgstr "[86] Suma de la liquidación"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_95
msgid ""
"[95] Total resultados a ingresar en las autoliquidaciones de IVA del "
"ejercicio"
msgstr ""
"[95] Total resultados a ingresar en las autoliquidaciones de IVA del "
"ejercicio"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_96
msgid ""
"[96] Total devoluciones mensuales de IVA solicitadas por sujetos pasivos "
"inscritos en el Registro de devolución mensual"
msgstr ""
"[96] Total devoluciones mensuales de IVA solicitadas por sujetos pasivos "
"inscritos en el Registro de devolución mensual"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_97
msgid ""
"[97] Si el resultado de la autoliquidación del último periodo es a compensar"
" o a devolver consigne su importe: a compensar"
msgstr ""
"[97] Si el resultado de la autoliquidación del último periodo es a compensar"
" o a devolver consigne su importe: a compensar"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_98
msgid ""
"[98] Si el resultado de la autoliquidación del último periodo es a compensar"
" o a devolver consigne su importe: a devolver"
msgstr ""
"[98] Si el resultado de la autoliquidación del último periodo es a compensar"
" o a devolver consigne su importe: a devolver"

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_99
msgid "[99] Operaciones en régimen general"
msgstr "[99] Operaciones en régimen general"
