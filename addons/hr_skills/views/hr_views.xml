<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="hr_employee_view_search" model="ir.ui.view">
        <field name="name">hr.employee.skill.search</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='job_id']" position="after">
                <field name="employee_skill_ids"/>
                <field name="resume_line_ids" string="Resume" filter_domain="
                    ['|',
                        ('resume_line_ids.name', 'ilike', self),
                        ('resume_line_ids.description', 'ilike', self),
                    ]"/>
            </xpath>
        </field>
    </record>

    <record id="hr_employee_public_view_search" model="ir.ui.view">
        <field name="name">hr.employee.public.skill.search</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='job_id']" position="after">
                <field name="employee_skill_ids"/>
                <field name="resume_line_ids" string="Resume" filter_domain="['|', ('resume_line_ids.name', 'ilike', self), ('resume_line_ids.description', 'ilike', self)]"/>
            </xpath>
        </field>
    </record>

    <record id="resume_line_view_form" model="ir.ui.view">
        <field name="name">hr.resume.line.form</field>
        <field name="model">hr.resume.line</field>
        <field name="arch" type="xml">
            <form string="Resume">
                <sheet>
                    <div class="oe_title">
                        <label for="name" string="Title"/>
                        <h1>
                            <field name="name" placeholder="e.g. Odoo Inc." required="True"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="employee_id" invisible="1"/>
                            <field name="line_type_id"/>
                            <field name="display_type" required="1"/>
                        </group>
                        <group>
                            <field name="date_start" string="Duration" widget="daterange" options="{'end_date_field': 'date_end'}"/>
                            <field name="date_end" invisible="1"/>
                        </group>
                    </group>
                    <field name="description" placeholder="Description"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_employee_view_form" model="ir.ui.view">
        <field name="name">hr.employee.view.form.inherit.resume</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='global_information']" position="before">
                <page name="skills_resume" string="Resume">
                    <div class="row">
                        <div class="o_hr_skills_editable o_hr_skills_group o_group_resume col-lg-7 d-flex flex-column">
                            <separator string="Resume" class="mb-4"/>
                            <!-- This field uses a custom list view rendered by the 'resume_one2many' widget.
                                Adding fields in the list arch below makes them accessible to the widget
                            -->
                            <field mode="list" nolabel="1" name="resume_line_ids" widget="resume_one2many">
                                <list>
                                    <field name="line_type_id"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="date_start"/>
                                    <field name="date_end"/>
                                    <field name="display_type" column_invisible="True"/>
                                </list>
                            </field>

                        </div>
                        <div class="o_hr_skills_editable o_hr_skills_group o_group_skills col-lg-5 d-flex flex-column">
                            <field mode="list" nolabel="1" name="employee_skill_ids" widget="skills_one2many" context="{'no_timeline': not employee_skill_ids}" class="mt-2">
                                <list>
                                    <field name="skill_id"/>
                                    <field name="skill_level_id"/>
                                    <field name="level_progress" widget="progressbar"/>
                                    <field name="skill_type_id" optional="hidden"/>
                                </list>
                            </field>
                        </div>
                    </div>
                </page>
            </xpath>
        </field>
    </record>


    <record id="hr_employee_public_view_form_inherit" model="ir.ui.view">
        <field name="name">hr.employee.public.view.form.inherit.resume</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='contract_information']" position="before">
                <page name="skills_resume" string="Resume">
                    <div class="row">
                        <div class="o_hr_skills_group o_group_resume col-lg-6 d-flex flex-column">
                            <!-- This field uses a custom list view rendered by the 'resume_one2many' widget.
                                Adding fields in the list arch below makes them accessible to the widget
                            -->
                            <field mode="list" nolabel="1" name="resume_line_ids" widget="resume_one2many">
                                <list>
                                    <field name="line_type_id"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="date_start"/>
                                    <field name="date_end"/>
                                    <field name="display_type" column_invisible="True"/>
                                </list>
                            </field>
                        </div>
                        <div class="o_hr_skills_group o_group_skills col-lg-5 d-flex flex-column">
                            <field name="employee_id" invisible="1"/>
                            <field mode="list" nolabel="1" name="employee_skill_ids" invisible="not employee_skill_ids"  widget="skills_one2many">
                                <list>
                                    <field name="skill_type_id" optional="hidden"/>
                                    <field name="skill_id"/>
                                    <field name="skill_level_id"/>
                                    <field name="level_progress" widget="progressbar"/>
                                </list>
                            </field>
                        </div>
                    </div>
                </page>
            </xpath>
        </field>
    </record>

    <record id="res_users_view_form" model="ir.ui.view">
        <field name="name">hr.user.preferences.form.inherit.hr.skills</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="hr.res_users_view_form_profile" />
        <field name="arch" type="xml">
            <xpath expr="//page[@name='contract_information']" position="before">
                <page name="skills_resume" string="Resume">
                    <div class="row">
                        <field name="employee_id" invisible="1" />
                        <div class="o_hr_skills_group o_group_resume col-lg-6 d-flex margin-left: 0.1em">
                            <!-- This field uses a custom list view rendered by the 'resume_one2many' widget.
                                Adding fields in the list arch below makes them accessible to the widget
                            -->
                            <field mode="list" nolabel="1" name="resume_line_ids" widget="resume_one2many" readonly="not can_edit">
                                <list>
                                    <field name="line_type_id"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="date_start"/>
                                    <field name="date_end"/>
                                    <field name="display_type" invisible="1"/>
                                </list>
                            </field>
                        </div>
                        <div class="o_hr_skills_group o_group_skills col-lg-5 d-flex flex-column">
                            <field mode="list" name="employee_skill_ids"  widget="skills_one2many" context="{'no_timeline': not employee_skill_ids}" readonly="not can_edit">
                                <list>
                                    <field name="skill_type_id" optional="hidden"/>
                                    <field name="skill_id"/>
                                    <field name="skill_level_id"/>
                                    <field name="level_progress" widget="progressbar"/>
                                </list>
                            </field>
                        </div>
                    </div>
                </page>
            </xpath>
        </field>
    </record>

    <record id="hr_resume_line_type_tree_view" model="ir.ui.view">
        <field name="name">hr.resume.line.type.list.view</field>
        <field name="model">hr.resume.line.type</field>
        <field name="arch" type="xml">
            <list name="Resume Line Types" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
            </list>
        </field>
    </record>

    <record id="hr_resume_type_action" model="ir.actions.act_window">
        <field name="name">Resume Line Types</field>
        <field name="res_model">hr.resume.line.type</field>
        <field name="view_mode">list,form</field>
    </record>

    <menuitem
            id="menu_human_resources_configuration_resume"
            name="Resume"
            parent="hr.menu_human_resources_configuration"
            sequence="15"
            groups="base.group_no_one"/>

    <menuitem
        id="hr_resume_line_type_menu"
        name="Line Types"
        action="hr_resume_type_action"
        parent="hr_skills.menu_human_resources_configuration_resume"
        sequence="3"
        groups="base.group_no_one"/>

    <!-- Skills -->

    <record id="hr_skill_type_action" model="ir.actions.act_window">
        <field name="name">Skill Types</field>
        <field name="res_model">hr.skill.type</field>
        <field name="view_mode">list,form</field>
    </record>

    <record id="employee_skill_level_view_tree" model="ir.ui.view">
        <field name="name">hr.skill.level.list</field>
        <field name="model">hr.skill.level</field>
        <field name="arch" type="xml">
            <list string="Skill Levels" class="o_skill_level_tree" editable="bottom">
                <field name="name"/>
                <field name="level_progress" widget="progressbar" options="{'editable': true}"/>
                <field name="default_level" widget="boolean_toggle_load"/>
                <field name="technical_is_new_default" column_invisible="1"/> <!-- needs to be here to be accessible for the front-end -->
            </list>
        </field>
    </record>

    <record id="employee_skill_view_tree" model="ir.ui.view">
        <field name="name">hr.skill.list</field>
        <field name="model">hr.skill</field>
        <field name="arch" type="xml">
            <list string="Skill Levels">
                <field name="name"/>
                <field name="skill_type_id"/>
            </list>
        </field>
    </record>

    <record id="employee_skill_level_view_form" model="ir.ui.view">
        <field name="name">hr.skill.level.form</field>
        <field name="model">hr.skill.level</field>
        <field name="arch" type="xml">
            <form string="Skill Level">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="level_progress" string="Progress (%)"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="employee_skill_view_form" model="ir.ui.view">
        <field name="name">hr.employees.skill.form</field>
        <field name="model">hr.employee.skill</field>
        <field name="arch" type="xml">
            <form string="Skills" class="o_hr_skills_dialog_form">
                <sheet>
                    <group>
                        <group>
                            <field name="employee_id" invisible="1"/>
                            <field name="skill_type_id" widget="radio" />
                        </group>
                        <group>
                            <field name="skill_id" options="{'no_open': True, 'no_create': True}"
                                    context="{'default_skill_type_id': skill_type_id}"
                                    domain="[('skill_type_id', '=', skill_type_id)]"
                                    invisible="not skill_type_id"/>
                            <label for="skill_level_id"
                                   invisible="not (skill_id or skill_type_id)"/>
                            <div class="o_row" invisible="not (skill_id or skill_type_id)">
                                <span class="ps-0" style="flex:1">
                                    <field name="skill_level_id"
                                           readonly="not skill_id"
                                           options="{'no_open': True, 'no_create': True}"
                                           context="{'from_skill_level_dropdown': True, 'default_skill_type_id': skill_type_id}" />
                                </span>
                                <span style="flex:1">
                                    <field name="level_progress" widget="progressbar" class="o_hr_skills_progress" invisible="not skill_level_id" />
                                </span>
                            </div>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_skill_view_form" model="ir.ui.view">
        <field name="name">hr.skill.form</field>
        <field name="model">hr.skill</field>
        <field name="arch" type="xml">
            <form string="Skills">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="skill_type_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_skill_view_search" model="ir.ui.view">
        <field name="name">hr.skill.view.search</field>
        <field name="model">hr.skill</field>
        <field name="arch" type="xml">
            <search string="Search Skill">
                <field name="name" string="Skill"/>
                <field name="skill_type_id" string="Skill Type"/>
                <separator/>
                <group expand="0" string="Group By...">
                        <filter string="Skill Type" name="group_skill_type_id" domain="[]" context="{'group_by':'skill_type_id'}"/>
                </group>
            </search>
        </field>
    </record>

     <record id="hr_skill_type_view_search" model="ir.ui.view">
        <field name="name">hr.skill.type.search</field>
        <field name="model">hr.skill.type</field>
        <field name="arch" type="xml">
            <search string="Search Skill Type">
                <field name="name" string="Skill Types"/>
                <field name="skill_ids"/>
                <field name="skill_level_ids"/>
                <field name="active" invisible="1"/>
                <filter name="inactive" string="Archived" domain="[('active', '=', False)]"/>
                <separator/>
            </search>
        </field>
    </record>

    <record id="hr_skill_type_view_tree" model="ir.ui.view">
        <field name="name">hr.skill.type.list</field>
        <field name="model">hr.skill.type</field>
        <field name="arch" type="xml">
            <list string="Skill Types">
                <field name="name" string="Skill Types"/>
                <field name="color" widget="color_picker" optional="show"/>
                <field name="skill_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                <field name="skill_level_ids" widget="many2many_tags_skills"/>
            </list>
        </field>
    </record>

    <record id="hr_employee_skill_type_view_form" model="ir.ui.view">
        <field name="name">hr.skill.type.form</field>
        <field name="model">hr.skill.type</field>
        <field name="arch" type="xml">
            <form string="Skill Type">
                <field name="id" invisible="1"/>
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <div class="oe_title">
                        <label for="name" string="Skill Type"/>
                        <h1>
                            <field name="name" placeholder="e.g. Languages" required="True"/>
                            <field name="active" invisible="1"/>
                        </h1>
                    </div>
                    <group string="Skills">
                    </group>
                    <field name="skill_ids" nolabel="1" context="{'default_skill_type_id': id}">
                        <list editable="bottom">
                            <field name="sequence" widget="handle" />
                            <field name="name"/>
                        </list>
                    </field>
                    <group string="Levels">
                    </group>
                    <field name="skill_level_ids" nolabel="1" context="{'default_skill_type_id': id}"/>
                    <group string="Display">
                        <field name="color" widget="color_picker"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <menuitem
        id="hr_skill_type_menu"
        name="Skill Types"
        action="hr_skill_type_action"
        parent="hr.menu_config_employee"
        sequence="7"
        groups="hr.group_hr_user"/>
</odoo>
