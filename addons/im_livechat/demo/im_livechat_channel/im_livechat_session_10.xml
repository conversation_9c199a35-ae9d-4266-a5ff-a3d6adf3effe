<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_10" model="discuss.channel">
            <field name="name"><PERSON><PERSON><PERSON><PERSON>, <PERSON></field>
            <field name="livechat_lang_id" ref="base.lang_fr"/>
            <field name="livechat_operator_id" ref="base.partner_admin"/>
            <field name="livechat_channel_id" ref="im_livechat.im_livechat_channel_data"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1)"/>
            <field name="livechat_failure">no_failure</field>
            <field name="channel_type">livechat</field>
            <field name="anonymous_name">Visiteur</field>
            <field name="country_id" ref="base.be"/>
        </record>
        <record id="im_livechat.livechat_channel_session_10_member_admin" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_10"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(months=-1)"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_10_guest" model="mail.guest">
            <field name="name">Visiteur</field>
        </record>
        <record id="im_livechat.livechat_channel_session_10_member_guest" model="discuss.channel.member">
            <field name="guest_id" ref="im_livechat.livechat_channel_session_10_guest"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_10"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_10_message_1" model="mail.message">
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_10_guest"/>
            <field name="author_id"/>
            <field name="email_from">Visiteur</field>
            <field name="date" eval="datetime.now() - timedelta(days=1)"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1)"/>
            <field name="write_date" eval="datetime.now() - timedelta(days=1)"/>
            <field name="body">Bonjour</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_10"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_10_message_2" model="mail.message">
            <field name="author_id" ref="base.partner_admin"/>
            <field name="date" eval="datetime.now() - timedelta(days=1, seconds=-15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1, seconds=-15)"/>
            <field name="write_date" eval="datetime.now() - timedelta(days=1, seconds=-15)"/>
            <field name="body">Bonjour, comment puis-je vous aider?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_10"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_10_message_3" model="mail.message">
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_10_guest"/>
            <field name="author_id"/>
            <field name="email_from">Visiteur</field>
            <field name="date" eval="datetime.now() - timedelta(days=1, seconds=-25)"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1, seconds=-25)"/>
            <field name="write_date" eval="datetime.now() - timedelta(days=1, seconds=-25)"/>
            <field name="body">Je voudrais en savoir plus au sujet de l'application Discuss</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_10"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_10_message_call_notification" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_10"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_10_guest"/>
            <field name="author_id"/>
            <field name="body">&lt;div data-oe-type=&#34;call&#34; class=&#34;o_mail_notification&#34;&gt;&lt;/div&gt;</field>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(days=-1, seconds=20)" name="date"/>
            <field eval="DateTime.today() + relativedelta(days=-1, seconds=20)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_10_call_1" model="discuss.call.history">
            <field name="channel_id" ref="im_livechat.livechat_channel_session_10" />
            <field name="start_dt"
                eval="DateTime.today() + relativedelta(days=-1, seconds=30)" />
            <field name="end_dt"
                eval="DateTime.today() + relativedelta(days=-1, minutes=25)" />
            <field name="start_call_message_id" ref="livechat_channel_session_10_message_call_notification" />
        </record>
        <record id="livechat_channel_session_10_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_10"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_10_guest"/>
            <field name="author_id"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field eval="datetime.now() - timedelta(days=1, seconds=-60)" name="date"/>
            <field eval="datetime.now() - timedelta(days=1, seconds=-60)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_10_rating" model="rating.rating">
            <field name="access_token">LIVECHAT_10</field>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="message_id" ref="im_livechat.livechat_channel_session_10_rating_message"/>
            <field name="rated_partner_id" ref="base.partner_admin"/>
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1, seconds=-53)"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_10"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="([ref('im_livechat.livechat_channel_session_10')], 1, 'LIVECHAT_10', None, 'Pas satisfait')"/>
    </data>
</odoo>
