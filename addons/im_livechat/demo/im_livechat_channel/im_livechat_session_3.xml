<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_3" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_lang_id" ref="base.lang_en"/>
            <field name="livechat_channel_id" ref="im_livechat_channel_data"/>
            <field name="livechat_operator_id" ref="base.partner_admin"/>
            <field name="name"><PERSON>, <PERSON></field>
            <field name="anonymous_name"><PERSON></field>
            <field name="create_date" eval="DateTime.today() + relativedelta(months=-1, days=-2)"/>
        </record>
        <record id="im_livechat.livechat_channel_session_3_member_admin" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_3"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(months=-1)"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_3_member_portal" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_demo_portal"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_3"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_3_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_3"/>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Hello, how may I help you?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=11)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=11)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_3_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_3"/>
            <field name="author_id" ref="base.partner_demo_portal"/>
            <field name="body">Hello, are you single?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=12)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=12)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_3_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_3"/>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Nope, sorry to disappoint :(</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=13)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=13)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_3_message_4" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_3"/>
            <field name="author_id" ref="base.partner_demo_portal"/>
            <field name="body">Oh :(</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=14)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=14)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_3_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_3"/>
            <field name="author_id" ref="base.partner_demo_portal"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=15)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-2, minutes=15)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_3_rating" model="rating.rating">
            <field name="access_token">LIVECHAT_3</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_3"/>
            <field name="message_id" ref="im_livechat.livechat_channel_session_3_rating_message"/>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="rated_partner_id" ref="base.partner_admin"/>
            <field name="partner_id" ref="base.partner_demo_portal"/>
            <field eval="True" name="consumed"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="([ref('im_livechat.livechat_channel_session_3')], 5, 'LIVECHAT_3', None, 'Mega Job')"/>
    </data>
</odoo>
