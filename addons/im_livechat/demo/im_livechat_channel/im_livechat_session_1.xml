<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_1" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_lang_id" ref="base.lang_en"/>
            <field name="livechat_channel_id" ref="im_livechat_channel_data"/>
            <field name="livechat_operator_id" ref="base.partner_admin"/>
            <field name="name">Visitor #234, <PERSON></field>
            <field name="anonymous_name">Visitor #234</field>
            <field name="create_date" eval="DateTime.today() + relativedelta(months=-1)"/>
        </record>
        <record id="im_livechat.livechat_channel_session_1_member_admin" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(months=-1)"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_1_guest" model="mail.guest">
            <field name="name">Visitor #234</field>
        </record>
        <record id="im_livechat.livechat_channel_session_1_member_guest" model="discuss.channel.member">
            <field name="guest_id" ref="im_livechat.livechat_channel_session_1_guest"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_1_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="message_type">email</field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Hello, how may I help you?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=1)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=1)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_1_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_1_guest"/>
            <field name="author_id"/>
            <field name="email_from">Visitor</field>
            <field name="body">I'm looking for an application to record my timesheet, any tips?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=2)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=2)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_1_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Yes, you can use our Timesheets application to record your time efficiently!</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=3)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=3)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_1_message_4" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_1_guest"/>
            <field name="author_id"/>
            <field name="email_from">Visitor</field>
            <field name="body">Great! Thanks for the info</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=4)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=4)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_1_message_5" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="message_type">email</field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">You're welcome, have a nice day!</field>
            <field name="message_type">comment</field>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=5)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=5)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_1_message_call_notification" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_1_guest"/>
            <field name="author_id"/>
            <field name="body">&lt;div data-oe-type=&#34;call&#34; class=&#34;o_mail_notification&#34;&gt;&lt;/div&gt;</field>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=3)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=3)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_1_call_1" model="discuss.call.history">
            <field name="channel_id" ref="im_livechat.livechat_channel_session_1" />
            <field name="start_dt"
                eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=3)" />
            <field name="end_dt"
                eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=4)" />
            <field name="start_call_message_id" ref="livechat_channel_session_1_message_call_notification" />
        </record>
        <record id="livechat_channel_session_1_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_1_guest"/>
            <field name="author_id"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=5, seconds=25)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-0, minutes=5, seconds=25)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_1_rating" model="rating.rating">
            <field name="access_token">LIVECHAT_1</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_1"/>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="message_id" ref="livechat_channel_session_1_rating_message"/>
            <field name="rated_partner_id" ref="base.partner_admin"/>
            <field eval="False" name="partner_id"/>
            <field eval="True" name="consumed"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="([ref('im_livechat.livechat_channel_session_1')], 5, 'LIVECHAT_1', None, 'Good Job')"/>
    </data>
</odoo>
