<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_12" model="discuss.channel">
            <field name="name">Visitor, <PERSON></field>
            <field name="livechat_lang_id" ref="base.lang_en"/>
            <field name="livechat_operator_id" ref="base.partner_admin"/>
            <field name="livechat_channel_id" ref="im_livechat.im_livechat_channel_data"/>
            <field name="livechat_failure">no_answer</field>
            <field name="create_date" eval="datetime.now()"/>
            <field name="channel_type">livechat</field>
            <field name="anonymous_name">Visitor</field>
            <field name="country_id" ref="base.us"/>
        </record>
        <record id="im_livechat.livechat_channel_session_12_member_admin" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_12"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_12_guest" model="mail.guest">
            <field name="name">Visitor</field>
        </record>
        <record id="im_livechat.livechat_channel_session_12_member_guest" model="discuss.channel.member">
            <field name="guest_id" ref="im_livechat.livechat_channel_session_12_guest"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_12"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_12_message_1" model="mail.message">
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_12_guest"/>
            <field name="author_id"/>
            <field name="record_name">Visitor</field>
            <field name="date" eval="datetime.now()"/>
            <field name="create_date" eval="datetime.now()"/>
            <field name="write_date" eval="datetime.now()"/>
            <field name="body">Hello? Anybody there?</field>
            <field name="email_from">Visitor</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_12"/>
            <field name="model">discuss.channel</field>
        </record>
    </data>
</odoo>
