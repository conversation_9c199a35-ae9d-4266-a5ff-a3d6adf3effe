<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_2" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_lang_id" ref="base.lang_en"/>
            <field name="livechat_channel_id" ref="im_livechat_channel_data"/>
            <field name="livechat_operator_id" ref="base.partner_demo"/>
            <field name="name">Visitor #323, <PERSON></field>
            <field name="anonymous_name">Visitor #323</field>
            <field name="create_date" eval="DateTime.today() + relativedelta(months=-1, days=-1)"/>
        </record>
        <record id="im_livechat.livechat_channel_session_2_member_demo" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_demo"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(months=-1)"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_2_guest" model="mail.guest">
            <field name="name">Visitor #323</field>
        </record>
        <record id="im_livechat.livechat_channel_session_2_member_guest" model="discuss.channel.member">
            <field name="guest_id" ref="im_livechat.livechat_channel_session_2_guest"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_2_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="body">Hello, how may I help you?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=6)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=6)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_2_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_2_guest"/>
            <field name="author_id"/>
            <field name="email_from">Visitor</field>
            <field name="body">I was wondering if Odoo has an application to easily manage social media for my business..</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=7)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=7)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_2_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="body">Yes, we just released a new application called Social Marketing that should fit your needs! Check it out :)</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=8)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=8)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_2_message_4" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_2_guest"/>
            <field name="author_id"/>
            <field name="email_from">Visitor</field>
            <field name="body">Awesome, thanks!</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=9)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=9)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_2_message_5" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="body">You're welcome, enjoy Odoo!</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=10)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=10)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_2_message_call_notification" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_2_guest"/>
            <field name="author_id"/>
            <field name="body">&lt;div data-oe-type=&#34;call&#34; class=&#34;o_mail_notification&#34;&gt;&lt;/div&gt;</field>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=6)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=6)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_2_call_1" model="discuss.call.history">
            <field name="channel_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="start_dt" eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=8)"/>
            <field name="end_dt" eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=10)"/>
            <field name="start_call_message_id" ref="livechat_channel_session_2_message_call_notification"/>
        </record>
        <record id="livechat_channel_session_2_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_2_guest"/>
            <field name="author_id"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=11)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-1, days=-1, minutes=11)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_2_rating" model="rating.rating">
            <field name="access_token">LIVECHAT_2</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_2"/>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="rated_partner_id" ref="base.partner_demo"/>
            <field name="message_id" ref="im_livechat.livechat_channel_session_2_rating_message"/>
            <field eval="False" name="partner_id"/>
            <field eval="True" name="consumed"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="([ref('im_livechat.livechat_channel_session_2')], 5, 'LIVECHAT_2', None, 'Super Job')"/>
    </data>
</odoo>
