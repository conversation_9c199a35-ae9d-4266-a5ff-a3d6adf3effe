<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_4" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_lang_id" ref="base.lang_en"/>
            <field name="livechat_channel_id" ref="im_livechat_channel_data"/>
            <field name="livechat_operator_id" ref="base.partner_demo"/>
            <field name="livechat_failure">no_failure</field>
            <field name="name"><PERSON>, <PERSON></field>
            <field name="anonymous_name"><PERSON></field>
            <field name="create_date" eval="DateTime.today() + relativedelta(months=-2, days=-3)"/>
        </record>
        <record id="im_livechat.livechat_channel_session_4_member_demo" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_demo"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_4"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(months=-1)"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_4_member_portal" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_demo_portal"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_4"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_4_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_4"/>
            <field name="author_id" ref="base.partner_demo_portal"/>
            <field name="body">Hello, I'm looking for a software that can be easily updated with my needs.</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-3, minutes=15)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-3, minutes=15)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_4_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_4"/>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="body">Hello Joel Willis, you're at the right place! You can customize Odoo using our Studio application in just a few clicks.</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-3, minutes=16)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-3, minutes=16)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_4_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_4"/>
            <field name="author_id" ref="base.partner_demo_portal"/>
            <field name="body">Thanks for the info, I'll look into it!</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-3, minutes=17)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-3, minutes=17)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_4_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_4"/>
            <field name="author_id" ref="base.partner_demo_portal"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-3, minutes=20)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-3, minutes=20)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_4_rating" model="rating.rating">
            <field name="access_token">LIVECHAT_4</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_4"/>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="message_id" ref="im_livechat.livechat_channel_session_4_rating_message"/>
            <field name="rated_partner_id" ref="base.partner_demo"/>
            <field name="partner_id" ref="base.partner_demo_portal"/>
            <field eval="True" name="consumed"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="([ref('im_livechat.livechat_channel_session_4')], 5, 'LIVECHAT_4', None, 'Good Job')"/>
    </data>
</odoo>
