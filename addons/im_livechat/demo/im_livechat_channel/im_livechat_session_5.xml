<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_5" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_lang_id" ref="base.lang_en"/>
            <field name="livechat_channel_id" ref="im_livechat_channel_data"/>
            <field name="livechat_operator_id" ref="base.partner_admin"/>
            <field name="livechat_failure">no_failure</field>
            <field name="name">Visitor #532, <PERSON></field>
            <field name="anonymous_name">Visitor #532</field>
            <field name="create_date" eval="DateTime.today() + relativedelta(months=-2, days=-4)"/>
        </record>
        <record id="im_livechat.livechat_channel_session_5_member_admin" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_5"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(months=-1)"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_5_guest" model="mail.guest">
            <field name="name">Visitor #532</field>
        </record>
        <record id="im_livechat.livechat_channel_session_5_member_guest" model="discuss.channel.member">
            <field name="guest_id" ref="im_livechat.livechat_channel_session_5_guest"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_5"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_5_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_5"/>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Hello, how may I help you?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=18)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=18)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_5_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_5"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_5_guest"/>
            <field name="author_id"/>
            <field name="email_from">Visitor</field>
            <field name="body">Hello, it seems that I can't log in to my database. Can you help?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=19)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=19)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_5_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_5"/>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Hi, if you need help with your database, feel free to contact our support via http://www.odoo.com/help</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=20)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=20)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_5_message_4" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_5"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_5_guest"/>
            <field name="author_id"/>
            <field name="email_from">Visitor</field>
            <field name="body">Ok.. Will do, thanks</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=21)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=21)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_5_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_5"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_5_guest"/>
            <field name="author_id"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=33)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-2, days=-4, minutes=33)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_5_rating" model="rating.rating">
            <field name="access_token">LIVECHAT_5</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_5"/>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="message_id" ref="im_livechat.livechat_channel_session_5_rating_message"/>
            <field name="rated_partner_id" ref="base.partner_admin"/>
            <field eval="False" name="partner_id"/>
            <field eval="True" name="consumed"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="([ref('im_livechat.livechat_channel_session_5')], 5, 'LIVECHAT_5', None, 'Super Job')"/>
    </data>
</odoo>
