<?xml version="1.0"?>
<odoo>
    <data>
        <record id="support_bot_session_4_demo" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_lang_id" ref="base.lang_it"/>
            <field name="livechat_channel_id" ref="support_bot_channel_demo"/>
            <field name="livechat_operator_id" ref="support_bot_operator_partner_demo"/>
            <field name="name">Visitatore #304, Support</field>
            <field name="anonymous_name">Visitatore #304</field>
            <field name="create_date" eval="datetime.now() - timedelta(minutes=3)"/>
        </record>
        <record id="support_bot_session_4_member_bot_demo" model="discuss.channel.member">
            <field name="partner_id" ref="support_bot_operator_partner_demo"/>
            <field name="channel_id" ref="support_bot_session_4_demo"/>
            <field name="last_interest_dt" eval="datetime.now() - timedelta(minutes=4)"/>
            <field name="livechat_member_type">bot</field>
            <field name="chatbot_script_id" ref="chatbot_script_support_bot_demo"/>
        </record>
        <record id="support_bot_session_4_guest_demo" model="mail.guest">
            <field name="name">Visitatore #304</field>
        </record>
        <record id="support_bot_session_4_member_guest_demo" model="discuss.channel.member">
            <field name="guest_id" ref="support_bot_session_4_guest_demo"/>
            <field name="channel_id" ref="support_bot_session_4_demo"/>
            <field name="livechat_member_type">visitor</field>
        </record>
        <record id="support_bot_session_4_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_4_demo"/>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Benvenuto al Supporto! Come posso aiutarla oggi?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=30)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=30)"/>
        </record>
        <record id="support_bot_session_4_chatbot_message_1" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_4_message_1"/>
            <field name="discuss_channel_id" ref="support_bot_session_4_demo"/>
            <field name="script_step_id" ref="chatbot_script_initial_step_demo"/>
        </record>
        <record id="support_bot_session_4_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_4_demo"/>
            <field name="author_id"/>
            <field name="author_guest_id" ref="support_bot_session_4_guest_demo"/>
            <field name="body">Troubleshooting</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=30)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=30)"/>
        </record>
        <record id="support_bot_session_4_chatbot_message_2" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_4_message_2"/>
            <field name="discuss_channel_id" ref="support_bot_session_4_demo"/>
            <field name="script_step_id" ref="chatbot_script_initial_step_demo"/>
            <field name="user_script_answer_id" ref="chatbot_script_answer_troubleshooting_demo"/>
            <field name="user_raw_script_answer_id" ref="chatbot_script_answer_troubleshooting_demo"/>
        </record>
        <record id="support_bot_session_4_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_4_demo"/>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Che browser sta utilizzando?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_4_chatbot_message_3" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_4_message_3"/>
            <field name="discuss_channel_id" ref="support_bot_session_4_demo"/>
            <field name="script_step_id" ref="chatbot_script_troubleshooting_step_browser_demo"/>
        </record>
        <record id="support_bot_session_4_message_4" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_4_demo"/>
            <field name="author_id"/>
            <field name="author_guest_id" ref="support_bot_session_4_guest_demo"/>
            <field name="body">Firefox</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_4_chatbot_message_4" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_4_message_4"/>
            <field name="discuss_channel_id" ref="support_bot_session_4_demo"/>
            <field name="script_step_id" ref="chatbot_script_troubleshooting_step_browser_demo"/>
            <field name="user_script_answer_id" ref="chatbot_script_troubleshooting_answer_firefox_demo"/>
            <field name="user_raw_script_answer_id" ref="chatbot_script_troubleshooting_answer_firefox_demo"/>
        </record>
        <record id="support_bot_session_4_message_5" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_4_demo"/>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Quale modulo le interessa?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=10)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=10)"/>
        </record>
        <record id="support_bot_session_4_message_6" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_4_demo"/>
            <field name="author_id"/>
            <field name="author_guest_id" ref="support_bot_session_4_guest_demo"/>
            <field name="body">Livechat</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=10)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=10)"/>
        </record>
        <record id="support_bot_session_4_chatbot_message_5" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_4_message_6"/>
            <field name="discuss_channel_id" ref="support_bot_session_4_demo"/>
            <field name="script_step_id" ref="chatbot_script_initial_step_demo"/>
            <field name="user_script_answer_id" ref="chatbot_script_answer_livechat_demo"/>
            <field name="user_raw_script_answer_id" ref="chatbot_script_answer_livechat_demo"/>
        </record>
        <record id="support_bot_session_4_message_7" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_4_demo"/>
            <field name="message_type">comment</field>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Verifico se posso connetterla con l'esperto adatto...</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <function model="discuss.channel" name="_add_members">
            <value eval="[ref('im_livechat.support_bot_session_4_demo')]"/>
            <value name="partners" model="res.partner" eval="obj().env['res.partner'].browse(ref('base.partner_admin'))"/>
            <value name="create_member_params" eval="{
                'livechat_member_type': 'agent',
                'agent_expertise_ids': [ref('im_livechat.chatbot_script_expertise_livechat_demo')]
            }"/>
            <value name="inviting_partner" model="res.partner" eval="obj().env['res.partner'].browse(ref('support_bot_operator_partner_demo'))"/>
        </function>
        <record id="support_bot_session_4_message_8" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_4_demo"/>
            <field name="message_type">comment</field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Salve 👋, ha qualche domanda riguardante livechat?</field>
            <field name="date" eval="datetime.now() + timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() + timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_4_message_call_notification" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.support_bot_session_4_demo"/>
            <field name="author_guest_id" ref="im_livechat.support_bot_session_4_guest_demo"/>
            <field name="author_id"/>
            <field name="body">&lt;div data-oe-type=&#34;call&#34; class=&#34;o_mail_notification&#34;&gt;&lt;/div&gt;</field>
            <field name="message_type">notification</field>
            <field name="date" eval="datetime.now() - timedelta(seconds=20)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=20)"/>
        </record>
        <record id="support_bot_session_4_call_1" model="discuss.call.history">
            <field name="channel_id" ref="im_livechat.support_bot_session_4_demo"/>
            <field name="start_dt"
                eval="datetime.now() - timedelta(seconds=70)"/>
            <field name="end_dt"
                eval="datetime.now() - timedelta(seconds=10)"/>
            <field name="start_call_message_id" ref="support_bot_session_4_message_call_notification"/>
        </record>
    </data>
</odoo>
