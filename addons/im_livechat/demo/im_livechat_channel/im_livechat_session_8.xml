<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_8" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_lang_id" ref="base.lang_en"/>
            <field name="livechat_channel_id" ref="im_livechat_channel_data"/>
            <field name="livechat_operator_id" ref="base.partner_demo"/>
            <field name="livechat_failure">no_failure</field>
            <field name="name">Visitor #722, <PERSON></field>
            <field name="anonymous_name">Visitor #722</field>
            <field name="create_date" eval="DateTime.today() + relativedelta(months=-3, days=-7)"/>
        </record>
        <record id="im_livechat.livechat_channel_session_8_member_demo" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_demo"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_8"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(months=-1)"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_8_guest" model="mail.guest">
            <field name="name">Visitor #722</field>
        </record>
        <record id="im_livechat.livechat_channel_session_8_member_guest" model="discuss.channel.member">
            <field name="guest_id" ref="im_livechat.livechat_channel_session_8_guest"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_8"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_8_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_8"/>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="body">Hello, how may I help you?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=30)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=30)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_8_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_8"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_8_guest"/>
            <field name="author_id"/>
            <field name="body">Heeeey Marc, how are you?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=31)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=31)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_8_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_8"/>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="body">I'm fine, and you?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=32)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=32)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_8_message_4" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_8"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_8_guest"/>
            <field name="author_id"/>
            <field name="body">I'm great, thanks for asking!</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=33)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=33)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_8_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_8"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_8_guest"/>
            <field name="author_id"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=52)" name="date"/>
            <field eval="DateTime.today() + relativedelta(months=-3, days=-7, minutes=52)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_8_rating" model="rating.rating">
            <field name="access_token">LIVECHAT_8</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_8"/>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="message_id" ref="livechat_channel_session_8_rating_message"/>
            <field name="rated_partner_id" ref="base.partner_demo"/>
            <field eval="False" name="partner_id"/>
            <field eval="True" name="consumed"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="([ref('im_livechat.livechat_channel_session_8')], 5, 'LIVECHAT_8', None, 'Super Job')"/>
    </data>
</odoo>
