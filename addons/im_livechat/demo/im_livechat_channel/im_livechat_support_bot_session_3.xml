<?xml version="1.0"?>
<odoo>
    <data>
        <record id="support_bot_session_3_demo" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_lang_id" ref="base.lang_it"/>
            <field name="livechat_channel_id" ref="support_bot_channel_demo"/>
            <field name="livechat_operator_id" ref="support_bot_operator_partner_demo"/>
            <field name="name">Visitatore #303, Support</field>
            <field name="anonymous_name">Visitatore #303</field>
            <field name="create_date" eval="DateTime.today() + relativedelta(days=-10)"/>
        </record>
        <record id="support_bot_session_3_member_bot_demo" model="discuss.channel.member">
            <field name="partner_id" ref="support_bot_operator_partner_demo"/>
            <field name="channel_id" ref="support_bot_session_3_demo"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(days=-10)"/>
            <field name="livechat_member_type">bot</field>
            <field name="chatbot_script_id" ref="chatbot_script_support_bot_demo"/>
        </record>
        <record id="support_bot_session_3_guest_demo" model="mail.guest">
            <field name="name">Visitatore #303</field>
        </record>
        <record id="support_bot_session_3_member_guest_demo" model="discuss.channel.member">
            <field name="guest_id" ref="support_bot_session_3_guest_demo"/>
            <field name="channel_id" ref="support_bot_session_3_demo"/>
            <field name="livechat_member_type">visitor</field>
        </record>
        <record id="support_bot_session_3_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_3_demo"/>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Benvenuto al Supporto! Come posso aiutarla oggi?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="DateTime.today() + relativedelta(days=-10, minutes=1)"/>
            <field name="create_date" eval="DateTime.today() + relativedelta(days=-10, minutes=1)"/>
        </record>
        <record id="support_bot_session_3_chatbot_message_1" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_3_message_1"/>
            <field name="discuss_channel_id" ref="support_bot_session_3_demo"/>
            <field name="script_step_id" ref="chatbot_script_initial_step_demo"/>
        </record>
        <record id="support_bot_session_3_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_3_demo"/>
            <field name="author_id"/>
            <field name="author_guest_id" ref="support_bot_session_3_guest_demo"/>
            <field name="body">Troubleshooting</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="DateTime.today() + relativedelta(days=-10, minutes=2)"/>
            <field name="create_date" eval="DateTime.today() + relativedelta(days=-10, minutes=2)"/>
        </record>
        <record id="support_bot_session_3_chatbot_message_2" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_3_message_2"/>
            <field name="discuss_channel_id" ref="support_bot_session_3_demo"/>
            <field name="script_step_id" ref="chatbot_script_initial_step_demo"/>
            <field name="user_script_answer_id" ref="chatbot_script_answer_troubleshooting_demo"/>
            <field name="user_raw_script_answer_id" ref="chatbot_script_answer_troubleshooting_demo"/>
        </record>
        <record id="support_bot_session_3_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_3_demo"/>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Che browser sta utilizzando?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="DateTime.today() + relativedelta(days=-10, minutes=3)"/>
            <field name="create_date" eval="DateTime.today() + relativedelta(days=-10, minutes=3)"/>
        </record>
        <record id="support_bot_session_3_chatbot_message_3" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_3_message_3"/>
            <field name="discuss_channel_id" ref="support_bot_session_3_demo"/>
            <field name="script_step_id" ref="chatbot_script_troubleshooting_step_browser_demo"/>
        </record>
        <record id="support_bot_session_3_message_4" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_3_demo"/>
            <field name="author_id"/>
            <field name="author_guest_id" ref="support_bot_session_3_guest_demo"/>
            <field name="body">Chrome</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="DateTime.today() + relativedelta(days=-10, minutes=4)"/>
            <field name="create_date" eval="DateTime.today() + relativedelta(days=-10, minutes=4)"/>
        </record>
        <record id="support_bot_session_3_chatbot_message_4" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_3_message_4"/>
            <field name="discuss_channel_id" ref="support_bot_session_3_demo"/>
            <field name="script_step_id" ref="chatbot_script_troubleshooting_step_browser_demo"/>
            <field name="user_script_answer_id" ref="chatbot_script_troubleshooting_answer_chrome_demo"/>
            <field name="user_raw_script_answer_id" ref="chatbot_script_troubleshooting_answer_chrome_demo"/>
        </record>
    </data>
</odoo>
