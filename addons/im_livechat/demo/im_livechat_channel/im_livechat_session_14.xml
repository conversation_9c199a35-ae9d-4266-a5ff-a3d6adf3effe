<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_14" model="discuss.channel">
            <field name="name"><PERSON><PERSON><PERSON><PERSON>, <PERSON></field>
            <field name="livechat_lang_id" ref="base.lang_fr"/>
            <field name="livechat_operator_id" ref="base.partner_admin"/>
            <field name="livechat_channel_id" ref="im_livechat.im_livechat_channel_data"/>
            <field name="create_date" eval="datetime.now() - timedelta(minutes=1)"/>
            <field name="livechat_failure">no_failure</field>
            <field name="channel_type">livechat</field>
            <field name="anonymous_name">Visiteur</field>
            <field name="country_id" ref="base.be"/>
        </record>
        <record id="im_livechat.livechat_channel_session_14_member_admin" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_14"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() - timedelta(minutes=1)"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_14_guest" model="mail.guest">
            <field name="name">Visiteur</field>
        </record>
        <record id="im_livechat.livechat_channel_session_14_member_guest" model="discuss.channel.member">
            <field name="guest_id" ref="im_livechat.livechat_channel_session_14_guest"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_14"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_14_message_1" model="mail.message">
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_14_guest"/>
            <field name="author_id"/>
            <field name="email_from">Visiteur</field>
            <field name="date" eval="datetime.now() - timedelta(seconds=30)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=30)"/>
            <field name="write_date" eval="datetime.now() - timedelta(seconds=30)"/>
            <field name="body">Bonjour, pourrais-je avoir de l'aide concernant l'application Discuss?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_14"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_14_message_2" model="mail.message">
            <field name="author_id" ref="base.partner_admin"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="write_date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="body">Bien sur, je vous transfère à un expert dans quelques instants.</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_14"/>
            <field name="model">discuss.channel</field>
        </record>
        <function model="discuss.channel" name="_add_members">
            <value eval="[ref('im_livechat.livechat_channel_session_14')]"/>
            <value name="partners" model="res.partner" eval="obj().env['res.partner'].browse(ref('base.partner_demo'))"/>
            <value name="inviting_partner" model="res.partner" eval="obj().env['res.partner'].browse(ref('base.partner_admin'))"/>
        </function>
        <record id="livechat_channel_session_14_message_3" model="mail.message">
            <field name="author_id" ref="base.partner_demo"/>
            <field name="date" eval="datetime.now() + timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() + timedelta(seconds=15)"/>
            <field name="write_date" eval="datetime.now() + timedelta(seconds=15)"/>
            <field name="body">Bonjour, comment puis-je vous aider?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_14"/>
            <field name="model">discuss.channel</field>
        </record>
    </data>
</odoo>
