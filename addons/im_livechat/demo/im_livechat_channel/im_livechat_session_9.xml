<?xml version="1.0"?>
<odoo>
    <data>
        <record id="livechat_channel_session_9" model="discuss.channel">
            <field name="name">Visitor, <PERSON></field>
            <field name="livechat_lang_id" ref="base.lang_en"/>
            <field name="livechat_operator_id" ref="base.partner_admin"/>
            <field name="livechat_channel_id" ref="im_livechat.im_livechat_channel_data"/>
            <field name="livechat_failure">no_failure</field>
            <field name="create_date" eval="datetime.now() - timedelta(days=1)"/>
            <field name="channel_type">livechat</field>
            <field name="anonymous_name">Visitor</field>
        </record>
        <record id="im_livechat.livechat_channel_session_9_member_admin" model="discuss.channel.member">
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_9"/>
            <field name="unpin_dt" eval="DateTime.today()"/>
            <field name="last_interest_dt" eval="DateTime.today() + relativedelta(months=-1)"/>
            <field name="livechat_member_type">agent</field>
        </record>
        <record id="im_livechat.livechat_channel_session_9_guest" model="mail.guest">
            <field name="name">Visitor</field>
        </record>
        <record id="im_livechat.livechat_channel_session_9_member_guest" model="discuss.channel.member">
            <field name="guest_id" ref="im_livechat.livechat_channel_session_9_guest"/>
            <field name="channel_id" ref="im_livechat.livechat_channel_session_9"/>
            <field name="livechat_member_type">visitor</field>
        </record>

        <record id="livechat_channel_session_9_message_1" model="mail.message">
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_9_guest"/>
            <field name="author_id"/>
            <field name="record_name">Visitor</field>
            <field name="date" eval="datetime.now() - timedelta(days=1)"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1)"/>
            <field name="write_date" eval="datetime.now() - timedelta(days=1)"/>
            <field name="body">Hi</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_9"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_9_message_2" model="mail.message">
            <field name="author_id" ref="base.partner_admin"/>
            <field name="date" eval="datetime.now() - timedelta(days=1, seconds=-15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1, seconds=-15)"/>
            <field name="write_date" eval="datetime.now() - timedelta(days=1, seconds=-15)"/>
            <field name="body">Hello, how may I help you?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_9"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_9_message_3" model="mail.message">
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_9_guest"/>
            <field name="author_id"/>
            <field name="record_name">Visitor</field>
            <field name="date" eval="datetime.now() - timedelta(days=1, seconds=-25)"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1, seconds=-25)"/>
            <field name="write_date" eval="datetime.now() - timedelta(days=1, seconds=-25)"/>
            <field name="body">I would like to know more about the CRM application</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_9"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_9_message_4" model="mail.message">
            <field name="author_id" ref="base.partner_admin"/>
            <field name="date" eval="datetime.now() - timedelta(days=1, seconds=-33)"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1, seconds=-33)"/>
            <field name="write_date" eval="datetime.now() - timedelta(days=1, seconds=-33)"/>
            <field name="body">The CRM application helps you to track leads, close opportunities and get accurate forecasts. You can test it for free on our website.</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_9"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_9_message_5" model="mail.message">
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_9_guest"/>
            <field name="author_id"/>
            <field name="record_name">Visitor</field>
            <field name="date" eval="datetime.now() - timedelta(days=1, seconds=-42)"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1, seconds=-42)"/>
            <field name="write_date" eval="datetime.now() - timedelta(days=1, seconds=-42)"/>
            <field name="body">Great, thanks!</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_9"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_9_message_6" model="mail.message">
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_9_guest"/>
            <field name="author_id"/>
            <field name="record_name">Visitor</field>
            <field name="date" eval="datetime.now() - timedelta(days=1, seconds=-53)"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1, seconds=-53)"/>
            <field name="write_date" eval="datetime.now() - timedelta(days=1, seconds=-53)"/>
            <field name="body">Rating: :-)</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_9"/>
            <field name="model">discuss.channel</field>
        </record>
        <record id="livechat_channel_session_9_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.livechat_channel_session_9"/>
            <field name="author_guest_id" ref="im_livechat.livechat_channel_session_9_guest"/>
            <field name="author_id"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field eval="datetime.now() - timedelta(days=1, seconds=-60)" name="date"/>
            <field eval="datetime.now() - timedelta(days=1, seconds=-60)" name="create_date"/>
        </record>
        <record id="livechat_channel_session_9_rating" model="rating.rating">
            <field name="access_token">LIVECHAT_9</field>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="message_id" ref="im_livechat.livechat_channel_session_9_rating_message"/>
            <field name="rated_partner_id" ref="base.partner_admin"/>
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="create_date" eval="datetime.now() - timedelta(days=1, seconds=-53)"/>
            <field name="res_id" ref="im_livechat.livechat_channel_session_9"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="([ref('im_livechat.livechat_channel_session_9')], 5, 'LIVECHAT_9', None, 'Super Job')"/>
    </data>
</odoo>
