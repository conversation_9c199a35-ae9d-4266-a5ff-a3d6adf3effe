<?xml version="1.0"?>
<odoo>
    <data>
        <record id="support_bot_session_1_demo" model="discuss.channel">
            <field name="channel_type">livechat</field>
            <field name="livechat_lang_id" ref="base.lang_fr"/>
            <field name="livechat_channel_id" ref="support_bot_channel_demo"/>
            <field name="livechat_operator_id" ref="support_bot_operator_partner_demo"/>
            <field name="name">Visiteur #301, Support</field>
            <field name="anonymous_name">Visiteur #301</field>
            <field name="create_date" eval="datetime.now() - timedelta(minutes=5)"/>
        </record>
        <record id="support_bot_session_1_member_bot_demo" model="discuss.channel.member">
            <field name="partner_id" ref="support_bot_operator_partner_demo"/>
            <field name="channel_id" ref="support_bot_session_1_demo"/>
            <field name="last_interest_dt" eval="datetime.now() - timedelta(minutes=4)"/>
            <field name="livechat_member_type">bot</field>
            <field name="chatbot_script_id" ref="chatbot_script_support_bot_demo"/>
        </record>
        <record id="support_bot_session_1_guest_demo" model="mail.guest">
            <field name="name">Visiteur #301</field>
        </record>
        <record id="support_bot_session_1_member_guest_demo" model="discuss.channel.member">
            <field name="guest_id" ref="support_bot_session_1_guest_demo"/>
            <field name="channel_id" ref="support_bot_session_1_demo"/>
            <field name="livechat_member_type">visitor</field>
        </record>
        <record id="support_bot_session_1_message_1" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Bienvenue au support! Comment puis-je vous aider aujourd'hui?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=30)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=30)"/>
        </record>
        <record id="support_bot_session_1_chatbot_message_1" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_1_message_1"/>
            <field name="discuss_channel_id" ref="support_bot_session_1_demo"/>
            <field name="script_step_id" ref="chatbot_script_initial_step_demo"/>
        </record>
        <record id="support_bot_session_1_message_2" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="author_id"/>
            <field name="author_guest_id" ref="support_bot_session_1_guest_demo"/>
            <field name="body">Troubleshooting</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_1_chatbot_message_2" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_1_message_2"/>
            <field name="discuss_channel_id" ref="support_bot_session_1_demo"/>
            <field name="script_step_id" ref="chatbot_script_initial_step_demo"/>
            <field name="user_script_answer_id" ref="chatbot_script_answer_troubleshooting_demo"/>
            <field name="user_raw_script_answer_id" ref="chatbot_script_answer_troubleshooting_demo"/>
        </record>
        <record id="support_bot_session_1_message_3" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Quel navigateur utilisez-vous?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_1_chatbot_message_3" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_1_message_3"/>
            <field name="discuss_channel_id" ref="support_bot_session_1_demo"/>
            <field name="script_step_id" ref="chatbot_script_troubleshooting_step_browser_demo"/>
        </record>
        <record id="support_bot_session_1_message_4" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="author_id"/>
            <field name="author_guest_id" ref="support_bot_session_1_guest_demo"/>
            <field name="body">Chrome</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_1_chatbot_message_4" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_1_message_4"/>
            <field name="discuss_channel_id" ref="support_bot_session_1_demo"/>
            <field name="script_step_id" ref="chatbot_script_troubleshooting_step_browser_demo"/>
            <field name="user_script_answer_id" ref="chatbot_script_troubleshooting_answer_chrome_demo"/>
            <field name="user_raw_script_answer_id" ref="chatbot_script_troubleshooting_answer_chrome_demo"/>
        </record>
        <record id="support_bot_session_1_message_5" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Quelle application vous intéresse?</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_1_message_6" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="author_id"/>
            <field name="author_guest_id" ref="support_bot_session_1_guest_demo"/>
            <field name="body">Discuss et Livechat</field>
            <field name="message_type">comment</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_1_chatbot_message_5" model="chatbot.message">
            <field name="mail_message_id" ref="support_bot_session_1_message_6"/>
            <field name="discuss_channel_id" ref="support_bot_session_1_demo"/>
            <field name="script_step_id" ref="chatbot_script_initial_step_demo"/>
            <field name="user_script_answer_id" ref="chatbot_script_answer_discuss_livechat_demo"/>
            <field name="user_raw_script_answer_id" ref="chatbot_script_answer_discuss_livechat_demo"/>
        </record>
        <record id="support_bot_session_1_message_7" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="message_type">comment</field>
            <field name="author_id" ref="support_bot_operator_partner_demo"/>
            <field name="body">Laissez-moi vérifier si je peux vous mettre en relation avec l'expert approprié...</field>
            <field name="subtype_id" ref="mail.mt_comment"/>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <function model="discuss.channel" name="_add_members">
            <value eval="[ref('im_livechat.support_bot_session_1_demo')]"/>
            <value name="partners" model="res.partner" eval="obj().env['res.partner'].browse(ref('base.partner_admin'))"/>
            <value name="create_member_params" eval="{
                'livechat_member_type': 'agent',
                'agent_expertise_ids': [ref('im_livechat.chatbot_script_expertise_discuss_demo'), ref('im_livechat.chatbot_script_expertise_livechat_demo')]
            }"/>
            <value name="inviting_partner" model="res.partner" eval="obj().env['res.partner'].browse(ref('support_bot_operator_partner_demo'))"/>
        </function>
        <record id="support_bot_session_1_message_8" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="message_type">comment</field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="body">Bonjour 👋, comment puis-je vous aider?</field>
            <field name="date" eval="datetime.now() + timedelta(seconds=10)"/>
            <field name="create_date" eval="datetime.now() + timedelta(seconds=10)"/>
        </record>
        <record id="support_bot_session_1_rating_message" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="author_guest_id" ref="support_bot_session_1_guest_demo"/>
            <field name="author_id"/>
            <field name="subtype_id" ref="mail.mt_note"/>
            <field name="message_type">notification</field>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_1_message_call_notification" model="mail.message">
            <field name="model">discuss.channel</field>
            <field name="res_id" ref="im_livechat.support_bot_session_1_demo"/>
            <field name="author_guest_id" ref="im_livechat.support_bot_session_1_guest_demo"/>
            <field name="author_id"/>
            <field name="body">&lt;div data-oe-type=&#34;call&#34; class=&#34;o_mail_notification&#34;&gt;&lt;/div&gt;</field>
            <field name="message_type">notification</field>
            <field name="date" eval="datetime.now() - timedelta(seconds=15)"/>
            <field name="create_date" eval="datetime.now() - timedelta(seconds=15)"/>
        </record>
        <record id="support_bot_session_1_call_1" model="discuss.call.history">
            <field name="channel_id" ref="im_livechat.support_bot_session_1_demo"/>
            <field name="start_dt"
                eval="datetime.now() - timedelta(seconds=200)"/>
            <field name="end_dt"
                eval="datetime.now() - timedelta(seconds=10)"/>
            <field name="start_call_message_id" ref="support_bot_session_1_message_call_notification"/>
        </record>
        <record id="support_bot_session_1_rating" model="rating.rating">
            <field name="access_token">BOT_SESSION_1</field>
            <field name="res_id" ref="support_bot_session_1_demo"/>
            <field name="res_model_id" ref="mail.model_discuss_channel"/>
            <field name="message_id" ref="support_bot_session_1_rating_message"/>
            <field name="rated_partner_id" ref="base.partner_admin"/>
            <field eval="False" name="partner_id"/>
            <field eval="True" name="consumed"/>
        </record>
        <function model="discuss.channel" name="rating_apply"
            eval="[ref('im_livechat.support_bot_session_1_demo')], 5, 'BOT_SESSION_1', None, 'Bon travail'"/>
    </data>
</odoo>
