<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="action_fetch_qris_status" model="ir.actions.server">
            <field name="name">Check QRIS Payment Status</field>
            <field name="group_ids" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_view_types">list,kanban,form</field>
            <field name="state">code</field>
            <field name="code">
                if records:
                    action = records.action_l10n_id_update_payment_status()
            </field>
        </record>
</odoo>
