<templates id="template" xml:space="preserve">

    <t t-name="html_editor.linkPopover">
        <div class="o-we-linkpopover d-flex bg-light overflow-auto shadow" t-on-keydown="onKeydown">
            <div t-if="state.editing" class="container-fluid d-flex vertical-center p-2" t-ref="editing-wrapper">
                <div t-if="state.isImage" class="col p-2" style="max-width: 250px;">
                    <div class="input-group mb-1">
                        <input t-ref="url" class="o_we_href_input_link border-dark-subtle form-control form-control-sm" t-model="state.url" title="URL" placeholder="Type your URL" t-on-keydown="onKeydownEnter"/>
                        <button class="o_we_apply_link btn btn-sm btn-primary" t-att-class="{'mx-1': state.type ===  ''}" t-on-click="onClickApply">Apply</button>
                    </div>
                </div>
                <div t-else="" class="d-flex">
                    <div class="col p-2" style="max-width: 250px;">
                        <div class="input-group mb-1" t-att-class="{'d-none': state.isLabelHidden}">
                            <input t-ref="label"
                                class="o_we_label_link border-dark-subtle form-control form-control-sm"
                                t-model="state.label"
                                title="Label"
                                placeholder="Add a label for your link"
                                t-on-input="onChange"/>
                        </div>
                        <div class="input-group mb-1">
                            <input t-ref="url"
                                class="o_we_href_input_link border-dark-subtle form-control form-control-sm"
                                t-model="state.url"
                                title="URL"
                                placeholder="e.g. https://www.odoo.com"
                                t-on-keydown="onKeydownEnter"
                                t-on-input="onChange"/>
                            <span class="ms-1" t-if="props.canUpload and !state.url">
                                or <button class="btn btn-light btn-sm" t-on-click="uploadFile"><i class="fa fa-upload"/></button>
                            </span>

                        </div>
                        <div class="input-group">
                            <select name="link_type" class="form-select form-select-sm border-dark-subtle w-100 mb-1" t-model="state.type" t-on-change="onChange">
                                <t t-foreach="this.colorsData" t-as="colorData" t-key="colorData.type">
                                    <t t-if="props.allowCustomStyle or colorData.type !== 'custom'">
                                        <option t-att-value="colorData.type" t-att-selected="state.type === colorData.type" t-attf-class="o_btn_preview">
                                            <span t-esc="colorData.label"/>
                                        </option>
                                    </t>
                                </t>
                            </select>
                        </div>

                        <t t-if="state.type === 'custom'">
                            <div class="d-flex mb-1 custom-text-color">
                                <label>Text Color</label>
                                <button class="o_we_color_preview custom-text-picker" t-att-data-color="this.customTextColorState.selectedColor" t-ref="customTextColorButton"
                                        t-attf-style="background-color: {{this.customTextColorState.selectedColor}}" />
                                <label>Fill Color</label>
                                <button class="o_we_color_preview custom-fill-picker" t-att-data-color="this.customFillColorState.selectedColor" t-ref="customFillColorButton"
                                        t-attf-style="background-color: {{this.customFillColorState.selectedColor}}" />
                            </div>
                            <div class="d-flex mb-1 custom-border-color">
                                <label>Border</label>
                                <button class="o_we_color_preview custom-border-picker" t-att-data-color="this.customBorderColorState.selectedColor" t-ref="customBorderColorButton"
                                        t-attf-style="background-color: {{this.customBorderColorState.selectedColor}}" />
                            </div>
                            <div class="d-flex mb-1 custom-border">
                                <div class="input-group">
                                    <input t-ref="customBoderSize" type="text" pattern="[0-9]*"
                                           class="form-control form-control-sm custom-border-size" t-model="state.customBorderSize"
                                           placeholder="Border Size"  t-on-keydown="onKeydownEnter"/>
                                    <span class="input-group-text">px</span>
                                </div>
                                <select name="link_style_border" class="form-select form-select-sm custom-border-style" t-model="state.customBorderStyle" t-on-change="onChange">
                                    <t t-foreach="this.borderData" t-as="borderData" t-key="borderData.style">
                                        <option t-att-value="borderData.style" t-att-selected="state.customBorderStyle === borderData.style">
                                            <span t-esc="borderData.label"/>
                                        </option>
                                    </t>
                                </select>
                            </div>
                            <div class="input-group mb-1">
                                <label>Size</label>
                                <select name="link_style_size" class="form-select form-select-sm link-style" t-model="state.buttonSize" t-on-change="onChange">
                                    <t t-foreach="this.buttonSizesData" t-as="buttonSizesData" t-key="buttonSizesData.size">
                                        <option t-att-value="buttonSizesData.size" t-att-selected="state.buttonSize === buttonSizesData.size">
                                            <span t-esc="buttonSizesData.label"/>
                                        </option>
                                    </t>
                                </select>
                            </div>
                        </t>
                        <t t-if="props.allowTargetBlank">
                            <div class="d-flex mb-1 target-blank-option">
                                <input class="me-1" type="checkbox" t-att-checked="state.linkTarget === '_blank'" t-on-change="(ev)=>this.state.linkTarget = ev.target.checked ? '_blank' : ''"/>
                                <label>Open in new window</label>
                            </div>
                        </t>
                        <div class="mt-3">
                            <button class="o_we_apply_link btn btn-sm btn-primary" t-att-disabled="!state.url" t-on-click="onClickApply">Apply</button>
                            <button class="o_we_discard_link btn btn-sm btn-dark ms-1" t-on-click="props.onDiscard">Discard</button>
                        </div>
                    </div>
                </div>
            </div>
            <div t-else="" style="width: 260px;" data-prevent-closing-overlay="true">
                <div class="d-flex flex-column p-2">
                    <div class="d-flex">
                        <span class="o_we_preview_favicon" style="width: 16px; height: 32px">
                            <img t-if="state.previewIcon.type === 'imgSrc'" t-att-src="state.previewIcon.value" class="align-content-center"/>
                            <span t-elif="state.previewIcon.type === 'mimetype'" class="o_image" t-att-data-mimetype="state.previewIcon.value"/>
                            <i t-else="" t-attf-class="fa fa-fw {{state.previewIcon.value}}"></i>
                        </span>
                        <div class="ms-1 w-100">
                            <div class="d-flex">
                                <a href="#" target="_blank" t-on-click="onClickForceEditMode" t-attf-href="{{state.url}}" class="o_we_url_link fw-bold flex-grow-1 text-truncate" style="max-width: 160px;" t-attf-title="{{state.urlTitle}}">
                                    <t t-esc="state.urlTitle"/>
                                </a>
                                <div class="flex-grow-1 d-flex justify-content-end">
                                    <a href="#" class="mx-1 o_we_replace_title_btn text-dark"
                                        t-if="!state.isImage and state.urlTitle and state.label === '' and !state.showReplaceTitleBanner"
                                        t-on-click="onClickReplaceTitle"
                                        title="Replace URL with its title"
                                    >
                                        <i class="fa fa-magic"></i>
                                    </a>
                                    <a href="#" class="mx-1 o_we_copy_link text-dark" t-on-click="onClickCopy" title="Copy Link">
                                        <i class="fa fa-clipboard"></i>
                                    </a>
                                    <t t-if="props.canEdit">
                                        <a href="#" class="mx-1 o_we_edit_link text-dark" t-on-click="onClickEdit" title="Edit Link">
                                            <i class="fa fa-edit"></i>
                                        </a>
                                        <a href="#" class="ms-1 o_we_remove_link text-dark" t-on-click="onClickRemove" title="Remove Link">
                                            <i class="fa fa-chain-broken"></i>
                                        </a>
                                    </t>
                                </div>
                            </div>
                            <div t-if="state.urlTitle and state.url and state.urlTitle !== state.url" class="text-truncate" style="max-width: 200px; font-size: 12px;">
                                <span t-attf-class="o_we_full_url text-muted o_we_webkit_box" t-attf-title="{{state.url}}">
                                    <t t-if="state.url" t-esc="state.url"/>
                                    <t t-else="">No URL specified</t>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div t-if="state.imgSrc" class="o_extra_info_card" style="align-self: center; max-width: 235px">
                            <a href="#" target="_blank" t-attf-href="{{state.url}}" title="Open in a new tab">
                                <img t-att-src="state.imgSrc" class="img-fluid mb-1" style="max-width: 230; max-height: 100%;"/>
                            </a>
                    </div>
                    <div t-if="state.urlDescription" class="d-flex">
                        <i class="fa fa-align-right align-content-center"></i>
                        <span class="ms-1 o_we_description_link_preview" style="display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; font-size: 12px;color: gray; overflow: hidden;" t-attf-title="{{state.urlDescription}}">
                            <t t-esc="state.urlDescription"/>
                        </span>
                    </div>
                </div>
                <div t-if="!state.isImage and state.urlTitle and state.label === '' and state.showReplaceTitleBanner" class="d-flex align-items-baseline" style="background-color: var(--primary);">
                    <i class="fa fa-magic fa-fw m-1" style="color: var(--o-cc1-btn-primary-text);"></i>
                    <span class="me-2 flex-grow-1" style="color: var(--o-cc1-btn-primary-text);font-size: smaller;">Replace URL with its title?</span>
                    <button class=" btn btn-sm btn-primary o_we_replace_title_btn" style="margin: 1px;font-size: smaller;" t-on-click="onClickReplaceTitle">Yes</button>
                </div>
            </div>
        </div>
    </t>

</templates>
