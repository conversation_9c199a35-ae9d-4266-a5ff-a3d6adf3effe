<?xml version="1.0" ?>
<odoo>

    <!-- Mail template are declared in a NOUPDATE block
         so users can freely customize/delete them -->
    <data noupdate="1">
        <!--Email template -->
        <record id="email_template_edi_invoice" model="mail.template">
            <field name="name">Invoice: Sending</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="email_from">{{ (object.invoice_user_id.email_formatted or object.company_id.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to" eval="False"/>
            <field name="use_default_to" eval="True"/>
            <field name="subject">{{ object.company_id.name }} Invoice (Ref {{ object.name or 'n/a' }})</field>
            <field name="description">Sent to customers with their invoices in attachment</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Dear
        <t t-if="object.partner_id.parent_id">
            <t t-out="object.partner_id.name or ''"><PERSON></t> (<t t-out="object.partner_id.parent_id.name or ''">Azure Interior</t>),
        </t>
        <t t-else="">
            <t t-out="object.partner_id.name or ''">Brandon Freeman</t>,
        </t>
        <br /><br />
        Here is your
        <t t-if="object.name">
            invoice <span style="font-weight:bold;" t-out="object.name or ''">INV/2021/05/0005</span>
        </t>
        <t t-else="">
            invoice
        </t>
        <t t-if="object.invoice_origin">
            (with reference: <t t-out="object.invoice_origin or ''">SUB003</t>)
        </t>
        amounting in <span style="font-weight:bold;" t-out="format_amount(object.amount_total, object.currency_id) or ''">$ 143,750.00</span>
        from <t t-out="object.company_id.name or ''">YourCompany</t>.
        <t t-if="object.payment_state in ('paid', 'in_payment')">
            This invoice is already paid.
        </t>
        <t t-else="">
            Please remit payment at your earliest convenience.
            <t t-if="object.payment_reference">
                <br /><br />
                Please use the following communication for your payment: <strong t-out="object.payment_reference or ''">INV/2021/05/0005</strong>
                <t t-if="object.partner_bank_id">
                    on the account <strong t-out="object.partner_bank_id.acc_number"/>
                </t>
                .
            </t>
        </t>
        <t t-if="hasattr(object, 'timesheet_count') and object.timesheet_count">
            <br /><br />
            PS: you can review your timesheets <a t-att-href="'/my/timesheets?search_in=invoice&amp;search=%s' % object.name">from the portal.</a>
        </t>
        <br /><br />
        Do not hesitate to contact us if you have any questions.
        <t t-if="not is_html_empty(object.invoice_user_id.signature)">
            <br /><br />
            <div>--<br/><t t-out="object.invoice_user_id.signature or ''">Mitchell Admin</t></div>
        </t>
    </p>
</div>
            </field>
            <field name="report_template_ids" eval="[]"/>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="mail_template_data_payment_receipt" model="mail.template">
            <field name="name">Payment: Payment Receipt</field>
            <field name="model_id" ref="account.model_account_payment"/>
            <field name="subject">{{ object.company_id.name }} Payment Receipt (Ref {{ object.name or 'n/a' }})</field>
            <field name="partner_to" eval="False"/>
            <field name="use_default_to" eval="True"/>
            <field name="description">Sent manually to customer when clicking on 'Send receipt by email' in payment action</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Dear <t t-out="object.partner_id.name or ''">Azure Interior</t><br/><br/>
        Thank you for your payment.
        Here is your payment receipt <span style="font-weight:bold;" t-out="(object.name or '').replace('/','-') or ''">BNK1-2021-05-0002</span> amounting
        to <span style="font-weight:bold;" t-out="format_amount(object.amount, object.currency_id) or ''">$ 10.00</span> from <t t-out="object.company_id.name or ''">YourCompany</t>.
        <br/><br/>
        Do not hesitate to contact us if you have any questions.
        <br/><br/>
        Best regards,
        <t t-if="not is_html_empty(user.signature)">
            <br/><br/>
            <div>--<br/><t t-out="user.signature or ''">Mitchell Admin</t></div>
        </t>
    </p>
</div>
</field>
            <field name="report_template_ids" eval="[(4, ref('account.action_report_payment_receipt'))]"/>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>
        <!-- Credit note template -->
        <record id="email_template_edi_credit_note" model="mail.template">
            <field name="name">Credit Note: Sending</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="email_from">{{ (object.invoice_user_id.email_formatted or object.company_id.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to" eval="False"/>
            <field name="use_default_to" eval="True"/>
            <field name="subject">{{ object.company_id.name }} Credit Note (Ref {{ object.name or 'n/a' }})</field>
            <field name="description">Sent to customers with the credit note in attachment</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Dear
        <t t-if="object.partner_id.parent_id">
            <t t-out="object.partner_id.name or ''">Brandon Freeman</t> (<t t-out="object.partner_id.parent_id.name or ''">Azure Interior</t>),
        </t>
        <t t-else="">
            <t t-out="object.partner_id.name or ''">Brandon Freeman</t>,
        </t>
        <br /><br />
        Here is your
        <t t-if="object.name">
            credit note <span style="font-weight:bold;" t-out="object.name or ''">RINV/2021/05/0001</span>
        </t>
        <t t-else="">
            credit note
        </t>
        <t t-if="object.invoice_origin">
            (with reference: <t t-out="object.invoice_origin or ''">SUB003</t>)
        </t>
        amounting in <span style="font-weight:bold;" t-out="format_amount(object.amount_total, object.currency_id) or ''">$ 143,750.00</span>
        from <t t-out="object.company_id.name or ''">YourCompany</t>.
        <br /><br />
        Do not hesitate to contact us if you have any questions.
        <t t-if="not is_html_empty(object.invoice_user_id.signature)">
            <br /><br />
            <div>--<br/><t t-out="object.invoice_user_id.signature or ''">Mitchell Admin</t></div>
        </t>
    </p>
</div>
            </field>
            <field name="report_template_ids" eval="[]"/>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>
        <!-- Invoice mail gateway failed -->
        <template id="email_template_mail_gateway_failed">
<p>
    Hi,
    <br/><br/>
    Your email has been discarded. the e-mail address you have used only accepts new invoices:
    <ul>
        <li>For new invoices, please ensure a PDF or electronic invoice file is attached</li>
        <li>To add information to a previously sent invoice, reply to your "sent" email</li>
    </ul>
    For any other question, write to <t t-esc="company_email"/>.
    <br/>
    --
    <br/>
    <t t-esc="company_name"/>
</p>
        </template>
        <record id="mail_template_einvoice_notification" model="mail.template">
            <field name="name">New eInvoices Notification</field>
            <field name="subject">New Electronic Invoices Received</field>
            <field name="email_from">{{ object.company_id.email_formatted }}</field>
            <field name="email_to">{{ object.incoming_einvoice_notification_email }}</field>
            <field name="use_default_to" eval="False"/>
            <field name="model_id" ref="account.model_account_journal"/>
            <field name="auto_delete" eval="True"/>
            <field name="description">Notification email for newly received eInvoices</field>
            <field name="body_html" type="html">

<t t-set="invoices" t-value="ctx.get('einvoices', [])"/>
<t t-set="invoice_preview_ids" t-value="','.join([str(i) for i in invoices.ids]) if invoices else ''"/>
<t t-set="invoice_count" t-value="len(invoices)"/>
<t t-set="MAX_TABLE_LINES" t-value="10"/>

<table border="0" cellpadding="0" cellspacing="0"
       style="padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;">
    <tr>
        <td align="center">
            <table border="0" cellpadding="0" cellspacing="0" width="590"
                   style="padding: 16px; background-color: white; color: #454748; border-collapse:separate;">
                <!-- HEADER -->
                <tr>
                    <td align="center" style="min-width: 590px;">
                        <table width="590" cellpadding="0" cellspacing="0"
                               style="padding: 0px 8px; background-color: white; border-collapse:separate;">
                            <tr>
                                <td valign="middle">
                                    <span style="font-size: 10px;">Notification</span>
                                    <br/>
                                    <span style="font-size: 20px; font-weight: bold;">
                                        New Electronic Invoices Received
                                    </span>
                                    <div style="margin-bottom: 5px; margin-top: 18px;">
                                        <a t-if="invoice_count != 1" t-attf-href="/odoo/accounting/action-account.action_account_moves_email_preview?active_ids={{ invoice_preview_ids }}"
                                           target="_blank"
                                           t-attf-style="padding: 8px 12px; font-size: 12px; color: {{ object.company_id.email_primary_color or '#FFFFFF' }}; text-decoration: none !important; font-weight: 400; background-color: {{ object.company_id.email_secondary_color or '#875A7B' }}; border-radius:3px">
                                            Review Invoices
                                        </a>
                                        <a t-else="" t-attf-href="/odoo/accounting/{{ object.id }}/account.move/{{ invoices.id }}"
                                           target="_blank"
                                           t-attf-style="padding: 8px 12px; font-size: 12px; color: {{ object.company_id.email_primary_color or '#FFFFFF' }}; text-decoration: none !important; font-weight: 400; background-color: {{ object.company_id.email_secondary_color or '#875A7B' }}; border-radius:3px">
                                            Review Invoice
                                        </a>
                                    </div>
                                </td>
                                <td valign="middle" align="right">
                                    <t t-if="not object.company_id.uses_default_logo">
                                        <img t-att-src="'/logo.png?company=%s' % object.company_id.id"
                                             style="height: auto; width: 80px; margin-right: 10px;"
                                             t-att-alt="'%s' % object.company_id.name"/>
                                    </t>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" style="text-align:center;">
                                    <hr style="background-color:#CCCCCC; border:none; height:1px; margin:16px 0;" />
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!-- BODY -->
                <tr>
                    <td align="center">
                        <table width="590" cellpadding="0" cellspacing="0"
                               style="padding: 0px 8px; background-color: white; border-collapse:separate;">
                            <tr>
                                <td style="font-size: 14px;">
                                    <p>
                                        Dear <t t-out="object.company_id.name or 'Company'"/>,
                                        <br/><br/>
                                        You have received <strong><t t-out="invoice_count or 'new'"/></strong> invoice(s).
                                        These invoices are now available for your review in Odoo.
                                        <br/><br/>
                                        <table width="100%" cellpadding="6" cellspacing="0"
                                               style="border: 1px solid #DDDDDD; border-collapse: collapse; font-size: 13px; margin-bottom: 16px;">
                                            <thead style="background-color: #F9F9F9; text-align: left;">
                                                <tr>
                                                    <th style="border: 1px solid #DDDDDD;">Invoice</th>
                                                    <th style="border: 1px solid #DDDDDD;">Vendor</th>
                                                    <th style="border: 1px solid #DDDDDD;">Date</th>
                                                    <th style="border: 1px solid #DDDDDD;">Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-set="MAX_TABLE_LINES" t-value="10"/>
                                                <t t-foreach="invoices[:MAX_TABLE_LINES]" t-as="inv">
                                                    <tr>
                                                        <td style="border: 1px solid #DDDDDD;">
                                                            <t t-out="inv.name or 'INV'"/>
                                                        </td>
                                                        <td style="border: 1px solid #DDDDDD;">
                                                            <t t-out="inv.partner_id.name or 'Unknown'"/>
                                                        </td>
                                                        <td style="border: 1px solid #DDDDDD;">
                                                            <t t-out="inv.invoice_date or 'Unknown'"/>
                                                        </td>
                                                        <td style="border: 1px solid #DDDDDD;">
                                                            <t t-if="inv.amount_total is not None" t-out="inv.amount_total"
                                                               t-options="{'widget': 'monetary', 'display_currency': inv.currency_id or object.company_id.currency_id}"/>
                                                            <t t-else="">Unknown</t>
                                                        </td>
                                                    </tr>
                                                </t>
                                                <t t-if="invoice_count > MAX_TABLE_LINES">
                                                    <tr>
                                                        <td colspan="4" style="border: 1px solid #DDDDDD; text-align: center;">
                                                            <strong>...</strong>
                                                        </td>
                                                    </tr>
                                                </t>
                                                <t t-if="invoice_count == 0">
                                                    <tr>
                                                        <td style="border: 1px solid #DDDDDD;">
                                                            <t t-out="'INV'"/>
                                                        </td>
                                                        <td style="border: 1px solid #DDDDDD;">
                                                            Example Vendor
                                                        </td>
                                                        <td style="border: 1px solid #DDDDDD;">
                                                            2025-01-01
                                                        </td>
                                                        <td style="border: 1px solid #DDDDDD;">
                                                            <t t-out="1234.56"
                                                               t-options="{'widget': 'monetary', 'display_currency': object.company_id.currency_id}"/>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                        Don’t want these emails? <a t-attf-href="/my/journal/{{ object.id }}/unsubscribe">Unsubscribe here</a> or update your <a t-attf-href="/mail/view?model=account.journal&amp;res_id={{ object.id }}"><t t-out="object.name or 'Vendor Bills'"/></a> journal settings.
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
            </field>
        </record>
    </data>
</odoo>
