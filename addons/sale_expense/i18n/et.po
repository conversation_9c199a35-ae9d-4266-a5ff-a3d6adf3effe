# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_expense
#
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>s, 2022
# JanaAvalah, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:03+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: JanaAvalah, 2023\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"Language: et\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_count
msgid "# of Expenses"
msgstr "kulude arv"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__can_be_reinvoiced
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__can_be_reinvoiced
msgid "Can be reinvoiced"
msgstr "Saab teha arveks"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__sale_order_id
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__sale_order_id
msgid "Customer to Reinvoice"
msgstr "Kulu kliendile edasi esitada"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense
msgid "Expense"
msgstr "Kulu"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_product_product__expense_policy_tooltip
#: model:ir.model.fields,field_description:sale_expense.field_product_template__expense_policy_tooltip
msgid "Expense Policy Tooltip"
msgstr "Kulupoliitika tooltip"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Kuluaruanne"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_split
msgid "Expense Split"
msgstr "Kulu jagamine"

#. module: sale_expense
#: model:ir.actions.act_window,name:sale_expense.hr_expense_action_from_sale_order
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_ids
#: model_terms:ir.ui.view,arch_db:sale_expense.sale_order_form_view_inherit
msgid "Expenses"
msgstr "Kulud"

#. module: sale_expense
#: code:addons/sale_expense/models/product_template.py:0
msgid "Expenses of this category may not be added to a Sales Order."
msgstr "Selle kategooria kulusid ei pruugita müügitellimusele lisada."

#. module: sale_expense
#: code:addons/sale_expense/models/product_template.py:0
msgid "Expenses will be added to the Sales Order at their actual cost when posted."
msgstr "Kulud lisatakse müügitellimusele nende tegeliku maksumusega postitamisel."

#. module: sale_expense
#: code:addons/sale_expense/models/product_template.py:0
msgid "Expenses will be added to the Sales Order at their sales price (product price, pricelist, etc.) when posted."
msgstr "Kulud lisatakse müügitellimusele nende müügihinnaga (toote hind, hinnakiri jne) postitamisel."

#. module: sale_expense
#: model:ir.model.fields,help:sale_expense.field_hr_expense__sale_order_id
msgid "If the category has an expense policy, it will be reinvoiced on this sales order"
msgstr "Kui sellel kategoorial on kulupoliitika, esitatakse see sellel müügitellimusel uuesti"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.product_product_view_form_inherit_sale_expense
msgid "Invoicing"
msgstr "Raamatupidamine"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_move_line
msgid "Journal Item"
msgstr "Andmiku kanderida"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_product_template
msgid "Product"
msgstr "Toode"

#. module: sale_expense
#: code:addons/sale_expense/models/hr_expense_sheet.py:0
msgid "Reinvoiced Sales Orders"
msgstr "Arveldatud müügitellimused"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_sheet__sale_order_count
msgid "Sale Order Count"
msgstr "Müügitellimuste arv"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_sale_order
msgid "Sales Order"
msgstr "Müügitellimus"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.hr_expense_sheet_view_form
msgid "Sales Orders"
msgstr "Müügitellimused"
