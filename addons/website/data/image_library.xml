<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- Pre loaded images -->
<record id="website.business_conference" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">business_conference.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/business_conference.jpg</field>
</record>
<record id="website.library_image_team" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">team.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/team.jpg</field>
</record>
<record id="website.library_image_01" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">bridge.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/bridge.jpg</field>
</record>
<record id="website.library_image_02" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_02.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_02.jpg</field>
</record>
<record id="website.library_image_03" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_03.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_03.jpg</field>
</record>
<record id="website.library_image_04" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">manufacturing.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/manufacturing.jpg</field>
</record>
<record id="website.library_image_05" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_05.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_05.jpg</field>
</record>
<record id="website.library_image_06" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">gift.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/gift.jpg</field>
</record>
<record id="website.library_image_07" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_07.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_07.jpg</field>
</record>
<record id="website.library_image_08" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_08.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_08.jpg</field>
</record>
<record id="website.library_image_09" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">office.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/office.jpg</field>
</record>
<record id="website.library_image_10" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_10.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_10.jpg</field>
</record>
<record id="website.library_image_11" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_11.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_11.jpg</field>
</record>
<record id="website.library_image_12" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">sell.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/sell.jpg</field>
</record>
<record id="website.library_image_13" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_13.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_13.jpg</field>
</record>
<record id="website.library_image_14" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_14.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_14.jpg</field>
</record>
<record id="website.library_image_15" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">sweet.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/sweet.jpg</field>
</record>
<record id="website.library_image_16" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">library_image_16.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/library_image_16.jpg</field>
</record>
<record id="website.library_image_17" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">marketing.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/marketing.jpg</field>
</record>
<record id="website.library_image_18" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">firework.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/firework.jpg</field>
</record>

<!-- Website Builder Background Images -->
<record id="website.s_background_image_01" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_background_image_01.jpg</field>
    <field name="type">url</field>
    <field name="res_model">ir.ui.view</field>
    <field name="url">/website/static/src/img/backgrounds/peak.jpg</field>
</record>
<record id="website.s_background_image_02" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_background_image_02.jpg</field>
    <field name="type">url</field>
    <field name="res_model">ir.ui.view</field>
    <field name="url">/website/static/src/img/backgrounds/la.jpg</field>
</record>
<record id="website.s_background_image_03" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_background_image_03.jpg</field>
    <field name="type">url</field>
    <field name="res_model">ir.ui.view</field>
    <field name="url">/website/static/src/img/backgrounds/panama-sky.jpg</field>
</record>
<record id="website.s_background_image_04" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_background_image_04.jpg</field>
    <field name="type">url</field>
    <field name="res_model">ir.ui.view</field>
    <field name="url">/website/static/src/img/backgrounds/cubes.jpg</field>
</record>
<record id="website.s_background_image_05" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_background_image_05.jpg</field>
    <field name="type">url</field>
    <field name="res_model">ir.ui.view</field>
    <field name="url">/website/static/src/img/backgrounds/building-profile.jpg</field>
</record>
<record id="website.s_background_image_06" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_background_image_06.jpg</field>
    <field name="type">url</field>
    <field name="res_model">ir.ui.view</field>
    <field name="url">/website/static/src/img/backgrounds/type.jpg</field>
</record>
<record id="website.s_background_image_07" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_background_image_07.jpg</field>
    <field name="type">url</field>
    <field name="res_model">ir.ui.view</field>
    <field name="url">/website/static/src/img/backgrounds/people.jpg</field>
</record>
<record id="website.s_background_image_08" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_background_image_08.jpg</field>
    <field name="type">url</field>
    <field name="res_model">ir.ui.view</field>
    <field name="url">/website/static/src/img/backgrounds/city.jpg</field>
</record>
<record id="website.s_background_image_09" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_background_image_09.jpg</field>
    <field name="type">url</field>
    <field name="res_model">ir.ui.view</field>
    <field name="url">/website/static/src/img/backgrounds/sails.jpg</field>
</record>

<!-- Header default images (to be replaced by themes) -->
<record id="website.header_image_1_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">header_image_1_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/header_image_1_default_image.jpg</field>
</record>

<!-- Snippets' Default Images (to be replaced by themes) -->
<record id="website.s_cover_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_cover_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_cover.jpg</field>
</record>
<record id="website.s_text_cover_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_text_cover_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_text_cover.jpg</field>
</record>
<record id="website.s_masonry_block_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_masonry_block_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_masonry_block_1.jpg</field>
</record>
<record id="website.s_masonry_block_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_masonry_block_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_picture.jpg</field>
</record>
<record id="website.s_image_punchy_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_image_punchy_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_image_punchy.jpg</field>
</record>
<record id="website.s_sidegrid_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_sidegrid_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_sidegrid_default_image_1.jpg</field>
</record>
<record id="website.s_sidegrid_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_sidegrid_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_sidegrid_default_image_2.jpg</field>
</record>
<record id="website.s_sidegrid_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_sidegrid_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_sidegrid_default_image_3.jpg</field>
</record>
<record id="website.s_sidegrid_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_sidegrid_default_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_sidegrid_default_image_4.jpg</field>
</record>
<record id="website.s_media_list_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_media_list_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_media_list_1.jpg</field>
</record>
<record id="website.s_media_list_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_media_list_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_media_list_2.jpg</field>
</record>
<record id="website.s_media_list_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_media_list_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_media_list_3.jpg</field>
</record>
<record id="website.s_product_list_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_product_list_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_product_list_1.jpg</field>
</record>
<record id="website.s_product_list_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_product_list_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_product_list_2.jpg</field>
</record>
<record id="website.s_product_list_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_product_list_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_product_list_3.jpg</field>
</record>
<record id="website.s_product_list_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_product_list_default_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_product_list_4.jpg</field>
</record>
<record id="website.s_product_list_default_image_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_product_list_default_image_5.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_product_list_5.jpg</field>
</record>
<record id="website.s_product_list_default_image_6" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_product_list_default_image_6.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_product_list_6.jpg</field>
</record>
<record id="website.s_quotes_carousel_demo_image_0" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quotes_carousel_image_0.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_quotes_carousel_0.jpg</field>
</record>
<record id="website.s_quotes_carousel_demo_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quotes_carousel_image_01.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_quotes_carousel_1.jpg</field>
</record>
<record id="website.s_quotes_carousel_demo_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quotes_carousel_image_02.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_quotes_carousel_2.jpg</field>
</record>
<record id="website.s_quotes_carousel_demo_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quotes_carousel_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_3.jpg</field>
</record>
<record id="website.s_quotes_carousel_demo_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quotes_carousel_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_2.jpg</field>
</record>
<record id="website.s_quotes_carousel_demo_image_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quotes_carousel_image_5.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_4.jpg</field>
</record>
<record id="website.s_showcase_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_showcase_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_showcase.jpg</field>
</record>
<record id="website.s_mockup_image_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mockup_image_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mockup_image.jpg</field>
</record>
<record id="website.s_image_text_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_image_text_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_image_text.jpg</field>
</record>
<record id="website.s_text_image_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_text_image_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_text_image.jpg</field>
</record>
<record id="website.s_image_hexagonal_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_image_hexagonal_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_image_hexagonal.jpg</field>
</record>
<record id="website.s_image_hexagonal_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_image_hexagonal_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_image_hexagonal_1.jpg</field>
</record>
<record id="website.s_three_columns_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_three_columns_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.library_image_11</field>
</record>
<record id="website.s_three_columns_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_three_columns_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.library_image_13</field>
</record>
<record id="website.s_three_columns_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_three_columns_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.library_image_07</field>
</record>
<record id="website.s_card_offset_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_card_offset_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_card_offset.jpg</field>
</record>
<record id="website.s_card_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_card_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_card_1.jpg</field>
</record>
<record id="website.s_carousel_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_carousel_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_carousel_1.jpg</field>
</record>
<record id="website.s_carousel_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_carousel_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_carousel_2.jpg</field>
</record>
<record id="website.s_carousel_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_carousel_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_carousel_3.jpg</field>
</record>
<record id="website.s_framed_intro_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_framed_intro_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_framed_intro.jpg</field>
</record>
<record id="website.s_floating_blocks_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_floating_blocks_1</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/shop_generic_1_lg.jpg</field>
</record>
<record id="website.s_floating_blocks_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_floating_blocks_2</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/shop_generic_2_lg.jpg</field>
</record>
<record id="website.s_floating_blocks_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_floating_blocks_3</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/library/shop_generic_3_lg.jpg</field>
</record>
<record id="website.s_carousel_intro_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_carousel_intro_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_text_cover.jpg</field>
</record>
<record id="website.s_carousel_intro_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_carousel_intro_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_image_title_default_image.jpg</field>
</record>
<record id="website.s_carousel_intro_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_carousel_intro_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_banner_3.jpg</field>
</record>
<record id="website.s_carousel_cards_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_carousel_cards_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_carousel_1.jpg</field>
</record>
<record id="website.s_carousel_cards_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_carousel_cards_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_carousel_2.jpg</field>
</record>
<record id="website.s_carousel_cards_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_carousel_cards_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_carousel_3.jpg</field>
</record>
<record id="website.s_picture_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_picture_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_picture.jpg</field>
</record>
<record id="website.s_striped_top_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_striped_top_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_striped_top.jpg</field>
</record>
<record id="website.s_banner_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_banner_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_banner.jpg</field>
</record>
<record id="website.s_banner_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_banner_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_banner_2.jpg</field>
</record>
<record id="website.s_banner_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_banner_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_banner_3.jpg</field>
</record>
<record id="website.s_closer_look_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_closer_look_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_closer_look.jpg</field>
</record>
<record id="website.s_faq_horizontal_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_faq_horizontal_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_faq_horizontal_1.jpg</field>
</record>
<record id="website.s_images_constellation_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_images_constellation_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_images_constellation_default_image.jpg</field>
</record>
<record id="website.s_images_constellation_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_images_constellation_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_images_constellation_default_image_1.jpg</field>
</record>
<record id="website.s_images_constellation_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_images_constellation_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_images_constellation_default_image_2.jpg</field>
</record>
<record id="website.s_parallax_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_parallax_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_parallax.jpg</field>
</record>
<record id="website.s_reference_demo_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_reference_demo_image_1.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_references_1.png</field>
</record>
<record id="website.s_reference_demo_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_reference_demo_image_2.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_references_2.png</field>
</record>
<record id="website.s_reference_demo_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_reference_demo_image_3.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_references_3.png</field>
</record>
<record id="website.s_reference_demo_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_reference_demo_image_4.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_references_4.png</field>
</record>
<record id="website.s_reference_demo_image_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_reference_demo_image_5.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_references_5.png</field>
</record>
<record id="website.s_reference_default_image_6" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_reference_default_image_6.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_references_6.png</field>
</record>
<record id="website.s_cta_mockups_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_cta_mockups_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_cta_mockups.jpg</field>
</record>
<record id="website.s_cta_mockups_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_cta_mockups_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_cta_mockups_1.jpg</field>
</record>
<record id="website.s_company_team_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_company_team_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_1.jpg</field>
</record>
<record id="website.s_company_team_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_company_team_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_2.jpg</field>
</record>
<record id="website.s_company_team_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_company_team_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_3.jpg</field>
</record>
<record id="website.s_company_team_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_company_team_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_4.jpg</field>
</record>
<record id="website.s_company_team_image_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_company_team_image_5.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_5.jpg</field>
</record>
<record id="website.s_company_team_image_6" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_company_team_image_6.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_6.jpg</field>
</record>
<record id="website.s_mega_menu_menu_image_menu_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_menu_image_menu_default_image.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_references_5.png</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_1.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_2.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_3.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_4.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_5.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_5.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_6" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_6.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_6.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_7" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_7.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_7.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_8" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_8.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_8.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_9" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_9.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_9.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_10" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_10.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_10.jpg</field>
</record>
<record id="website.s_mega_menu_thumbnails_default_image_11" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_thumbnails_default_image_11.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_thumbnails_default_image_11.jpg</field>
</record>
<record id="website.s_mega_menu_images_subtitles_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_images_subtitles_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_images_subtitles_default_image_1.jpg</field>
</record>
<record id="website.s_mega_menu_images_subtitles_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_images_subtitles_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_images_subtitles_default_image_2.jpg</field>
</record>
<record id="website.s_mega_menu_images_subtitles_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_images_subtitles_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_images_subtitles_default_image_3.jpg</field>
</record>
<record id="website.s_mega_menu_images_subtitles_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_images_subtitles_default_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_images_subtitles_default_image_4.jpg</field>
</record>
<record id="website.s_mega_menu_images_subtitles_default_image_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_images_subtitles_default_image_5.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_images_subtitles_default_image_5.jpg</field>
</record>
<record id="website.s_mega_menu_images_subtitles_default_image_6" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_images_subtitles_default_image_6.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_images_subtitles_default_image_6.jpg</field>
</record>
<record id="website.s_mega_menu_images_subtitles_default_image_7" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_images_subtitles_default_image_7.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_images_subtitles_default_image_7.jpg</field>
</record>
<record id="website.s_mega_menu_menus_logos_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_menus_logos_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_menus_logos_default_image.jpg</field>
</record>
<record id="website.s_mega_menu_menus_logos_default_logo_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_menus_logos_default_logo_1.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_menus_logos_default_logo_1.png</field>
</record>
<record id="website.s_mega_menu_menus_logos_default_logo_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_menus_logos_default_logo_2.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_menus_logos_default_logo_2.png</field>
</record>
<record id="website.s_mega_menu_menus_logos_default_logo_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_menus_logos_default_logo_3.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_menus_logos_default_logo_3.png</field>
</record>
<record id="website.s_mega_menu_menus_logos_default_logo_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_menus_logos_default_logo_4.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_menus_logos_default_logo_4.png</field>
</record>
<record id="website.s_mega_menu_menus_logos_default_logo_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_menus_logos_default_logo_5.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_menus_logos_default_logo_5.png</field>
</record>
<record id="website.s_mega_menu_menus_logos_default_logo_6" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_menus_logos_default_logo_6.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_menus_logos_default_logo_6.png</field>
</record>
<record id="website.s_mega_menu_cards_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_cards_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_cards_default_image_1.jpg</field>
</record>
<record id="website.s_mega_menu_cards_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_cards_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_cards_default_image_2.jpg</field>
</record>
<record id="website.s_mega_menu_cards_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_cards_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_cards_default_image_3.jpg</field>
</record>
<record id="website.s_mega_menu_cards_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_cards_default_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_cards_default_image_4.jpg</field>
</record>
<record id="website.s_mega_menu_cards_default_image_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_cards_default_image_5.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_cards_default_image_5.jpg</field>
</record>
<record id="website.s_mega_menu_cards_default_image_6" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_cards_default_image_6.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_cards_default_image_6.jpg</field>
</record>
<record id="website.s_mega_menu_cards_default_image_7" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_cards_default_image_7.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_cards_default_image_7.jpg</field>
</record>
<record id="website.s_mega_menu_cards_default_image_8" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_mega_menu_cards_default_image_8.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_mega_menu_cards_default_image_8.jpg</field>
</record>
<record id="website.s_product_catalog_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_product_catalog_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_product_catalog.jpg</field>
</record>
<record id="website.s_pricelist_cafe_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_pricelist_cafe_default_image.png</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_pricelist_cafe_default_image.png</field>
</record>
<record id="website.s_blockquote_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_blockquote_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_team_member_2.jpg</field>
</record>
<record id="website.s_blockquote_cover_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_blockquote_cover_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_blockquote_cover.jpg</field>
</record>
<record id="website.s_quadrant_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quadrant_default_image_1</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_quadrant_default_image_1.jpg</field>
</record>
<record id="website.s_quadrant_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quadrant_default_image_2</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_quadrant_default_image_2.jpg</field>
</record>
<record id="website.s_quadrant_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quadrant_default_image_3</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_quadrant_default_image_3.jpg</field>
</record>
<record id="website.s_quadrant_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_quadrant_default_image_4</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_quadrant_default_image_4.jpg</field>
</record>
<record id="website.s_popup_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_popup_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_popup.jpg</field>
</record>
<record id="website.s_cta_box_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_cta_box_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_cta_box_default_image.jpg</field>
</record>
<record id="website.s_accordion_image_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_accordion_image_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_accordion_image_default_image.jpg</field>
</record>
<record id="website.s_pricelist_boxed_default_background" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_pricelist_boxed_default_background.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_pricelist_boxed_default_background.jpg</field>
</record>
<record id="website.s_adventure_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_adventure_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_adventure_default_image.jpg</field>
</record>
<record id="website.s_image_title_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_image_title_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_image_title_default_image.jpg</field>
</record>
<record id="website.s_key_images_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_key_images_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_key_images_default_image_1.jpg</field>
</record>
<record id="website.s_key_images_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_key_images_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_key_images_default_image_2.jpg</field>
</record>
<record id="website.s_key_images_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_key_images_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_key_images_default_image_3.jpg</field>
</record>
<record id="website.s_key_images_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_key_images_default_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_key_images_default_image_4.jpg</field>
</record>
<record id="website.s_kickoff_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_kickoff_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_kickoff_default_image.jpg</field>
</record>
<record id="website.s_intro_pill_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_connections_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_intro_pill_default_image.jpg</field>
</record>
<record id="website.s_intro_pill_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_connections_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_intro_pill_default_image_2.jpg</field>
</record>
<record id="website.s_image_frame_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_image_frame_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_image_frame.jpg</field>
</record>
<record id="website.s_wavy_grid_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_wavy_grid_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_wavy_grid_default_image_1.jpg</field>
</record>
<record id="website.s_wavy_grid_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_wavy_grid_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_wavy_grid_default_image_2.jpg</field>
</record>
<record id="website.s_wavy_grid_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_wavy_grid_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_wavy_grid_default_image_3.jpg</field>
</record>
<record id="website.s_wavy_grid_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_wavy_grid_default_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_wavy_grid_default_image_4.jpg</field>
</record>
<record id="website.s_images_mosaic_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_images_mosaic_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_images_mosaic_default_image_1.jpg</field>
</record>
<record id="website.s_images_mosaic_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_images_mosaic_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_images_mosaic_default_image_2.jpg</field>
</record>
<record id="website.s_images_mosaic_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_images_mosaic_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_images_mosaic_default_image_3.jpg</field>
</record>
<record id="website.s_images_mosaic_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_images_mosaic_default_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_images_mosaic_default_image_4.jpg</field>
</record>
<record id="website.s_images_mosaic_default_image_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_images_mosaic_default_image_5.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_images_mosaic_default_image_5.jpg</field>
</record>
<record id="website.s_shape_image_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_shape_image_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_shape_image_default_image.jpg</field>
</record>
<record id="website.s_empowerment_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_empowerment_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_empowerment_default_image.jpg</field>
</record>
<record id="website.s_website_form_overlay_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_website_form_overlay_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_cover.jpg</field>
</record>
<record id="website.app_store_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">app_store_image.svg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/app_store.svg</field>
</record>
<record id="website.google_play_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">google_play_image.svg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/google_play.svg</field>
</record>
<record id="website.s_opening_hours_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_opening_hours_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_opening_hours.jpg</field>
</record>
<record id="website.s_newsletter_grid_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_newsletter_grid_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_images_mosaic_default_image_5</field>
</record>
<record id="website.s_newsletter_grid_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_newsletter_grid_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_banner_default_image_2</field>
</record>
<record id="website.s_website_form_info_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_website_form_info_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_website_form_info.jpg</field>
</record>
<record id="website.s_website_form_cover_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_website_form_cover_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_cover_default_image</field>
</record>
<record id="website.s_timeline_images_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_timeline_images_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_carousel_default_image_2</field>
</record>
<record id="website.s_timeline_images_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_timeline_images_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_carousel_default_image_3</field>
</record>
<record id="website.s_split_intro_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_split_intro_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_split_intro_default_image.jpg</field>
</record>
<record id="website.s_form_aside_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_form_aside_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_empowerment_default_image</field>
</record>
<record id="website.s_tabs_images_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_tabs_images_default_image_1.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_carousel_default_image_1</field>
</record>
<record id="website.s_tabs_images_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_tabs_images_default_image_2.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_carousel_default_image_2</field>
</record>
<record id="website.s_tabs_images_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_tabs_images_default_image_3.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_carousel_default_image_3</field>
</record>
<record id="website.s_tabs_images_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_tabs_images_default_image_4.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_quotes_carousel_demo_image_1</field>
</record>
<record id="website.s_tabs_images_default_image_5" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_tabs_images_default_image_5.jpg</field>
    <field name="type">url</field>
    <field name="url">/web/image/website.s_quotes_carousel_demo_image_2</field>
</record>
<record id="website.s_banner_connected_default_image" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_banner_connected_default_image.jpg</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_banner_connected_default_image.jpg</field>
</record>
<record id="website.s_bento_grid_default_image_1" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_bento_grid_default_image_1.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_bento_grid_default_image_1.jpg</field>
</record>
<record id="website.s_bento_grid_default_image_2" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_bento_grid_default_image_2.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_bento_grid_default_image_2.jpg</field>
</record>
<record id="website.s_bento_grid_default_image_3" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_bento_grid_default_image_3.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_bento_grid_default_image_3.jpg</field>
</record>
<record id="website.s_bento_grid_default_image_4" model="ir.attachment">
    <field name="public" eval="True"/>
    <field name="name">s_bento_grid_default_image_4.jpg</field>
    <field name="res_model">ir.ui.view</field>
    <field name="type">url</field>
    <field name="url">/website/static/src/img/snippets_demo/s_bento_grid_default_image_4.jpg</field>
</record>

</odoo>
