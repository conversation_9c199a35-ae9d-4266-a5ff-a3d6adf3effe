<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">


<t t-name="website.headerTemplateOption">
    <BuilderRow label.translate="Template">
        <BuilderSelect action="'reloadComposite'">
            <BuilderSelectItem
                title.translate="Default"
                id="'header_default_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_default'],
                            vars: {'header-links-style': 'default', 'header-template': 'default'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_default.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Hamburger menu"
                id="'header_hamburger_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_hamburger', 'website.no_autohide_menu'],
                            vars: {'header-links-style': 'default', 'header-template': 'hamburger'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_hamburger.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Rounded box menu"
                id="'header_boxed_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.header_navbar_pills_style','website.template_header_boxed'],
                            vars: {'header-links-style': 'pills', 'header-template': 'boxed'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_boxed.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Stretch menu"
                id="'header_stretch_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_stretch'],
                            vars: {'header-links-style': 'default', 'header-template': 'stretch'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_stretch.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Vertical"
                id="'header_vertical_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_vertical'],
                            vars: {'header-links-style': 'default', 'header-template': 'vertical'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_vertical.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Menu with Search bar"
                id="'header_search_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_search'],
                            vars: {'header-links-style': 'default', 'header-template': 'search'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_search.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Menu - Sales 1"
                id="'header_sales_one_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_sales_one'],
                            vars: {'header-links-style': 'default', 'header-template': 'sales_one'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_sales_one.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Menu - Sales 2"
                id="'header_sales_two_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_sales_two'],
                            vars: {'header-links-style': 'default', 'header-template': 'sales_two'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_sales_two.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Menu - Sales 3"
                id="'header_sales_three_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_sales_three'],
                            vars: {'header-links-style': 'default', 'header-template': 'sales_three'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_sales_three.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Menu - Sales 4"
                id="'header_sales_four_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_sales_four'],
                            vars: {'header-links-style': 'default', 'header-template': 'sales_four'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_sales_four.svg'"/>
            </BuilderSelectItem>
            <BuilderSelectItem
                title.translate="Sidebar"
                id="'header_sidebar_opt'"
                actionParam="[
                    {
                        action: 'websiteConfig',
                        actionParam: {
                            views: ['website.template_header_sidebar', 'website.no_autohide_menu'],
                            vars: {'header-links-style': 'default', 'header-template': 'sidebar'},
                            checkVars: false,
                        }
                    }
                ]"
            >
                <Img attrs="{style:'width: 100%;'}" src="'/website/static/src/img/snippets_options/header_template_sidebar.svg'"/>
            </BuilderSelectItem>

        </BuilderSelect>
    </BuilderRow>
</t>

<t t-name="website.headerContentWidthOption">
    <BuilderRow t-if="!isActiveItem('header_sidebar_opt')" label.translate="Content Width">
        <BuilderButtonGroup action="'websiteConfig'">
            <BuilderButton title.translate="Small"
                iconImg="'/website/static/src/img/snippets_options/content_width_small.svg'"
                actionParam="{ views: ['website.header_width_small'] }"/>
            <BuilderButton title.translate="Regular"
                iconImg="'/website/static/src/img/snippets_options/content_width_normal.svg'"
                actionParam="{ views: [] }"/>
            <BuilderButton title.translate="Full"
                iconImg="'/website/static/src/img/snippets_options/content_width_full.svg'"
                actionParam="{ views: ['website.header_width_full'] }"/>
        </BuilderButtonGroup>
    </BuilderRow>
</t>

<t t-name="website.headerSidebarWidthOption">
    <BuilderRow t-if="isActiveItem('header_sidebar_opt')" label.translate="Width" level="1">
    <BuilderNumberInput
            action="'customizeWebsiteVariable'"
            actionParam="'sidebar-width'"
            unit="'px'"
            saveUnit="'rem'"/>
    </BuilderRow>
</t>

<t t-name="website.headerBackgroundOption">
    <BuilderRow label.translate="Background">
        <BuilderColorPicker
            enabledTabs="['theme', 'custom', 'gradient']"
            preview="false"
            defaultColor="''"
            action="'customizeWebsiteColor'"
            actionParam="{
                mainParam: 'menu-custom',
                gradientColor: 'menu-gradient',
                combinationColor: 'menu',
                nullValue: 'NULL',
            }"/>
        <BuilderColorPicker t-if="isActiveItem('header_sales_one_opt')"
            enabledTabs="['theme', 'custom', 'gradient']"
            preview="false"
            defaultColor="''"
            action="'customizeWebsiteColor'"
            actionParam="{
                mainParam: 'header-sales_one-custom',
                gradientColor: 'menu-secondary-gradient',
                combinationColor: 'header-sales_one',
                nullValue: 'NULL',
            }"/>
        <BuilderColorPicker t-if="isActiveItem('header_sales_two_opt')"
            enabledTabs="['theme', 'custom', 'gradient']"
            preview="false"
            defaultColor="''"
            action="'customizeWebsiteColor'"
            actionParam="{
                mainParam: 'header-sales_two-custom',
                gradientColor: 'menu-secondary-gradient',
                combinationColor: 'header-sales_two',
                nullValue: 'NULL',
            }"/>
        <BuilderColorPicker t-if="isActiveItem('header_sales_three_opt')"
            enabledTabs="['theme', 'custom', 'gradient']"
            preview="false"
            defaultColor="''"
            action="'customizeWebsiteColor'"
            actionParam="{
                mainParam: 'header-sales_three-custom',
                gradientColor: 'menu-secondary-gradient',
                combinationColor: 'header-sales_three',
                nullValue: 'NULL',
            }"/>
        <BuilderColorPicker t-if="isActiveItem('header_sales_four_opt')"
            enabledTabs="['theme', 'custom', 'gradient']"
            preview="false"
            defaultColor="''"
            action="'customizeWebsiteColor'"
            actionParam="{
                mainParam: 'header-sales_four-custom',
                gradientColor: 'menu-secondary-gradient',
                combinationColor: 'header-sales_four',
                nullValue: 'NULL',
            }"/>
    </BuilderRow>
</t>

<t t-name="website.headerScrollEffectOption">
    <BuilderRow label.translate="Scroll Effect" t-if="!this.isActiveItem('header_sidebar_opt')">
        <BuilderSelect action="'websiteConfig'" className="'o_scroll_effects_selector'">
            <BuilderSelectItem
                id="'header_visibility_standard_opt'"
                label.translate="Standard"
                actionParam="{ views: ['website.header_visibility_standard'], vars: {'header-scroll-effect': ''} }"
                classAction="'o_header_standard'"
                className="'o_we_img_animate'"
            >
                <Img src="'/website/static/src/img/snippets_options/header_effect_standard.png'" attrs="{style:`--animate-src: '/website/static/src/img/snippets_options/header_effect_standard.gif';`}"/>
                <span>Standard</span>
            </BuilderSelectItem>
            <BuilderSelectItem
                id="'header_effect_scroll_opt'"
                label.translate="Scroll"
                actionParam="{ views: [], vars: {'header-scroll-effect': 'scroll'} }"
                classAction="''"
                className="'o_we_img_animate'"
            >
                <Img src="'/website/static/src/img/snippets_options/header_effect_scroll.png'" attrs="{style:`--animate-src: '/website/static/src/img/snippets_options/header_effect_scroll.gif';`}"/>
                <span>Scroll</span>
            </BuilderSelectItem>
            <BuilderSelectItem
                id="'header_effect_fixed_opt'"
                label.translate="Fixed"
                actionParam="{ views: ['website.header_visibility_fixed'], vars: {'header-scroll-effect': 'fixed'} }"
                classAction="'o_header_fixed'"
                className="'o_we_img_animate'"
            >
                <Img src="'/website/static/src/img/snippets_options/header_effect_fixed.png'" attrs="{style:`--animate-src: '/website/static/src/img/snippets_options/header_effect_fixed.gif';`}"/>
                <span>Fixed</span>
            </BuilderSelectItem>
            <BuilderSelectItem
                id="'header_effect_disappears_opt'"
                label.translate="Disappears"
                actionParam="{ views: ['website.header_visibility_disappears'], vars: {'header-scroll-effect': 'disappears'} }"
                classAction="'o_header_disappears'"
                className="'o_we_img_animate'"
            >
                <Img src="'/website/static/src/img/snippets_options/header_effect_disappears.png'" attrs="{style:`--animate-src: '/website/static/src/img/snippets_options/header_effect_disappears.gif';`}"/>
                <span>Disappears</span>
            </BuilderSelectItem>
            <BuilderSelectItem
                id="'header_effect_fade_out_opt'"
                label.translate="Fade Out"
                actionParam="{ views: ['website.header_visibility_fade_out'], vars: {'header-scroll-effect': 'fade-out'} }"
                classAction="'o_header_fade_out'"
                className="'o_we_img_animate'"
            >
                <Img src="'/website/static/src/img/snippets_options/header_effect_fade_out.png'" attrs="{style:`--animate-src: '/website/static/src/img/snippets_options/header_effect_fade_out.gif';`}"/>
                <span>Fade Out</span>
            </BuilderSelectItem>
        </BuilderSelect>
    </BuilderRow>
</t>

<t t-name="website.headerElementOption">
    <BuilderRow label.translate="Elements" action="'websiteConfig'">
        <div class="flex-grow-1">
            <div class="d-flex mb-1">
                <BuilderButton
                    title.translate="Show/hide text element"
                    className="'flex-grow-1 me-1'"
                    actionParam="{
                        views: ['website.header_text_element'],
                        resetViewArch: true,
                    }"
                >
                    <Img src="'/website/static/src/img/snippets_options/header_extra_element_text.svg'"/>
                </BuilderButton>
                <t t-set="language_ids" t-value="env.services.website.currentWebsite.language_ids || []"/>
                <BuilderButton
                    t-if="language_ids.length > 1"
                    title.translate="Show/hide language selector"
                    className="'flex-grow-1 me-1 fa fa-flag'"
                    actionParam="{
                        views: ['website.header_language_selector'],
                        resetViewArch: true,
                    }"
                >
                </BuilderButton>
                <BuilderButton
                    title.translate="Show/hide search bar"
                    className="'fa fa-search flex-grow-1 me-1'"
                    actionParam="{
                        views: ['website.header_search_box'],
                        resetViewArch: true,
                    }"/>
                <BuilderButton
                    title.translate="Show/hide sign in button"
                    className="'fa fa-sign-in flex-grow-1'"
                    actionParam="{
                        views: ['portal.user_sign_in'],
                        resetViewArch: true,
                    }"/>
            </div>
            <div class="d-flex">
                <BuilderButton
                    title.translate="Show/hide social links"
                    className="'flex-grow-1 me-1'"
                    actionParam="{
                        views: ['website.header_social_links'],
                        resetViewArch: true,
                    }"
                >
                    <Img src="'/website/static/src/img/snippets_options/header_extra_element_social.svg'"/>
                </BuilderButton>
                <BuilderButton
                    title.translate="Show/hide button"
                    className="'flex-grow-1 me-1'"
                    actionParam="{
                        views: ['website.header_call_to_action'],
                        resetViewArch: true,
                    }"
                >
                    <Img src="'/website/static/src/img/snippets_options/header_extra_element_cta.svg'"/>
                </BuilderButton>
                <BuilderButton
                    title.translate="Show/hide logo"
                    className="'flex-grow-1'"
                    actionParam="websiteLogoParams"
                >
                    <Img src="'/website/static/src/img/snippets_options/header_extra_element_logo.svg'"/>
                </BuilderButton>
            </div>
        </div>
    </BuilderRow>
</t>

</templates>
