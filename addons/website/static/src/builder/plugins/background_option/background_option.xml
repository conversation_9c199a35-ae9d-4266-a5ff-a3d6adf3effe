<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="html_builder.BackgroundOption">
    <!-- BackgroundToggler -->
    <BuilderRow label.translate="Background" t-if="props.withColors or props.withImages">
    <!-- todo adapt when colorpicker is implemented: snippet_options_background_color_widget-->
        <t t-if="props.withColors" t-call="html_builder.BackgroundColorWidgetOption"/>
        <t t-if="props.withImages">
            <BuilderButton
                action="'toggleBgImage'"
                title.translate="Image"
                preview="false"
                className="'ms-auto fa fa-fw fa-camera'"
                id="'toggle_bg_image_id'"
            />
            <t t-if="props.withShapes">
                <BuilderButton
                    action="'toggleBgShape'"
                    preview="false"
                    id="'toggle_bg_shape_id'"
                    iconImg="'/html_builder/static/img/options/bg_shape.svg'"
                    title.translate="Shape"
                />
            </t>
        </t>
    </BuilderRow>
    <t t-if="props.withImages">
        <BackgroundImageOption/>
        <BackgroundPositionOption/>
        <ImageFilterOption level="2"/>
        <ImageFormatOption level="2" computeMaxDisplayWidth="this.computeMaxDisplayWidth"/>
        <!-- Color filter -->
        <BuilderRow t-if="this.showColorFilter()" label.translate="Color Filter" level="2">
            <!-- TODO handle all the attributes -->
            <BuilderColorPicker action="'selectFilterColor'"/>
        </BuilderRow>
        <!-- <div t-att-data-js="with_colors and with_color_combinations and 'ColoredLevelBackground' or 'BackgroundToggler'"
            <we-colorpicker string="Color Filter"
                            data-opacity="0.5"
                            data-with-gradients="1"
                            data-selected-tab="gradients"
                            data-excluded="theme, common"
            />
        </div> -->
        <BackgroundShapeOption t-if="props.withShapes"/>
    </t>
</t>

<!-- TODO: handle bg_color_opt-->
<t t-name="html_builder.BackgroundColorWidgetOption">
    <BuilderColorPicker title.translate="Color" styleAction="'background-color'"/>
</t>

</templates>
