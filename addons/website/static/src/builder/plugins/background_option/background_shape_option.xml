<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="website.BackgroundShapeOption">
    <BuilderRow label.translate="Shape" level="1" t-if="isActiveItem('toggle_bg_shape_id')">
        <button class="btn btn-primary" t-on-click="this.showBackgroundShapes" t-out="state.shapeName"/>
        <BuilderButton
                title.translate="Show/Hide on Mobile"
                preview="false"
                action="'showOnMobile'"
                className="'o_hb_device'">
            <Img src="'/html_builder/static/img/options/mobile_invisible.svg'" style="'width: 12px'" />
        </BuilderButton>
        <BuilderButton action="'setBackgroundShape'" actionValue="''" preview="false" icon="'fa-times'"/>
    </BuilderRow>

    <BuilderRow label.translate="Flip" level="2" t-if="isActiveItem('toggle_bg_shape_id')">
        <BuilderButton icon="'oi-arrows-h'" preview="false" action="'flipShape'" actionParam="'x'"/>
        <BuilderButton icon="'oi-arrows-v'" preview="false" action="'flipShape'" actionParam="'y'"/>
    </BuilderRow>

    <BuilderRow label.translate="Colors" level="2">
        <t t-foreach="getDefaultColorNames()" t-as="colorName" t-key="colorName_index">
            <BuilderColorPicker action="'backgroundShapeColor'" actionParam="colorName" enabledTabs="['solid', 'custom']"/>
        </t>
        <!-- TODO handle all the attributes -->
        <BuilderColorPicker styleAction="'background-color'" applyTo="':scope > .o_we_shape'" enabledTabs="['solid', 'custom']"/>
    </BuilderRow>
    <BuilderRow label.translate="Speed" level="2" t-if="state.isAnimated">
        <BuilderRange
            preview="false"
            displayRangeValue="true"
            max="2"
            min="-2"
            step="0.1"
            action="'setBgAnimationSpeed'"
            computedOutput="this.toRatio"
        />
    </BuilderRow>
</t>

</templates>
