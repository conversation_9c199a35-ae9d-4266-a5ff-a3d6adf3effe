<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.CashMoveListPopup">
        <Dialog title.translate="Cash move details" size="'md'" footer="false">
            <div class="cash-move-list overflow-y-auto flex-grow-1 rounded-bottom-3 bg-view">
                <t t-if="props.cashMoves.length !== 0">
                    <table t-if="!ui.isSmall"  class="table table-striped table-hover">
                        <tbody>
                            <t t-foreach="props.cashMoves" t-as="cm" t-key="cm.id">
                                <tr class="cash-move-row" >
                                    <td>
                                        <div class="fs-6 fw-bolder"><t t-esc="this.pos.getDate(cm.date)"></t></div>
                                        <div class="small text-muted"><t t-esc="this.pos.getTime(cm.date)"></t></div>
                                    </td>
                                    <td>
                                        <t t-if="cm.cashier_name" t-esc="cm.cashier_name"/>
                                    </td>
                                    <td class="align-middle text-end">
                                        <t t-set="status" t-value="getStatus(cm)" />
                                        <div t-attf-class="w-50 text-center badge rounded fs-6 text-bg-{{status === 'In' ? 'success' : 'danger'}}">
                                            <t t-esc="status"></t>
                                        </div>
                                    </td>
                                    
                                    <td class="align-middle">
                                        <div class="cash-move-amount fw-bolder"><t t-esc="getAmount(cm)"></t></div>
                                    </td>
                                    
                                    <td class="text-end delete-row">
                                        <button t-on-click.stop="callbacks[cm.id].call" class="btn btn-link btn-lg text-danger">
                                            <i t-if="callbacks[cm.id].status != 'loading'" class="fa fa-trash" aria-hidden="true"/>
                                            <i t-else="" class="fa fa-spin fa-circle-o-notch" aria-hidden="true" />
                                        </button>
                                    </td>
                                </tr>
                            </t>
                        </tbody>

                    </table>
                    <t t-if="ui.isSmall" t-foreach="props.cashMoves" t-as="cm" t-key="cm.id">
                        <div class="container">
                            <div class="cash-move-row row">
                                <div class="col-2 align-self-center">
                                        <div class="fw-bolder"><t t-esc="this.pos.getDate(cm.date)"></t></div>
                                        <div class="small text-muted"><t t-esc="this.pos.getTime(cm.date)"></t></div>
                                </div>
                                <div class="col-3 align-self-center">
                                    <t t-if="cm.cashier_name" t-esc="cm.cashier_name"/>
                                </div>
                                <t t-set="status" t-value="getStatus(cm)" />
                                <div t-attf-class="col-1 align-self-center fs-6 px-0 badge rounded text-bg-{{status === 'In' ? 'success' : 'danger'}}">
                                    <t t-esc="status"></t>
                                </div>
                                <div class="cash-move-amount col-4 align-self-center fw-bolder"><t t-esc="getAmount(cm)"></t></div>
                                <div class="col-2 align-self-center delete-row">
                                    <div class="d-flex align-items-center justify-content-center h-100" name="delete" t-on-click.stop="callbacks[cm.id].call">
                                        <div class="btn btn-danger btn-lg">
                                            <i t-if="callbacks[cm.id].status != 'loading'" class="fa fa-trash" aria-hidden="true"/>
                                            <i t-else="" class="fa fa-spin fa-circle-o-notch" aria-hidden="true" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </t>
                <div t-else="" class="d-flex justify-content-center">
                    <span class="fs-4">No cash move found</span>
                </div>
            </div>
            <t t-set-slot="footer">
                <button class="btn btn-secondary" t-on-click="props.close">Discard</button>
            </t>
        </Dialog>
    </t>

</templates>
