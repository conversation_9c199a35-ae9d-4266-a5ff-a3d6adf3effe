<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- HR WORK ENTRY -->

    <record id="hr_work_entry_action_conflict" model="ir.actions.act_window">
        <field name="name">Work Entry</field>
        <field name="res_model">hr.work.entry</field>
        <field name="context">{'search_default_work_entries_error': 1}</field>
        <field name="view_mode">list,calendar,form,pivot</field>
    </record>

    <record id="hr_work_entry_action" model="ir.actions.act_window">
        <field name="name">Work Entry</field>
        <field name="res_model">hr.work.entry</field>
        <field name="path">work-entries</field>
        <field name="view_mode">calendar,list,form,pivot</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data to display
            </p>
            <p>
                Try to add some records, or make sure that there is no active filter in the search bar.
            </p>
        </field>
    </record>

    <record id="hr_work_entry_view_calendar_multi_create_form" model="ir.ui.view">
        <field name="name">hr.work.entry.calendar.multi_create</field>
        <field name="model">hr.work.entry</field>
        <field name="priority" eval="50"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="name" invisible="1"/>
                    <field name="work_entry_type_id"/>
                </group>
            </form>
        </field>
    </record>


    <record id="hr_work_entry_view_calendar" model="ir.ui.view">
        <field name="name">hr.work.entry.calendar</field>
        <field name="model">hr.work.entry</field>
        <field name="arch" type="xml">
            <calendar string="Work Entry"
                date_start="date_start"
                date_stop="date_stop"
                mode="month"
                month_overflow="0"
                quick_create="0"
                color="color"
                event_limit="5"
                multi_create_view="hr_work_entry.hr_work_entry_view_calendar_multi_create_form"
                js_class="work_entries_calendar">
                <!-- Sidebar favorites filters -->
                <field name="employee_id" write_model="hr.user.work.entry.employee" write_field="employee_id" filter_field="is_checked" avatar_field="avatar_128" widget="many2one_avatar_employee"/>
                <field name="state"/>
            </calendar>
        </field>
    </record>

    <record id="hr_work_entry_view_form" model="ir.ui.view">
        <field name="name">hr.work.entry.form</field>
        <field name="model">hr.work.entry</field>
        <field name="arch" type="xml">
            <form string="Work Entry" >
                <header>
                    <field name="state" widget="statusbar" readonly="1" statusbar_visible="draft,validated,conflict"/>
                </header>
                <div class="alert alert-warning text-center" role="alert" invisible="state != 'validated'">
                    Note: Validated work entries cannot be modified.
                </div>
                <div invisible="state != 'conflict'">
                    <div class="alert alert-warning" role="alert" invisible="work_entry_type_id" name="work_entry_undefined">
                        This work entry cannot be validated. The work entry type is undefined.
                    </div>
                </div>
                <sheet>
                    <group>
                        <group>
                            <field name="name" string="Description" placeholder="Work Entry Name" readonly="state == 'validated'"/>
                            <field name="employee_id" readonly="state != 'draft'" widget="many2one_avatar_employee"/>
                            <field name="work_entry_type_id" readonly="state == 'validated'" options="{'no_create': True, 'no_open': True}"/>
                        </group>
                        <group>
                            <field name="date_start" readonly="state != 'draft'" />
                            <field name="date_stop" required="1" readonly="state != 'draft'"/>
                            <label for="duration"/>
                            <div class="o_row mw-50 mw-sm-25">
                                <field name="duration"
                                    nolabel="1"
                                    widget="float_time"
                                    class="o_hr_narrow_field"
                                    readonly="state != 'draft'" />
                                <span>Hours</span>
                            </div>
                            <field name="company_id" invisible="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_work_entry_view_tree" model="ir.ui.view">
        <field name="name">hr.work.entry.list</field>
        <field name="model">hr.work.entry</field>
        <field name="arch" type="xml">
            <list multi_edit="1" sample="1">
                <field name="name"/>
                <field name="work_entry_type_id" options="{'no_create': True, 'no_open': True}"/>
                <field name="code" optional="hidden"/>
                <field name="external_code" optional="hidden"/>
                <field name="duration" widget="float_time" readonly="1"/>
                <field name="state"/>
                <field name="date_start" string="Beginning" readonly="1"/>
                <field name="date_stop" string="End" readonly="1"/>
            </list>
        </field>
    </record>

    <record id="hr_work_entry_view_pivot" model="ir.ui.view">
        <field name="name">hr.work.entry.pivot</field>
        <field name="model">hr.work.entry</field>
        <field name="arch" type="xml">
            <pivot string="Work Entries" sample="1">
                <field name="duration" widget="float_time" type="measure"/>
                <field name="employee_id" type="row"/>
                <field name="work_entry_type_id" type="col"/>
            </pivot>
        </field>
    </record>

    <record id="hr_work_entry_view_search" model="ir.ui.view">
        <field name="name">hr.work.entry.filter</field>
        <field name="model">hr.work.entry</field>
        <field name="arch" type="xml">
            <search string="Search Work Entry">
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="work_entry_type_id"/>
                <field name="name"/>
                <filter name="work_entries_draft" string="Draft" domain="[('state', '=', 'draft')]"/>
                <filter name="work_entries_validated" string="Validated" domain="[('state', '=', 'validated')]"/>
                <filter name="work_entries_error" string="Conflicting" domain="[('state', '=', 'conflict')]"/>
                <separator/>
                <filter name="date_filter" string="Date" date="date_start"/>
                <filter name="current_month" string="Current Month" domain="[
                    ('date_stop', '&gt;=', (context_today()).strftime('%Y-%m-01')),
                    ('date_start', '&lt;', (context_today() + relativedelta(months=1)).strftime('%Y-%m-01'))]"/>
                <separator/>
                <filter name="group_employee" string="Employee" context="{'group_by': 'employee_id'}"/>
                <filter name="group_department" string="Department" context="{'group_by': 'department_id'}"/>
                <filter name="group_work_entry_type" string="Type" context="{'group_by': 'work_entry_type_id'}"/>
                <filter name="group_start_date" string="Start Date" context="{'group_by': 'date_start'}"/>
                <separator/>
                <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <!-- HR WORK ENTRY TYPE -->

    <record id="hr_work_entry_type_view_search" model="ir.ui.view">
        <field name="name">hr.work.entry.type.view.search</field>
        <field name="model">hr.work.entry.type</field>
        <field name="arch" type="xml">
            <search string="Search Work Entry Type">
                <field name="name" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                <separator/>
                <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="hr_work_entry_type_action" model="ir.actions.act_window">
        <field name="name">Work Entry Types</field>
        <field name="res_model">hr.work.entry.type</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="search_view_id" ref="hr_work_entry_type_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new work entry type
            </p>
        </field>
    </record>

    <record id="hr_work_entry_type_view_tree" model="ir.ui.view">
        <field name="name">hr.work.entry.type.list</field>
        <field name="model">hr.work.entry.type</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="code"/>
                <field name="color" widget="color_picker"/>
                <field name="country_id" optional="hide"/>
            </list>
        </field>
    </record>

    <record id="hr_work_entry_type_view_form" model="ir.ui.view">
        <field name="name">hr.work.entry.type.form</field>
        <field name="model">hr.work.entry.type</field>
        <field name="arch" type="xml">
            <form string="Work Entry Type" >
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Work Entry Type Name"/>
                        </h1>
                    </div>
                    <group name="main_group">
                        <group name="identification" class="o_form_fw_labels">
                            <field name="active" invisible="1"/>
                            <field name="code"/>
                            <field name="external_code"/>
                            <field name="sequence" groups="base.group_no_one"/>
                            <field name="color" widget="color_picker"/>
                        </group>
                        <group>
                            <field name="country_id" options="{'no_create': True, 'no_open': True}"/>
                        </group>
                    </group>
                    <group name="other">
                        <group name="time_off" string="Time Off Options" class="o_form_fw_labels"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_work_entry_type_view_kanban" model="ir.ui.view">
        <field name="name">hr.work.entry.type.kanban.view</field>
        <field name="model">hr.work.entry.type</field>
        <field name="arch" type="xml">
            <kanban highlight_color="color">
                <templates>
                    <t t-name="menu" t-if="!selection_mode">
                        <field name="color" widget="kanban_color_picker"/>
                    </t>
                    <t t-name="card" t-attf-class="#{!selection_mode ? record.color.raw_value : ''}">
                        <field class="fw-bold fs-5" name="name"/>
                        <field class="text-muted" name="code"/>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

</odoo>
