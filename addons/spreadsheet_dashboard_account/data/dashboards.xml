<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="dashboard_invoicing" model="spreadsheet.dashboard">
        <field name="name">Invoicing</field>
        <field name="spreadsheet_binary_data" type="base64" file="spreadsheet_dashboard_account/data/files/invoicing_dashboard.json"/>
        <field name="main_data_model_ids" eval="[(4, ref('account.model_account_move'))]"/>
        <field name="sample_dashboard_file_path">spreadsheet_dashboard_account/data/files/invoicing_sample_dashboard.json</field>
        <field name="dashboard_group_id" ref="spreadsheet_dashboard.spreadsheet_dashboard_group_finance"/>
        <field name="group_ids" eval="[Command.link(ref('account.group_account_readonly')), Command.link(ref('account.group_account_invoice'))]"/>
        <field name="sequence">20</field>
        <field name="is_published">True</field>
    </record>

</odoo>
