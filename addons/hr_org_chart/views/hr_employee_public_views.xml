<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="action_hr_employee_public_org_chart" model="ir.actions.act_window">
        <field name="name">Org Chart</field>
        <field name="res_model">hr.employee.public</field>
        <field name="path">org-chart-public</field>
        <field name="view_mode">hierarchy,kanban,list,form,graph,pivot</field>
        <field name="domain">[]</field>
        <field name="context">{'chat_icon': True}</field>
        <field name="view_id" eval="False"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an employee.
            </p><p>
                Find all the information on employees.
            </p>
        </field>
    </record>
    <record id="hr_employee_public_hierarchy_view" model="ir.ui.view">
        <field name="name">hr.employee.public.hierarchy.view</field>
        <field name="model">hr.employee.public</field>
        <field name="arch" type="xml">
            <hierarchy child_field="child_ids" js_class="hr_employee_hierarchy" icon="fa-users" draggable="0">
                <field name="name" />
                <field name="job_id" />
                <field name="department_color" />
                <field name="hr_icon_display" />
                <field name="department_id" />
                <templates>
                    <t t-name="hierarchy-box">
                        <div t-attf-class="o_hierarchy_node_header d-flex justify-content-center pb-4 o_hierarchy_node_color_{{ record.department_color.raw_value }}"
                             t-att-title="record.department_id.value"
                        >
                            <field name="image_1024" preview_image="image_128" options="{'zoom': true, 'zoom_delay': 1000}" widget="background_image" />
                        </div>
                        <div class="d-flex flex-column text-center">
                            <div class="d-flex">
                                <field class="fw-bold w-100" name="name" />
                                <field name="hr_icon_display" class="flex-shrink-0" widget="hr_presence_status" />
                            </div>
                            <field name="job_id"/>
                        </div>
                    </t>
                </templates>
            </hierarchy>
        </field>
    </record>

    <record id="act_hr_employee_public_hierarchy_view" model="ir.actions.act_window.view">
        <field name="sequence" eval="16"/>
        <field name="view_mode">hierarchy</field>
        <field name="act_window_id" ref="hr.hr_employee_public_action"/>
    </record>
</odoo>
