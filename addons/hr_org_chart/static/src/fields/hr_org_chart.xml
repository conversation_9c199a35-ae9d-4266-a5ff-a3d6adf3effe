<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

<t t-name="hr_org_chart.hr_org_chart_employee">
    <t t-set="is_self" t-value="employee.id == view_employee_id"/>

    <section t-if="employee_type == 'self'" t-attf-class="o_org_chart_entry_self_container #{managers.length &gt; 0 ? 'o_org_chart_has_managers' : ''}">
        <div t-attf-class="o_org_chart_entry o_org_chart_entry_#{employee_type} d-flex position-relative py-2 overflow-visible #{managers.length &gt; 0 ? 'o_treeEntry' : ''}">
            <t t-call="hr_org_chart.hr_org_chart_employee_content">
                <t t-set="is_self" t-value="is_self"/>
            </t>
        </div>
    </section>

    <div t-else="" t-attf-class="o_org_chart_entry o_org_chart_entry_#{employee_type} o_treeEntry d-flex position-relative py-2 overflow-visible">
        <t t-call="hr_org_chart.hr_org_chart_employee_content">
            <t t-set="is_self" t-value="is_self"/>
        </t>
    </div>
</t>

<t t-name="hr_org_chart.hr_org_chart_employee_content">
    <div class="o_media_left position-relative">
        <!-- NOTE: Since by the default on not squared images odoo add white borders,
            use bg-images to get a clean and centred images -->
        <a t-if="! is_self"
            class="o_media_object d-block rounded o_employee_redirect"
            t-att-style="'background-image:url(\'/web/image/hr.employee.public/' + employee.id + '/avatar_1024/\')'"
            t-att-alt="employee.name"
            t-att-data-employee-id="employee.id"
            t-att-href="employee.link"
            t-on-click.prevent="() => this._onEmployeeRedirect(employee.id)"/>
        <div t-if="is_self"
            class="o_media_object d-block rounded border border-info"
            t-att-style="'background-image:url(\'/web/image/hr.employee.public/' + employee.id + '/avatar_1024/\')'"/>
    </div>

    <div class="d-flex flex-grow-1 align-items-center justify-content-between position-relative px-3">
        <a t-if="!is_self" t-att-href="employee.link" class="o_employee_redirect d-flex flex-column" t-att-data-employee-id="employee.id" t-on-click.prevent="() => this._onEmployeeRedirect(employee.id)">
            <b class="o_media_heading m-0 fs-6" t-esc="employee.name"/>
            <small class="text-muted fw-bold" t-esc="employee.job_name"/>
        </a>
        <div t-if="is_self" class="d-flex flex-column">
            <h5 class="o_media_heading m-0" t-esc="employee.name"/>
            <small class="text-muted fw-bold" t-esc="employee.job_name"/>
        </div>
        <button t-if="employee.indirect_sub_count &gt; 0"
                class="btn p-0 fs-3"
                tabindex="0"
                t-att-data-emp-name="employee.name"
                t-att-data-emp-id="employee.id"
                t-att-data-emp-dir-subs="employee.direct_sub_count"
                t-att-data-emp-ind-subs="employee.indirect_sub_count"
                data-bs-trigger="focus"
                data-bs-toggle="popover"
                t-on-click="(event) => this._onOpenPopover(event, employee)">
            <a href="#"
                t-attf-class="badge rounded-pill bg-300 border {{employee.indirect_sub_count &lt; 10 ? 'px-2' : 'px-1' }}"
                t-esc="employee.indirect_sub_count"
                />
        </button>

    </div>
</t>

<t t-name="hr_org_chart.hr_org_chart">
    <!-- NOTE: Desidered behaviour:
            The maximun number of people is always 7 (including 'self'). Managers have priority over suburdinates
            Eg. 1 Manager + 1 self = show just 5 subordinates (if availables)
            Eg. 0 Manager + 1 self = show 6 subordinates (if available)

        -->
    <t t-set="emp_count" t-value="0"/>
    <div t-if='managers.length &gt; 0' class="o_org_chart_group_up position-relative">
        <div t-if='managers_more' class="o_org_chart_more pe-3">
            <a href="#" t-att-data-employee-id="managers[0].id" class="o_employee_more_managers d-block bg-100 px-3" t-on-click.prevent="() => this._onEmployeeMoreManager(managers[0].id)">
                <i class="fa fa-angle-double-up" role="img" aria-label="More managers" title="More managers"/>
            </a>
        </div>

        <t t-foreach="managers" t-as="employee" t-key="employee_index">
            <t t-set="emp_count" t-value="emp_count + 1"/>
            <t t-call="hr_org_chart.hr_org_chart_employee">
                <t t-set="employee_type" t-value="'manager'"/>
            </t>
        </t>
    </div>

    <t t-if="children.length || managers.length" t-call="hr_org_chart.hr_org_chart_employee">
        <t t-set="employee_type" t-value="'self'"/>
        <t t-set="employee" t-value="self"/>
    </t>

    <t t-if="!children.length &amp;&amp; !managers.length">
        <div class="alert alert-info" role="alert">
            <p><b>No hierarchy defined.</b></p>
            <p>Define a manager or subordinates to see this employee in the org chart.</p>
        </div>
    </t>

    <div t-if="children.length" t-attf-class="o_org_chart_group_down position-relative #{managers.length &gt; 0 ? 'o_org_chart_has_managers' : ''}">
        <t t-foreach="children" t-as="employee" t-key="employee_index">
            <t t-set="emp_count" t-value="emp_count + 1"/>
            <t t-if="emp_count &lt; 20">
                <t t-call="hr_org_chart.hr_org_chart_employee">
                    <t t-set="employee_type" t-value="'sub'"/>
                </t>
            </t>
        </t>

        <t t-if="(children.length + managers.length) &gt; 19">
            <div class="o_org_chart_entry o_org_chart_more d-flex overflow-visible">
                <div class="o_media_left position-relative">
                    <a href="#"
                        t-att-data-employee-id="self.id"
                        t-att-data-employee-name="self.name"
                        class="o_org_chart_show_more o_employee_sub_redirect btn btn-link ps-2"
                        t-on-click.prevent="_onEmployeeSubRedirect">See All</a>
                </div>
            </div>
        </t>
    </div>
</t>

<t t-name="hr_org_chart.hr_orgchart_emp_popover">
    <div class="popover o_org_chart_popup" role="tooltip">
        <div class="tooltip-arrow">
            
        </div>
        <h3 class="popover-header">
            <div class="d-flex align-items-center">
                <span class="flex-shrink-0" t-att-style='"background-image:url(\"/web/image/hr.employee.public/" + props.employee.id + "/avatar_1024/\")"'/>
                <b class="flew-grow-1"><t t-esc="props.employee.name"/></b>
                <a href="#" class="ms-auto o_employee_redirect" t-att-data-employee-id="props.employee.id" t-on-click.prevent="() => this._onEmployeeRedirect(props.employee.id)"><i class="fa fa-external-link" role="img" aria-label='Redirect' title="Redirect"></i></a>
            </div>
        </h3>
        <div class="popover-body">
            <table class="table table-sm table-borderless mb-0">
                <tbody>
                    <tr>
                        <td class="text-end"><b t-esc="props.employee.direct_sub_count"/></td>
                        <td>
                            <a href="#" class="o_employee_sub_redirect" data-type='direct'
                                    t-att-data-employee-name="props.employee.name" t-att-data-employee-id="props.employee.id"
                                    t-on-click.prevent="_onEmployeeSubRedirect">
                                <b>Direct subordinates</b></a>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-end">
                            <b t-esc="props.employee.indirect_sub_count - props.employee.direct_sub_count"/>
                        </td>
                        <td>
                            <a href="#" class="o_employee_sub_redirect" data-type='indirect'
                                    t-att-data-employee-name="props.employee.name" t-att-data-employee-id="props.employee.id"
                                    t-on-click.prevent="_onEmployeeSubRedirect">
                                Indirect subordinates</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-end"><b t-esc="props.employee.indirect_sub_count"/></td>
                        <td>
                            <a href="#" class="o_employee_sub_redirect" data-type='total'
                                    t-att-data-employee-name="props.employee.name" t-att-data-employee-id="props.employee.id"
                                    t-on-click.prevent="_onEmployeeSubRedirect">
                                Total</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</t>

</templates>
