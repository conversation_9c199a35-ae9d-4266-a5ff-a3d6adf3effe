# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_edi_tbai
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-13 15:08+0000\n"
"PO-Revision-Date: 2025-01-13 15:08+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner_recibidas
msgid "1.0"
msgstr "1.0"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner
msgid "1.1"
msgstr "1.1"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__l10n_es_edi_tbai_document__state__accepted
msgid "Accepted"
msgstr "Aceptado"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move_reversal
msgid "Account Move Reversal"
msgstr "Reversión de movimiento de cuenta"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move_send
msgid "Account Move Send"
msgstr "Enviar asiento contable"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_refund_reason
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_refund_reason
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move_reversal__l10n_es_tbai_refund_reason
msgid ""
"BOE-A-1992-28740. Ley 37/1992, de 28 de diciembre, del Impuesto sobre el "
"Valor Añadido. Artículo 80. Modificación de la base imponible."
msgstr ""
"BOE-A-1992-28740. Ley 37/1992, de 28 de diciembre, del Impuesto sobre el "
"Valor Añadido. Artículo 80. Modificación de la base imponible."

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_state__cancelled
msgid "Cancelled"
msgstr "Cancelado"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid "Cannot send an entry that is not posted to TicketBAI."
msgstr ""
"No es posible enviar a través de TicketBAI un asiento o registro sin "
"contabilizar."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid "Cannot send this entry as it is already being processed."
msgstr "No es posible enviar este registro porque ya está en proceso."

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_certificate_certificate
msgid "Certificate"
msgstr "Certificado"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_certificate_id
msgid "Certificate (TicketBAI)"
msgstr "Certificado (TicketBAI)"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_certificate_certificate__scope
msgid "Certificate scope"
msgstr "Alcance del certificado"

#. module: l10n_es_edi_tbai
#: model:ir.ui.menu,name:l10n_es_edi_tbai.menu_l10n_es_edi_tbai_certificates
msgid "Certificates"
msgstr "Certificados"

#. module: l10n_es_edi_tbai
#: model:ir.actions.act_window,name:l10n_es_edi_tbai.l10n_es_edi_tbai_certificate_action
msgid "Certificates for EDI TicketBAI invoices on Spain"
msgstr "Certificados para las facturas EDI TicketBAI en España"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__chain_index
msgid "Chain Index"
msgstr "Índice de secuencia"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__company_id
msgid "Company"
msgstr "Compañía"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: l10n_es_edi_tbai
#: model_terms:ir.actions.act_window,help:l10n_es_edi_tbai.l10n_es_edi_tbai_certificate_action
msgid "Create the first certificate"
msgstr "Crear el primer certificado"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__date
msgid "Date"
msgstr "Fecha"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_line__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_send__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_certificate_certificate__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move_send.py:0
msgid "Error when sending the invoice to TicketBAI:"
msgstr "Se ha producido un error al enviar la factura a TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__res_company__l10n_es_tbai_tax_agency__araba
msgid "Hacienda Foral de Araba"
msgstr "Hacienda Foral de la Diputación de Álava"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__res_company__l10n_es_tbai_tax_agency__bizkaia
msgid "Hacienda Foral de Bizkaia"
msgstr "Hacienda Foral de Bizkaia"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__res_company__l10n_es_tbai_tax_agency__gipuzkoa
msgid "Hacienda Foral de Gipuzkoa"
msgstr "Diputación Foral de Gipuzkoa"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_line__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_send__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_certificate_certificate__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__id
msgid "ID"
msgstr "ID"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"In case of a foreign customer, you need to configure the tax scope on taxes:\n"
"%s"
msgstr ""
"En el caso de un cliente extranjero, es necesario configurar el ámbito fiscal en impuestos:\n"
"%s"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"In order to use Ticketbai Batuz for freelancers, you will need to configure "
"the Epigrafe or Main Activity.  In this version, you need to go in debug "
"mode to Settings > Technical > System Parameters and set the parameter "
"'l10n_es_edi_tbai.epigrafe'to your epigrafe number. You can find them in %s"
msgstr ""
"Para utilizar TicketBAI Batuz para autónomos, necesitará configurar el "
"epígrafe o actividad principal.  En esta versión, debe ir al modo "
"desarrolladorAjustes > Técnico > Parámetros del sistema y configurar los "
"parámetros 'l10n_es_edi_tbai.epigrafe' con su número de epígrafe. Puede "
"encontrar la lista de epígrafes en: %s"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_reversed_ids
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_reversed_ids
msgid ""
"In the case where a vendor refund has multiple original invoices, you can "
"set them here. "
msgstr ""
"Para las facturas rectificativas de un proveedor que tenga diversas facturas"
" reembolsadas, puede configurarlas aquí."

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_refund_reason
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_refund_reason
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__l10n_es_tbai_refund_reason
msgid "Invoice Refund Reason Code (TicketBai)"
msgstr "Código de motivo de reembolso de la factura (TicketBAI)"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_chain_index
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_chain_index
msgid ""
"Invoice index in chain, set if and only if an in-chain XML was submitted and"
" did not error"
msgstr ""
"Índice de facturas en cadena que se establece si y solo si se envió un XML y"
" no se produjo un error."

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__is_cancel
msgid "Is Cancel"
msgstr "Está cancelado"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__l10n_es_tbai_is_required
msgid "Is TicketBai required for this reversal"
msgstr "¿Se necesita TicketBAI para realizar una reversión?"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_is_required
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_is_required
msgid "Is the Basque EDI (TicketBAI) needed ?"
msgstr "¿Se necesita el EDI vasco (TicketBAI)?"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_cancel_document_id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_cancel_document_id
msgid "L10N Es Tbai Cancel Document"
msgstr "Cancelar documento L10N Es Tbai"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_certificate_ids
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__l10n_es_tbai_certificate_ids
msgid "L10N Es Tbai Certificate"
msgstr "Certificado L10N Es Tbai"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_is_enabled
msgid "L10N Es Tbai Is Enabled"
msgstr "L10N Es Tbai activado"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_post_document_id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_post_document_id
msgid "L10N Es Tbai Post Document"
msgstr "Publicar documento L10N Es Tbai"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Licence NIF"
msgstr "Licencia NIF"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Licence number"
msgstr "Número de licencia"

#. module: l10n_es_edi_tbai
#: model:ir.ui.menu,name:l10n_es_edi_tbai.menu_l10n_es_edi_tbai_license
msgid "Licenses"
msgstr "Licencias (TicketBAI)"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Manage certificates (TicketBAI)"
msgstr "Gestionar certificados (TicketBAI)"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__name
msgid "Name"
msgstr "Nombre"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "No XML response received."
msgstr "No se ha recibido respuesta XML."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "No certificate found"
msgstr "No se ha encontrado ningún certificado"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "No tax agency selected: TicketBAI not activated."
msgstr ""
"No se ha seleccionado ninguna agencia tributaria: TicketBai no activado."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"No valid certificate found for this company, TicketBAI file will not be "
"signed.\n"
msgstr ""
"No se ha encontrado ningún certificado válido para esta empresa, el archivo "
"TicketBAI no se podrá firmar.\n"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Please configure the Tax ID on your company for TicketBAI."
msgstr "Configure el NIF en su compañía par TicketBai."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Please configure the certificate for TicketBAI."
msgstr "Por favor, configure el certificado para TicketBAI/SII."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Please specify a tax agency on your company for TicketBAI."
msgstr "Especifique una agencia tributaria para su compañía en TicketBAI."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Production license"
msgstr "Licencia de producción"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Production mode: EDI data is sent to the official agency servers."
msgstr "Modo producción: los datos EDI se envían a los servidores oficiales."

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r1
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r1
msgid "R1: Art. 80.1, 80.2, 80.6 and rights founded error"
msgstr "R1: Art. 80.1, 80.2, 80.6 y por derechos fundados en el error"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r2
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r2
msgid "R2: Art. 80.3"
msgstr "R2: Art. 80.3"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r3
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r3
msgid "R3: Art. 80.4"
msgstr "R3: Art. 80.4"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r4
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r4
msgid "R4: Art. 80 - other"
msgstr "R4: Art. 80 - resto"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r5
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r5
msgid "R5: Factura rectificativa en facturas simplificadas"
msgstr "R5: Factura rectificativa en facturas simplificadas"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Refund reason cannot be R5 for non-simplified invoices (TicketBAI)"
msgstr ""
"El motivo de reembolso no puede ser R5 para facturas no simplificadas "
"(TicketBAI)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Refund reason must be R5 for simplified invoices (TicketBAI)"
msgstr ""
"El motivo de reembolso debe ser R5 para facturas simplificadas (TicketBAI)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Refund reason must be specified (TicketBAI)"
msgstr "Se debe especificar el motivo del reembolso (TicketBAI)"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_reversed_ids
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_reversed_ids
msgid "Refunded Vendor Bills"
msgstr "Facturas de proveedores reembolsadas"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Registro de Libros connection TicketBAI"
msgstr "Registro de Libros conexión TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__l10n_es_edi_tbai_document__state__rejected
msgid "Rejected"
msgstr "Rechazado"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__response_message
msgid "Response Message"
msgstr "Mensaje de respuesta"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/wizards/account_move_reversal.py:0
msgid "Reversals mixing invoices with and without TicketBAI are not allowed."
msgstr "No se permiten reversiones que mezclen facturas con y sin TicketBAI."

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.view_move_form_inherit_l10n_es_edi_tbai
msgid "Send Bill to TicketBAI"
msgstr "Envíe facturas a TicketBAI"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move_send.py:0
msgid "Send the e-invoice to the Basque Government."
msgstr "Envíe la factura electrónica al Gobierno Vasco"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_state__sent
msgid "Sent"
msgstr "Enviado"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Software name"
msgstr "Nombre del software"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Software version"
msgstr "Versión del software"

#. module: l10n_es_edi_tbai
#: model:ir.ui.menu,name:l10n_es_edi_tbai.menu_l10n_es_edi_tbai_root
msgid "Spain TicketBAI"
msgstr "TicketBAI España"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__certificate_certificate__scope__tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.certificate_certificate_view_search
msgid "TBAI"
msgstr "TBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_test_env
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__l10n_es_tbai_test_env
msgid "TBAI Test Mode"
msgstr "Modo de prueba TBAI"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.certificate_certificate_view_search
msgid "TBAI certificates"
msgstr "Certificados TBAI"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_invoice_bundle
msgid "TEST-DEVICE-001"
msgstr "PRUEBA-DISPOSITIVO-001"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_tax_agency
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__l10n_es_tbai_tax_agency
msgid "Tax Agency for TBAI"
msgstr "Agencia tributaria para TBAI"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Tax agency selected: invoices will be sent by TicketBAI."
msgstr "Agencia tributaria seleccionada: las facturas las enviará TicketBAI."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Test license (Araba)"
msgstr "Licencia de prueba (Araba)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Test license (Bizkaia)"
msgstr "Licencia de prueba (Bizkaia)"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Test license (Gipuzkoa)"
msgstr "Licencia de prueba (Gipuzkoa)"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid ""
"Test mode: EDI data is sent to separate test servers and is not considered "
"official."
msgstr ""
"Modo de prueba: los datos EDI se envían a servidores de prueba distintos y "
"no se consideran oficiales."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"There should be at least one tax set on each line in order to send to "
"TicketBAI."
msgstr ""
"Debería haber, como mínimo, un impuesto definido en cada línea para enviarlo"
" a TicketBAI."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid "This entry has already been posted."
msgstr "Este asiento ya se ha publicado."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move_send.py:0
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.view_move_form_inherit_l10n_es_edi_tbai
msgid "TicketBAI"
msgstr "TicketBAI"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.view_move_form_inherit_l10n_es_edi_tbai
msgid "TicketBAI Cancel"
msgstr "Cancelar TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_cancel_file
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_cancel_file
msgid "TicketBAI Cancel File"
msgstr "Cancelar archivo TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_cancel_file_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_cancel_file_name
msgid "TicketBAI Cancel File Name"
msgstr "Cancelar nombre de archivo TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_l10n_es_edi_tbai_document
msgid "TicketBAI Document"
msgstr "Documento TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_post_file_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_post_file_name
msgid "TicketBAI Post Attachment Name"
msgstr "Nombre del archivo adjunto de TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_post_file
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_post_file
msgid "TicketBAI Post File"
msgstr "Archivo de publicación de TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_chain_index
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_chain_index
msgid "TicketBAI chain index"
msgstr "Índice de secuencia de TicketBAI"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "TicketBAI is not configured"
msgstr "TicketBAI no está configurado"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_license_html
msgid "TicketBAI license"
msgstr "Licencia de TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_is_required
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_is_required
msgid "TicketBAI required"
msgstr "Se requiere TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_state
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_state
msgid "TicketBAI status"
msgstr "Estado de TicketBAI"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"TicketBAI: Cannot post a reversal document while the source document has not"
" been posted"
msgstr ""
"TicketBAI: No es posible publicar un documento de reversión mientras el "
"documento original no haya sido publicado"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"TicketBAI: Cannot post invoice while chain head (%s) has not been posted"
msgstr ""
"TicketBAI: No se puede publicar la factura mientras no se haya publicado la "
"cabeza de cadena (%s)"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_chain_sequence_id
msgid "TicketBai account.move chain sequence"
msgstr "Secuencia en cadena del account.move de TicketBai"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_state__to_send
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__l10n_es_edi_tbai_document__state__to_send
msgid "To Send"
msgstr "Por enviar"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_test_env
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_res_config_settings__l10n_es_tbai_test_env
msgid "Use the test environment for TicketBAI"
msgstr "Utilizar entorno de prueba para TicketBAI"

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__xml_attachment_id
msgid "XML Attachment"
msgstr "Adjunto XML"

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid "You cannot delete a move that has a TicketBAI chain id."
msgstr "No puede eliminar un asiento que tiene un ID en cadena de TicketBai."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid ""
"You cannot reset to draft an entry that has been posted to TicketBAI's chain"
msgstr ""
"No puede reestablecer a borrador un asiento que se publicó en la cadena de "
"TicketBai."

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid ""
"You need to fill in the Reference field as the invoice number from your "
"vendor."
msgstr ""
"Debe rellenar el campo de referencia con el número de factura de su "
"proveedor."

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__state
msgid "status"
msgstr "estado"
