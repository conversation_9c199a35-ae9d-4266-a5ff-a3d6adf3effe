# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es_edi_tbai
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-21 14:26+0000\n"
"PO-Revision-Date: 2025-05-21 14:26+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner_recibidas
msgid "1.0"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_LROE_240_inner
msgid "1.1"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__l10n_es_edi_tbai_document__state__accepted
msgid "Accepted"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move_reversal
msgid "Account Move Reversal"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move_send
msgid "Account Move Send"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_refund_reason
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_refund_reason
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move_reversal__l10n_es_tbai_refund_reason
msgid ""
"BOE-A-1992-28740. Ley 37/1992, de 28 de diciembre, del Impuesto sobre el "
"Valor Añadido. Artículo 80. Modificación de la base imponible."
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_state__cancelled
msgid "Cancelled"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid "Cannot send an entry that is not posted to TicketBAI."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid "Cannot send this entry as it is already being processed."
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_certificate_certificate
msgid "Certificate"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_certificate_id
msgid "Certificate (TicketBAI)"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_certificate_certificate__scope
msgid "Certificate scope"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.ui.menu,name:l10n_es_edi_tbai.menu_l10n_es_edi_tbai_certificates
msgid "Certificates"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.actions.act_window,name:l10n_es_edi_tbai.l10n_es_edi_tbai_certificate_action
msgid "Certificates for EDI TicketBAI invoices on Spain"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__chain_index
msgid "Chain Index"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__company_id
msgid "Company"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.actions.act_window,help:l10n_es_edi_tbai.l10n_es_edi_tbai_certificate_action
msgid "Create the first certificate"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__create_date
msgid "Created on"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__date
msgid "Date"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_line__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_send__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_certificate_certificate__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move_send.py:0
msgid "Error when sending the invoice to TicketBAI:"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__res_company__l10n_es_tbai_tax_agency__araba
msgid "Hacienda Foral de Araba"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__res_company__l10n_es_tbai_tax_agency__bizkaia
msgid "Hacienda Foral de Bizkaia"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__res_company__l10n_es_tbai_tax_agency__gipuzkoa
msgid "Hacienda Foral de Gipuzkoa"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_line__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_send__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_certificate_certificate__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__id
msgid "ID"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"In case of a foreign customer, you need to configure the tax scope on taxes:\n"
"%s"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"In order to use Ticketbai Batuz for freelancers, you will need to configure "
"the Epigrafe or Main Activity.  In this version, you need to go in debug "
"mode to Settings > Technical > System Parameters and set the parameter "
"'l10n_es_edi_tbai.epigrafe'to your epigrafe number. You can find them in %s"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_reversed_ids
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_reversed_ids
msgid ""
"In the case where a vendor refund has multiple original invoices, you can "
"set them here. "
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_refund_reason
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_refund_reason
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__l10n_es_tbai_refund_reason
msgid "Invoice Refund Reason Code (TicketBai)"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_chain_index
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_chain_index
msgid ""
"Invoice index in chain, set if and only if an in-chain XML was submitted and"
" did not error"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__is_cancel
msgid "Is Cancel"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move_reversal__l10n_es_tbai_is_required
msgid "Is TicketBai required for this reversal"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_is_required
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_is_required
msgid "Is the Basque EDI (TicketBAI) needed ?"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_cancel_document_id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_cancel_document_id
msgid "L10N Es Tbai Cancel Document"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_certificate_ids
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__l10n_es_tbai_certificate_ids
msgid "L10N Es Tbai Certificate"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_is_enabled
msgid "L10N Es Tbai Is Enabled"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_post_document_id
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_post_document_id
msgid "L10N Es Tbai Post Document"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Licence NIF"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Licence number"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.ui.menu,name:l10n_es_edi_tbai.menu_l10n_es_edi_tbai_license
msgid "Licenses"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Manage certificates (TicketBAI)"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__name
msgid "Name"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "No XML response received."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "No certificate found"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "No tax agency selected: TicketBAI not activated."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"No valid certificate found for this company, TicketBAI file will not be "
"signed.\n"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Please configure the Tax ID on your company for TicketBAI."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Please configure the certificate for TicketBAI."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Please specify a tax agency on your company for TicketBAI."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Production license"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Production mode: EDI data is sent to the official agency servers."
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r1
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r1
msgid "R1: Art. 80.1, 80.2, 80.6 and rights founded error"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r2
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r2
msgid "R2: Art. 80.3"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r3
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r3
msgid "R3: Art. 80.4"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r4
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r4
msgid "R4: Art. 80 - other"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_refund_reason__r5
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move_reversal__l10n_es_tbai_refund_reason__r5
msgid "R5: Factura rectificativa en facturas simplificadas"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Refund reason cannot be R5 for non-simplified invoices (TicketBAI)"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Refund reason must be R5 for simplified invoices (TicketBAI)"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid "Refund reason must be specified (TicketBAI)"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_reversed_ids
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_reversed_ids
msgid "Refunded Vendor Bills"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Registro de Libros connection TicketBAI"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__l10n_es_edi_tbai_document__state__rejected
msgid "Rejected"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__response_message
msgid "Response Message"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/wizards/account_move_reversal.py:0
msgid "Reversals mixing invoices with and without TicketBAI are not allowed."
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.view_move_form_inherit_l10n_es_edi_tbai
msgid "Send Bill to TicketBAI"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move_send.py:0
msgid "Send the e-invoice to the Basque Government."
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_state__sent
msgid "Sent"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Software name"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Software version"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.ui.menu,name:l10n_es_edi_tbai.menu_l10n_es_edi_tbai_root
msgid "Spain TicketBAI"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__certificate_certificate__scope__tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.certificate_certificate_view_search
msgid "TBAI"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_test_env
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__l10n_es_tbai_test_env
msgid "TBAI Test Mode"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.certificate_certificate_view_search
msgid "TBAI certificates"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.template_invoice_bundle
msgid "TEST-DEVICE-001"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_tax_agency
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_config_settings__l10n_es_tbai_tax_agency
msgid "Tax Agency for TBAI"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid "Tax agency selected: invoices will be sent by TicketBAI."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Test license (Araba)"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Test license (Bizkaia)"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "Test license (Gipuzkoa)"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.res_config_settings_view_form
msgid ""
"Test mode: EDI data is sent to separate test servers and is not considered "
"official."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"There should be at least one tax set on each line in order to send to "
"TicketBAI."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid "This entry has already been posted."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move_send.py:0
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.view_move_form_inherit_l10n_es_edi_tbai
msgid "TicketBAI"
msgstr ""

#. module: l10n_es_edi_tbai
#: model_terms:ir.ui.view,arch_db:l10n_es_edi_tbai.view_move_form_inherit_l10n_es_edi_tbai
msgid "TicketBAI Cancel"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_cancel_file
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_cancel_file
msgid "TicketBAI Cancel File"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_cancel_file_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_cancel_file_name
msgid "TicketBAI Cancel File Name"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model,name:l10n_es_edi_tbai.model_l10n_es_edi_tbai_document
msgid "TicketBAI Document"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_post_file_name
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_post_file_name
msgid "TicketBAI Post Attachment Name"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_post_file
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_post_file
msgid "TicketBAI Post File"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_chain_index
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_chain_index
msgid "TicketBAI chain index"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/res_company.py:0
msgid "TicketBAI is not configured"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_license_html
msgid "TicketBAI license"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_is_required
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_is_required
msgid "TicketBAI required"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_bank_statement_line__l10n_es_tbai_state
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_account_move__l10n_es_tbai_state
msgid "TicketBAI status"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"TicketBAI: Cannot post a reversal document while the source document has not"
" been posted"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py:0
msgid ""
"TicketBAI: Cannot post invoice while chain head (%s) has not been posted"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_chain_sequence_id
msgid "TicketBai account.move chain sequence"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__account_move__l10n_es_tbai_state__to_send
#: model:ir.model.fields.selection,name:l10n_es_edi_tbai.selection__l10n_es_edi_tbai_document__state__to_send
msgid "To Send"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_res_company__l10n_es_tbai_test_env
#: model:ir.model.fields,help:l10n_es_edi_tbai.field_res_config_settings__l10n_es_tbai_test_env
msgid "Use the test environment for TicketBAI"
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__xml_attachment_id
msgid "XML Attachment"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid "You cannot delete a move that has a TicketBAI chain id."
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid ""
"You cannot reset to draft an entry that has been posted to TicketBAI's chain"
msgstr ""

#. module: l10n_es_edi_tbai
#. odoo-python
#: code:addons/l10n_es_edi_tbai/models/account_move.py:0
msgid ""
"You need to fill in the Reference field as the invoice number from your "
"vendor."
msgstr ""

#. module: l10n_es_edi_tbai
#: model:ir.model.fields,field_description:l10n_es_edi_tbai.field_l10n_es_edi_tbai_document__state
msgid "status"
msgstr ""
