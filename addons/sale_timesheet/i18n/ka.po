# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_timesheet
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2015-09-10 15:21+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Georgian (http://www.transifex.com/odoo/odoo-9/language/ka/)\n"
"Language: ka\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  ka.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"#-#-#-#-#  ka.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice___candidate_orders
msgid " Candidate Orders"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__billable_percentage
msgid "% of timesheets that are billable compared to the total number of timesheets linked to the AA of the project, rounded to the unit."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
msgid "%(amount)s %(label)s will be added to the new Sales Order."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", for a revenue of"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", leading to a"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Invoice</span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>To invoice:</strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Sales</u>"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "About us"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: sale_timesheet
#: model:account.analytic.account,name:sale_timesheet.account_analytic_account_project_support
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allocated_hours
msgid "Allocated Hours"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_create_sale_order_line_unique_employee_per_wizard
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid "An employee cannot be selected more than once in the mapping. Please remove duplicate(s) and try again."
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"As a leading professional services firm,\n"
"                                we know that success is all about the\n"
"                                commitment we put on strong services."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
msgid "At least one line should be filled."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
msgid "Based on Timesheets"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__allow_billable
msgid "Billable"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Billable Hours"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billable_percentage
msgid "Billable Percentage"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_type
msgid "Billable Type"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_manual
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_manual
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed Manually"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_milestones
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_milestones
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Milestones"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billing Type"
msgstr ""

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__company_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__company_id
msgid "Company"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost_currency_id
msgid "Cost Currency"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order_line
msgid "Create SO Line from project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order
msgid "Create SO from project"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Create Sales Order"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Create a Sales Order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__partner_id
msgid "Customer"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__partner_id
msgid "Customer of the sales order"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Delivered"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Discard"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__display_create_order
msgid "Display Create Order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "Draft Invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__employee_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "თანამშრომელი"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__employee_id
msgid "Employee that has timesheets on the project."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Furniture Delivery (Manual)"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"Great quotation templates will significantly\n"
"                                <strong>boost your success rate</strong>. The\n"
"                                first section is usually about your company,\n"
"                                your references, your methodology or\n"
"                                guarantees, your team, SLA, terms and conditions, etc."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__has_displayed_warning_upsell
msgid "Has Displayed Warning Upsell"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_cost
msgid "Hourly Cost"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"If you edit a quotation from the 'Preview' of a quotation, you will\n"
"                        update that quotation only. If you edit the quotation\n"
"                        template (from the Configuration menu), all future quotations will\n"
"                        use this modified template."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__info_invoice
msgid "Info Invoice"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/account.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_account_move
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
msgid "Invoice based on timesheets (delivered quantity) on projects or tasks you'll create later on."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
msgid "Invoice based on timesheets (delivered quantity), and create a project for the order with a task for each sales order line to track the time spent."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
msgid "Invoice based on timesheets (delivered quantity), and create a task in an existing project to track the time spent."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
msgid "Invoice based on timesheets (delivered quantity), and create an empty project for the order to track the time spent."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form_simplified_inherit
msgid "Invoice your time and material to customers"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Invoiced"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Invoices"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__invoicing_timesheet_enabled
msgid "Invoicing Timesheet Enabled"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__is_cost_changed
msgid "Is Cost Manually Changed"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_milestones_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__line_ids
msgid "Lines"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__margin
msgid "Margin"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_costs
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_revenues
msgid "Materials"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Invoice"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Sales Order"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Sales Order Item"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid "No activities found. Let's start a new one!"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__non_billable
msgid "Non Billable Tasks"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non-Billable"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_tree_inherit
msgid "Non-billable"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Non-billable Hours"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
msgid "Number of hours spent multiplied by the unit price per hour/day."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Number of hours/days linked to a SOL."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Number of hours/days not linked to a SOL."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_count
msgid "Number of timesheets"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Only timesheets not yet invoiced (and validated, if applicable) from this period will be invoiced. If the period is not indicated, all timesheets not yet invoiced (and validated, if applicable) will be invoiced without distinction."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "Operation not supported"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Ordered,"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "Our Offer"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "Our Quality"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "Our Service"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Percentage of time delivered compared to the prepaid amount that must be reached for the upselling opportunity activity to be triggered."
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "Price"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
msgid "Product Variant"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__product_id
msgid "Product of the sales order item. Must be a service invoiced based on timesheets on tasks."
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"Product quality is the foundation we\n"
"                                stand on; we build it with a relentless\n"
"                                focus on fabric, performance and craftsmanship."
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__project_id
msgid "Project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__project_id
msgid "Project for which we are creating a sales order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Quotation"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Remaining"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Days on SO"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_report_project_task_user__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Hours on SO"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Remaining)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "Review your timesheets by billing type and make sure your time is billable."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account.py:0
msgid "Sale Order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__so_analytic_account_id
msgid "Sale Order Analytic Account"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_line_employee_ids
msgid "Sale line/Employee map"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__order_id
#: model:project.project,name:sale_timesheet.so_template_project
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order"
msgstr "გაყიდვის ორდერი"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order Item"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Sales Order Items"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "გაყიდვის ორდერის ხაზი"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet_so_button
msgid "Sales Orders"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Sales order item that will be selected by default on the timesheets of the corresponding employee. It bypasses the sales order item defined on the project and the task, and can be modified on each timesheet entry if necessary. In other words, it defines the rate at which an employee's time is billed based on their expertise, skills or experience, for instance.\n"
"If you would like to bill the same service at a different rate, you need to create two separate sales order items as each sales order item can only have a single unit price at a time.\n"
"You can also define the hourly company cost of your employees for their timesheets on this project specifically. It will bypass the timesheet cost set on the employee."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__so_line
msgid "Sales order item to which the time spent will be added in order to be invoiced to your customer. Remove the sales order item for the timesheet entry to be non-billable."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__sale_order_id
msgid "Sales order to which the project is linked."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Invoice"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order Item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__product_id
msgid "Service"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold_ratio
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold_ratio
msgid "Service Upsell Threshold Ratio"
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheets"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid "Service that will be used by default when invoicing the time spent on a task. It can be modified on each task individually by selecting a specific sales order item."
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Services"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Sold"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
msgid "Task"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task_recurrence
msgid "Task Recurrence"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr ""

#. module: sale_timesheet
#: model:project.project,label_tasks:sale_timesheet.so_template_project
msgid "Tasks"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
msgid "The %s product is required by the Timesheets app and cannot be archived nor deleted."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
msgid ""
"The Sales Order cannot be created because you did not enter some employees that entered timesheets on this project. Please list all the relevant employees before creating the Sales Order.\n"
"Missing employee(s): %s"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "The cost of the project is now at"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
msgid "The project has already a sale order."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
msgid "The project is already linked to a sales order item."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
msgid "The sales order cannot be created because some timesheets of this project are already linked to another sales order."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "The selected Sales Order should contain something to invoice."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid "The task rate is perfect if you would like to bill different services to different customers at different rates. The fixed rate is perfect if you bill a service at a fixed rate per hour or day worked regardless of the employee who performed it. The employee rate is preferable if your employees deliver the same service at a different rate. For instance, junior and senior consultants would deliver the same service (= consultancy), but at a different rate because of their level of seniority."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "This cost overrides the employee's default employee hourly wage in employee's HR Settings"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"This is a <strong>sample quotation template</strong>. You should\n"
"                                customize it to fit your own needs from the <i>Sales</i>\n"
"                                application, using the menu: Configuration /\n"
"                                Quotation Templates."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Time Billing"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
msgid "Timesheet"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_count
msgid "Timesheet activities"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order_item
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_account_move
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__timesheet_ids
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheets"
msgstr "დროის აღრიცხვა"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "Timesheets (Billed Manually)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "Timesheets (Billed on Milestones)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "Timesheets (Billed on Timesheets)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "Timesheets (Fixed Price)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "Timesheets (Non Billable)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
msgid "Timesheets Analysis"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "Timesheets for the"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "Timesheets of %s"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "Timesheets revenues"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__margin
msgid "Timesheets revenues minus the costs"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Total amount to invoice on the sales order, including all items (services, storables, expenses, ...)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid "Total recorded duration, expressed in the encoding UoM, and rounded to the unit"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Total:"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid "Track your working hours by projects every day and invoice this time to your customers."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__price_unit
msgid "Unit price of the sales order item."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "Value does not exist in the pricing type"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__warning_employee_rate
msgid "Warning Employee Rate"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"We always ensure that our products are\n"
"                                set at a fair price so that you will be\n"
"                                happy to buy them."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__wizard_id
msgid "Wizard"
msgstr ""

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"You can <strong>set a description per product</strong>. Odoo will\n"
"                        automatically create a quotation using the descriptions\n"
"                        of all products in the proposal. The table of content\n"
"                        on the left is generated automatically using the styles you\n"
"                        used in your description (heading 1, heading 2, ...)"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
msgid "You can only apply this action from a project."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "You cannot link a billable project to a sales order item that comes from an expense or a vendor bill."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
msgid "You cannot link a billable project to a sales order item that is not a service."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account.py:0
msgid "You cannot modify timesheets that are already invoiced."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account.py:0
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
msgid "days"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order.py:0
msgid "days remaining"
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
msgid "hours"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "margin ("
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order.py:0
msgid "remaining"
msgstr ""
