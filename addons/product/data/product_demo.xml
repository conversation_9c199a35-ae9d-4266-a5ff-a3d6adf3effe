<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- We want to activate product variant by default for easier demoing. -->
        <record id="base.group_user" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('product.group_product_variant'))]"/>
        </record>

        <record id="base.default_user_group" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('product.group_product_manager'))]"/>
        </record>

        <!-- Expensable products -->
        <record id="expense_product" model="product.product">
            <field name="name">Restaurant Expenses</field>
            <field name="list_price">14.0</field>
            <field name="standard_price">8.0</field>
            <field name="type">service</field>
            <field name="categ_id" eval="ref('product.product_category_expenses', raise_if_not_found=False)"/>
        </record>

        <record id="expense_hotel" model="product.product">
            <field name="name">Hotel Accommodation</field>
            <field name="list_price">400.0</field>
            <field name="standard_price">400.0</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_day"/>
            <field name="categ_id" eval="ref('product_category_expenses', raise_if_not_found=False)"/>
        </record>

        <!-- Service products -->
        <record id="product_product_1" model="product.product">
            <field name="name">Virtual Interior Design</field>
            <field name="categ_id" eval="ref('product_category_services', raise_if_not_found=False)"/>
            <field name="standard_price">20.5</field>
            <field name="list_price">30.75</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_hour"/>
        </record>

        <record id="product_product_2" model="product.product">
            <field name="name">Virtual Home Staging</field>
            <field name="categ_id" eval="ref('product_category_services', raise_if_not_found=False)"/>
            <field name="standard_price">25.5</field>
            <field name="list_price">38.25</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_hour"/>
        </record>

        <!-- Physical Products -->

        <record id="product_delivery_01" model="product.product">
            <field name="name">Office Chair</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">55.0</field>
            <field name="list_price">70.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_7777</field>
            <field name="image_1920" type="base64" file="product/static/img/product_chair.jpg"/>
        </record>

        <record id="product_delivery_02" model="product.product">
            <field name="name">Office Lamp</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">35.0</field>
            <field name="list_price">40.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_8888</field>
            <field name="image_1920" type="base64" file="product/static/img/product_lamp.png"/>
        </record>

        <record id="product_order_01" model="product.product">
            <field name="name">Office Design Software</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">235.0</field>
            <field name="list_price">280.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_9999</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_43-image.jpg"/>
        </record>

        <record id="product_product_3" model="product.product">
            <field name="name">Desk Combination</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="list_price">450.0</field>
            <field name="standard_price">300.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">Desk combination, black-brown: chair + desk + drawer.</field>
            <field name="default_code">FURN_7800</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_3-image.jpg"/>
        </record>

        <!-- Variants -->

        <record id="product_product_4_product_template" model="product.template">
            <field name="name">Customizable Desk</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">500.0</field>
            <field name="list_price">750.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">160x80cm, with large legs.</field>
        </record>

        <!-- the product template attribute lines have to be defined before creating the variants -->
        <record
            id="product_4_attribute_customization_product_template_attribute_line"
            model="product.template.attribute.line"
        >
            <field name="product_tmpl_id" ref="product_product_4_product_template"/>
            <field name="attribute_id" ref="product.product_attribute_customization"/>
            <field
                name="value_ids"
                eval="[Command.set([ref('product.pav_custom')])]"
            />
        </record>
        <record id="product_4_attribute_1_product_template_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product_product_4_product_template"/>
            <field name="attribute_id" ref="product.product_attribute_legs"/>
            <field
                name="value_ids"
                eval="[Command.set([
                    ref('product.pav_steel'),
                    ref('product.pav_aluminum'),
                    ref('product.pav_wood'),
                ])]"
            />
        </record>
        <record id="product_4_attribute_2_product_template_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product_product_4_product_template"/>
            <field name="attribute_id" ref="product.product_attribute_color"/>
            <field
                name="value_ids"
                eval="[Command.set([
                    ref('product.pav_white'),
                    ref('product.pav_black'),
                ])]"/>
        </record>

        <!--
        Handle automatically created product.template.attribute.value.
        Meaning that the combination between the "customizable desk" and the attribute value "black" will be materialized
        into a "product.template.attribute.value" with the ref "product.product_4_attribute_1_value_1".
        This will allow setting fields like "price_extra" and "exclude_for"
         -->
        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'product.product_4_attribute_1_value_1',
                'record': obj().env.ref('product.product_4_attribute_1_product_template_attribute_line').product_template_value_ids[0],
                'noupdate': True,
            }, {
                'xml_id': 'product.product_4_attribute_1_value_2',
                'record': obj().env.ref('product.product_4_attribute_1_product_template_attribute_line').product_template_value_ids[1],
                'noupdate': True,
            }, {
                'xml_id': 'product.product_4_attribute_1_value_3',
                'record': obj().env.ref('product.product_4_attribute_1_product_template_attribute_line').product_template_value_ids[2],
                'noupdate': True,
            }, {
                'xml_id': 'product.product_4_attribute_2_value_1',
                'record': obj().env.ref('product.product_4_attribute_2_product_template_attribute_line').product_template_value_ids[0],
                'noupdate': True,
            }, {
                'xml_id': 'product.product_4_attribute_2_value_2',
                'record': obj().env.ref('product.product_4_attribute_2_product_template_attribute_line').product_template_value_ids[1],
                'noupdate': True,
            },]"/>
        </function>

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'product.product_product_4',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('product.product_4_attribute_1_value_1') + obj().env.ref('product.product_4_attribute_2_value_1')),
                'noupdate': True,
            }, {
                'xml_id': 'product.product_product_4b',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('product.product_4_attribute_1_value_1') + obj().env.ref('product.product_4_attribute_2_value_2')),
                'noupdate': True,
            }, {
                'xml_id': 'product.product_product_4c',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('product.product_4_attribute_1_value_2') + obj().env.ref('product.product_4_attribute_2_value_1')),
                'noupdate': True,
            }, {
                'xml_id': 'product.product_product_4d',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('product.product_4_attribute_1_value_3') + obj().env.ref('product.product_4_attribute_2_value_1')),
                'noupdate': True,
            }, {
                'xml_id': 'product.product_product_4e',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('product.product_4_attribute_1_value_3') + obj().env.ref('product.product_4_attribute_2_value_2')),
                'noupdate': True,
            },]"/>
        </function>

        <record id="product_product_4" model="product.product">
            <field name="default_code">FURN_0096</field>
            <field name="standard_price">500.0</field>
            <field name="weight">0.01</field>
            <field name="image_1920" type="base64" file="product/static/img/table02.jpg"/>
        </record>
        <record id="product_product_4b" model="product.product">
            <field name="default_code">FURN_0097</field>
            <field name="weight">0.01</field>
            <field name="standard_price">500.0</field>
            <field name="image_1920" type="base64" file="product/static/img/table04.jpg"/>
        </record>
        <record id="product_product_4c" model="product.product">
            <field name="default_code">FURN_0098</field>
            <field name="weight">0.01</field>
            <field name="standard_price">500.0</field>
            <field name="image_1920" type="base64" file="product/static/img/table03.jpg"/>
        </record>
        <record id="product_product_4d" model="product.product">
            <field name="default_code">FURN_0099</field>
            <field name="weight">0.01</field>
            <field name="standard_price">500.0</field>
            <field name="image_1920" type="base64" file="product/static/img/table02.jpg"/>
        </record>
        <record id="product_product_4e" model="product.product">
            <field name="default_code">FURN_0100</field>
            <field name="weight">0.01</field>
            <field name="standard_price">500.0</field>
            <field name="image_1920" type="base64" file="product/static/img/table04.jpg"/>
        </record>

        <record id="product_product_5" model="product.product">
            <field name="name">Corner Desk Right Sit</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">600.0</field>
            <field name="list_price">147.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM06</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_5-image.jpg"/>
        </record>

        <function model="ir.model.data" name="_update_xmlids">
            <value
                model="base"
                eval="[{
                    'xml_id': 'product.product_template_5',
                    'record': obj().env.ref('product.product_product_5').product_tmpl_id,
                    'noupdate': True,
                }]"
            />
        </function>
        <record id="product_5_options_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product.product_template_5"/>
            <field name="attribute_id" ref="product.pa_options"/>
            <field
                name="value_ids"
                eval="[Command.set([
                    ref('product.pav_drawers'),
                    ref('product.pav_file_cabinets'),
                    ref('product.pav_under_desk_pedestals'),
                    ref('product.pav_overhead_shelves'),
            ])]"/>
        </record>

        <record id="product_product_6" model="product.product">
            <field name="name">Large Cabinet</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">800.0</field>
            <field name="list_price">320.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM07</field>
            <field name='weight'>0.330</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_6-image.jpg"/>
        </record>

        <record id="product_product_7" model="product.product">
            <field name="name">Storage Box</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">14.0</field>
            <field name="list_price">15.8</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM08</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_7-image.png"/>
        </record>

        <record id="product_product_8" model="product.product">
            <field name="name">Large Desk</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">1299.0</field>
            <field name="list_price">1799.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM09</field>
            <field name='weight'>9.54</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_8-image.jpg"/>
        </record>
        <function model="ir.model.data" name="_update_xmlids">
            <value
                model="base"
                eval="[{
                    'xml_id': 'product.product_template_8',
                    'record': obj().env.ref('product.product_product_8').product_tmpl_id,
                    'noupdate': True,
                }]"
            />
        </function>
        <record id="product_8_fabric_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product.product_template_8"/>
            <field name="attribute_id" ref="product.fabric_attribute"/>
            <field
                name="value_ids"
                eval="[Command.set([
                    ref('product.fabric_attribute_wood'),
                    ref('product.fabric_attribute_glass'),
                    ref('product.fabric_attribute_metal'),
            ])]"/>
        </record>
        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[
                {
                    'xml_id': 'product.pav_large_desk_wood',
                    'record': obj().env.ref('product.product_8_fabric_attribute_line').product_template_value_ids[0],
                    'noupdate': True,
                },
                {
                    'xml_id': 'product.pav_large_desk_glass',
                    'record': obj().env.ref('product.product_8_fabric_attribute_line').product_template_value_ids[1],
                    'noupdate': True,
                },
                {
                    'xml_id': 'product.pav_large_desk_metal',
                    'record': obj().env.ref('product.product_8_fabric_attribute_line').product_template_value_ids[2],
                    'noupdate': True,
                },
            ]"/>
        </function>

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[
                {
                    'xml_id': 'product.product_product_8',
                    'record': obj().env.ref('product.product_template_8')._get_variant_for_combination(obj().env.ref('product.pav_large_desk_wood')),
                    'noupdate': True,
                },
                {
                    'xml_id': 'product.product_product_8_glass',
                    'record': obj().env.ref('product.product_template_8')._get_variant_for_combination(obj().env.ref('product.pav_large_desk_glass')),
                    'noupdate': True,
                },
                {
                    'xml_id': 'product.product_product_8_metal',
                    'record': obj().env.ref('product.product_template_8')._get_variant_for_combination(obj().env.ref('product.pav_large_desk_metal')),
                    'noupdate': True,
                },
            ]"/>
        </function>

        <record id="product_product_9" model="product.product">
            <field name="name">Pedal Bin</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">10.0</field>
            <field name="list_price">47.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM10</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_9-image.jpg"/>
        </record>

        <record id="product_product_10" model="product.product">
            <field name="name">Cabinet with Doors</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">120.50</field>
            <field name="list_price">140</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM11</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_10-image.jpg"/>
        </record>

        <record id="product_product_11_product_template" model="product.template">
            <field name="name">Conference Chair</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">28</field>
            <field name="list_price">33</field>
            <field name="type">consu</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="image_1920" type="base64" file="product/static/img/product_product_11-image.jpg"/>
        </record>

        <!-- the product template attribute lines have to be defined before creating the variants -->
        <record id="product_11_attribute_1_product_template_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product_product_11_product_template"/>
            <field name="attribute_id" ref="product_attribute_legs"/>
            <field
                name="value_ids"
                eval="[Command.set([
                    ref('product.pav_steel'),
                    ref('product.pav_aluminum'),
                ])]"
            />
        </record>

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'product.product_11_attribute_1_value_1',
                'record': obj().env.ref('product.product_11_attribute_1_product_template_attribute_line').product_template_value_ids[0],
                'noupdate': True,
            }, {
                'xml_id': 'product.product_11_attribute_1_value_2',
                'record': obj().env.ref('product.product_11_attribute_1_product_template_attribute_line').product_template_value_ids[1],
                'noupdate': True,
            }]"/>
        </function>

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'product.product_product_11',
                'record': obj().env.ref('product.product_product_11_product_template')._get_variant_for_combination(obj().env.ref('product.product_11_attribute_1_value_1')),
                'noupdate': True,
            }, {
                'xml_id': 'product.product_product_11b',
                'record': obj().env.ref('product.product_product_11_product_template')._get_variant_for_combination(obj().env.ref('product.product_11_attribute_1_value_2')),
                'noupdate': True,
            },]"/>
        </function>

        <record id="product_product_11" model="product.product">
            <field name="default_code">E-COM12</field>
            <field name="weight">0.01</field>
        </record>
        <record id="product_product_11b" model="product.product">
            <field name="default_code">E-COM13</field>
            <field name="weight">0.01</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_11b-image.jpg"/>
        </record>

        <record id="product.product_4_attribute_1_value_2" model="product.template.attribute.value">
            <field name="price_extra">50.40</field>
        </record>

        <record id="product.product_11_attribute_1_value_2" model="product.template.attribute.value">
            <field name="price_extra">6.40</field>
        </record>

        <record id="product_template_attribute_exclusion_1" model="product.template.attribute.exclusion">
            <field name="product_tmpl_id" ref="product.product_product_4_product_template" />
            <field name="value_ids" eval="[(6, 0, [ref('product.product_4_attribute_2_value_2')])]"/>
        </record>
        <record id="product_template_attribute_exclusion_2" model="product.template.attribute.exclusion">
            <field name="product_tmpl_id" ref="product.product_product_11_product_template" />
            <field name="value_ids" eval="[(6, 0, [ref('product.product_11_attribute_1_value_1')])]"/>
        </record>
        <record id="product_template_attribute_exclusion_3" model="product.template.attribute.exclusion">
            <field name="product_tmpl_id" ref="product.product_product_11_product_template" />
            <field name="value_ids" eval="[(6, 0, [ref('product.product_11_attribute_1_value_2')])]"/>
        </record>

        <!--
        The "Customizable Desk's Aluminium" attribute value will excude:
        - The "Customizable Desk's Black" attribute
        - The "Office Chair's Steel" attribute
         -->
        <record id="product_4_attribute_1_value_2" model="product.template.attribute.value">
            <field name="exclude_for" eval="[(6, 0, [ref('product.product_template_attribute_exclusion_1'), ref('product.product_template_attribute_exclusion_2')])]" />
        </record>
        <!--
        The "Customizable Desk's Steel" attribute value will excude:
        - The "Office Chair's Aluminium" attribute
        -->
        <record id="product_4_attribute_1_value_1" model="product.template.attribute.value">
            <field name="exclude_for" eval="[(6, 0, [ref('product.product_template_attribute_exclusion_3')])]" />
        </record>

        <!-- MRP Demo Data-->

        <record id="product_product_12" model="product.product">
            <field name="name">Office Chair Black</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">180</field>
            <field name="list_price">120.50</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_0269</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_12-image.jpg"/>
        </record>

        <record id="product_product_13" model="product.product">
            <field name="name">Corner Desk Left Sit</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">78.0</field>
            <field name="list_price">85.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_1118</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_13-image.jpg"/>
        </record>
        <function model="ir.model.data" name="_update_xmlids">
            <value
                model="base"
                eval="[{
                    'xml_id': 'product.product_template_13',
                    'record': obj().env.ref('product.product_product_13').product_tmpl_id,
                    'noupdate': True,
                }]"
            />
        </function>
        <record id="product_13_options_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product.product_template_13"/>
            <field name="attribute_id" ref="product.pa_options"/>
            <field
                name="value_ids"
                eval="[Command.set([
                    ref('product.pav_drawers'),
                    ref('product.pav_file_cabinets'),
                    ref('product.pav_under_desk_pedestals'),
                    ref('product.pav_overhead_shelves'),
            ])]"/>
        </record>

        <record id="product_product_16" model="product.product">
            <field name="name">Drawer Black</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">20.0</field>
            <field name="list_price">25.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_8900</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_16-image.jpg"/>
        </record>

        <record id="product_product_20" model="product.product">
            <field name="name">Flipover</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">1700.0</field>
            <field name="list_price">1950.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_9001</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_20-image.png"/>
        </record>
        <record id="product_product_22" model="product.product">
            <field name="name">Desk Stand with Screen</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">2010.0</field>
            <field name="list_price">2100.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_7888</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_22-image.png"/>
        </record>

        <record id="product_product_24" model="product.product">
            <field name="name">Individual Workplace</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">876.0</field>
            <field name="list_price">885.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_0789</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_24-image.jpg"/>
        </record>

        <record id="product_template_acoustic_bloc_screens" model="product.template">
            <field name="name">Acoustic Bloc Screens</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">287.0</field>
            <field name="list_price">295.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="image_1920" type="base64" file="product/static/img/product_product_25-image.png"/>
            <field name="attribute_line_ids" eval="[
                Command.create({
                    'attribute_id': ref('product.product_attribute_color'),
                    'value_ids': [
                        Command.set([
                            ref('product.pav_white'),
                            ref('product.pav_black')
                        ])
                    ]
                })
            ]"/>
        </record>
        <record
            id="product_acoustic_bloc_screens_customization_attribute_line"
            model="product.template.attribute.line"
        >
            <field name="product_tmpl_id" ref="product_template_acoustic_bloc_screens"/>
            <field name="attribute_id" ref="product.product_attribute_customization"/>
            <field
                name="value_ids"
                eval="[Command.set([ref('product.pav_custom')])]"
            />
        </record>

        <record id="product_product_27" model="product.product">
            <field name="name">Drawer</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">100.0</field>
            <field name="list_price">110.50</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="description">Drawer with two routing possiblities.</field>
            <field name="default_code">FURN_8855</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_27-image.jpg"/>
        </record>

        <record id="consu_delivery_03" model="product.product">
            <field name="name">Four Person Desk</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">2500.0</field>
            <field name="list_price">2350.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">Four person modern office workstation</field>
            <field name="default_code">FURN_8220</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_d03-image.png"/>
        </record>

        <record id="consu_delivery_02" model="product.product">
            <field name="name">Large Meeting Table</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">4500.0</field>
            <field name="list_price">4000.0</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">Conference room table</field>
            <field name="default_code">FURN_6741</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_46-image.jpg"/>
        </record>

        <record id="consu_delivery_01" model="product.product">
            <field name="name">Two-Seat Sofa</field>
            <field name="categ_id" ref="product_category_office"/>
            <field name="standard_price">1000</field>
            <field name="list_price">1500</field>
            <field name="type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">Two-Seater Sofa with Oak Wood Frame</field>
            <field name="default_code">FURN_8999</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_d01-image.jpg"/>
        </record>
        <function model="ir.model.data" name="_update_xmlids">
            <value
                model="base"
                eval="[{
                    'xml_id': 'product.sofa_product_template',
                    'record': obj().env.ref('product.consu_delivery_01').product_tmpl_id,
                    'noupdate': True,
                }]"
            />
        </function>
        <record id="sofa_fabric_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product.sofa_product_template"/>
            <field name="attribute_id" ref="product.fabric_attribute"/>
            <field
                name="value_ids"
                eval="[Command.set([
                    ref('product.fabric_attribute_linen'),
                    ref('product.fabric_attribute_velvet'),
                    ref('product.fabric_attribute_leather'),
            ])]"/>
        </record>
        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[
                {
                    'xml_id': 'product.pav_sofa_linen',
                    'record': obj().env.ref('product.sofa_fabric_attribute_line').product_template_value_ids[0],
                    'noupdate': True,
                },
                {
                    'xml_id': 'product.pav_sofa_velvet',
                    'record': obj().env.ref('product.sofa_fabric_attribute_line').product_template_value_ids[1],
                    'noupdate': True,
                },
                {
                    'xml_id': 'product.pav_sofa_leather',
                    'record': obj().env.ref('product.sofa_fabric_attribute_line').product_template_value_ids[2],
                    'noupdate': True,
                },
            ]"/>
        </function>
        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[
                {
                    'xml_id': 'product.consu_delivery_01',
                    'record': obj().env.ref('product.sofa_product_template')._get_variant_for_combination(obj().env.ref('product.pav_sofa_linen')),
                    'noupdate': True,
                },
                {
                    'xml_id': 'product.consu_delivery_01_velvet',
                    'record': obj().env.ref('product.sofa_product_template')._get_variant_for_combination(obj().env.ref('product.pav_sofa_velvet')),
                    'noupdate': True,
                },
                {
                    'xml_id': 'product.consu_delivery_01_leather',
                    'record': obj().env.ref('product.sofa_product_template')._get_variant_for_combination(obj().env.ref('product.pav_sofa_leather')),
                    'noupdate': True,
                },
            ]"/>
        </function>
        <record id="consu_delivery_01_velvet" model="product.product">
            <field
                name="image_1920"
                type="base64"
                file="product/static/img/product_product_d01b-image.jpg"
            />
        </record>
        <record id="consu_delivery_01_leather" model="product.product">
            <field
                name="image_1920"
                type="base64"
                file="product/static/img/product_product_d01c-image.jpg"
            />
        </record>

        <record id="product_product_local_delivery" model="product.product">
            <field name="name">Local Delivery</field>
            <field name="default_code">Delivery_010</field>
            <field name="type">service</field>
            <field name="categ_id" eval="ref('product_category_services', raise_if_not_found=False)"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="list_price">10.0</field>
        </record>

        <record id="product_product_furniture" model="product.product">
            <field name="name">Furniture Assembly</field>
            <field name="categ_id" ref="product.product_category_construction"/>
            <field name="list_price">2000.00</field>
            <field name="standard_price">2500.00</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_hour"/>
        </record>

        <record id="product_template_dining_table" model="product.template">
            <field name="name">Outdoor dining table</field>
            <field name="categ_id" ref="product_category_outdoor_furniture"/>
            <field name="list_price">589</field>
            <field name="image_1920" type="base64" file="product/static/img/dining_table.png"/>
        </record>

        <record id="desk_organizer" model="product.product">
            <field name="list_price">5.10</field>
            <field name="name">Desk Organizer</field>
            <field name="description_sale">The desk organiser is perfect for storing all kinds of small things and since the 5 boxes are loose, you can move and place them in the way that suits you and your things best.</field>
            <field name="default_code">FURN_0001</field>
            <field name="barcode">2300001000008</field>
            <field name="weight">0.01</field>
            <field name="categ_id" ref="product.product_category_office"/>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="image_1920" type="base64" file="product/static/img/desk_organizer.jpg"/>
        </record>
        <function model="ir.model.data" name="_update_xmlids">
            <value
                model="base"
                eval="[{
                    'xml_id': 'product.desk_organizer_product_template',
                    'record': obj().env.ref('product.desk_organizer').product_tmpl_id,
                    'noupdate': True,
                }]"
            />
        </function>

        <record id="desk_pad" model="product.product">
            <field name="list_price">1.98</field>
            <field name="name">Desk Pad</field>
            <field name="default_code">FURN_0002</field>
            <field name="weight">0.01</field>
            <field name="categ_id" ref="product.product_category_office"/>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="image_1920" type="base64" file="product/static/img/desk_pad.jpg"/>
        </record>

        <record id="monitor_stand" model="product.product">
            <field name="list_price">3.19</field>
            <field name="name">Monitor Stand</field>
            <field name="default_code">FURN_0006</field>
            <field name="weight">0.01</field>
            <field name="categ_id" ref="product.product_category_office"/>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="image_1920" type="base64" file="product/static/img/monitor_stand.jpg"/>
        </record>

        <record id="desk_accessories_combo" model="product.combo">
            <field name="name">Desk Accessories Combo</field>
            <field
                name="combo_item_ids"
                eval="[
                    Command.clear(),
                    Command.create({
                        'product_id': ref('product.desk_organizer'),
                        'extra_price': 0,
                    }),
                    Command.create({
                        'product_id': ref('product.desk_pad'),
                        'extra_price': 0,
                    }),
                    Command.create({
                        'product_id': ref('product.monitor_stand'),
                        'extra_price': 2,
                    }),
                ]"
            />
        </record>

        <record id="desks_combo" model="product.combo">
            <field name="name">Desks Combo</field>
            <field
                name="combo_item_ids"
                eval="[
                    Command.clear(),
                    Command.create({
                        'product_id': ref('product.product_product_3'),
                        'extra_price': 0,
                    }),
                    Command.create({
                        'product_id': ref('product.product_product_5'),
                        'extra_price': 0,
                    }),
                ]"
            />
        </record>

        <record id="chairs_combo" model="product.combo">
            <field name="name">Chairs Combo</field>
            <field
                name="combo_item_ids"
                eval="[
                    Command.clear(),
                    Command.create({
                        'product_id': ref('product.product_product_11'),
                        'extra_price': 0,
                    }),
                    Command.create({
                        'product_id': ref('product.product_product_11b'),
                        'extra_price': 0,
                    }),
                    Command.create({
                        'product_id': ref('product.product_product_12'),
                        'extra_price': 0,
                    }),
                ]"
            />
        </record>

        <record id="office_combo" model="product.product">
            <field name="list_price">160</field>
            <field name="name">Office Combo</field>
            <field name="type">combo</field>
            <field name="purchase_ok">False</field>
            <field name="categ_id" ref="product.product_category_office"/>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="image_1920" type="base64" file="product/static/img/office_combo.jpg"/>
            <field
                name="combo_ids"
                eval="[(6, 0, [
                    ref('desks_combo'),
                    ref('chairs_combo'),
                    ref('desk_accessories_combo'),
                ])]"
            />
        </record>

        <function model="product.template" name="_demo_configure_variants"/>
    </data>
</odoo>
