<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="project_update_view_search" model="ir.ui.view">
        <field name="name">project.update.view.search</field>
        <field name="model">project.update</field>
        <field name="arch" type="xml">
            <search string="Search Update">
                <field name="name"/>
                <field name="project_id" invisible="1"/>
                <field name="user_id"/>
                <field name="description"/>
                <field name="status"/>
                <filter string="My Updates" name="my_updates" domain="[('user_id', '=', uid)]"/>
                <separator/>
                <filter string="On Track" name="on_track" domain="[('status', '=', 'on_track')]"/>
                <filter string="At Risk" name="at_risk" domain="[('status', '=', 'at_risk')]"/>
                <filter string="Off Track" name="off_track" domain="[('status', '=', 'off_track')]"/>
                <filter string="On Hold" name="on_hold" domain="[('status', '=', 'on_hold')]"/>
                <separator/>
                <filter name="date" string="Date" date="date"/>
            </search>
        </field>
    </record>

    <record id="project_update_view_form" model="ir.ui.view">
        <field name="name">project.update.view.form</field>
        <field name="model">project.update</field>
        <field name="arch" type="xml">
            <form string="Project Update" js_class="form_description_expander">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" class="o_text_overflow" placeholder="e.g. Monthly review"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="project_id" invisible="1"/>
                            <field name="color" invisible="1"/>
                            <field name="status" widget="status_with_color"/>
                            <field name="progress" widget="progressbar" options="{'editable': true}"/>
                        </group>
                        <group>
                            <field name="user_id" widget="many2one_avatar_user" readonly="1"/>
                            <field name="date"/>
                        </group>
                    </group>
                    <separator/>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" nolabel="1" class="o_project_update_description" options="{'resizable': false, 'collaborative': true}"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter class="d-print-none" reload_on_follower="True"/>
            </form>
        </field>
    </record>

    <record id="project_update_view_kanban" model="ir.ui.view">
        <field name="name">project.update.view.kanban</field>
        <field name="model">project.update</field>
        <field name="arch" type="xml">
            <kanban highlight_color="color" sample="0" js_class="project_update_kanban">
                <field name="color"/>
                <templates>
                    <t t-name="card" class="o_project_update_card column-gap-0 column-gap-md-3 row-gap-2 flex-row flex-wrap align-items-center px-3">
                        <div class="flex-grow-1 flex-shrink-0 w-50 w-md-auto px-2 px-md-0">
                            <field name="name_cropped" class="fw-bolder"/>
                            <div class="d-flex gap-1">
                                <field name="user_id" widget="many2one_avatar_user"/>
                                <field name="user_id"/>
                            </div>
                        </div>
                        <div class="flex-grow-1 flex-shrink-0 w-50 w-md-auto px-2 px-md-0">
                            <span>Status</span>
                            <field name="color" invisible="1"/>
                            <field name="status" class="fw-bolder" readonly="1" widget="status_with_color"/>
                        </div>
                        <div class="flex-grow-1 flex-shrink-0 w-50 w-md-auto px-2 px-md-0">
                            <span>Progress</span>
                            <field name="progress_percentage" class="fw-bolder" widget="percentage"/>
                        </div>
                        <div class="flex-grow-1 flex-shrink-0 w-50 w-md-auto px-2 px-md-0">
                            <div id="tasks_stats" class="fw-bolder">
                                <span invisible="not task_count">
                                    <field name="closed_task_count"/> / <field name="task_count"/> <field name="label_tasks"/> (<field name="closed_task_percentage"/>%)
                                </span>
                                <span invisible="task_count">0 <field name="label_tasks"/></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 flex-shrink-0 w-50 w-md-auto px-2 px-md-0">
                            <div>Date</div>
                            <field name="date" class="fw-bolder"/>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="project_update_view_tree" model="ir.ui.view">
        <field name="name">project.update.view.list</field>
        <field name="model">project.update</field>
        <field name="arch" type="xml">
            <list sample="1" js_class="project_update_list">
                <field name="name"/>
                <field name="user_id" widget="many2one_avatar_user" class="fw-bolder" optional="show"/>
                <field name="date" optional="show"/>
                <field name="progress" string="Progress" widget="progressbar" optional="show"/>
                <field name="color" column_invisible="True"/>
                <field name="status" widget="status_with_color"/>
            </list>
        </field>
    </record>

    <record id="project_update_all_action" model="ir.actions.act_window">
        <field name="name">Dashboard</field>
        <field name="res_model">project.update</field>
        <field name="path">project-dashboard</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="domain">[('project_id', '=', active_id)]</field>
        <field name="search_view_id" ref="project_update_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            No updates found. Let's create one!
            </p><p>
            Get a snapshot of the status of your project and share its progress with key stakeholders.
            </p>
        </field>
    </record>
</odoo>
