<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-inherit="mail.ImStatus" t-inherit-mode="extension">
        <xpath expr="//*[@name='icon']" position="replace">
            <t t-if="persona.im_status">
                <t t-if="persona.im_status.split('_').length == 2">
                    <t t-set="location" t-value="persona.im_status.split('_')[0]"/>
                    <t t-if="location == 'home' || location == 'office' || location == 'other'">
                        <t t-set="status" t-value="persona.im_status.split('_')[1]"/>
                        <t t-set="location_map" t-value="{'home': 'fa-home', 'office': 'fa-building', 'other': 'fa-map-marker'}"/>
                        <t t-set="status_map" t-value="{'online': 'text-success', 'away': 'o-away', 'offline': 'text-body'}"/>
                        <t t-set="icon" t-value="location_map[location]"/>
                        <t t-set="text" t-value="status_map[status]"/>
                        <i class="fa" t-attf-class="{{icon}} {{text}}" t-attf-title="At {{location}} - {{status}}" role="img" t-attf-aria-label=" User work at {{location}} and is {{status}}"/>
                    </t>
                    <t t-else="">$0</t>
                </t>
                <t t-else="">$0</t>
            </t>
            <t t-else="">$0</t>
        </xpath>
    </t>

    <t t-inherit="mail.ThreadIcon" t-inherit-mode="extension">
        <xpath expr="//*[@name='chat_static']" position="replace">
            <t t-if="correspondent.persona.im_status">
                <t t-if="correspondent.persona.im_status.split('_').length >= 2">
                    <t t-set="location" t-value="correspondent.persona.im_status.split('_')[0]"/>
                    <t t-if="location == 'home' || location == 'office' || location == 'other'">
                        <t t-set="status" t-value="correspondent.persona.im_status.split('_')[1]"/>
                        <t t-set="location_map" t-value="{'home': 'fa-home', 'office': 'fa-building', 'other': 'fa-map-marker'}"/>
                        <t t-set="status_map" t-value="{'online': 'text-success', 'away': 'o-away', 'offline': 'text-body'}"/>
                        <t t-set="icon" t-value="location_map[location]"/>
                        <t t-set="text" t-value="status_map[status]"/>
                        <i class="fa" t-attf-class="{{icon}} {{text}}" t-attf-title="At {{location}} - {{status}}" role="img" t-attf-aria-label=" User work at {{location}} and is {{status}}"/>
                    </t>
                    <t t-else="">$0</t>
                </t>
                <t t-else="">$0</t>
            </t>
            <t t-else="">$0</t>
        </xpath>
    </t>
</templates>
