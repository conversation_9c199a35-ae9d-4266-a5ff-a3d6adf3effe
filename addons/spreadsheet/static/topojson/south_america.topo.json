{"type": "Topology", "arcs": [[[425, 6718], [-40, -9], [0, -41], [23, -15], [-16, -13], [4, -18], [-10, -22], [-5, -20]], [[381, 6580], [-56, 23], [-21, 21], [12, 18], [-4, 23], [-28, 25], [-41, 20], [-35, 13], [-7, 30], [-28, 19], [7, -30], [-20, -25], [-24, 29], [-33, 10], [-15, 21], [2, 31], [13, 33], [-29, 15], [24, 19]], [[98, 6875], [16, 13], [67, -27], [24, 13], [33, -8], [17, -21], [31, -7], [24, 22]], [[310, 6860], [26, -57], [40, -41], [49, -44]], [[98, 6875], [-36, 33], [-49, 41], [-13, 20], [306, 0], [-3, -11], [8, -27], [-21, -27], [4, -27], [16, -17]], [[962, 6626], [-13, -15], [24, -61], [-19, -31], [-33, 8], [-13, -50]], [[908, 6477], [-34, 30], [-23, 55], [25, 28], [-26, 7], [-19, 33], [-51, 30], [-45, -7], [-22, -36], [-42, -26], [-23, -4], [-10, -21], [50, -56], [-29, -13], [-14, -16], [-48, -5], [-19, 62], [-13, -18], [-34, 6], [-20, 42], [-43, 7], [-27, 12], [-44, -1], [-4, -22], [-12, 16]], [[425, 6718], [37, -37], [-2, -21], [42, -6], [9, 9], [29, -26], [50, 8], [44, 26], [63, 21], [35, 31], [58, -6], [-4, -11], [57, -3], [46, -18], [34, -31], [39, -28]], [[2583, 6841], [59, 14], [23, -4], [-4, -77], [-87, -11], [-19, 10], [30, 28], [-2, 40]], [[1864, 306], [0, -229], [111, 0], [62, -4]], [[2037, 73], [-34, -41], [-89, -32], [-50, 4], [-62, 7], [-74, 31], [-109, 15], [-131, 58], [-105, 55], [-143, 116], [85, -22], [146, -69], [137, -36], [53, 47], [33, 70], [96, 43], [74, -13]], [[1765, 3920], [51, -70], [13, -74], [55, -44], [-33, -99], [55, -116], [41, -142], [74, 14]], [[2021, 3389], [14, -27], [-36, -106], [-113, -51], [3, -172], [-21, -33], [32, -40], [-75, -65], [-67, -97], [-37, -93], [10, -100], [-63, -106], [48, -177], [26, -20], [0, -94], [-60, -101], [3, -86], [-78, -67], [0, -95], [32, -101], [-62, -37], [-28, -92], [-24, -105], [17, -127], [-42, -20], [25, -119], [46, -39], [-34, -43], [48, -21], [11, -39], [-45, -20], [11, -60], [-37, -136], [-56, -89], [12, -52], [-33, -65], [-78, -45], [8, -110], [37, -37], [69, 6], [-3, -76], [44, -60], [250, -14], [95, -16]], [[1870, 342], [-92, 1], [-49, -26], [-94, -37], [-16, -96], [-44, -3], [-116, 34], [-119, 71], [-129, 59], [-33, 65], [31, 61], [-53, 68], [-14, 176], [44, 99], [110, 80], [-158, 30], [100, 91], [34, 171], [115, -36], [55, 214], [-69, 27], [-33, -129], [-65, 14], [31, 148], [36, 191], [48, 70], [-31, 101], [-7, 116], [43, 4], [64, 166], [71, 165], [44, 154], [-24, 154], [31, 85], [-12, 127], [61, 126], [18, 200], [33, 213], [32, 231], [-7, 169], [-22, 145]], [[1684, 3841], [53, 26], [28, 53]], [[1771, 4604], [77, -9], [53, 2], [24, 31], [90, 42], [54, 39], [136, 17], [-11, -77], [13, -40], [-8, -69], [111, -93], [117, -16], [41, -39], [68, -21], [44, -30], [65, 1], [60, -30], [5, -60], [20, -30], [1, -44], [-30, -2], [39, -120], [199, -4], [-15, -59], [11, -41], [57, -29], [24, -64], [-19, -82], [-28, -45], [10, -58], [-32, -22]], [[2947, 3652], [-1, 32], [-97, 53], [-97, 2], [-180, -31], [-49, -90], [-2, -56], [-41, -123]], [[2480, 3439], [-18, 22], [-118, 4], [-39, -83], [-61, 75], [-135, 25], [-88, -93]], [[1765, 3920], [66, 112], [-45, 86], [24, 35], [-19, 37], [40, 53], [2, 87], [6, 72], [22, 36], [-90, 166]], [[1734, 5290], [-94, 4], [-13, -16], [-85, -19], [-119, -70], [-7, -49], [-26, -36], [10, -55], [-63, -30], [0, -44], [-27, -18], [43, -93], [58, -63], [-22, -44], [68, -6], [40, -56], [91, -2], [85, 61], [-8, -157], [48, -11], [58, 18]], [[1684, 3841], [-103, 59], [-10, 42], [-205, 104], [-185, 113], [-80, 63], [-43, 85], [17, 30], [-88, 135], [-102, 191], [-97, 205], [-43, 46], [-32, 76], [-80, 68], [-75, 42], [35, 46], [-50, 98], [31, 72], [82, 65]], [[656, 5381], [14, -43], [-31, -25], [4, -37], [43, 8], [41, -11], [43, -52], [59, 42], [19, 70], [64, 90], [124, 40], [113, 108], [32, 67], [-14, 79]], [[1167, 5717], [27, 9], [69, -49], [33, -49], [48, -26], [60, -108], [79, -13], [57, 27], [37, -17], [62, 9], [79, -49], [-67, -105], [31, -2], [52, -54]], [[1864, 306], [40, -47], [51, -77], [135, -62], [145, -26], [-47, -52], [-98, -5], [-53, 36]], [[3002, 2617], [-25, -82], [-27, -106], [1, -103], [-23, -23], [-8, -66]], [[2920, 2237], [-8, -54], [132, -88], [-14, -71], [65, -45], [-5, -50], [-99, -133], [-154, -55], [-208, -21], [-114, 10], [23, -62], [-21, -76], [18, -52], [-62, -37], [-106, -14], [-99, 37], [-40, -26], [15, -103], [68, -31], [57, 32], [31, -53], [-94, -32], [-84, -64], [-16, -104], [-23, -55], [-98, -1], [-81, -52], [-31, -77], [102, -75], [100, -22], [-36, -92], [-122, -59], [-67, -121], [-94, -40], [-43, -48], [33, -108], [69, -59], [-44, 6]], [[2480, 3439], [190, -168], [84, -16], [127, -76], [106, -41], [15, -45], [-101, -157], [103, -27], [116, -16], [82, 17], [94, 78], [18, 91]], [[3314, 3079], [50, 19], [52, -59], [-2, -82], [-87, -57], [-69, -42], [-118, -100], [-138, -141]], [[3323, 5970], [-58, 22], [-49, -10], [-43, 8], [-10, -29], [18, -21], [-9, -20], [-57, 8]], [[3115, 5928], [-64, 90], [-13, 58], [-33, 0], [-46, 75], [20, 53], [-6, 24], [63, 27], [17, 93]], [[3053, 6348], [123, -21], [12, 18], [83, 8], [111, -27]], [[3382, 6326], [-54, -89], [8, -71], [41, -61], [-18, -44], [-10, -48], [-26, -43]], [[3115, 5928], [-25, -4], [-57, 9], [-35, -27], [-46, -19], [-33, -4], [-11, -21], [-51, 6], [-64, 48], [-6, 48], [-27, 52], [16, 87], [29, 37], [-24, 48], [-35, 16], [13, 45], [-24, 24], [-54, -4]], [[2681, 6269], [-70, 77], [29, 28], [-3, 48], [64, 17], [26, 19], [-36, 38], [10, 38], [82, 60]], [[2783, 6594], [67, -38], [64, -67], [2, -53], [40, -2], [56, -51], [41, -35]], [[3442, 2251], [-28, 59], [45, 49], [-59, 69], [-81, 58], [-107, 66], [-39, -3], [-103, 80], [-68, -12]], [[3314, 3079], [19, 59], [15, 61], [0, 58], [-38, 18], [-39, -17], [-38, 5], [-12, 39], [-9, 95], [-20, 31], [-70, 28], [-43, -20], [-108, 19], [6, 140], [-30, 57]], [[1734, 5290], [46, 282], [3, 44], [-17, 59], [-45, 38], [0, 75], [58, 17], [21, -11], [3, 39], [-60, 12], [-1, 64], [201, -2], [35, 35], [28, -33], [21, -61], [18, 14]], [[2045, 5862], [57, -55], [81, 7], [20, 31], [77, 24], [43, 17], [12, 44], [73, 29], [-5, 22], [-88, 9], [-14, 64], [4, 70], [-47, 26], [20, 10], [77, -13], [82, -26], [31, 24], [74, 16], [115, 39], [38, 39], [-14, 30]], [[3323, 5970], [46, -21], [32, 28], [22, -4], [15, -29], [49, 7], [40, 39], [32, 76], [60, 95]], [[3619, 6161], [36, 4], [25, -56], [59, -181], [55, -17], [3, -71], [-78, -85], [33, -31], [183, -16], [3, -103], [79, 67], [129, -36], [172, -64], [51, -60], [-17, -57], [120, 31], [201, -54], [155, 4], [152, -86], [133, -115], [79, -29], [89, -5], [38, -32], [35, -131], [17, -63], [-41, -170], [-53, -67], [-146, -144], [-66, -117], [-77, -89], [-25, -2], [-29, -75], [8, -194], [-29, -159], [-11, -68], [-33, -40], [-18, -138], [-105, -134], [-18, -107], [-84, -45], [-24, -62], [-112, 1], [-163, -40], [-73, -46], [-115, -30], [-122, -82], [-87, -102], [-16, -77], [17, -57], [-18, -104], [-24, -51], [-73, -56], [-114, -181], [-91, -82], [-70, -49], [-48, -98], [-69, -59]], [[3442, 2251], [-44, -64], [-118, -58], [-75, 21], [-57, -11], [-95, 44], [-70, -3], [-63, 57]], [[656, 5381], [56, 77], [-22, 45], [-40, -48], [-62, 45], [21, 30], [-17, 93], [35, 16], [20, 64], [39, 66], [-7, 42], [57, 22], [70, 42]], [[806, 5875], [105, -59], [18, 1], [25, -44], [89, -15], [29, 17], [50, -34], [45, -24]], [[806, 5875], [-13, 32], [38, 8], [-4, 51], [24, 37], [52, 7], [43, 65], [39, 54], [-37, 25], [18, 59], [-22, 94], [22, 28], [-17, 87], [-41, 55]], [[962, 6626], [53, -4], [78, 73], [43, 10], [1, 35], [19, 86], [59, 48], [65, 3], [8, 21], [82, -9], [81, 52], [40, 23], [4, 5], [103, 0], [-13, -23]], [[1585, 6946], [-67, -17], [-26, -52], [-40, -29], [-30, -38], [-12, -75], [-29, -60], [53, -7], [13, -47], [24, -22], [8, -42], [-12, -38], [2, -22], [27, -9], [24, -35], [133, 10], [60, -14], [73, -88], [42, 11], [74, -6], [59, 12], [37, -18], [-19, -55], [-22, -35], [-10, -74], [21, -68], [30, -31], [4, -23], [-53, -51], [38, -23], [28, -36], [30, -102]], [[1585, 6946], [-3, -24], [-61, -13], [34, -46], [-1, -54], [-46, -60], [39, -81], [45, 6], [23, 75], [-32, 36], [-5, 78], [128, 42], [-14, 49], [18, 15], [27, 0], [29, -56], [73, -1], [66, -58], [4, -34], [94, -1], [110, 11], [59, -47], [80, -12], [57, 32], [1, 26], [128, 6], [125, 2], [-89, -31], [36, -48], [83, -8], [78, -51], [16, -83], [55, 3], [41, -25]], [[2633, 388], [125, 61], [87, -25], [62, 41], [82, -47], [-30, -36], [-139, -31], [-48, 37], [-87, -46], [-52, 46]], [[3382, 6326], [35, -12], [77, -24], [110, -88], [15, -41]]], "transform": {"scale": [0.009667402309057395, 0.009701797962404935], "translate": [-86.65394808957126, -55.61183]}, "objects": {"countries": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2, 3]], "type": "Polygon", "properties": {"name": "Costa Rica"}, "id": "CR"}, {"arcs": [[-3, 4]], "type": "Polygon", "properties": {"name": "Nicaragua"}, "id": "NI"}, {"arcs": [[5, 6, -1, 7]], "type": "Polygon", "properties": {"name": "Panama"}, "id": "PA"}, {"arcs": [[8]], "type": "Polygon", "properties": {"name": "Trinidad and Tobago"}, "id": "TT"}, {"arcs": [[[9, 10]], [[11, 12, 13, 14]]], "type": "MultiPolygon", "properties": {"name": "Chile"}, "id": "CL"}, {"arcs": [[15, 16, 17, -12, 18]], "type": "Polygon", "properties": {"name": "Bolivia"}, "id": "BO"}, {"arcs": [[19, -19, -15, 20, 21, 22]], "type": "Polygon", "properties": {"name": "Peru"}, "id": "PE"}, {"arcs": [[[23, -10]], [[24, 25, -13, -18, 26, 27]]], "type": "MultiPolygon", "properties": {"name": "Argentina"}, "id": "AR"}, {"arcs": [[28, 29, 30, 31]], "type": "Polygon", "properties": {"name": "Suriname"}, "id": "SR"}, {"arcs": [[32, 33, 34, -30]], "type": "Polygon", "properties": {"name": "Guyana"}, "id": "GY"}, {"arcs": [[35, -28, 36, -16, -20, 37, 38, -33, -29, 39, 40]], "type": "Polygon", "properties": {"name": "Brazil"}, "id": "BR"}, {"arcs": [[-36, 41, -25]], "type": "Polygon", "properties": {"name": "Uruguay"}, "id": "UY"}, {"arcs": [[-22, 42, 43]], "type": "Polygon", "properties": {"name": "Ecuador"}, "id": "EC"}, {"arcs": [[-38, -23, -44, 44, -6, 45, 46]], "type": "Polygon", "properties": {"name": "Colombia"}, "id": "CO"}, {"arcs": [[-37, -27, -17]], "type": "Polygon", "properties": {"name": "Paraguay"}, "id": "PY"}, {"arcs": [[-39, -47, 47, -34]], "type": "Polygon", "properties": {"name": "Venezuela"}, "id": "VE"}, {"arcs": [[48]], "type": "Polygon", "properties": {"name": "Falkland Is."}, "id": "FK"}, {"arcs": [[-40, -32, 49]], "type": "Polygon", "properties": {"name": "France"}, "id": "FR"}]}}}