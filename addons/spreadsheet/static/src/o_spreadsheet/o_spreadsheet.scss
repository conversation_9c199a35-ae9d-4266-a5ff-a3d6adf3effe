
/*
 * This file is generated by o-spreadsheet build tools. Do not edit it.
 * @see https://github.com/odoo/o-spreadsheet
 * @version 18.4.0-alpha.8
 * @date 2025-06-12T10:55:26.270Z
 * @hash 9b7a8d02b
 */
/* Originates from src/components/top_bar/top_bar.scss */
.o-spreadsheet {
  @media (max-width: 1200px) {
    .o-topbar-responsive {
      flex-direction: column !important;
    }
  }

  @media (max-width: 768px) {
    .irregularity-map span {
      overflow: auto;
      align-items: normal !important;
    }
  }

  .tool-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

/* Originates from src/components/top_bar/dropdown_action/dropdown_action.scss */
.o-spreadsheet {
  .o-dropdown {
    position: relative;
    display: flex;
    align-items: center;
  }

  .o-dropdown-content {
    background-color: white;

    .o-dropdown-line {
      display: flex;

      > span {
        padding: 4px;
      }
    }
  }
}

/* Originates from src/components/spreadsheet/spreadsheet.scss */
.o-spreadsheet {
  color: $os-text-body;

  input {
    background-color: white;
  }

  &.o-spreadsheet-mobile .o-spreadsheet-topbar-wrapper,
  .o-spreadsheet-bottombar-wrapper {
    box-shadow: 0 0 3px 1px lightgray;
  }
  .o-spreadsheet-bottombar-wrapper {
    overscroll-behavior: none;
  }
}

/* Originates from src/components/small_bottom_bar/small_bottom_bar.scss */
.o-spreadsheet .o-spreadsheet-small-bottom-bar {
  background-color: $background-gray-color;
  border-bottom: #f2f2f2;

  .o-selection-button {
    border-radius: 2px;
    background-color: #e7e9ed;
    .o-icon {
      width: 24px;
      height: 24px;
      color: #6aa84f;
    }
  }

  .o-small-composer {
    background-color: white;
    border: lightgrey solid 1px;
    line-height: 26px;
    display: flex;
  }

  .bottom-bar-menu {
    background-color: $background-gray-color;
    border-top: 1px solid lightgrey;
    border-bottom: 1px solid lightgrey;
  }

  .o-spreadsheet-bottom-bar {
    border: none;
  }

  .o-composer {
    overscroll-behavior: contain;
  }

  .o-composer-assistant-container {
    transform: translateY(calc(-26px - 100%));
  }

  .o-spreadsheet-editor-symbol {
    height: 33px;
    cursor: pointer;
    user-select: none;
  }
}

/* Originates from src/components/small_bottom_bar/ribbon_menu/ribbon_menu.scss */
$item-height: 40px;

.o-spreadsheet .o-ribbon-menu {
  height: 250px;

  .o-ribbon-title {
    background-color: lighten($background-gray-color, 5%);
    border-bottom: 2px solid #e0e2e4;
  }
  .o-previous-button {
    background-color: $background-gray-color;
  }
  .o-ribbon-menu-wrapper {
    max-height: 100%;
  }
  .o-menu-item {
    height: $item-height;
  }
}

/* Originates from src/components/side_panel/chart/building_blocks/pie_hole_size/pie_hole_size.scss */
.o-spreadsheet .o-sidePanel {
  .o-pie-hole-size-input {
    width: 40px;
  }
}

/* Originates from src/components/full_screen_chart/full_screen_chart.scss */
.o-spreadsheet {
  .o-fullscreen-chart-overlay {
    z-index: 34; /* TODO: use css variables once ComponentsImportance is available in the scss. */
    background-color: rgba(0, 0, 0, 0.4);
    padding: 60px;

    .o-figure:not(:hover) .o-dashboard-chart-select {
      display: block !important;
    }
  }
}

/* Originates from src/components/filters/filter_menu_value_list/filter_menu_value_list.scss */
.o-spreadsheet .o-filter-menu {
  .o-search-icon {
    right: 5px;
    top: 3px;
    opacity: 0.4;

    svg {
      height: 16px;
      width: 16px;
      vertical-align: middle;
    }
  }

  .o-filter-menu-actions {
    display: flex;
    flex-direction: row;
    margin-bottom: 4px;
  }

  .o-filter-menu-list {
    flex: auto;
    overflow-y: auto;
    border: 1px solid $os-gray-300;
    height: 130px;

    .o-filter-menu-no-values {
      color: #949494;
      font-style: italic;
    }
  }
}

/* Originates from src/components/figures/chart/chart_dashboard_menu/chart_dashboard_menu.scss */
.o-spreadsheet .o-figure {
  &:not(:hover) .o-dashboard-chart-select {
    display: none !important;
  }

  .o-dashboard-chart-select {
    cursor: default;

    .o-chart-dashboard-item {
      opacity: 0.3;

      &.active,
      &:hover,
      &:target {
        background: $os-button-active-bg;
      }
    }
  }
}

.o-spreadsheet {
  &.o-spreadsheet-mobile .o-figure {
    .o-dashboard-chart-select {
      display: block !important;
    }
  }
}
