<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Menus -->
    <menuitem
        id="iot_menu_root"
        name="IoT"
        web_icon="iot,static/description/icon.png"
        groups="base.group_system"
        sequence="265"
    />

    <!-- IoT Boxes -->
    <record id="iot_box_view_form" model="ir.ui.view">
        <field name="name">iot.box.view.form</field>
        <field name="model">iot.box</field>
        <field name="arch" type="xml">
            <form create="false">
                <header groups="base.group_no_one">
                    <widget name="iot_download_logs"/>
                    <widget name="iot_restart_odoo_or_reboot" btn_name="Restart Odoo" action="restart_odoo"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="ip_url" widget="url"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Devices" name="devices">
                            <field name="device_ids" readonly="False" widget="device_list_field">
                                <list>
                                    <field name="name"/>
                                    <field name="identifier"/>
                                    <field name="type"/>
                                    <field name="connection"/>
                                </list>
                            </field>
                        </page>
                        <page string="Technical Information" groups="base.group_no_one">
                            <group>
                                <field name="identifier" readonly="True"/>
                                <field name="ip"/>
                                <field name="version"/>
                                <field name="drivers_auto_update"/>
                                <field name="is_websocket_active"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <record id="iot_device_action_search_iot_box" model="ir.actions.act_window">
        <field name="name">Devices</field>
        <field name="res_model">iot.device</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{'search_default_iot_id': [active_id], 'search_default_group_by_type': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                There is no device connected to this IoT Box
            </p>
        </field>
    </record>
    <record id="iot_box_view_kanban" model="ir.ui.view">
        <field name="name">iot.box.view.kanban</field>
        <field name="model">iot.box</field>
        <field name="arch" type="xml">
            <kanban create="false">
                <header>
                    <button name="%(iot.action_add_iot_box)d" type="action" string="Connect" class="btn-primary o-kanban-button-new"  display="always"/>
                </header>
                <templates>
                    <t t-name="card" class="p-0">
                        <div class="p-4 border-start">
                            <div class="text-truncate fw-bold" t-att-title="record.name.raw_value"><field name="name"/></div>
                            <field name="ip_url" widget="url"/>
                            <a class="p-2" name="%(iot_device_action_search_iot_box)d" type="action">
                                <span class="float-end" aria-label="Device Count"><field name="device_count"/> <i t-attf-class="fa fa-plug"/></span>
                            </a>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="iot_box_view_list" model="ir.ui.view">
        <field name="name">iot.box.view.list</field>
        <field name="model">iot.box</field>
        <field name="arch" type="xml">
            <list create="false">
                <header>
                    <button name="%(iot.action_add_iot_box)d" type="action" string="Connect" class="btn-primary o_list_button_save" display="always"/>
                </header>
                <field name="name"/>
                <field name="identifier"/>
            </list>
        </field>
    </record>
    <record id="iot_box_action" model="ir.actions.act_window">
        <field name="name">IoT Boxes</field>
        <field name="path">iot</field>
        <field name="res_model">iot.box</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No IoT Box found!
            </p><p>
                Click on the 
                <a name="%(iot.action_add_iot_box)d" type="action" tabindex="-1">connect</a>
                 to add an IoT Box.
            </p>
        </field>
    </record>

    <!-- IoT Devices -->
    <record id="iot_device_view_form" model="ir.ui.view">
        <field name="name">iot.device.view.form</field>
        <field name="model">iot.device</field>
        <field name="arch" type="xml">
            <form create="false" js_class="iot_device_form">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group name="iot_device_details">
                        <group>
                            <field name="iot_id" readonly="type != 'printer' or connection != 'network'"/>
                            <field name="iot_ip" invisible="True"/>
                            <field name="identifier" readonly="True"/>
                            <field name="type"/>
                            <field name="subtype" invisible= "type != 'printer'"/>
                            <field name="is_scanner" invisible="type not in ['keyboard', 'scanner']" widget="boolean_toggle"/>
                            <field name="manufacturer" invisible="manufacturer == ''"/>
                            <field name="connection"/>
                            <field name="connected"/>
                            <field name="keyboard_layout" invisible="type not in ['keyboard', 'scanner']"/>
                            <field name="display_url" invisible="type != 'display'"/>
                            <div class='fw-bold' groups="base.group_no_one">Last Sent Value</div>
                            <widget name="iot_device_value_display" groups="base.group_no_one"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Printer Reports" name="reports" invisible="type != 'printer'">
                            <field name="report_ids" widget="many2many" context="{'list_view_ref': 'iot.act_report_xml_view_tree_iot'}"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <record id="iot_device_view_kanban" model="ir.ui.view">
        <field name="name">iot.device.view.kanban</field>
        <field name="model">iot.device</field>
        <field name="arch" type="xml">
            <kanban create="false" group_create="false" sample="1">
                <field name="type"/>
                <field name="connection"/>
                <field name="connected"/>
                <templates>
                    <t t-name="card" class="row g-0 p-0">
                        <aside class="col-3 text-center align-self-center">
                            <t t-set="types" t-value="{'camera': 'camera', 'printer': 'printer', 'device': 'rulers', 'payment': 'payment-terminal', 'scale': 'scales', 'keyboard': 'keyboard', 'scanner': 'barcode-scanner', 'display': 'desktop', 'fiscal_data_module': 'fdm'}"/>
                            <img t-attf-src="/iot/static/src/img/#{types[record.type.raw_value]}.svg" t-attf-alt="Device type is #{record.type.raw_value}"/>
                        </aside>
                        <main class="col p-2 border-start">
                            <div class="text-truncate fw-bold" t-att-title="record.name.raw_value"><field name="name"/></div>
                            <field name="iot_id" class="text-muted fst-italic text-truncate"/>
                            <div>
                                <t t-if="record.connected.raw_value">
                                    <span class="badge text-bg-success">Connected</span>
                                </t>
                                <t t-else="">
                                    <span class="badge text-bg-secondary">Disconnected</span>
                                </t>
                            </div>
                            <t t-set="connections" t-value="{'network': 'wifi', 'direct': 'usb', 'bluetooth': 'bluetooth-b', 'serial': 'usb', 'hdmi': 'usb'}"/>
                            <span class="ms-auto fa-stack" t-att-title="record.connection.raw_value"><i t-attf-class="fa fa-#{connections[record.connection.raw_value]}"></i></span>
                        </main>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="iot_device_view_list" model="ir.ui.view">
        <field name="name">iot.device.view.list</field>
        <field name="model">iot.device</field>
        <field name="arch" type="xml">
            <list create="false">
                <field name="name"/>
                <field name="iot_id"/>
                <field name="identifier"/>
            </list>
        </field>
    </record>
    <record id="iot_device_search" model="ir.ui.view">
        <field name="name">iot.device.view.search</field>
        <field name="model">iot.device</field>
        <field name="arch" type="xml">
            <search string="IoT Device">
                <field name="name"/>
                <field name="type"/>
                <field name="iot_id"/>
                <group expand="0" string="Group By">
                    <filter name="group_by_type" string="Device type" context="{'group_by': 'type'}"/>
                    <filter name="group_by_iot_id" string="IoT Box" context="{'group_by':'iot_id'}"/>
                    <filter name="group_by_connection" string="Connection" context="{'group_by':'connection'}"/>
                </group>
            </search>
        </field>
    </record>
    <record id="iot_device_action" model="ir.actions.act_window">
        <field name="name">Devices</field>
        <field name="res_model">iot.device</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{'search_default_group_by_iot_id': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                There is no device connected to your IoT Boxes
            </p>
        </field>
    </record>

    <record id="action_iot_delete_linked_devices_menu" model="ir.actions.client">
        <field name="name">reset.linked.printers</field>
        <field name="tag">iot_delete_linked_devices_action</field>
    </record>
    
    <menuitem
        id="iot_box_menu_action"
        action="iot_box_action"
        parent="iot_menu_root"
        sequence="1"/>
    <menuitem
        id="iot_device_menu_action"
        action="iot_device_action"
        parent="iot_menu_root"
        sequence="2"/>
    <menuitem
        id="view_iot_selected_printer_local_action"
        name="Configuration"
        parent="iot_menu_root"
        sequence="3"/>

    <!-- Reports submenu -->
    <menuitem
        id="iot_settings_menu_action"
        name="Reports"
        parent="view_iot_selected_printer_local_action"
        sequence="4"/>
    <menuitem
        id="iot_clear_selected_devices"
        name="Reset Linked Printers"
        sequence="5"
        parent="iot_settings_menu_action"
        action="action_iot_delete_linked_devices_menu"/>

    <!-- ir.action.report add the device -->
    <record id="act_report_xml_view_iot" model="ir.ui.view">
        <field name="name">ir.actions.report.iot</field>
        <field name="model">ir.actions.report</field>
        <field name="inherit_id" ref="base.act_report_xml_view"/>
         <field name="arch" type="xml">
            <field name="report_type" position="after">
                <field name="device_ids" widget="many2many_tags"/>
            </field>
         </field>
    </record>

    <record id="act_report_xml_view_tree_iot" model="ir.ui.view">
        <field name="name">ir.actions.report.list.iot</field>
        <field name="model">ir.actions.report</field>
        <field name="priority">100</field>
        <field name="arch" type="xml">
            <list string="Report xml">
                <field name="name"/>
                <field name="model"/>
                <field name="report_type"/>
            </list>
        </field>
    </record>
</odoo>
