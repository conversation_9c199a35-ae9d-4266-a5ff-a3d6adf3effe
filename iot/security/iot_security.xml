<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="iot_box_comp_rule" model="ir.rule">
        <field name="name">IoT Box multi company rule</field>
        <field name="model_id" ref="model_iot_box"/>
        <field eval="True" name="global"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="iot_device_comp_rule" model="ir.rule">
        <field name="name">IoT Device multi company rule</field>
        <field name="model_id" ref="model_iot_device"/>
        <field eval="True" name="global"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

</odoo>
