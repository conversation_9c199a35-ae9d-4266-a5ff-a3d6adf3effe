# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
# Translators:
# Jonatan Gk, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> Bo<PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# ericrolo, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# ma<PERSON><PERSON>, 2024
# Noemi <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>em<PERSON>, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
".\n"
"\n"
"                    <br/><br/>\n"
"\n"
"                    <strong>Option A. Pairing Code</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box. You can also find it on the IoT Box Homepage<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>\n"
"                    3. After some time the IoT Box should appear in the list along with the connected devices.<br/>"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box and connect it to the internet through ethernet"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<br/>\n"
"                    1. Copy the token below<br/>\n"
"                    2. While on the same network as the IoT Box open a web browser and go to the IoT Box ip address<br/>\n"
"                    3. In the \"Server\" section click on \"Configure\" button<br/>\n"
"                    4. Paste the token in the \"Server token\" section and click on \"Connect\" button<br/>\n"
"                    5. After some time the IoT Box should appear in the list along with the connected devices<br/>"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-secondary\">Disconnected</span>"
msgstr "<span class=\"badge text-bg-secondary\">ha desconnectat</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-success\">Connected</span>"
msgstr "<span class=\"badge text-bg-success\">Connectat</span>"

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr "Afegeix un assistent de caixa d'IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Are you sure you want to restart Odoo on the IoT box?"
msgstr "Esteu segur que voleu reiniciar l'Odoo al quadre IoT?"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr "Actualització automàtica de controladors"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr "Actualitzar automàticament els controladors en arrencar el IoT Box"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr "Escàner de codi de barres"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr "Bluetooth"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr "Càmera"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Check IoT Box connection. Try restarting if needed."
msgstr "Comprova la connexió del dispositiu IoT. Intenta-ho de nou, si cal."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Check if the device is still connected"
msgstr "Comprova si el dispositiu encara està connectat"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click here to open your IoT Homepage"
msgstr "Feu clic aquí per obrir la pàgina d'inici de l'IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "Feu clic a Avançat/Mostra els detalls/Detalls/Més informació"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"Feu clic a Continuar a.../Afegeix l'excepció/Visiteu aquest lloc web/Go a la"
" pàgina web"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr "Feu clic a"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Close"
msgstr "Tancar"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Close this window and try again"
msgstr "Tanca aquesta finestra i torna-ho a provar"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr "Empresa"

#. module: iot
#: model:ir.ui.menu,name:iot.view_iot_selected_printer_local_action
msgid "Configuration"
msgstr "Configuració"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_list
msgid "Connect"
msgstr "Connecta't"

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect my IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr "Connexió"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.js:0
msgid "Connection to IoT Box failed"
msgstr "Ha fallat la connexió a la IoT Box"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Connection to device failed"
msgstr "Ha fallat la connexió amb el dispositiu"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Connection to printer failed "
msgstr "Ha fallat la connexió a la impressora"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_date
msgid "Created on"
msgstr "Creat el"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__device_ids
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr "Dispositiu"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr "Nombre de dispositius"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr "Tipus de dispositiu"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr "El tipus de dispositiu és #{record.type.raw_value}"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr "Dispositius"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr "Mostrar "

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_device_ids
msgid "Display Device"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr "Mostra l'URL"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr "Adreça del domini"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download Logs"
msgstr "Descarregar registres"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download logs"
msgstr "Descarrega els registres"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
msgid "Failed to send to printer."
msgstr "No s'ha pogut enviar a la impressora."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr "Mòdul de dades Fiscal"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "Agrupar per"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "HDMI"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__id
msgid "ID"
msgstr "ID"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr "Dispositiu IOT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr "Identificador"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr "Identificador (adreça Mac)"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr "Si el dispositiu està connectat a la IoT Box"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"Si esteu en un servidor segur (HTTPS) comproveu si heu acceptat el "
"certificat:"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr "Versió de la imatge"

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr "IoT"

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr "Capsa IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr "Pàgina d'inici de IoT Box"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr "Caixes IoT"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr "Dispositiu IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_ids
msgid "IoT Devices"
msgstr "Dispositius IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr "És Escàner"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__is_websocket_active
msgid "Is Websocket active?"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr "Teclat"

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr "Disposició del teclat"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__label_printer
msgid "Label Printer"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr "Darrer valor enviat"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Last restarting process hasn't finished yet"
msgstr "El darrer procés de reinici encara no ha finalitzat"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr "Disseny"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr "Mesura manual"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "Llegiu manualment la mesura del dispositiu"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr "Canvia manualment el tipus de dispositiu entre el teclat i l'escàner"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr "Fabricant"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "Nom"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr "Xarxa"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "New IoT Box connected!"
msgstr "Nou dispositiu IoT connectat!"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found!"
msgstr "No s'ha trobat cap dispositiu IoT!"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "No new IoT Box found."
msgstr "No s'ha trobat cap dispositiu IoT."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "No printer selected"
msgstr "No s'ha seleccionat impressora"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "No report to display"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Odoo cannot reach the IoT Box."
msgstr "Odoo no pot arribar a la caixa IoT."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__office_printer
msgid "Office Printer"
msgstr ""

#. module: iot
#. odoo-python
#: code:addons/iot/models/ir_actions_report.py:0
msgid ""
"One of the printer used to print document have been removed. Please retry "
"the operation to choose new printers to print."
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Option B. Connection Token"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr "Parella"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr "Codi d'aparellament"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr "Terminal de pagament"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser):"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Please check if the IoT Box is still connected."
msgstr "Comproveu si la IoT Box encara està connectada."

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Please enter a pairing code."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Please wait"
msgstr "Espereu"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Print"
msgstr "Imprimir"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr "Impressora "

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr "Informes de la impressora"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Printers"
msgstr "Impressores"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Printing operation completed on printer %s"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "Contracte de garantia de l'editor per a IoT Box"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__receipt_printer
msgid "Receipt Printer"
msgstr "Impressora de tiquets "

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr "Acció d'informe"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr "Informe xml "

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
#: model:ir.ui.menu,name:iot.iot_settings_menu_action
msgid "Reports"
msgstr "Informes"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "Reports with linked printers"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
#: model:ir.ui.menu,name:iot.iot_clear_selected_devices
msgid "Reset Linked Printers"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart Failed"
msgstr "Ha fallat en reiniciar"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart finished"
msgstr "Ha finalitzat el reinici"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restarting"
msgstr "S'està reiniciant"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Sales Details"
msgstr "Detalls de venda"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr "Balança"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "Select at least one printer"
msgstr "Selecciona almenys una impressora"

#. module: iot
#: model:ir.model,name:iot.model_select_printers_wizard
msgid "Selection of printers"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Sending to printer %s..."
msgstr "Enviant a la impressora %s..."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr "Sèrie"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr "Estat"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__subtype
msgid "Subtype"
msgstr "Subtipus"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__subtype
msgid "Subtype of device."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Success"
msgstr "Èxit"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Technical Information"
msgstr "Informació tècnica"

#. module: iot
#: model:ir.model,name:iot.model_iot_channel
msgid "The Websocket Iot Channel"
msgstr ""

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid ""
"The pairing code you provided was not found in our system. Please check that"
" you entered it correctly."
msgstr ""
"El codi d'aparellament que ha proporcionat no s'ha trobat en el nostre "
"sistema. Si us plau, comprova que l'has introduït correctament."

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr "No hi ha cap dispositiu connectat a aquest IoT Box"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr "No hi ha cap dispositiu connectat a les seves Caixes IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Timed out"
msgstr "Temps excedit"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr "Token"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "Tipus"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr "Tipus de connexió."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr "Tipus de dispositiu."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr ""
"L'URL de la pàgina que serà mostrada pel dispositiu, deixi-la buida per a "
"utilitzar la pantalla de cara al client del TPV."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr "USB"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Using Pairing Code to connect..."
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr "Variant"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr ""
"Vam tenir problemes per a aparellar el teu IoT Box. Torna a intentar-ho més "
"tard."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "We're looking for your IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_ids
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr ""
"En configurar un dispositiu aquí, l'informe s'imprimirà a través d'aquest "
"dispositiu en el IoT Box"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr "connectar"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "or wifi"
msgstr ""

#. module: iot
#: model:ir.actions.client,name:iot.action_iot_delete_linked_devices_menu
msgid "reset.linked.printers"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr "per afegir una caixa d'IoT."

#. module: iot
#: model:ir.model,name:iot.model_ir_websocket
msgid "websocket message handling"
msgstr "gestió de missatges websocket"
