# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
".\n"
"\n"
"                    <br/><br/>\n"
"\n"
"                    <strong>Option A. Pairing Code</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box. You can also find it on the IoT Box Homepage<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>\n"
"                    3. After some time the IoT Box should appear in the list along with the connected devices.<br/>"
msgstr ""
".\n"
"\n"
"                    <br/><br/>\n"
"\n"
"                    <strong>Opțiune A. Cod Împerechere</strong><br/>\n"
"                    1. Citiți codul de împerechere de pe un display sau imprimantă termică conectată la IoT Box; sau de pe pagina principală Iot Box.<br/>\n"
"                    2. Introduceți codul în câmpul de mai jos și apasați \"Pair\".<br/>\n"
"                    3. La scurt timp, caseta IoT ar trebui să apară în lista de device-uri conectate<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box and connect it to the internet through ethernet"
msgstr "0. Porniți IoT Box si connectați-l la internet prin cablu"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<br/>\n"
"                    1. Copy the token below<br/>\n"
"                    2. While on the same network as the IoT Box open a web browser and go to the IoT Box ip address<br/>\n"
"                    3. In the \"Server\" section click on \"Configure\" button<br/>\n"
"                    4. Paste the token in the \"Server token\" section and click on \"Connect\" button<br/>\n"
"                    5. After some time the IoT Box should appear in the list along with the connected devices<br/>"
msgstr ""
"<br/>\n"
"                    1. Copiați tokenul de mai jos<br/>\n"
"                    2. Deschideți un browser web pe network, și accesați adresa IP a IoT Box-ului<br/>\n"
"                    3. În secțiunea \"Server\", alegeți \"Configure\"<br/>\n"
"                    4. Puneți tokenul copiat în secțiunea \"Server token\" și apasați \"Connect\"<br/>\n"
"                    5. La scurt timp, caseta IoT ar trebui să apară în lista de device-uri conectate<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-secondary\">Disconnected</span>"
msgstr "<span class=\"badge text-bg-secondary\">Deconectat</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-success\">Connected</span>"
msgstr "<span class=\"badge text-bg-success\">Conectat</span>"

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr "Adăugați wizard IoT Box"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Are you sure you want to restart Odoo on the IoT box?"
msgstr "Sigur doriți să reporniți Odoo pe caseta IoT?"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr "Actualizarea automată a driverelor"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr "Actualizați automat driverele la pornirea IoT Box"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr "Cititor de coduri de bare"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr "Bluetooth"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr "Cameră"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Check IoT Box connection. Try restarting if needed."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Check if the device is still connected"
msgstr "Verificați dacă dispozitivul încă este conectat"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click here to open your IoT Homepage"
msgstr "Clic aici pentru a deschide pagina de pornire IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr ""
"Faceți clic pe Avansat / Afișați detalii / Detalii / Mai multe informații"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"Faceți clic pe Continuați cu ... / Adăugați o excepție / Vizitați acest site"
" web / Accesați pagina web"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr "Faceți clic pe"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Close"
msgstr "Închide"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Close this window and try again"
msgstr "Închideți această fereastră și încercați din nou"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr "Companie"

#. module: iot
#: model:ir.ui.menu,name:iot.view_iot_selected_printer_local_action
msgid "Configuration"
msgstr "Configurare"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_list
msgid "Connect"
msgstr "Conectare"

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect my IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr "Conectare"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.js:0
msgid "Connection to IoT Box failed"
msgstr "Conectarea la caseta IoT a eșuat"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Connection to device failed"
msgstr "Conexiunea la dispozitiv a eșuat"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Connection to printer failed "
msgstr "Conexiunea la imprimantă a eșuat"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__device_ids
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr "Dispozitiv"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr "Număr de dispozitive"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr "Tip Dispozitiv"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr "Tip dizpozitiv este #{record.type.raw_value}"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr "Dispozitive"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr "Afișare"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_device_ids
msgid "Display Device"
msgstr "Dispozitiv de afișare"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr "Afișare URL"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr "Adresă de domeniu"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download Logs"
msgstr "Jurnale descărcări"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download logs"
msgstr "Descarcă jurnale"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
msgid "Failed to send to printer."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr "Doar Firefox: Faceți clic pe Confirmarea excepției de securitate"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr "Modulul de date fiscale"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "Grupează după"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "HDMI"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__id
msgid "ID"
msgstr "ID"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr "Dispozitiv IOT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr "Identificator"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr "Identificator (adresa Mac)"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr "Dacă dispozitivul este conectat la caseta IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"Dacă vă aflați pe un server securizat (HTTPS), verificați dacă ați acceptat "
"certificatul:"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr "Versiune imagine"

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr "IoT"

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr "IoT Box"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr "Pagina principală IoT Box"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr "Casete IoT"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr "Dispozitiv IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_ids
msgid "IoT Devices"
msgstr "Dispozitive IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr "Este Scanner"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__is_websocket_active
msgid "Is Websocket active?"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr "Tastatură"

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr "Keyboard Layout"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__label_printer
msgid "Label Printer"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr "Ultima valoare trimisă"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Last restarting process hasn't finished yet"
msgstr "Ultimul proces de repornire nu s-a terminat încă"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr "Layout"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr "Măsurare manuală"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "Citiți manual măsurarea de pe dispozitiv"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr "Comutați manual tipul dispozitivului între tastatură și scaner"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr "Producător"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "Nume"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr "Rețea"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "New IoT Box connected!"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found!"
msgstr "Nu a fost găsită nicio casetă IoT!"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "No new IoT Box found."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "No printer selected"
msgstr "Nu a fost selectată nicio imprimantă"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "No report to display"
msgstr "Niciun raport de afișat"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Odoo cannot reach the IoT Box."
msgstr "Odoo nu poate ajunge la caseta IoT."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__office_printer
msgid "Office Printer"
msgstr ""

#. module: iot
#. odoo-python
#: code:addons/iot/models/ir_actions_report.py:0
msgid ""
"One of the printer used to print document have been removed. Please retry "
"the operation to choose new printers to print."
msgstr ""
"Una din imprimantele folosite pentru printarea de documente a fost "
"deconectată. Vă rugam să repetați operația pentru a alege imprimante noi."

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Option B. Connection Token"
msgstr "Option B. Connection Token"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr "Imperechere"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr "Cod Împerechere"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr "Terminal de plată"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser):"
msgstr ""
"Vă rugăm să acceptați certificatul cutiei dvs. IoT (procedura depinde de "
"browserul dvs.):"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Please check if the IoT Box is still connected."
msgstr "Vă rugăm să verificați dacă IoT Box este încă conectat."

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Please enter a pairing code."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Please wait"
msgstr "Te rog așteaptă"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Print"
msgstr "Tipăriți"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr "Imprimantă"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr "Rapoarte de imprimantă"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Printers"
msgstr "Imprimante"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Printing operation completed on printer %s"
msgstr "Operațiune completată pe imprimanta %s"

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "Contract de garanție a editorului pentru caseta IoT"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__receipt_printer
msgid "Receipt Printer"
msgstr "Imprimantă pentru bonuri"

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr "Acțiune raport"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr "Raport xml"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
#: model:ir.ui.menu,name:iot.iot_settings_menu_action
msgid "Reports"
msgstr "Rapoarte"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "Reports with linked printers"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
#: model:ir.ui.menu,name:iot.iot_clear_selected_devices
msgid "Reset Linked Printers"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart Failed"
msgstr "Repornirea a eșuat"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart finished"
msgstr "Repornire finalizată"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restarting"
msgstr "Repornire"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Sales Details"
msgstr "Detalii vânzări"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr "Cântar"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "Select at least one printer"
msgstr "Selectați cel puțin o imprimantă"

#. module: iot
#: model:ir.model,name:iot.model_select_printers_wizard
msgid "Selection of printers"
msgstr "Selecția imprimantelor"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Sending to printer %s..."
msgstr "Se trimite la imprimanta %s..."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr "Serial"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr "Stare"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__subtype
msgid "Subtype"
msgstr "Subtip"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__subtype
msgid "Subtype of device."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Success"
msgstr "Succes"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Technical Information"
msgstr "Informații tehnice"

#. module: iot
#: model:ir.model,name:iot.model_iot_channel
msgid "The Websocket Iot Channel"
msgstr "Canalul Iot Websocket"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid ""
"The pairing code you provided was not found in our system. Please check that"
" you entered it correctly."
msgstr ""
"Codul de împerechere pe care l-ați furnizat nu a fost găsit în sistemul "
"nostru."
"                                                                               "
"Vă rugăm să verificați dacă l-ați introdus corect."
"                                                                    "

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr "Nu există niciun dispozitiv conectat la această casetă IoT"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr "Nu există niciun dispozitiv conectat la casetele dvs. IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Timed out"
msgstr "A expirat"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr "Token"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "Tip"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr "Tip de conexiune."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr "Tip de dizpozitiv."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr ""
"Adresa URL a paginii care va fi afișată de dispozitiv, lăsați-o goală pentru"
" a utiliza afișarea POS a clientului."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr "USB"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Using Pairing Code to connect..."
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr "Variantă"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr ""
"Am întâmpinat probleme la asocierea casetei dvs. IoT. Vă rugăm să încercați "
"din nou mai târziu."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "We're looking for your IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_ids
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr ""
"Când setați un dispozitiv aici, raportul va fi tipărit prin intermediul "
"acestui dispozitiv în caseta IoT"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr "Conectare"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "or wifi"
msgstr "sau WiFi"

#. module: iot
#: model:ir.actions.client,name:iot.action_iot_delete_linked_devices_menu
msgid "reset.linked.printers"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr "pentru a adăuga o casetă IoT."

#. module: iot
#: model:ir.model,name:iot.model_ir_websocket
msgid "websocket message handling"
msgstr "gestionarea mesajelor websocket"
