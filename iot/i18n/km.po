# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * iot
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON>g <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2018-10-08 07:14+0000\n"
"Last-Translator: <PERSON><PERSON>nn Seang <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"Language: km\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box<br/><br/>\n"
"\n"
"                    <strong>A. Ethernet Connection</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box.<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-secondary\">Disconnected</span>"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-success\">Connected</span>"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<strong>B. WiFi Connection (or Ethernet Connection doesn't work)</strong><br/>\n"
"                    1. Make sure no ethernet cable is connected to the IoT Box<br/>\n"
"                    2. Copy the token that is below<br/>\n"
"                    3. Connect to the IoT Box WiFi network (you should see it in your available WiFi networks)<br/>\n"
"                    4. You will be redirected to the IoT Box Homepage<br/>\n"
"                    5. Paste the token in token field and follow the steps described on the IoT Box Homepage<br/>"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Are you sure you want to reboot the IoT box?"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Are you sure you want to restart Odoo on the IoT box?"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Check if the device is still connected"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
msgid "Check if the printer is still connected"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click here to open your IoT Homepage"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click on Proceed to .../Add Exception/Visit this website/Go on to the webpage"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Close"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Close this window and try again"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_list
msgid "Connect"
msgstr ""

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect an IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.js:0
msgid "Connection to IoT Box failed"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Connection to device failed"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
msgid "Connection to printer failed"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr ""

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download Logs"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download logs"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "ជា​ក្រុម​តាម"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "Hdmi"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
msgid "ID"
msgstr "ID"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr ""

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr ""

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Last restarting process hasn't finished yet"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "ឈ្មោះ"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found!"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Odoo cannot reach the IoT Box."
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Please accept the certificate of your IoT Box (procedure depends on your browser):"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Please check if the IoT Box is still connected."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Please wait"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
msgid "Printer "
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
msgid "Reports"
msgstr "របាយការណ៍"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart Failed"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart finished"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restarting"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Success"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
msgid "Successfully sent to printer!"
msgstr ""

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "The pairing code you provided was not found in our system. Please check that you entered it correctly."
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Timed out"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "ប្រភេទ"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid "URL of the page that will be displayed by the device, leave empty to use the customer facing display of the POS."
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr ""

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_id
msgid "When setting a device here, the report will be printed through this device on the IoT Box"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr ""
