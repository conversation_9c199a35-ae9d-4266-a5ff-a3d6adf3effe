# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
".\n"
"\n"
"                    <br/><br/>\n"
"\n"
"                    <strong>Option A. Pairing Code</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box. You can also find it on the IoT Box Homepage<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>\n"
"                    3. After some time the IoT Box should appear in the list along with the connected devices.<br/>"
msgstr ""
".\n"
"\n"
"                    <br/><br/>\n"
"\n"
"                    <strong>Tùy chọn A. Mã ghép nối</strong><br/>\n"
"                    1. Đọc mã ghép nối từ màn hình hoặc máy in nhiệt được kết nối với Hộp IoT. Bạn cũng có thể tìm thấy mã này trên Trang chủ Hộp IoT<br/>\n"
"                    2. Nhập mã dưới đây và nhấp vào \"Ghép nối\".<br/>\n"
"                    3. Sau đó, Hộp IoT sẽ xuất hiện trong danh sách cùng với các thiết bị đã kết nối.<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box and connect it to the internet through ethernet"
msgstr "0. Bật nguồn Hộp IoT và kết nối với internet qua ethernet"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<br/>\n"
"                    1. Copy the token below<br/>\n"
"                    2. While on the same network as the IoT Box open a web browser and go to the IoT Box ip address<br/>\n"
"                    3. In the \"Server\" section click on \"Configure\" button<br/>\n"
"                    4. Paste the token in the \"Server token\" section and click on \"Connect\" button<br/>\n"
"                    5. After some time the IoT Box should appear in the list along with the connected devices<br/>"
msgstr ""
"<br/>\n"
"                    1. Sao chép token dưới đây<br/>\n"
"                    2. Khi kết nối cùng mạng với Hộp IoT, hãy mở trình duyệt web và truy cập vào địa chỉ IP của Hộp IoT<br/>\n"
"                    3. Trong phần \"Máy chủ\", nhấp vào nút \"Cấu hình\"<br/>\n"
"                    4. Dán token vào phần \"Token máy chủ\" và nhấp vào nút \"Kết nối\"<br/>\n"
"                    5. Sau đó, Hộp IoT sẽ xuất hiện trong danh sách cùng với các thiết bị đã kết nối<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-secondary\">Disconnected</span>"
msgstr "<span class=\"badge text-bg-secondary\">Đã ngắt kết nối</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-success\">Connected</span>"
msgstr "<span class=\"badge text-bg-success\">Đã kết nối</span>"

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr "Thêm hướng dẫn IoT Box"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Are you sure you want to restart Odoo on the IoT box?"
msgstr "Bạn có chắc chắn muốn khởi động lại hộp IoT không?"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr "Cập nhật trình điều khiển tự động"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr "Tự động cập nhật trình điều khiển khi Hộp IoT khởi động"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr "Máy quét mã vạch"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr "Bluetooth"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr "Máy ảnh"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Check IoT Box connection. Try restarting if needed."
msgstr "Kiểm tra kết nối hộp IoT. Thử khởi động lại nếu cần thiết."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Check if the device is still connected"
msgstr "Kiểm tra liệu thiết bị vẫn kết nối"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click here to open your IoT Homepage"
msgstr "Bấm vào đây để mở Trang chủ IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "Bấm vào Nâng cao/Hiển thị chi tiết/Chi tiết/Thêm thông tin"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"Bấm vào Tiến hành.../Thêm ngoại lệ/Truy cập trang web này/Đi tới trang web"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr "Nhấp vào"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Close"
msgstr "Đóng"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Close this window and try again"
msgstr "Đóng cửa sổ này và thử lại"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr "Công ty"

#. module: iot
#: model:ir.ui.menu,name:iot.view_iot_selected_printer_local_action
msgid "Configuration"
msgstr "Cấu hình"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_list
msgid "Connect"
msgstr "Kết nối"

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect my IoT Box"
msgstr "Kết nối Hộp IoT của tôi"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr "Kết nối"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.js:0
msgid "Connection to IoT Box failed"
msgstr "Kết nối với Hộp IoT không thành công"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Connection to device failed"
msgstr "Kết nối với thiết bị không thành công"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Connection to printer failed "
msgstr "Kết nối với máy in không thành công"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__device_ids
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr "Thiết bị"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr "Số thiết bị"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr "Loại thiết bị"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr "Loại thiết bị là #{record.type.raw_value}"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr "Thiết bị"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr "Hiển thị"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_device_ids
msgid "Display Device"
msgstr "Hiển thị thiết bị"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr "Hiển thị URL"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr "Địa chỉ miền"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download Logs"
msgstr "Tải xuống nhật ký"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download logs"
msgstr "Tải xuống nhật ký"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
msgid "Failed to send to printer."
msgstr "Không thể gửi đến máy in."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr "Chỉ Firefox: Bấm vào Xác nhận ngoại lệ an ninh"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr "Phân hệ dữ liệu tài chính"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "HDMI"
msgstr "HDMI"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__id
msgid "ID"
msgstr "ID"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr "Thiết bị IOT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr "Định danh"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr "Mã định danh (Địa chỉ Mac)"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr "Nếu thiết bị được kết nối với IoT Box"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"Nếu bạn đang sử dụng máy chủ bảo mật (HTTPS) hãy kiểm tra xem bạn đã chấp "
"nhận chứng chỉ hay chưa: "

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr "Phiên bản hình ảnh"

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr "IoT"

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr "Hộp IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr "Trang chính Box IoT"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr "IoT Box"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr "Thiết bị IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_ids
msgid "IoT Devices"
msgstr "Thiết bị IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr "Là máy quét"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__is_websocket_active
msgid "Is Websocket active?"
msgstr "Websocket có hoạt động không?"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr "Bàn phím"

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr "Bố cục bàn phím"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__label_printer
msgid "Label Printer"
msgstr "Máy in nhãn"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr "Giá trị gửi cuối cùng"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Last restarting process hasn't finished yet"
msgstr "Quá trình khởi động lại cuối cùng vẫn chưa kết thúc"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr "Bố cục"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr "Đo lường thủ công"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "Đọc chỉ số từ thiết bị theo cách thủ công"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr "Chuyển đổi loại thiết bị giữa bàn phím và máy quét theo cách thủ công"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr "Nhà sản xuất"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "Tên"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr "Network"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "New IoT Box connected!"
msgstr "Đã kết nối Hộp IoT mới!"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found!"
msgstr "Không tìm thấy Hộp IoT nào!"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "No new IoT Box found."
msgstr "Không tìm thấy Hộp IoT nào."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "No printer selected"
msgstr "Chưa chọn máy in nào"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "No report to display"
msgstr "Không có báo cáo để hiển thị"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Odoo cannot reach the IoT Box."
msgstr "Odoo không thể kết nối với Hộp IoT."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__office_printer
msgid "Office Printer"
msgstr "Máy in văn phòng"

#. module: iot
#. odoo-python
#: code:addons/iot/models/ir_actions_report.py:0
msgid ""
"One of the printer used to print document have been removed. Please retry "
"the operation to choose new printers to print."
msgstr ""
"Một trong những máy in được sử dụng để in tài liệu đã bị gỡ bỏ. Vui lòng "
"thao tác lại để chọn máy in mới để in."

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Option B. Connection Token"
msgstr "Tuỳ chọn B. Token kết nối"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr "Ghép nối"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr "Mã ghép nối"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr "Thiết bị đầu cuối thanh toán"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser):"
msgstr ""
"Vui lòng chấp nhận chứng chỉ Hộp IoT của bạn (quy trình tùy thuộc vào trình "
"duyệt bạn sử dụng):"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Please check if the IoT Box is still connected."
msgstr "Vui lòng kiểm tra xem Hộp IoT của bạn có còn kết nối không."

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Please enter a pairing code."
msgstr "Vui lòng nhập mã ghép nối."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Please wait"
msgstr "Vui lòng đợi"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Print"
msgstr "In"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr "Máy In"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr "Báo cáo máy in"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Printers"
msgstr "Máy in"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Printing operation completed on printer %s"
msgstr "Hoạt động in đã hoàn tất trên máy in %s"

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "Hợp đồng bảo hành nhà phát hành cho Hộp IoT"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__receipt_printer
msgid "Receipt Printer"
msgstr "Máy in biên lai"

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr "Báo cáo tác vụ"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr "Báo cáo xml"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
#: model:ir.ui.menu,name:iot.iot_settings_menu_action
msgid "Reports"
msgstr "Báo cáo"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "Reports with linked printers"
msgstr "Báo cáo có máy in liên kết"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
#: model:ir.ui.menu,name:iot.iot_clear_selected_devices
msgid "Reset Linked Printers"
msgstr "Đặt lại máy in liên kết"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart Failed"
msgstr "Khởi động lại không thành công"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart finished"
msgstr "Khởi động lại hoàn tất"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restarting"
msgstr "Đang khởi động lại"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Sales Details"
msgstr "Thông tin bán hàng"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr "Cân"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "Select at least one printer"
msgstr "Chọn ít nhất một máy in"

#. module: iot
#: model:ir.model,name:iot.model_select_printers_wizard
msgid "Selection of printers"
msgstr "Lựa chọn máy in"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Sending to printer %s..."
msgstr "Đang gửi tới máy in %s..."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr "Sê-ri"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr "Trạng thái"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__subtype
msgid "Subtype"
msgstr "Kiểu phụ"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__subtype
msgid "Subtype of device."
msgstr "Dòng thiết bị."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Success"
msgstr "Thành công"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Technical Information"
msgstr "Thông tin kỹ thuật"

#. module: iot
#: model:ir.model,name:iot.model_iot_channel
msgid "The Websocket Iot Channel"
msgstr "Kênh Websocket Iot"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid ""
"The pairing code you provided was not found in our system. Please check that"
" you entered it correctly."
msgstr ""
"Không tìm thấy mã ghép nối bạn cung cấp trong hệ thống của chúng tôi. Vui "
"lòng kiểm tra lại để đảm bảo bạn đã nhập đúng mã."

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr "Không có thiết bị nào được kết nối với Hộp IoT này"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr "Không có thiết bị nào được kết nối với Hộp IoT của bạn"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Timed out"
msgstr "Hết thời gian"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr "Token"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "Loại"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr "Loại kết nối."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr "Loại thiết bị."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr ""
"URL của trang mà thiết bị sẽ hiển thị, hãy để trống để sử dụng màn hình hiển"
" thị cho khách hàng của POS."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr "USB"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Using Pairing Code to connect..."
msgstr "Sử dụng mã ghép nối để kết nối..."

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr "Biến thể"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr "Đã gặp sự cố khi ghép nối Hộp IoT của bạn. Vui lòng thử lại sau."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "We're looking for your IoT Box"
msgstr "Chúng tôi đang tìm kiếm Hộp IoT của bạn"

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_ids
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr ""
"Khi cài đặt thiết bị tại đây, báo cáo sẽ được in trên Hộp IoT thông qua "
"thiết bị này"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr "kết nối"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "or wifi"
msgstr "hoặc wifi"

#. module: iot
#: model:ir.actions.client,name:iot.action_iot_delete_linked_devices_menu
msgid "reset.linked.printers"
msgstr "reset.linked.printers"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr "để thêm một Hộp IoT."

#. module: iot
#: model:ir.model,name:iot.model_ir_websocket
msgid "websocket message handling"
msgstr "xử lý thông báo websocket"
