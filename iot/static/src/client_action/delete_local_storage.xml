<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="iot.delete_printer">
        <div class="container mt-4">
            <h2>Reports with linked printers</h2>
            <div class="mt-3">
                <p t-if="!state.reportList.length">No report to display</p>
                <div class="card mb-3"  t-foreach="state.reportList" t-as="item" t-key="item.id">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <h5 class="card-title m-0" t-out="item.name" />
                        <button class="btn btn-primary" t-on-click="(ev) => this.removeFromLocal(ev, item.id)">Reset Linked Printers</button>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
