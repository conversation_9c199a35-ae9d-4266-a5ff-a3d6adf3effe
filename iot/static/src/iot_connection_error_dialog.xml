<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="iot.IoTConnectionErrorDialog">
        <Dialog title="props.title"  size="'md'" contentClass="'o_error_dialog'">
            <div role="alert">
                <p><PERSON><PERSON><PERSON> cannot reach the IoT Box.</p>
                <span>Please check if the IoT Box is still connected.</span>
                <p>If you are on a secure server (HTTPS) check if you accepted the certificate:</p>
                <p>
                    <a t-att-href="props.href" target="_blank">
                        <i class="fa fa-external-link"/>
                        Click here to open your IoT Homepage
                    </a>
                </p>
                <li>Please accept the certificate of your IoT Box (procedure depends on your browser):</li>
                <li>Click on Advanced/Show Details/Details/More information</li>
                <li>Click on Proceed to .../Add Exception/Visit this website/Go on to the webpage</li>
                <li>Firefox only: Click on Confirm Security Exception</li>
                <li>Close this window and try again</li>
            </div>
            <t t-set-slot="footer">
                <button class="btn btn-secondary o_form_button_cancel" t-on-click="props.close">Close</button>
            </t>
        </Dialog>
    </t>

</templates>
