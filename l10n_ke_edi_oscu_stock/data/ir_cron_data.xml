<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="ir_cron_send_stock_moves" model="ir.cron">
            <field name="name">KE eTIMS: Send Stock Operations</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="model_id" ref="model_stock_move"/>
            <field name="code">model._cron_l10n_ke_oscu_process_moves()</field>
            <field name="state">code</field>
        </record>
        <record id="ir_cron_fetch_customs" model="ir.cron">
            <field name="name">KE eTIMS: Receive Customs Imports from the OSCU</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="model_id" ref="model_l10n_ke_edi_customs_import"/>
            <field name="code">model._cron_l10n_ke_fetch_customs_import()</field>
            <field name="state">code</field>
        </record>
    </data>
</odoo>
