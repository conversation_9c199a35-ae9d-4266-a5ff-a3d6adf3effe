<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="sign.signItemTypesSidebar">
        <t t-set="icon_type" t-value="{
            'signature': 'fa-pencil-square-o',
            'initial': 'fa-pencil-square-o',
            'text': 'fa-font',
            'textarea': 'fa-bars',
            'checkbox': 'fa-check-square-o',
            'selection': 'fa-dot-circle-o',
        }"/>
        <div t-attf-class="o_sign_field_type_toolbar d-flex flex-column bg-white {{ sidebarHidden ? 'd-none' : ''}}">
            <div class="o_sign_field_type_toolbar_title d-flex justify-content-center align-items-center pt-2">Fields</div>
            <div class="o_sign_field_type_toolbar_items d-flex flex-column p-2">
                <t t-foreach="signItemTypes" t-as="itemType" t-key="itemType.id">
                    <button type="button" class="o_sign_field_type_button btn btn-secondary flex-shrink-0 mb-1" t-att-data-item-type-id="itemType.id" title="Drag &amp; Drop a field in the PDF">
                        <span class="d-flex justify-content-start align-items-center">
                            <i t-attf-class="fa fa-fw #{itemType.icon ? itemType.icon : icon_type[itemType.item_type]} me-2" role="img"/>
                            <t t-esc="itemType.name"/>
                        </span>
                    </button>
                </t>
            </div>
        </div>
    </t>
</templates>
