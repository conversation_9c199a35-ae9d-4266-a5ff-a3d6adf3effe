<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="sign.SignTemplateControlPanel">
        <ControlPanel display="controlPanelDisplay">
            <t t-set-slot="control-panel-always-buttons">
                <div class="d-flex gap-1">
                    <t t-if="props.actionType === '' || props.actionType === 'sign_send_request'">
                        <button type="button" class="btn btn-primary o_sign_template_send" t-on-click="onSendClick">Send</button>
                        <button type="button" class="btn btn-primary o_sign_template_sign_now text-nowrap" t-on-click="onSignNowClick">Sign Now</button>
                        <t t-if="showShareButton">
                            <button type="button" class="btn btn-secondary o_sign_template_share" t-on-click="onShareClick">Share</button>
                        </t>
                    </t>
                    <t t-elif="props.actionType === 'sign_template_edit'">
                        <button type="button" class="btn btn-primary o_sign_template_save" t-on-click="props.goBackToKanban">Save</button>
                        <button t-if="nextTemplate" type="button" class="btn btn-primary o_sign_template_next" t-on-click="onNextDocumentClick">
                            Next Document
                        </button>
                    </t>
                </div>
            </t>
        </ControlPanel>
    </t>
</templates>
