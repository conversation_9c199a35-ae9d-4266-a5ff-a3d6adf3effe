# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
{
    'name': 'Sign',
    'version': '1.0',
    'category': 'Sales/Sign',
    'sequence': 105,
    'summary': "Send documents to sign online and handle filled copies",
    'description': """
Sign and complete your documents easily. Customize your documents with text and signature fields and send them to your recipients.\n
Let your customers follow the signature process easily.
    """,
    'website': 'https://www.odoo.com/app/sign',
    'depends': ['mail', 'attachment_indexation', 'portal', 'sms'],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'data/mail_activity_type_data.xml',
        'data/mail_templates.xml',
        'data/sign_data.xml',
        'data/sign_tour.xml',
        'views/sign_template_views_mobile.xml',
        'wizard/sign_duplicate_template_with_pdf_views.xml',
        'wizard/sign_send_request_views.xml',
        'views/sign_request_templates.xml',
        'views/sign_template_templates.xml',
        'views/sign_request_views.xml',
        'views/sign_template_views.xml',
        'views/sign_log_views.xml',
        'views/sign_portal_templates.xml',
        'views/mail_activity_views.xml',
        'views/res_config_settings_views.xml',
        'views/res_users_views.xml',
        'views/res_partner_views.xml',
        'views/sign_pdf_iframe_templates.xml',
        'views/terms_views.xml',
        'report/sign_log_reports.xml',
        'report/green_saving_reports.xml'
    ],
    'demo': [
        'data/sign_demo.xml',
    ],
    'application': True,
    'post_init_hook': '_sign_post_init',
    'uninstall_hook': 'uninstall_hook',
    'installable': True,
    'license': 'OEEL-1',
    'assets': {
        'sign.assets_pdf_iframe': [
            'web/static/src/libs/fontawesome/css/font-awesome.css',
            'web/static/lib/bootstrap/scss/_functions.scss',
            'web/static/src/scss/functions.scss',
            'web/static/src/scss/pre_variables.scss',
            'web/static/lib/bootstrap/scss/_variables.scss',
            'web/static/lib/bootstrap/scss/_variables-dark.scss',
            'web/static/lib/bootstrap/scss/_maps.scss',
            'web/static/lib/bootstrap/scss/vendor/_rfs.scss',
            'web/static/lib/bootstrap/scss/mixins/_deprecate.scss',
            'web/static/lib/bootstrap/scss/mixins/_utilities.scss',
            'web/static/lib/bootstrap/scss/mixins/_breakpoints.scss',
            'web/static/lib/bootstrap/scss/mixins/_grid.scss',
            'web/static/lib/bootstrap/scss/_utilities.scss',
            'web/static/lib/bootstrap/scss/_grid.scss',
            'web/static/src/scss/bs_mixins_overrides.scss',
            'web/static/lib/bootstrap/scss/utilities/_api.scss',
            'web/static/src/scss/utils.scss',
            'web/static/src/scss/primary_variables.scss',
            'web_enterprise/static/src/scss/primary_variables.scss',
            'sign/static/src/css/iframe.css',
            'web/static/src/scss/secondary_variables.scss',
            'sign/static/src/scss/iframe.scss',
        ],
        'sign.assets_green_report': [
            'sign/static/src/css/green_saving_reports.scss',
        ],
        'web.assets_backend': [
            'sign/static/src/js/**/*',
            'sign/static/src/scss/sign_common.scss',
            'sign/static/src/scss/sign_backend.scss',
            'sign/static/src/activity/**',
            'sign/static/src/mixin/**/*',
            'sign/static/src/components/**/*',
            'sign/static/src/backend_components/**/*',
            'sign/static/src/views/**/*',
            'sign/static/src/dialogs/**/*',
            'sign/static/src/services/**/*',
            'sign/static/src/fields/**/*',
        ],
        'web.assets_frontend': [
            'sign/static/src/components/**/*',
            'sign/static/src/scss/sign_common.scss',
            'sign/static/src/scss/sign_frontend.scss',
            'sign/static/src/dialogs/**/*',
            'sign/static/src/services/**/*',
        ],
        'web.assets_tests': [
            'sign/static/tests/tours/**/*',
        ],
        'web.qunit_suite_tests': [
            'sign/static/tests/**/*',
            ('remove', 'sign/static/tests/tours/**/*'),
            ('remove', 'sign/static/tests/mock_server/**/*'),
        ],
        'web.assets_unit_tests': [
            'sign/static/tests/mock_server/**/*',
        ],
        'sign.assets_public_sign': [
            ('include', 'web._assets_helpers'),
            ('include', 'web._assets_frontend_helpers'),
            'web/static/src/scss/pre_variables.scss',
            'web/static/lib/bootstrap/scss/_variables.scss',
            'web/static/lib/bootstrap/scss/_variables-dark.scss',
            'web/static/lib/bootstrap/scss/_maps.scss',
            ('include', 'web._assets_bootstrap_frontend'),

            'web/static/src/libs/fontawesome/css/font-awesome.css',
            'web/static/src/scss/fontawesome_overridden.scss',
            ('include', 'web._assets_core'),

            'web/static/lib/jquery/jquery.js',

            'web/static/lib/popper/popper.js',
            'web/static/lib/bootstrap/js/dist/util/index.js',
            'web/static/lib/bootstrap/js/dist/dom/data.js',
            'web/static/lib/bootstrap/js/dist/dom/event-handler.js',
            'web/static/lib/bootstrap/js/dist/dom/manipulator.js',
            'web/static/lib/bootstrap/js/dist/dom/selector-engine.js',
            'web/static/lib/bootstrap/js/dist/util/config.js',
            'web/static/lib/bootstrap/js/dist/util/component-functions.js',
            'web/static/lib/bootstrap/js/dist/util/backdrop.js',
            'web/static/lib/bootstrap/js/dist/util/focustrap.js',
            'web/static/lib/bootstrap/js/dist/util/sanitizer.js',
            'web/static/lib/bootstrap/js/dist/util/scrollbar.js',
            'web/static/lib/bootstrap/js/dist/util/swipe.js',
            'web/static/lib/bootstrap/js/dist/util/template-factory.js',
            'web/static/lib/bootstrap/js/dist/base-component.js',
            'web/static/lib/bootstrap/js/dist/alert.js',
            'web/static/lib/bootstrap/js/dist/button.js',
            'web/static/lib/bootstrap/js/dist/carousel.js',
            'web/static/lib/bootstrap/js/dist/collapse.js',
            'web/static/lib/bootstrap/js/dist/dropdown.js',
            'web/static/lib/bootstrap/js/dist/modal.js',
            'web/static/lib/bootstrap/js/dist/offcanvas.js',
            'web/static/lib/bootstrap/js/dist/tooltip.js',
            'web/static/lib/bootstrap/js/dist/popover.js',
            'web/static/lib/bootstrap/js/dist/scrollspy.js',
            'web/static/lib/bootstrap/js/dist/tab.js',
            'web/static/lib/bootstrap/js/dist/toast.js',

            'web_editor/static/src/scss/web_editor.common.scss',
            'web_editor/static/src/scss/web_editor.frontend.scss',

            'sign/static/src/scss/sign_common.scss',
            'sign/static/src/scss/sign_frontend.scss',
            'sign/static/src/components/**/*',
            'sign/static/src/dialogs/**/*',
            'sign/static/src/services/**/*',

            'sign/static/tests/tours/**/*', 
            'web_tour/static/src/tour_pointer/**/*',
            'web_tour/static/src/tour_service/**/*',
            'web/static/lib/hoot-dom/**/*',
        ]
    }
}
