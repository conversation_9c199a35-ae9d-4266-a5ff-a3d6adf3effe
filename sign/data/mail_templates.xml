<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="sign_template_mail_request">
<table border="0" cellpadding="0" style="background-color: white; padding: 0px; border-collapse:separate;">
    <tr>
        <td valign="top">
        <p>Hello <t t-esc="record.partner_id.name"/>,</p>
        <t t-esc="record.create_uid.name"/>
        (<a t-att-href="record._get_mail_link(record.create_uid.email, subject)"
            style="color:#428BCA; text-decoration:none;"
            target="_blank"><t t-esc="record.create_uid.email"/></a>)
        has requested your signature on the document <t t-esc="record.sign_request_id.reference"/>.
        </td>
    </tr>
    <tr t-if="body"><td valign="top">
        <div style="margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;">
            <span>
                <t t-out="body"/>
            </span>
        </div>
    </td></tr>
    <tr><td valign="top">
        <div style="margin:16px auto; text-align:center;">
            <a t-att-href="link"
                style="padding: 8px 16px 8px 16px; border-radius: 3px; background-color:#875A7B; text-align:center; text-decoration:none; color: #FFFFFF;">
                Sign document
            </a>
        </div>
    </td></tr>
    <t t-if="show_validity">
        <tr><td>You have until <t t-out="record.sign_request_id.validity"/> to sign the document.</td></tr>
    </t>
    <tr><td valign="top">
        <div style="opacity: 0.7;">
            <strong>Warning</strong> do not forward this email to other people!<br/>
            They will be able to access this document and sign it as yourself.<br/>
            <span>Your IP address and localization are associated to your signature to ensure traceability.</span>
        </div>
        <br/>
        <div style="opacity: 0.7;"><small>If you do not wish to receive future reminders about this document,
            <a t-att-href="'%s/sign/sign_cancel/%s/%s' % (record.get_base_url(), record.id, record.access_token)"
            style="color: #000000; text-decoration:none; opacity:0.7;"> click here to cancel it.</a></small></div>
    </td></tr>
    <t t-set="company" t-value="record.create_uid.company_id"/>
    <tr t-if="use_sign_terms">
        <t t-if="company.sign_terms_type == 'html'">
            <td style="margin:16px auto; text-align:center;">
                <a t-att-href="'%s/sign/terms' % record.get_base_url()" class="text-dark">Terms &amp; Conditions</a>
            </td>
        </t>
        <t t-else="">
            <td>
                <t t-out="company.sign_terms" class="text-dark"/>
            </td>
        </t>
    </tr>
</table>
        <t t-out="user_signature"/>
    </template>

    <template id="sign_template_mail_refused">
<table border="0" cellpadding="0" style="background-color: white; padding: 0px; border-collapse:separate;">
    <tr><td valign="top">
        <p>Hello <t t-out="recipient.name"/>,</p>
        <t t-if="recipient.id == refuser.id">You have refused the document</t>
        <t t-else="">
            <t t-out="refuser.name"/>
            (<a t-att-href="record._get_mail_link(refuser.email, subject)"
                style="color:#428BCA; text-decoration:none;"
                target="_blank"><t t-out="refuser.email"/></a>)
            has refused the document <t t-out="record.reference"/>
        </t>
    </td></tr>
    <tr t-if="body"><td valign="top">
        <div style="margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;">
            <span>
                <t t-out="body"/>
            </span>
        </div>
    </td></tr>
    <tr><td valign="top">
        <div style="margin:16px auto; text-align:center;">
            <a t-att-href="link"
                style="padding: 8px 16px 8px 16px; border-radius: 3px; background-color:#875A7B; text-align:center; text-decoration:none; color: #FFFFFF;">
                View document
            </a>
        </div>
    </td></tr>
    <tr><td valign="top">
        <div style="opacity: 0.7;">
            <strong>Warning</strong> do not forward this email to other people!<br/>
        </div>
    </td></tr>
</table>
    </template>

    <template id="sign_template_mail_completed">
<table border="0" cellpadding="0" style="background-color: white; padding: 0px; border-collapse:separate;">
    <tr><td valign="top">
        <p>Hello <t t-esc="recipient_name"/>,</p>
        <t t-if="request_edited">The document <t t-esc="record.reference"/> has been edited, completed and signed by </t>
        <t t-else="">The document <t t-esc="record.reference"/> has been completed and signed by </t>
        <t t-foreach="signers" t-as="signer">
            <!-- signer_first is generated by qweb for-each and is a boolean that is set to true in the first iteration -->
            <t t-if="not signer_first">and </t>
            <t t-if="recipient_id == signer.get('id')">you</t>
            <t t-else="">
                <t t-esc="signer.get('name')"/>
                (<a t-att-href="record._get_mail_link(signer.get('email'), subject)"
                    style="color:#428BCA; text-decoration:none;"
                    target="_blank"><t t-esc="signer.get('email')"/></a>)</t></t>.
    </td></tr>
    <tr t-if="body"><td valign="top">
        <div style="margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;">
            <span>
                <t t-out="body"/>
            </span>
        </div>
    </td></tr>
    <tr><td valign="top">
        <div style="margin:16px auto; text-align:center">
            <a t-att-href="link"
                style="padding: 8px 16px 8px 16px; border-radius: 3px; background-color:#875A7B; text-align:center; text-decoration:none; color: #FFFFFF;">
                Signed document
            </a>
        </div>
    </td></tr>
</table>
    </template>

    <template id="sign_template_mail_not_enough_credits">
        <table border="0" cellpadding="0" style="background-color: white; padding: 0px; border-collapse:separate;">
            <tr>
                <td valign="top">
                    <p>Hello <t t-esc="recipient_name"/>,</p>
                    The document <t t-esc="record.reference"/> has been signed by
                    <t t-esc="signer.name"/>
                        (<a t-att-href="record._get_mail_link(signer.email, subject)"
                            style="color:#428BCA; text-decoration:none;"
                            target="_blank"><t t-esc="signer.email"/></a>)
                    <span class="text-dark">
                        without the requested extra-authentification step (<span t-esc="auth_method"/>)
                    </span> because you don't have enough credits for this operation.
                    The signatory was able to finish signing, but was not asked to authenticate fully.
                    <p>If you wish, you can request the document again after buying more credits for the operation.</p>
                    <p>
                        If you do not want to receive these notifications anymore, you can disable the extra-authentication step in the
                        <code>
                            Sign > Configuration > Roles
                        </code>
                        menu.
                    </p>
                </td>
            </tr>
        </table>
    </template>

    <template id="message_signature_link">
        <p>A document has been signed and a copy attached to <t t-esc="request.reference_doc.display_name"/>:
            <a href="#" t-att-data-oe-model="request._name" t-att-data-oe-id="request.id"> <t t-esc="request.reference"/></a>
        </p>
    </template>


    <template id="sign_mail_notification_light" inherit_id="mail.mail_notification_light" primary="True">
        <xpath expr="//table[@role='presentation'][@style]" position="attributes">
            <attribute name="style" add="width: 100%; table-layout: fixed;" separator=" "/>
        </xpath>
        <xpath expr="//table[@role='presentation'][@width='590']" position="attributes">
            <attribute name="style" add="max-width: 650px; width: 100%; table-layout: fixed;" separator=" "/>
        </xpath>
    </template>
</odoo>
