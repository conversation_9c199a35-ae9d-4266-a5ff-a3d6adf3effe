# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign
# 
# Translators:
# Larisa Pop, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>yall Kindmurr, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_partner__signature_count
#: model:ir.model.fields,field_description:sign.field_res_users__signature_count
msgid "# Signatures"
msgstr "# Semnături"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "#7898678"
msgstr "#7898678"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"%(partner)s validated the signature by SMS with the phone number "
"%(phone_number)s."
msgstr ""
"%(partner)s a validat semnătura prin SMS de pe numărul de telefon "
"%(phone_number)s."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "%s couldn't sign the document due to an insufficient credit error."
msgstr ""
"%snu s-a putut semna documentul din cauza unei erori de credit insuficiente."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been edited and signed"
msgstr "%s a fost modificat și semnat"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "%s has been linked to this sign request."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been signed"
msgstr "%s a fost semnat"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s: missing credits for extra-authentication"
msgstr "%s: credite lipsă pentru extra-autentificare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "(UTC)"
msgstr "(UTC)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
")\n"
"            has refused the document"
msgstr ""
")\n"
"            a refuzat documentul"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
")\n"
"        has requested your signature on the document"
msgstr ""
")\n"
"        v-a solicitat semnătura pe document"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "******1234"
msgstr "******1234"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid ""
"- <em>\n"
"                                            Waiting Signature\n"
"                                        </em>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "- <em>Cancelled</em>"
msgstr ""

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "- or -"
msgstr "- sau -"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "1001"
msgstr "1001"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "192.168.1.1"
msgstr "192.168.1.1"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "20-03-2000"
msgstr "20-03-2000"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18"
msgstr "2023-08-18"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18 - 12:30:45"
msgstr "2023-08-18 - 12:30:45"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created by:</b>"
msgstr "<b>Creat de:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created on:</b>"
msgstr "<b>Creat în:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Creation IP Address:</b>"
msgstr "<b>Adresă creare IP:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Document ID:</b>"
msgstr "<b>ID Document</b>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "<b>Drag & drop “Signature”</b> into the bottom of the document."
msgstr "<b>Trageți și plasați \"Semnătura\"</b> în partea de jos a documentului."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Gas:</b> 18,700 Pounds of CO² = 18,700*0.4536 = 8482.32 kg"
msgstr "<b>Gas:</b> 18,700 Pounds of CO² = 18,700*0.4536 = 8482.32 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signature:</b>"
msgstr "<b>Semnătură:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signers:</b>"
msgstr "<b>Semnatari:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Solid Waste:</b> 1290 Pounds = 1290*0.4536 = 585.14 kg"
msgstr "<b>Deșeuri solide:</b> 1290 Pounds = 1290*0.4536 = 585.14 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Total Energy:</b> 27 Millions BTU = 27,000,000*0.0002931 = 7.91 kWh"
msgstr ""
"<b>Energie totală:</b> 27 Millions BTU = 27,000,000*0.0002931 = 7.91 kWh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Water:</b> 23,400 Gallons = 23,400*3.78541 = 88578.59 L"
msgstr "<b>Apă:</b> 23,400 Galoane = 23,400*3.78541 = 88578.59 L"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Wood Use:</b> 4 US Short Tons = 4*907.18474 = 3628.73 kg"
msgstr "<b>Utilizare lemn:</b> 4 Tone scurte SUA = 4*907.18474 = 3628.73 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<br/>(the email access has not been sent)"
msgstr "<br/>(accesul prin e-mail nu a fost trimis)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "<i class=\"fa fa-check\"/> A fresh link has just been sent to your inbox!"
msgstr "<i class=\"fa fa-check\"/> Tocmai ți-a fost trimis un link nou în inbox!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-check\"/> The document's integrity is valid."
msgstr "<i class=\"fa fa-check\"/> Integritatea documentului este valabilă."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> The document's integrity could not "
"be verified."
msgstr ""
"<i class=\"fa fa-exclamation-circle\"/>Integritatea documentului nu a putut "
"fi verificată."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-globe\"/> View"
msgstr "<i class=\"fa fa-globe\"/> Vizualizare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"<i class=\"fa fa-info-circle\"/> Links sent via email expire after a set "
"delay to increase security."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Link-urile trimise prin e-mail expiră după "
"o perioadă de timp stabilită pentru a spori securitatea."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to settings"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Înapoi la setări"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Preview"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Previzualizare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Cancelled </i></small>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Waiting Signature </i></small>"
msgstr "<small><i> Așteaptă semnătură </i></small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>Email Verification: The signatory has confirmed control of their "
"email inbox by clicking on a unique link</small>"
msgstr ""
"<small>Verificarea e-mailului: Semnatarul a confirmat controlul asupra "
"căsuței sale de e-mail făcând click pe un link unic</small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>SMS: The signatory has confirmed their control of the phone number "
"using a unique code sent by SMS</small>"
msgstr ""
"<small>SMS: Semnatarul a confirmat controlul asupra numărului de telefon cu "
"ajutorul unui cod unic trimis prin SMS</small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_partner_view_form
msgid "<span class=\"o_stat_text\">Signature Requested</span>"
msgstr "<span class=\"o_stat_text\">Semnătură solicitată</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "<span class=\"o_stat_text\">Signed Document</span>"
msgstr "<span class=\"o_stat_text\">Document semnat</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid ""
"<span class=\"text-muted\" invisible=\"not share_link\">Sharing will create "
"a copy of the file to sign. That file can be reached by the link below. "
"Every public user using the link will generate a document when the Signature"
" is complete. The link is private, only those that receive the link will be "
"able to sign it.</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"not share_link\">Partajarea va crea o"
" copie a fișierului pentru semnare. Acel fișier poate fi accesat prin link-"
"ul de mai jos. Fiecare utilizator public care utilizează link-ul va genera "
"un document atunci când semnarea este completă. Linkul este privat, doar cei"
" care primesc linkul vor putea să îl semneze.</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<span class=\"text-muted\">Not available</span>"
msgstr "<span class=\"text-muted\">Indisponibil</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_type_view_form
msgid "<span>(1.0 = full page size)</span>"
msgstr "<span>(1.0 = dimensiunea completă a paginii)</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<span>This is a preview of your Terms &amp; Conditions.</span>"
msgstr ""
"<span>Aceasta este o previzualizare a Termene și amp; Condiții.</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid ""
"<span>You won't receive any notification for this signature request "
"anymore.</span>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<strong>Creation Date:</strong>"
msgstr "<strong>Data creării:</strong>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>"
msgstr ""
"<strong>Avertisment</strong> nu transmiteți acest e-mail altor "
"persoane!<br/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>\n"
"            They will be able to access this document and sign it as yourself.<br/>\n"
"            <span>Your IP address and localization are associated to your signature to ensure traceability.</span>"
msgstr ""
"<strong>Avertizare</strong>nu trimiteți acest e-mail altor persoane!<br/>\n"
"            Vor putea accesa acest document și îl puteți semna ca dvs.<br/>\n"
"            <span>Adresa dvs. IP și localizarea sunt asociate semnăturii dvs. pentru a asigura urmărirea</span>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid ""
"A SMS will be sent to the following phone number. Please update it if it's "
"not relevant."
msgstr ""
"Un SMS va fi trimis la următorul număr de telefon. Vă rugăm să îl "
"actualizați dacă nu este relevant."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.message_signature_link
msgid "A document has been signed and a copy attached to"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A non-shared sign request's should not have any signer with an empty partner"
msgstr ""
"O cerere de semnare care nu este partajată nu trebuie să aibă niciun "
"semnatar cu un partener necompletat"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A shared sign request should only have one signer with an empty partner"
msgstr ""
"O cerere de semnătură partajată ar trebui să aibă un singur semnatar cu un "
"partener necompletat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "A shower uses approximately 65 L of water"
msgstr "Un duș utilizează aproximativ 65 L de apă"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "A signature request has been linked to this document: %s"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "A valid sign request needs at least one sign request item"
msgstr ""
"O cerere de semnătură valabilă necesită cel puțin un element de cerere de "
"semnătură"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ABCD1234"
msgstr "ABCD1234"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Access Logs"
msgstr "Jurnale Acces"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_warning
msgid "Access warning"
msgstr "Avertizare acces"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Access your signed documents"
msgstr "Accesați documentele dvs. semnate"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_via_link
msgid "Accessed Through Token"
msgstr "Accesat prin Token"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__category
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Action"
msgstr "Acțiune"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction
msgid "Action Needed"
msgstr "Acțiune necesară"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__action
msgid "Action Performed"
msgstr "Acțiune realizată"

#. module: sign
#: model:ir.model.fields,help:sign.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Acțiunile pot declanșa un comportament specific, cum ar fi deschiderea "
"vizualizării calendarului sau marcarea automată, așa cum se face la "
"încărcarea unui document"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__active
#: model:ir.model.fields,field_description:sign.field_sign_template__active
msgid "Active"
msgstr "Activ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_ids
msgid "Activities"
msgstr "Activități"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorator Excepție Activitate"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.report,name:sign.action_sign_request_print_logs
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Activity Logs"
msgstr "Jurnal activitate"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_state
msgid "Activity State"
msgstr "Stare activitate"

#. module: sign
#: model:ir.model,name:sign.model_mail_activity_type
msgid "Activity Type"
msgstr "Tip activitate"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "Pictograma tipului de activitate"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__sign_log_ids
msgid "Activity logs linked to this request"
msgstr "Jurnalele de activitate legate de această solicitare"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initials_all_pages_dialog.js:0
msgid "Add Initials"
msgstr "Adăugați inițiale"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add Once"
msgstr "Adăugați o dată"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Add document tags here"
msgstr "Adăugați aici etichete pentru documente"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add to all pages"
msgstr "Adăugați la toate paginile"

#. module: sign
#: model:res.groups,name:sign.group_sign_manager
msgid "Administrator"
msgstr "Administrator"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.js:0
msgid "Adopt Your Signature"
msgstr "Adoptați semnătura dvs."

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__signed
msgid "After Signature"
msgstr "După Semnătură"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__alignment
msgid "Alignment"
msgstr "Aliniament"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "All"
msgstr "Tot"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_all_request_action
#: model:ir.ui.menu,name:sign.sign_request_documents
msgid "All Documents"
msgstr "Toate Documentele"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "All signers must have valid email addresses"
msgstr "Toți semnatarii trebuie să aibă adrese de e-mail valide"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow signatories to provide their identity using itsme® (available in "
"Belgium and the Netherlands)."
msgstr ""
"Permiteți semnatarilor să își furnizeze identitatea utilizând itsme® "
"(disponibil în Belgia și Țările de Jos)."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow users to define the users or groups which have access to the template."
msgstr ""
"Permiteți utilizatorilor să definească utilizatorii sau grupurile care au "
"acces la șablon."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "An average computer will consume 750 Wh"
msgstr "Un computer obișnuit consumă 750 Wh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Antwrep"
msgstr "Antwrep"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Archive"
msgstr "Arhivează"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Archived"
msgstr "Arhivat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Are you sure you want to cancel the sign request?"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__start_sign
msgid "At least one signer has signed the document."
msgstr "Cel puțin un semnatar a semnat documentul."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Attach a file"
msgstr "Atașează un fișier"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__attachment_id
msgid "Attachment"
msgstr "Atașament"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__attachment_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__attachment_ids
msgid "Attachments"
msgstr "Atașamente"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Authenticate by SMS"
msgstr "Autentificați-vă prin SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__group_ids
msgid "Authorized Groups"
msgstr "Grupuri autorizate"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Groups:"
msgstr "Grupuri autorizate:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__authorized_ids
msgid "Authorized Users"
msgstr "Utilizatori autorizați"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Users:"
msgstr "Utilizatori autorizați:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__auto_field
msgid "Auto-fill Partner Field"
msgstr "Completare automată câmp Partener"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__available
msgid "Available in new templates"
msgstr "Disponibil în șabloane noi"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Back to %s"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on"
msgstr "Bazat pe"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on various websites, here are our comparisons:"
msgstr "Pe baza diferitelor site-uri web, iată comparațiile noastre:"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__sent
msgid "Before Signature"
msgstr "Înainte de semnătură"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "By"
msgstr "De"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid ""
"By clicking Adopt & Sign, I agree that the chosen signature/initials will be"
" a valid electronic representation of my hand-written signature/initials for"
" all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"Făcând clic pe Adoptare și semnare, sunt de acord că semnătura/inițialele "
"alese vor fi o reprezentare electronică validă a semnăturii/inițialelor mele"
" scrise de mână pentru toate scopurile, atunci când sunt utilizate în "
"documente, inclusiv în contracte obligatorii din punct de vedere legal."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message_cc
msgid "CC Message"
msgstr "Mesaj CC"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__cancel
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Cancel"
msgstr "Anulează"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Cancel Sign Request"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Canceled"
msgstr "Anulat(ă)"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__canceled
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Cancelled"
msgstr "Anulat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Carbon Emissions"
msgstr "Emisii de carbon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Center"
msgstr "Centru"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Certificate <i class=\"fa fa-download\"/>"
msgstr "Certificat <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Certificate of Completion<br/>"
msgstr "Certificat de Finalizare<br/>"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,field_description:sign.field_sign_request_item__change_authorized
msgid "Change Authorized"
msgstr "Modificare Autorizare"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__checkbox
#: model:sign.item.type,name:sign.sign_item_type_checkbox
msgid "Checkbox"
msgstr "Checkbox"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__is_sharing
msgid "Checked if this template has created a shared document for you"
msgstr ""
"S-a verificat dacă acest șablon a creat un document partajat pentru dvs."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_item_navigator.js:0
msgid "Click to start"
msgstr "Click pentru a începe"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:sign.template,redirect_url_text:sign.template_sign_1
#: model:sign.template,redirect_url_text:sign.template_sign_2
#: model:sign.template,redirect_url_text:sign.template_sign_3
#: model:sign.template,redirect_url_text:sign.template_sign_4
#: model:sign.template,redirect_url_text:sign.template_sign_5
#: model:sign.template,redirect_url_text:sign.template_sign_tour
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "Close"
msgstr "Închide"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__color
#: model:ir.model.fields,field_description:sign.field_sign_request__color
#: model:ir.model.fields,field_description:sign.field_sign_request_item__color
#: model:ir.model.fields,field_description:sign.field_sign_template__color
msgid "Color"
msgstr "Culoare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__color
msgid "Color Index"
msgstr "Index Culori"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Communication history"
msgstr "Istoric comunicare"

#. module: sign
#: model:ir.model,name:sign.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_company
#: model:sign.item.type,placeholder:sign.sign_item_type_company
msgid "Company"
msgstr "Companie"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__communication_company_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__communication_company_id
msgid "Company used for communication"
msgstr "Companie utilizată pentru comunicare"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__completed
msgid "Completed"
msgstr "Finalizat"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document
msgid "Completed Document"
msgstr "Document Completat"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document_attachment_ids
msgid "Completed Documents"
msgstr "Documente finalizate"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_closed
msgid "Completed Signatures"
msgstr "Semnături completate"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completion_date
msgid "Completion Date"
msgstr "Data finalizării"

#. module: sign
#: model:ir.model,name:sign.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: sign
#: model:ir.ui.menu,name:sign.menu_sign_configuration
msgid "Configuration"
msgstr "Configurare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Configure the field types that can be used to sign documents (placeholder, "
"auto-completion, ...), as well as the values for selection fields in "
"signable documents."
msgstr ""
"Configurați tipurile de câmpuri care pot fi utilizate pentru semnarea "
"documentelor (spațiu rezervat, autocompletare, ...), precum și valorile "
"pentru câmpurile de selecție din documentele de semnat."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
msgid "Confirm"
msgstr "Confirmă"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Congrats, your signature is ready to be submitted!"
msgstr "Felicitări, semnătura dvs. este gata să fie trimisă!"

#. module: sign
#: model_terms:web_tour.tour,rainbow_man_message:sign.sign_tour
msgid "Congratulations, you signed your first document!"
msgstr "Felicitări, ați semnat primul dvs. document!"

#. module: sign
#: model:ir.model,name:sign.model_res_partner
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__partner_id
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Contact"
msgstr "Contactați"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts"
msgstr "Contacte"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts in copy"
msgstr "Contacte în copie"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__cc_partner_ids
msgid ""
"Contacts in copy will be notified by email once the document is either fully"
" signed or refused."
msgstr ""
"Persoanele de contact în copie vor fi informate prin e-mail în momentul în "
"care documentul este semnat integral sau refuzat."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__cc_partner_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__cc_partner_ids
msgid "Copy to"
msgstr "Copiaza în"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Create Sign Tags"
msgstr "Creați etichete de semnătură"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Create date"
msgstr "Crează dată"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_date
#: model:ir.model.fields,field_description:sign.field_sign_log__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__create
msgid "Creation"
msgstr "Creare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Current status of the signature request"
msgstr "Starea curentă a cererii de semnătură"

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_customer
msgid "Customer"
msgstr "ClientClient"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__access_url
msgid "Customer Portal URL"
msgstr "Adresa URL a Portalului Clientului"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: model:sign.item.type,name:sign.sign_item_type_date
#: model:sign.item.type,placeholder:sign.sign_item_type_date
msgid "Date"
msgstr "Dată"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Date (UTC)"
msgstr "Data (UTC)"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__default
msgid "Default"
msgstr "Implicit"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_height
msgid "Default Height"
msgstr "Înălțime implicită"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__default_sign_template_id
msgid "Default Signature Template"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_width
msgid "Default Width"
msgstr "Lățime implicită"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__sequence
msgid "Default order"
msgstr "Comandă implicită"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Delete"
msgstr "Șterge"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign"
msgstr "Ștergeți semnătura"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign item"
msgstr "Ștergeți elementul de semnătură"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Deliver one-time codes by SMS to identify signatories when signing a "
"document."
msgstr ""
"Furnizați coduri unice prin SMS pentru a identifica semnatarii atunci când "
"semnează un document."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Action"
msgstr "Acțiune demonstrativă"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo IP"
msgstr "Demonstrație IP"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Latitude"
msgstr "Demonstrație Latitudine"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Longitude"
msgstr "Demostrație Longitudine"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Partner"
msgstr "Demonstrație Partener"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Details"
msgstr "Detalii"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials
msgid "Digital Initials"
msgstr "Inițiale digitale"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials_frame
msgid "Digital Initials Frame"
msgstr "Cadru inițiale digitale"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature
msgid "Digital Signature"
msgstr "Semnatură digitală"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature_frame
msgid "Digital Signature Frame"
msgstr "Cadru semnătură digitală"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_users_view_form
#: model_terms:ir.ui.view,arch_db:sign.view_users_form_simple_modif
msgid "Digital Signatures"
msgstr "Semnături digitale"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Display"
msgstr "Afișare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_option__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_role__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__display_name
#: model:ir.model.fields,field_description:sign.field_sign_log__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_preview_ready
msgid "Display sign preview button"
msgstr "Afișați butonul de previzualizare a semnăturii"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Document"
msgstr "Document"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Document <i class=\"fa fa-download\"/>"
msgstr "Document <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Document Details"
msgstr "Detalii document"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference
#: model:ir.model.fields,field_description:sign.field_sign_request_item__reference
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "Document Name"
msgstr "Nume document"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__page
msgid "Document Page"
msgstr "Pagina Documentului"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__template_id
msgid "Document Template"
msgstr "Document șablon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Document saved as Template."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Document to send for signature or to sign yourself."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Document(s) to sign"
msgstr "Document(e) de semnat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Document/Signer"
msgstr "Document / Semnatar"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_action
#: model:ir.ui.menu,name:sign.sign_request_menu
msgid "Documents"
msgstr "Documente"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Download"
msgstr "Descarcă"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
msgid "Download Certificate"
msgstr "Descărcați certificatul"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Download Document"
msgstr "Descărcare document"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
msgid "Drag & Drop a field in the PDF"
msgstr "Tragere și fixare câmp în PDF"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Drag and drop"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Draw your most beautiful signature!<br>You can also create one automatically"
" or load a signature from your computer."
msgstr ""
"Desenează-ți cea mai frumoasă semnătură!<br>De asemenea, puteți crea una "
"automat sau încărca o semnătură de pe computer."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Dropdown menu"
msgstr "Meniul derulant"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "ERROR: Invalid PDF file!"
msgstr "EROARE: Fișier PDF invalid!"

#. module: sign
#: model:ir.actions.report,name:sign.sign_report_green_savings_action
msgid "Ecological Savings by using Electronic Signatures"
msgstr "Economii ecologice prin utilizarea semnăturilor electronice"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit"
msgstr "Editare"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Edit Template"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit field types"
msgstr "Editarea tipurilor de câmp"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit selection values"
msgstr "Editarea valorilor de selecție"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit template name"
msgstr "Editați nume șablon"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signer_email
#: model:sign.item.type,name:sign.sign_item_type_email
#: model:sign.item.type,placeholder:sign.sign_item_type_email
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email"
msgstr "E-mail"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__subject
msgid "Email Subject"
msgstr "Subiect Email"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email Verification"
msgstr "Verificare e-mail"

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_employee
msgid "Employee"
msgstr "Angajat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Energy"
msgstr "Energie"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Enter the code received through SMS to complete your signature"
msgstr "Introduceți codul primit prin SMS pentru a vă completa semnătura"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Error"
msgstr "Eroare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Error 404"
msgstr "Eroare 404"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Existing sign items are not allowed to be changed"
msgstr "Elementele de semnare existente nu pot fi modificate"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__expired
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__expired
msgid "Expired"
msgstr "Expirat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Expiring Soon"
msgstr "Expiră curând"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr "Pas suplimentar de autentificare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__favorited_ids
msgid "Favorite of"
msgstr "Favorit de"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__favorited_ids
msgid "Favorited Users"
msgstr "Utilizatori preferați"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__name
msgid "Field Name"
msgstr "Nume câmp"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_type_menu
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Field Types"
msgstr "Tipuri de câmpuri"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Fields"
msgstr "Câmpuri"

#. module: sign
#: model:ir.model,name:sign.model_sign_item
msgid "Fields to be sign on Document"
msgstr "Câmpurile de semnat pe Document"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__datas
msgid "File Content (base64)"
msgstr "Conținut fișier (base64)"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/hooks.js:0
msgid "File Error"
msgstr "Eroare fișier"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_pdf
msgid "File name"
msgstr "Nume fișier"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__filename
msgid "Filename"
msgstr "Nume fișier"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Filled by"
msgstr "Completat de"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Final Validation"
msgstr "Validare finala"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Follow the guide to sign the document."
msgstr "Urmați ghidul pentru a semna documentul."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_follower_ids
msgid "Followers"
msgstr "Urmăritori"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Pictogramă Font awesome, de ex. fa-sarcini"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"For 1000kg of paper usage, with 10% of recycled paper, environmental savings"
" are based on"
msgstr ""
"Pentru 1000 kg de hârtie utilizată, cu 10% hârtie reciclată, economiile de "
"mediu se bazează pe"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr ""
"Obligați semnatarul să se identifice folosind o a doua metodă de "
"autentificare. "

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Frame"
msgstr "Cadru"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_has_hash
msgid "Frame Has Hash"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__frame_hash
msgid "Frame Hash"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_value
msgid "Frame Value"
msgstr "Valoare ramă"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__signed
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Fully Signed"
msgstr "Complet semnat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Future Activities"
msgstr "Activități viitoare"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid "Generate PDF"
msgstr "Generați PDF"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Geolocation"
msgstr "Geolocation"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_report_green_savings
msgid "Green Savings"
msgstr "Economii ecologice"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Green Savings Report"
msgstr "Raport privind economiile ecologice"

#. module: sign
#: model:ir.model,name:sign.model_report_sign_green_savings_report
msgid "Green Savings Report model"
msgstr "Model de raport privind economiile ecologice"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Green Savings Summary"
msgstr "Sumar economii ecologice"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Group By"
msgstr "Grupează după"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_1
msgid "HR"
msgstr "Resurse Umane HR"

#. module: sign
#: model:ir.model,name:sign.model_ir_http
msgid "HTTP Routing"
msgstr "Rutare HTTP"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__has_default_template
msgid "Has Default Template"
msgstr "Are șablon implicit"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__has_sign_requests
msgid "Has Sign Requests"
msgstr "Are cereri de semnare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__height
msgid "Height"
msgstr "Înălțime"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Hello"
msgstr "Bună"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__tip
msgid "Hint displayed in the signing hint"
msgstr "Indicație afișată în sugestia de semnare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Home"
msgstr "Acasă"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "How are these results calculated?"
msgstr "Cum sunt calculate aceste rezultate?"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "How do we calculate?"
msgstr "Cum facem calculele?"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "I've got a total weight, and now?"
msgstr "Am o greutate totală, și acum?"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__id
#: model:ir.model.fields,field_description:sign.field_sign_item__id
#: model:ir.model.fields,field_description:sign.field_sign_item_option__id
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__id
#: model:ir.model.fields,field_description:sign.field_sign_item_role__id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__id
#: model:ir.model.fields,field_description:sign.field_sign_log__id
#: model:ir.model.fields,field_description:sign.field_sign_request__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__id
#: model:ir.model.fields,field_description:sign.field_sign_template__id
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__id
msgid "ID"
msgstr "ID"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "IP"
msgstr "IP"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "IP Address"
msgstr "Adresă IP"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__ip
msgid "IP address of the visitor"
msgstr "Adresă IP vizitator"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__icon
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_icon
msgid "Icon"
msgstr "Pictogramă"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Pictograma care indică o activitate de excepție."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__module_sign_itsme
msgid "Identify with itsme®"
msgstr "Identificați-vă cu itsme®"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Dacă este bifat, mesaje noi necesită atenția dumneavoastră."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,help:sign.field_sign_request_item__change_authorized
msgid ""
"If checked, recipient of a document with this role can be changed after "
"having sent the request. Useful to replace a signatory who is out of office,"
" etc."
msgstr ""
"Dacă este bifat, destinatarul unui document cu acest rol poate fi schimbat "
"după trimiterea cererii. Util pentru a înlocui un semnatar care nu este la "
"muncă, etc."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error
#: model:ir.model.fields,help:sign.field_sign_request__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, există mesaje cu eroare de livrare."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you do not want to receive these notifications anymore, you can disable the extra-authentication step in the\n"
"                        <code>\n"
"                            Sign &gt; Configuration &gt; Roles\n"
"                        </code>\n"
"                        menu."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "If you do not wish to receive future reminders about this document,"
msgstr "Dacă nu doriți să primiți memento-uri viitoare despre acest document,"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you wish, you can request the document again after buying more credits "
"for the operation."
msgstr ""
"Dacă doriți, puteți solicita documentul din nou după ce cumpărați mai multe "
"credite pentru operațiune."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__in_progress_count
msgid "In Progress Count"
msgstr "Nr. În desfășurare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "In favorites, remove it"
msgstr "În favorite, eliminați-l"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_hash
msgid "Inalterability Hash"
msgstr "Inalterabilitate Hash"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Include a visual security frame around your signature"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Information"
msgstr "Informații"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__initial
msgid "Initial"
msgstr "Inițial"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_initial
#: model:sign.item.type,placeholder:sign.sign_item_type_initial
msgid "Initials"
msgstr "Inițiale"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Insert your terms & conditions here..."
msgstr "Introduceți termenii & condițiile aici ..."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__integrity
msgid "Integrity of the Sign request"
msgstr "Integritatea cererii de logare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__is_mail_sent
msgid "Is Mail Sent"
msgstr "Este trimis e-mail-ul"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__is_sharing
msgid "Is Sharing"
msgstr "Se partajează"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Is Signing"
msgstr "Se semnează"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__is_user_signer
msgid "Is User Signer"
msgstr "Este utilizator semnat"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "It's signed!"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Doe"
msgstr "John Doe"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Smith"
msgstr "Ion Popescu"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "L of water saved"
msgstr "L de apă economisiți"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_action_date
msgid "Last Action Date"
msgstr "Data ultimei acțiuni"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_date
#: model:ir.model.fields,field_description:sign.field_sign_log__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_reminder
msgid "Last reminder"
msgstr "Ultimul memento"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Late Activities"
msgstr "Activități întârziate"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__latitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__latitude
msgid "Latitude"
msgstr "Latitudine"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__validity
msgid "Leave empty for requests without expiration."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Left"
msgstr "Stânga"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's <b>prepare & sign</b> our first document."
msgstr "Haide să <b>să pregătim și să semnăm</b>primul nostru document. "

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's send the request by email."
msgstr "Să trimitem solicitarea prin e-mail."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url_text
msgid "Link Label"
msgstr "Etichetă de legătură"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__activity_id
msgid "Linked Activity"
msgstr "Activitate Legată"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference_doc
msgid "Linked To"
msgstr "Legat de "

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reference_doc
msgid "Linked to"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_date
msgid "Log Date"
msgstr "Dată jurnal"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be deleted!"
msgstr "Istoricul cererilor de semnare nu poate fi șters!"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be modified!"
msgstr "Istoricul cererilor de semnare nu poate fi modificat!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Logo"
msgstr "Sigla"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__sign_log_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_log_view_tree
msgid "Logs"
msgstr "Jurnale"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__longitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__longitude
msgid "Longitude"
msgstr "Longitudine"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__mail_sent_order
msgid "Mail Sent Order"
msgstr "Comandă trimisă prin e-mail"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update_mail
msgid "Mail Update"
msgstr "Actualizare e-mail"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Malformed expression: %(exp)s"
msgstr "Expresie malformată: %(exp)s"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__group_manage_template_access
#: model:res.groups,name:sign.manage_template_access
msgid "Manage template access"
msgstr "Gestionați accesul la șablon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Mandatory field"
msgstr "Câmp obligatoriu"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message
msgid "Message"
msgstr "Mesaj"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error
msgid "Message Delivery error"
msgstr "Eroare de livrare a mesajului"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message_cc
msgid "Message to be sent to contacts in copy of the signed document"
msgstr ""
"Mesajul care urmează să fie trimis contactelor în copie a documentului "
"semnat"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message
msgid "Message to be sent to signers of the specified document"
msgstr "Mesaj care urmează să fie trimis semnatarilor documentului specificat"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "Missing Password"
msgstr "Lipsă Parolă"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_number
msgid "Mobile"
msgstr "Mobil"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Modify Template"
msgstr "Modificare Șablon"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__textarea
#: model:sign.item.type,name:sign.sign_item_type_multiline_text
#: model:sign.item.type,placeholder:sign.sign_item_type_multiline_text
msgid "Multiline Text"
msgstr "Text multilin"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Multiple Signature Requests"
msgstr "Cereri Semnături Multiple"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Data limită a activității mele"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_request_my_documents
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Documents"
msgstr "Documentele mele"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Favorites"
msgstr "Favorite mele"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Requests"
msgstr "Cererile mele"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Templates"
msgstr "Șabloanele mele"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_2
msgid "NDA"
msgstr "NDA"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__name
#: model:ir.model.fields,field_description:sign.field_sign_template__name
#: model:sign.item.type,name:sign.sign_item_type_name
#: model:sign.item.type,placeholder:sign.sign_item_type_name
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Name"
msgstr "Nume"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_role_name_uniq
msgid "Name already exists!"
msgstr "Numele există deja!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Name for the file"
msgstr "Nume pentru fișier"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Name of the file"
msgstr "Numele fișier"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Nearly there, keep going!"
msgstr "Aproape acolo, continuați!"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__need_my_signature
msgid "Need My Signature"
msgstr "Aveți nevoie de semnătura mea"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/PDF_iframe.js:0
msgid "Need a valid PDF to add signature fields!"
msgstr "Aveți nevoie de un PDF valid pentru a adăuga câmpuri de semnătură!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Need to sign documents?"
msgstr ""

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_send_request
msgid "New Signature Request"
msgstr "Cerere nouă de semnătură"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_template
msgid "New Template Name"
msgstr "Numele șablonului nou"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Newest"
msgstr "Cele mai noi"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Următoarea activitate din calendar"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data limită pentru următoarea activitate"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_summary
msgid "Next Activity Summary"
msgstr "Sumarul următoarei activități"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_id
msgid "Next Activity Type"
msgstr "Următorul tip de activitate"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
msgid "Next Document"
msgstr "Documentul următor"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Next signatory ("
msgstr "Următorul semnatar ("

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "No attachment was provided"
msgstr "Nu a fost furnizat niciun atașament"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "No document yet"
msgstr "Încă nu există niciun document"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "No specified reason"
msgstr "Niciun motiv specificat"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "No template yet"
msgstr "Niciun șablon încă"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "None"
msgstr "Fără"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, add it"
msgstr "Nu se află în favorite, adăugați-l"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, set it"
msgstr "Nu se află în favorite, setați-l"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__num_options
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__num_options
msgid "Number of Radio Button options"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents in progress for this template."
msgstr "Numărul de documente în curs pentru acest șablon."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents signed for this template."
msgstr "Numărul de documente semnate pentru acest șablon."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error_counter
msgid "Number of errors"
msgstr "Număr de erori"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numărul de mesaje care necesită acțiune"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__num_pages
msgid "Number of pages"
msgstr "Numărul de pagini"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Odoo Sign"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One can weighs 15 g"
msgstr "O cutie cântărește 15 g"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One liter of gas fuel will produce 8.9 kg of CO²"
msgstr "Un litru de combustibil de gaz va produce 8,9 kg de CO²"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One or more selection items have no associated options"
msgstr "Unul sau mai multe elemente de selecție nu au opțiuni asociate"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One uploaded file cannot be read. Is it a valid PDF?"
msgstr "Fișierul încărcat nu poate fi citit. Este un PDF valid?"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/controllers/terms.py:0
msgid "Oops"
msgstr "Oops"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "Operation successful"
msgstr "Operațiune reușită"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__value
msgid "Option"
msgstr "Opțiune"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_option
msgid "Option of a selection Field"
msgstr "Opțiunea unei selecții de câmp"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Optional Message..."
msgstr "Mesaj opțional ..."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url
msgid "Optional link for redirection after signature"
msgstr "Link opțional pentru redirecționare după semnătură"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url_text
msgid "Optional text to display on the button link"
msgstr "Text opțional de afișat pe linkul butonului"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Options"
msgstr "Opțiuni"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__original_template_id
msgid "Original File"
msgstr "Fișier original"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "PDF is encrypted"
msgstr "PDF Criptat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Paper Savings"
msgstr "Economii de hârtie"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Participants"
msgstr "Participanți"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__partner_id
msgid "Partner"
msgstr "Partener"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
msgid "Password is incorrect."
msgstr "Parolă incorectă."

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_phone
#: model:sign.item.type,placeholder:sign.sign_item_type_phone
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Phone"
msgstr "Telefon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Phone Number"
msgstr "Număr telefon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item_type__placeholder
msgid "Placeholder"
msgstr "Substitut"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Please configure senders'(%s) email addresses"
msgstr "Vă rugăm să configurați adresele(%s) de e-mail ale expeditorilor"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Please select recipients for the following roles: %(roles)s"
msgstr ""
"Vă rugăm să selectați destinatarii pentru următoarele roluri: %(roles)s"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_url
msgid "Portal Access URL"
msgstr "Adresa URL de acces la portal"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posX
msgid "Position X"
msgstr "Poziția X"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posY
msgid "Position Y"
msgstr "Poziția Y"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Preview"
msgstr "Previzualizare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Printed on"
msgstr "Tipărit la"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__progress
msgid "Progress"
msgstr "În desfășurare"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Public User"
msgstr "Utilizator public"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__radio
#: model:sign.item.type,name:sign.sign_item_type_radio
msgid "Radio Buttons"
msgstr "Butoane Radio"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__radio_items
msgid "Radio Items"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__radio_set_id
msgid "Radio button options"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_item_radio_set
msgid "Radio button set for keeping radio button items together"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__rating_ids
msgid "Ratings"
msgstr "Ratings"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Re-send SMS"
msgstr "Re-trimite SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url
msgid "Redirect Link"
msgstr "Link redirecționare"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__refuse
msgid "Refuse"
msgstr "Refuzați"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Refuse Document"
msgstr "Refuzați documentul"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Refuse to sign"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__refused
msgid "Refused Signature"
msgstr "Semnătura refuzată"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Reminder"
msgstr "Memento"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder_enabled
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder_enabled
msgid "Reminder Enabled"
msgstr ""

#. module: sign
#: model:ir.ui.menu,name:sign.sign_reports
msgid "Reports"
msgstr "Rapoarte"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_infos
msgid "Request Item Infos"
msgstr "Solicitați informații articol"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_patch.xml:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.xml:0
#: model:ir.model.fields.selection,name:sign.selection__mail_activity_type__category__sign_request
#: model:mail.activity.type,name:sign.mail_activity_data_signature_request
msgid "Request Signature"
msgstr "Cerere Semnătură"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Request expiration date must be set in the future."
msgstr "Data de expirare a cererii trebuie să fie setată în viitor."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_total
msgid "Requested Signatures"
msgstr "Semnături solicitate"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__required
msgid "Required"
msgstr "Necesar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Resend"
msgstr "Retrimite"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resend the invitation"
msgstr "Re-trimiteți invitația"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resent!"
msgstr "Retrimis!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__responsible_id
#: model:ir.model.fields,field_description:sign.field_sign_template__user_id
msgid "Responsible"
msgstr "Responsabil"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__responsible_count
msgid "Responsible Count"
msgstr "Contabil responsabil"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_user_id
msgid "Responsible User"
msgstr "Utilizator responsabil"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Restore"
msgstr "Restabilire"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Right"
msgstr "Dreapta"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__role_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__role_id
msgid "Role"
msgstr "Rol"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_tree
msgid "Role Name"
msgstr "Nume Rol"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_role_menu
msgid "Roles"
msgstr "Roluri"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_iframe.js:0
msgid "Rotate Clockwise"
msgstr "Rotește in sensul acelor de ceasornic"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "SMS Sent"
msgstr "SMS Trimis"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_token
msgid "SMS Token"
msgstr "SMS Token"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_3
msgid "Sales"
msgstr "Vânzări"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__save
msgid "Save"
msgstr "Salvează"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Save as Template"
msgstr "Salvare ca Șablon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Saved"
msgstr "Salvat"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Search <span class=\"nolabel\"> (in Document)</span>"
msgstr "Căutați <span class=\"nolabel\"> (in Document)</span>"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__access_token
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_token
msgid "Security Token"
msgstr "Token de securitate"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_selection
msgid "Select an option"
msgstr "Selectează o opțiune"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Select the contact who should sign, according to their role.<br>In this "
"example, select your own contact to sign the document yourself."
msgstr ""
"Selectați persoana de contact care trebuie să semneze, în funcție de rolul "
"său.<br>În acest exemplu, selectați propriul contact pentru a semna dvs. "
"documentul."

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__selection
#: model:sign.item.type,name:sign.sign_item_type_selection
#: model:sign.item.type,placeholder:sign.sign_item_type_selection
msgid "Selection"
msgstr "Selecție"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__option_ids
msgid "Selection options"
msgstr "Opțiuni selecție"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Send"
msgstr "Trimite"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Send SMS"
msgstr "Trimite SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_id
msgid "Send To"
msgstr "Trimite la"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "Send a new link"
msgstr "Trimiteți un link nou"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Send a reminder"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Send the invitation"
msgstr "Trimite Invitația"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Sent"
msgstr "Trimis"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_wait
msgid "Sent Requests"
msgstr "Cereri trimise"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sent by"
msgstr "Trimis de către"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_settings_action
#: model:ir.ui.menu,name:sign.sign_item_settings_menu
msgid "Settings"
msgstr "Setări"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Share"
msgstr "Partajează"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Share & Close"
msgstr "Partajează și închide"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Share Document by Link"
msgstr "Partajează documentul prin link"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__share_link
msgid "Share Link"
msgstr "Împarte legatură"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Shareable"
msgstr "Se poate distribui"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/models/sign_template.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__shared
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__shared
msgid "Shared"
msgstr "Partajat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Afișați toate înregistrările care au data următoarei acțiuni înainte de data"
" de astăzi"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Show standard terms & conditions on signature requests"
msgstr "Afișați termenii și condițiile standard pe cererile de semnătură"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: model:ir.ui.menu,name:sign.menu_document
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Sign"
msgstr "Sign"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__use_sign_terms
msgid "Sign Default Terms & Conditions"
msgstr "Semnați termenii și condițiile implicite"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms
msgid "Sign Default Terms and Conditions"
msgstr "Semnați termenii și condițiile implicite"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_html
msgid "Sign Default Terms and Conditions as a Web page"
msgstr "Semnați termenii și condițiile implicite ca pagină web"

#. module: sign
#: model:ir.model,name:sign.model_sign_duplicate_template_pdf
msgid "Sign Duplicate Template with new PDF"
msgstr "Semnați șablonul duplicat cu un nou PDF"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "Sign Next Document"
msgstr "Semnați documentul următor"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Sign Now"
msgstr "Semnează acum"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__mail_sent_order
msgid "Sign Order"
msgstr "Semnați comanda"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_id
msgid "Sign Request"
msgstr "Cerere de semnare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_item_id
msgid "Sign Request Item"
msgstr "Semnează articolul de solicitare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__sign_send_request_id
msgid "Sign Send Request"
msgstr "Semnare Trimitere Solicitare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Sign Settings"
msgstr "Setări semnătură"

#. module: sign
#: model:ir.model,name:sign.model_sign_template_tag
msgid "Sign Template Tag"
msgstr "Semnează eticheta șablonului"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms
msgid "Sign Terms & Conditions"
msgstr "Semnați Termenii și condițiile"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_html
msgid "Sign Terms & Conditions as a Web page"
msgstr "Semnați termenii și condițiile ca pagină web"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_type
msgid "Sign Terms & Conditions format"
msgstr "Semnați formatul Termeni și condiții"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Sign all"
msgstr "Semnați tot"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Sign document"
msgstr "Semnează documentul"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sign now"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Sign requests"
msgstr "Cereri de semnare"

#. module: sign
#: model:ir.model,name:sign.model_sign_log
msgid "Sign requests access history"
msgstr "Istoricul accesului cererilor de semnare"

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request
msgid "Sign send request"
msgstr "Semnează solicitarea de trimitere"

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request_signer
msgid "Sign send request signer"
msgstr "Semnați cererea de trimitere a semnatarului"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"Sign up for Odoo Sign to manage your own documents and signature requests!"
msgstr ""
"Înscrieți-vă la Odoo Sign pentru a vă gestiona propriile documente și cereri"
" de semnătură!"

#. module: sign
#: model:ir.actions.server,name:sign.sign_reminder_cron_ir_actions_server
msgid "Sign: Send mail reminder"
msgstr "Semnare: Trimitere memento prin e-mail"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory"
msgstr "Semnatar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory's hash:"
msgstr "Semnătura digitală a semnatarului:"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__sign
#: model:sign.item.type,name:sign.sign_item_type_signature
#: model:sign.item.type,placeholder:sign.sign_item_type_signature
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signature"
msgstr "Semnătură"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signature Date"
msgstr "Data Semnăturii"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_item_id
msgid "Signature Item"
msgstr "Articol Semnătură"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_option_action
msgid "Signature Item Options"
msgstr "Opțiuni articol semnătură"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_role
msgid "Signature Item Party"
msgstr "Obiect de semnătură Partener"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_role_action
msgid "Signature Item Role"
msgstr "Rol obiect de semnătură"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_type_action
#: model:ir.model,name:sign.model_sign_item_type
msgid "Signature Item Type"
msgstr "Tip obiect de semnătură"

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item_value
msgid "Signature Item Value"
msgstr "Valoare obiect de semnătură"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_item_ids
msgid "Signature Items"
msgstr "Articole Semnături"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_model_patch.js:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.js:0
#: model:ir.model,name:sign.model_sign_request
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_request_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_id
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Signature Request"
msgstr "Solicitare Semnătură"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %(file_name)s"
msgstr "Cerere de semnătură - %(file_name)s"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %s"
msgstr "Cerere de semnare - %s"

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item
msgid "Signature Request Item"
msgstr "Element solicitare semnătură"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_item_action
msgid "Signature Request Items"
msgstr "Elemente solicitare semnătură"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_item_id
msgid "Signature Request item"
msgstr "Element solicitare semnătură"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_request_ids
msgid "Signature Requests"
msgstr "Solicitări Semnătură"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__start_sign
msgid "Signature Started"
msgstr "Semnătură începută"

#. module: sign
#: model:ir.model,name:sign.model_sign_template
msgid "Signature Template"
msgstr "Șablon Semnătură"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Signature configuration"
msgstr "Configurare semnătură"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid ""
"Signature requested for template: %(template)s\n"
"Signatories: %(signatories)s"
msgstr ""
"Semnătură solicitată pentru șablon: %(template)s\n"
"Semnatari: %(signatories)s"

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid "Signature(s)"
msgstr "Semnătură(i)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signature:"
msgstr "Semnătură:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_menu_sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signatures"
msgstr "Semnături"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Signed"
msgstr "Semnat."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__signed_count
msgid "Signed Count"
msgstr "Număr de semnături"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Signed Documents"
msgstr "Documente semnate"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signed_without_extra_auth
msgid "Signed Without Extra Authentication"
msgstr "Semnat fără autentificare suplimentară"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "Signed document"
msgstr "Document semnat"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signing_date
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signed on"
msgstr "Semnat în"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__partner_id
msgid "Signer"
msgstr "Semnatar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Signer One 2 Many"
msgstr "Semnatar Unul la mai Mulți"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Signers"
msgstr "Semnatari"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signers_count
msgid "Signers Count"
msgstr "Numărătoare de semnatari"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Signing Date"
msgstr "Data semnării"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signing Events"
msgstr "Evenimente de semnare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__set_sign_order
msgid "Signing Order"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Since you're the one signing this document, you can do it directly within "
"Odoo.<br>External users can use the link provided by email."
msgstr ""
"Deoarece sunteți cel care semnează acest document, o puteți face direct în "
"Odoo.<br>Utilizatorii externi pot utiliza link-ul furnizat prin e-mail."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Some fields have still to be completed"
msgstr "Încă mai trebuie completate unele câmpuri"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some required items are not filled"
msgstr "Unele elemente obligatorii nu sunt completate"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some unauthorised items are filled"
msgstr "Unele elemente neautorizate sunt completate"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Somebody is already filling a document which uses this template"
msgstr "Cineva completează deja un document care folosește acest șablon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Sorry, an error occurred, please try to fill the document again."
msgstr ""
"Ne pare rău, a apărut o eroare, vă rugăm să încercați să completați "
"documentul din nou."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Sorry, you cannot refuse this document"
msgstr "Ne pare rău, nu puteți refuza acest document"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__set_sign_order
msgid ""
"Specify the order for each signer. The signature request only gets sent to                                     the next signers in the sequence when all signers from the previous level have                                     signed the document.\n"
"                                    "
msgstr ""
"Specificați ordinea pentru fiecare semnatar. Cererea de semnătură este trimisă următorilor semnatari din secvență numai atunci când toți semnatarii de la nivelul anterior            au semnat documentul.\n"
"                                    "

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_default
msgid "Standard"
msgstr "Standard"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__state
#: model:ir.model.fields,field_description:sign.field_sign_request_item__state
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "State"
msgstr "Județ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__request_state
msgid "State of the request on action log"
msgstr "Starea cererii în jurnalul de acțiuni"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Status"
msgstr "Stare"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stare bazată pe activități\n"
"Întârziată: data activitații este deja trecută\n"
"Astăzi: data activității este astăzi\n"
"Planificate: activități viitoare."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Stop Sharing"
msgstr "Opriți distribuirea"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__subject
msgid "Subject"
msgstr "Subiect"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Summary"
msgstr "Sumar"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__name
msgid "Tag Name"
msgstr "Nume etichetă"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_template_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Nume etichetă existent deja!"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_template_tag_action
#: model:ir.model.fields,field_description:sign.field_sign_request__template_tags
#: model:ir.model.fields,field_description:sign.field_sign_template__tag_ids
#: model:ir.ui.menu,name:sign.sign_template_tag_menu
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Tags"
msgstr "Etichete"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Tags:"
msgstr "Etichete:"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__auto_field
msgid ""
"Technical name of the field on the partner model to auto-complete this "
"signature field at the time of signature."
msgstr ""
"Denumirea tehnică a câmpului de pe modelul partenerului pentru completarea "
"automată a acestui câmp de semnătură în momentul semnării."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__template_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__template_id
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template"
msgstr "Șablon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.js:0
#: code:addons/sign/static/src/views/hooks.js:0
msgid "Template %s"
msgstr "Șablon %s"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Template Properties"
msgstr "Proprietăți șablon"

#. module: sign
#: model:ir.actions.server,name:sign.sign_template_tour_trigger_action
msgid "Template Sample Contract.pdf trigger"
msgstr "Șablon model de contract.pdf trigger"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template or Tag"
msgstr "Șablon sau etichetă"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.act_window,name:sign.sign_template_action
#: model:ir.ui.menu,name:sign.sign_template_menu
msgid "Templates"
msgstr "Șabloane"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Terms &amp; Conditions"
msgstr "Termeni și condiții"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__html
msgid "Terms as Web Page"
msgstr "Termeni și condiții ca pagină web"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__plain
msgid "Terms in Email"
msgstr "Termeni în e-mail"

#. module: sign
#: model:ir.model.fields,help:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,help:sign.field_res_config_settings__sign_terms_type
msgid ""
"Terms in Email - The text will be displayed at the bottom of every signature request email.\n"
"\n"
"        Terms as Web Page - A link will be pasted at the bottom of every signature request email, leading to your content.\n"
"        "
msgstr ""
"Termeni în e-mail - Textul va fi afișat în partea de jos a fiecărui e-mail de solicitare a semnăturii.\n"
"\n"
"Termeni ca pagină web - Un link va fi lipit în partea de jos a fiecărui e-mail de solicitare a semnăturii, conducând la conținutul dvs."

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__text
#: model:sign.item.type,name:sign.sign_item_type_text
#: model:sign.item.type,placeholder:sign.sign_item_type_text
msgid "Text"
msgstr "Text"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
msgid "Thank You!"
msgstr "Mulțumesc!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"That's it, all done!<br>The document is signed, and a copy has been sent by "
"email to all participants, along with a traceability report."
msgstr ""
"Totul este gata! Documentul este semnat, iar o copie a fost trimisă prin "
"e-mail tuturor participanților, împreună cu un raport de trasabilitate."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"The Odoo Sign document you are trying to reach does not exist. The signature"
" request might have been deleted or modified."
msgstr ""
"Documentul Odoo Sign la care încercați să ajungeți nu există. Este posibil "
"ca cererea de semnătură să fi fost ștearsă sau modificată."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "The PDF's password is required to generate the final document."
msgstr "Parola PDF este necesară pentru a genera documentul final."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The completed document cannot be created because the sign request is not "
"fully signed"
msgstr ""
"Documentul completat nu poate fi creat deoarece cererea de semnare nu este "
"semnată integral"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "The computation is based on the website"
msgstr "Calculul se bazează pe site-ul web"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The contact of %(role)s has been changed from %(old_partner)s to "
"%(new_partner)s."
msgstr ""
"Persoana de contact %(role)s a fost schimbată de la %(old_partner)s la "
"%(new_partner)s."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "The document"
msgstr "Documentul"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document %s has been fully signed."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document (%s) has been rejected by one of the signers"
msgstr "Documentul (%s) a fost respins de unul dintre semnatari"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "The document has been refused"
msgstr "Documentul a fost refuzat"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document has been signed by a signer and cannot be edited"
msgstr "Documentul a fost semnat de un semnatar și nu poate fi editat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"The final document and this completion history have been sent by email "
"on&amp;nbsp;"
msgstr ""
"Documentul final și acest istoric de finalizare au fost trimise prin e-mail "
"pe&amp;nbsp;"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"The integrity of the document's history cannot be verified. This could mean "
"that signature values or the underlying PDF document may have been modified "
"after the fact."
msgstr ""
"Integritatea istoricului documentului nu poate fi verificată. Acest lucru ar"
" putea însemna că valorile semnăturii sau documentul PDF subiacent ar fi "
"putut fi modificate după fapt."

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid ""
"The mail address of %(partner)s has been updated. The request will be "
"automatically resent."
msgstr ""
"Adresa de e-mail a%(partner)s a fost actualizată. Cererea va fi retrimisă "
"automat."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The mail has been sent to contacts in copy: %(contacts)s"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/terms.py:0
msgid "The requested page is invalid, or doesn't exist anymore."
msgstr "Pagina solicitată este invalidă sau nu mai există."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "The role %s is required by the Sign application and cannot be deleted."
msgstr "Rolul %s este necesar pentru aplicația Sign și nu poate fi șters."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The sign request has not been fully signed"
msgstr "Cererea de semnare nu a fost semnată integral"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "The signature has been canceled by %(partner)s(%(role)s)"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature has been refused by %(partner)s(%(role)s)"
msgstr "Semnătura a fost refuzată de %(partner)s(%(role)s)"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature mail has been sent to: "
msgstr "Mail-ul cu semnătura a fost trimis la:"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__is_mail_sent
msgid "The signature mail has been sent."
msgstr "Mesajul cu semnătura a fost trimis."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "The signature request has been cancelled"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature request has been edited by: %s."
msgstr "Cererea de semnătură a fost editată de: %s."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
msgid "The template doesn't exist anymore."
msgstr "Șablonul nu mai există."

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid ""
"The template has more pages than the current file, it can't be applied."
msgstr ""
"Șablonul are mai multe pagini decât fișierul curent, acesta nu poate fi "
"aplicat."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"The total of sheets you saved is based on: the number of sent sign requests "
"x number of sheets in the document x (number of contacts who need to sign + "
"number of contacts in copy if the sign request is signed) = total of pages. "
"We assume that one page weights 0.005 kilograms."
msgstr ""
"Totalul de foi salvate se bazează pe: numărul de cereri de semnătură trimise"
" înmulțit cu numărul de foi din document înmulțit cu (numărul de contacte "
"care trebuie să semneze + numărul de contacte în copie dacă cererea de "
"semnătură este semnată) = total de pagini. Presupunem că o pagină cântărește"
" 0,005 kilograme."

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid "The uploaded file is not a valid PDF. Please upload a valid PDF file."
msgstr ""
"Fișierul încărcat nu este un PDF valid. Vă rugăm să încărcați un fișier PDF "
"valid."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "There are no signatures request."
msgstr "Nu există cerere de semnătură."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "There are other documents waiting for your signature:"
msgstr "Există și alte documente care așteaptă semnătura dumneavoastră:"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"There was an issue downloading your document. Please contact an "
"administrator."
msgstr ""
"A apărut o problemă la descărcarea documentului dumneavoastră. Vă rugăm să "
"contactați un administrator."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"There's no reason to panic, <br/>\n"
"                        you can still sign your document in a few clicks!"
msgstr ""
"Nu există niciun motiv de panică, <br/>\n"
"                        puteți semna în continuare documentul dvs. în câteva click-uri!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "These files cannot be read, they may be corrupted or encrypted."
msgstr "Aceste fișiere nu pot fi citite, pot fi corupte sau criptate."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "They will be removed from the uploaded files"
msgstr "Acestea vor fi eliminate din fișierele încărcate"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This function can only be called with sudo."
msgstr "Această funcție poate fi apelată numai cu sudo."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__reference
#: model:ir.model.fields,help:sign.field_sign_request_item__reference
msgid "This is how the document will be named in the mail"
msgstr "Acesta este modul în care documentul va fi numit prin mail"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "This link has expired."
msgstr "Acest link a expirat."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be refused"
msgstr "Această cerere de semnare nu poate fi refuzată"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be signed"
msgstr "Această cerere de semnare nu poate fi semnată"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request is not valid anymore"
msgstr "Această cerere de semnare nu mai este valabilă"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be filled"
msgstr "Acest element al cererii de semnare nu poate fi completat"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be refused"
msgstr "Acest element al cererii de semnare nu poate fi refuzat"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be signed"
msgstr "Acest element al cererii de semnare nu poate fi semnat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"This will keep all the already completed signature of this request and "
"disable every sent access, are you sure?"
msgstr ""
"Acest lucru va păstra toate semnăturile deja completate ale acestei cereri "
"și va dezactiva fiecare acces trimis, sunteți sigur?"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__tip
msgid "Tip"
msgstr "Sfat"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__sent
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__sent
msgid "To Sign"
msgstr "Pentru semnare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "To produce 1000 kg of wood, we have to cut 12 trees"
msgstr "Pentru a produce 1000 kg de lemn, trebuie să tăiem 12 copaci"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "To sign"
msgstr "Pentru semnare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Today Activities"
msgstr "Activitățile de astăzi"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__transaction_id
msgid "Transaction"
msgstr "Tranzacție"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Try Odoo Sign"
msgstr "Încercați Odoo Sign"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Try a sample contract"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Try our sample document"
msgstr "Încercați exemplul nostru de document"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Try out this sample contract."
msgstr "Încercați acest exemplu de contract."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__type_id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__item_type
msgid "Type"
msgstr "Tip"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Type a name or email..."
msgstr "Introduceți un nume sau un e-mail..."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipul de activitate de excepție înregistrată."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
msgid "Type tag name here"
msgstr "Introduceți aici numele etichetei"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "UTC"
msgstr "UTC"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Unable to send the SMS, please contact the sender of the document."
msgstr ""
"Nu puteți trimite SMS-ul, vă rugăm să contactați expeditorul documentului."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"Unable to sign the document due to missing required data. Please contact an "
"administrator."
msgstr ""
"Nu se poate semna documentul din cauza lipsei datelor necesare. Vă rugăm să "
"contactați un administrator."

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_role__auth_method__sms
msgid "Unique Code via SMS"
msgstr "Cod unic prin SMS"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update
msgid "Update"
msgstr "Actualizare"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload"
msgstr "Încărcare"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload PDF"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Upload a PDF"
msgstr "Încărcare PDF"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a PDF & Sign"
msgstr "Încărcați un PDF și semnați"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "Upload a PDF file or use an existing template to begin."
msgstr ""
"Încărcați un fișier PDF sau utilizați un șablon existent pentru a începe."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload a PDF file to create a reusable template."
msgstr ""

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Upload a PDF file to create your first template"
msgstr "Încărcați un fișier PDF pentru a crea primul dvs. șablon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a pdf that you want to sign directly"
msgstr "Încărcați un pdf pe care doriți să îl semnați direct"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Use Layout"
msgstr "Utilizați aspectul"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Use Tags to manage your Sign Templates and Sign Requests"
msgstr ""
"Utilizați etichete pentru a vă gestiona șabloanele și cererile de semnare"

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_duplicate_template_with_pdf
msgid "Use the layout of fields on a new PDF"
msgstr "Utilizați aspectul câmpurilor pe un PDF nou"

#. module: sign
#: model:ir.model,name:sign.model_res_users
#: model:ir.model.fields,field_description:sign.field_sign_log__user_id
#: model:sign.item.role,name:sign.sign_item_role_user
msgid "User"
msgstr "Utilizator"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__token
msgid "User token"
msgstr "Jeton Utilizator"

#. module: sign
#: model:res.groups,name:sign.group_sign_user
msgid "User: Own Templates"
msgstr "Utilizator: Șabloane proprii"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__validity
#: model:ir.model.fields,field_description:sign.field_sign_send_request__validity
msgid "Valid Until"
msgstr "Valabil până la"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Validate"
msgstr "Validează"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Validate & Send"
msgstr "Validați & trimiteți"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/edit_while_signing_signable_pdf_iframe.js:0
msgid "Validate & the next signatory is “%s”"
msgstr "Validați & următorul semnatar este \"%s\""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
msgid "Validate &amp; Send Completed Document"
msgstr "Validați &amp; trimiteți documentul completat"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Validation Code"
msgstr "Cod de validare"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_item_value_ids
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__value
msgid "Value"
msgstr "Valoare"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_option_value_uniq
msgid "Value already exists!"
msgstr "Valoarea există deja!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Verify"
msgstr "Verificați"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "View Document"
msgstr "Vizualizați documentul"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "View document"
msgstr "Vizualizați documentul"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__open
msgid "View/Download"
msgstr "Vizualizați/Descărcați"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Viewed/downloaded by"
msgstr "Vizualizat/descărcat de"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for me"
msgstr "În așteptarea mea"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for others"
msgstr "În așteptarea altora"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Warning"
msgstr "Avertizare"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Waste"
msgstr "Risipă"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Water"
msgstr "Apa"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"We can only send reminders in the future - as soon as we find a way to send reminders in the past we'll notify you.\n"
"In the mean time, please make sure to input a positive number of days for the reminder interval."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "We couldn't find the signature request you're looking for!"
msgstr "Nu am putut găsi cererea de semnătură pe care o căutați!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"We display to you a ratio based on the saved weight versus 1000 kg of paper "
"usage."
msgstr ""
"Vă afișăm un raport bazat pe greutatea economisită față de 1000 kg de hârtie"
" utilizată."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "We will send you this document by email once everyone has signed."
msgstr ""
"Vă vom trimite acest document prin e-mail după ce toată lumea a semnat."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid ""
"We'll send an email to warn other contacts in copy & signers with the reason"
" you provided."
msgstr ""
"Vom trimite un e-mail pentru a avertiza celelalte contacte din copie și "
"semnatarii cu motivul furnizat de dvs."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__website_message_ids
msgid "Website Messages"
msgstr "Mesaje de pe site-ul web"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__website_message_ids
msgid "Website communication history"
msgstr "Istoricul comunicării pe site-ul web"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Well done, your document is ready!<br>Let's send it to get our first "
"signature."
msgstr ""
"Foarte bine, documentul dvs. este gata!<br> Să-l trimitem pentru a obține "
"prima semnătură."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "What about those conversions I see?"
msgstr "Ce se întâmplă cu conversiile pe care le văd?"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
msgid "Why do you refuse to sign this document?"
msgstr "De ce refuzați să semnați acest document?"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__width
msgid "Width"
msgstr "Lățime"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Wood"
msgstr "Lemn"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Write email or search contact..."
msgstr "Scrieți un e-mail sau căutați un contact ..."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "Wrong password"
msgstr "Parolă greșită"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "XYZ123456"
msgstr "XYZ123456"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"You can contact the person who invited you to sign the document by email for"
" help."
msgstr ""
"Puteți contacta persoana care v-a invitat să semnați documentul prin e-mail "
"pentru ajutor."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You can only add new items for the current role"
msgstr "Puteți adăuga numai elemente noi pentru rolul curent"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"You can request a new link to access your document and sign it, it will  be "
"delivered in your inbox right away."
msgstr ""
"Puteți solicita un nou link pentru a accesa documentul dvs. și pentru a-l "
"semna, acesta va fi livrat imediat în căsuța dvs. poștală."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You can't delete a template for which signature requests exist but you can "
"archive it instead."
msgstr ""
"Nu puteți șterge un șablon pentru care există cereri de semnătură, dar îl "
"puteți arhiva în schimb."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You cannot reassign this signatory"
msgstr "Nu puteți reatribui acest semnatar"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You cannot share this document by link, because it has fields to be filled "
"by different roles. Use Send button instead."
msgstr ""
"Nu puteți partaja acest document prin link, deoarece are câmpuri care "
"trebuie completate cu roluri diferite. Utilizați în schimb butonul Trimite."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"You do not have access to these documents, please contact a Sign "
"Administrator."
msgstr ""
"Dacă nu aveți acces la aceste documente, vă rugăm să contactați un "
"administrator al modulului Sign."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "You have refused the document"
msgstr "Ați refuzat documentul"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "You have until"
msgstr "Aveți termen până la"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You must specify one signer for each role of your sign template"
msgstr ""
"Trebuie să specificați un semnatar pentru fiecare rol al șablonului dvs. de "
"semnătură"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You need to define a signatory"
msgstr "Trebuie să definiți un semnatar"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You should select at least one document to download."
msgstr "Trebuie să selectați cel puțin un document pentru descărcare."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "You will get the signed document by email."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "You won't receive any notification for this signature request anymore."
msgstr ""
"Nu veți mai primi nicio notificare pentru această cerere de semnătură."

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "You're one click away from automating your signature process!"
msgstr "Sunteți la un click distanță de automatizarea procesului de semnare!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Your Information"
msgstr "Informațiile dvs."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Your confirmation code is %s"
msgstr "Codul dvs. de confirmare este %s"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your email"
msgstr "Email-ul dvs."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid ""
"Your file is encrypted, PDF's password is required to generate final "
"document. The final document will be encrypted with the same password."
msgstr ""
"Fișierul dvs. este criptat, parola PDF este necesară pentru a genera "
"documentul final. Documentul final va fi criptat cu aceeași parolă."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your name"
msgstr "Numele dvs."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Your signature has been saved. Next signatory is"
msgstr "Semnătura dvs. a fost salvată. Următorul semnatar este"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid ""
"Your signature was not submitted. Ensure the SMS validation code is correct."
msgstr ""
"Semnătura dvs. nu a fost trimisă. Asigurați-vă că este corect codul de "
"validare din SMS."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "and"
msgstr "și"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "and the process will continue"
msgstr "iar procesul va continua"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "and:"
msgstr "și:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"because you don't have enough credits for this operation.\n"
"                    The signatory was able to finish signing, but was not asked to authenticate fully."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "can"
msgstr "cutie"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "cans"
msgstr "cutii"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "click here to cancel it."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "days."
msgstr "zile."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. +1 415 555 0100"
msgstr "de exemplu. +1 415 555 0100"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. 314159"
msgstr "de exemplu. 314159"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_form
msgid "e.g. Employee"
msgstr "e.g. Angajat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "e.g. Non-disclosure agreement"
msgstr "de exemplu, acordul de confidențialitate"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "every"
msgstr ""

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_checkbox
#: model:sign.item.type,tip:sign.sign_item_type_company
#: model:sign.item.type,tip:sign.sign_item_type_date
#: model:sign.item.type,tip:sign.sign_item_type_email
#: model:sign.item.type,tip:sign.sign_item_type_multiline_text
#: model:sign.item.type,tip:sign.sign_item_type_name
#: model:sign.item.type,tip:sign.sign_item_type_phone
#: model:sign.item.type,tip:sign.sign_item_type_radio
#: model:sign.item.type,tip:sign.sign_item_type_text
msgid "fill in"
msgstr "completați"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been completed and signed by"
msgstr "a fost completat și semnat de"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been edited, completed and signed by"
msgstr "a fost editat, completat și semnat de"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "has been signed by"
msgstr "a fost semnat de"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hour of computer use"
msgstr "oră de utilizare a calculatorului"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hours of computer use"
msgstr "ore de utilizare a calculatorului"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "https://c.environmentalpaper.org/"
msgstr "https://c.environmentalpaper.org/"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ip"
msgstr "ip"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "is free, forever, with unlimited users - and it's fun to use!"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kWh of energy saved"
msgstr "kWh de energie economisită"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of reduced carbon emissions"
msgstr "kg de emisii de carbon reduse"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of waste prevented"
msgstr "kg de deșeuri evitate"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of wood saved"
msgstr "kg de lemn economisit"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liter of car fuel"
msgstr "litru de combustibil auto"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liters of car fuel"
msgstr "litri de combustibil auto"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_initial
msgid "mark it"
msgstr "marcați-l"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "next"
msgstr "următor"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "on"
msgstr "pe"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "or"
msgstr "sau"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "sheets of paper saved"
msgstr "foi de hârtie economisite"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "shower"
msgstr "ploaie torenţială"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "showers"
msgstr "ploi torenţiale"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "sign"
msgstr "semn"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_signature
msgid "sign it"
msgstr "Semnează"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message
msgid "sign.message"
msgstr "sign.message"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_cc
msgid "sign.message_cc"
msgstr "sign.message_cc"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "tags"
msgstr "etichete"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "that's"
msgstr "care este"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "to sign the document."
msgstr "pentru a semna documentul."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "to:"
msgstr "către"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "tree"
msgstr "arbore"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "trees"
msgstr "arbori"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "we've got a list of consumption for 1000 kg of paper usage."
msgstr "avem o listă de consumuri pentru 1000 kg de hârtie utilizată."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "without the requested extra-authentification step ("
msgstr "fără etapa suplimentară de autentificare solicitată ("

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "you"
msgstr "dvs."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}}"
msgstr "{{green_report_el_title}}"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}} Summary"
msgstr "{{green_report_el_title}} Summary"
