# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2025
# NumerSpiral HBG, 2025
# <PERSON>, 2025
# <PERSON><PERSON> <diogocar<PERSON><PERSON><EMAIL>>, 2025
# <PERSON><PERSON><PERSON>, 2025
# <PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# Arxi, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_partner__signature_count
#: model:ir.model.fields,field_description:sign.field_res_users__signature_count
msgid "# Signatures"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "#7898678"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"%(partner)s validated the signature by SMS with the phone number "
"%(phone_number)s."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "%s couldn't sign the document due to an insufficient credit error."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been edited and signed"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "%s has been linked to this sign request."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been signed"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s: missing credits for extra-authentication"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "(UTC)"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
")\n"
"            has refused the document"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
")\n"
"        has requested your signature on the document"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "******1234"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid ""
"- <em>\n"
"                                            Waiting Signature\n"
"                                        </em>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "- <em>Cancelled</em>"
msgstr ""

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "- or -"
msgstr "- ou -"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "1001"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "192.168.1.1"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "20-03-2000"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18 - 12:30:45"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created by:</b>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created on:</b>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Creation IP Address:</b>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Document ID:</b>"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "<b>Drag & drop “Signature”</b> into the bottom of the document."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Gas:</b> 18,700 Pounds of CO² = 18,700*0.4536 = 8482.32 kg"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signature:</b>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signers:</b>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Solid Waste:</b> 1290 Pounds = 1290*0.4536 = 585.14 kg"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Total Energy:</b> 27 Millions BTU = 27,000,000*0.0002931 = 7.91 kWh"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Water:</b> 23,400 Gallons = 23,400*3.78541 = 88578.59 L"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Wood Use:</b> 4 US Short Tons = 4*907.18474 = 3628.73 kg"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<br/>(the email access has not been sent)"
msgstr "<br/>(o acesso de e-mail não foi enviado)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "<i class=\"fa fa-check\"/> A fresh link has just been sent to your inbox!"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-check\"/> The document's integrity is valid."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> The document's integrity could not "
"be verified."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-globe\"/> View"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"<i class=\"fa fa-info-circle\"/> Links sent via email expire after a set "
"delay to increase security."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to settings"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Voltar às definições"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Preview"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Cancelled </i></small>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Waiting Signature </i></small>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>Email Verification: The signatory has confirmed control of their "
"email inbox by clicking on a unique link</small>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>SMS: The signatory has confirmed their control of the phone number "
"using a unique code sent by SMS</small>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_partner_view_form
msgid "<span class=\"o_stat_text\">Signature Requested</span>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "<span class=\"o_stat_text\">Signed Document</span>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid ""
"<span class=\"text-muted\" invisible=\"not share_link\">Sharing will create "
"a copy of the file to sign. That file can be reached by the link below. "
"Every public user using the link will generate a document when the Signature"
" is complete. The link is private, only those that receive the link will be "
"able to sign it.</span>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<span class=\"text-muted\">Not available</span>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_type_view_form
msgid "<span>(1.0 = full page size)</span>"
msgstr "<span>(1.0 = tamanho da página inteira)</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<span>This is a preview of your Terms &amp; Conditions.</span>"
msgstr "Aqui está uma Pré-visualização dos seus Termos e Condições.</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid ""
"<span>You won't receive any notification for this signature request "
"anymore.</span>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<strong>Creation Date:</strong>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>\n"
"            They will be able to access this document and sign it as yourself.<br/>\n"
"            <span>Your IP address and localization are associated to your signature to ensure traceability.</span>"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid ""
"A SMS will be sent to the following phone number. Please update it if it's "
"not relevant."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.message_signature_link
msgid "A document has been signed and a copy attached to"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A non-shared sign request's should not have any signer with an empty partner"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A shared sign request should only have one signer with an empty partner"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "A shower uses approximately 65 L of water"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "A signature request has been linked to this document: %s"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "A valid sign request needs at least one sign request item"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ABCD1234"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Access Logs"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_warning
msgid "Access warning"
msgstr "Aviso de acesso"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Access your signed documents"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_via_link
msgid "Accessed Through Token"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__category
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Action"
msgstr "Ação"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction
msgid "Action Needed"
msgstr "Ação Necessária"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__action
msgid "Action Performed"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"As ações podem despoletar comportamentos específicos, como abrir uma "
"visualização do calendário ou marcar automaticamente como concluído quando é"
" enviado um documento"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__active
#: model:ir.model.fields,field_description:sign.field_sign_template__active
msgid "Active"
msgstr "Ativo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_ids
msgid "Activities"
msgstr "Atividades"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Marcador da Exceção na Atividade"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.report,name:sign.action_sign_request_print_logs
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Activity Logs"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_state
msgid "Activity State"
msgstr "Estado da Atividade"

#. module: sign
#: model:ir.model,name:sign.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipo de Atividade"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícone de Tipo de Atividade"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__sign_log_ids
msgid "Activity logs linked to this request"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initials_all_pages_dialog.js:0
msgid "Add Initials"
msgstr "Adicionar Iniciais"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add Once"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Add document tags here"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add to all pages"
msgstr ""

#. module: sign
#: model:res.groups,name:sign.group_sign_manager
msgid "Administrator"
msgstr "Administrador"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.js:0
msgid "Adopt Your Signature"
msgstr "Adotar a Sua Assinatura"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__signed
msgid "After Signature"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__alignment
msgid "Alignment"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "All"
msgstr "Tudo"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_all_request_action
#: model:ir.ui.menu,name:sign.sign_request_documents
msgid "All Documents"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "All signers must have valid email addresses"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow signatories to provide their identity using itsme® (available in "
"Belgium and the Netherlands)."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow users to define the users or groups which have access to the template."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "An average computer will consume 750 Wh"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Antwrep"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Archive"
msgstr "Arquivar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Archived"
msgstr "Arquivados"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Are you sure you want to cancel the sign request?"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__start_sign
msgid "At least one signer has signed the document."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Attach a file"
msgstr "Anexar um ficheiro"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__attachment_id
msgid "Attachment"
msgstr "Anexo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_attachment_count
msgid "Attachment Count"
msgstr "Número de Anexos"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__attachment_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__attachment_ids
msgid "Attachments"
msgstr "Anexos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Authenticate by SMS"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__group_ids
msgid "Authorized Groups"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Groups:"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__authorized_ids
msgid "Authorized Users"
msgstr "Utilizadores Autorizados"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Users:"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__auto_field
msgid "Auto-fill Partner Field"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__available
msgid "Available in new templates"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Back to %s"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on"
msgstr "Com base em"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on various websites, here are our comparisons:"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__sent
msgid "Before Signature"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "By"
msgstr "Por"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid ""
"By clicking Adopt & Sign, I agree that the chosen signature/initials will be"
" a valid electronic representation of my hand-written signature/initials for"
" all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message_cc
msgid "CC Message"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__cancel
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Cancel Sign Request"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Canceled"
msgstr "Cancelado"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__canceled
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Cancelled"
msgstr "Cancelada"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Carbon Emissions"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Center"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Certificate <i class=\"fa fa-download\"/>"
msgstr "Certificado <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Certificate of Completion<br/>"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,field_description:sign.field_sign_request_item__change_authorized
msgid "Change Authorized"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__checkbox
#: model:sign.item.type,name:sign.sign_item_type_checkbox
msgid "Checkbox"
msgstr "Caixa de seleção"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__is_sharing
msgid "Checked if this template has created a shared document for you"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_item_navigator.js:0
msgid "Click to start"
msgstr "Clique para iniciar"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:sign.template,redirect_url_text:sign.template_sign_1
#: model:sign.template,redirect_url_text:sign.template_sign_2
#: model:sign.template,redirect_url_text:sign.template_sign_3
#: model:sign.template,redirect_url_text:sign.template_sign_4
#: model:sign.template,redirect_url_text:sign.template_sign_5
#: model:sign.template,redirect_url_text:sign.template_sign_tour
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "Close"
msgstr "Encerrar"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__color
#: model:ir.model.fields,field_description:sign.field_sign_request__color
#: model:ir.model.fields,field_description:sign.field_sign_request_item__color
#: model:ir.model.fields,field_description:sign.field_sign_template__color
msgid "Color"
msgstr "Cor"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__color
msgid "Color Index"
msgstr "Índice de Cor"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Communication history"
msgstr "Histórico de comunicação"

#. module: sign
#: model:ir.model,name:sign.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_company
#: model:sign.item.type,placeholder:sign.sign_item_type_company
msgid "Company"
msgstr "Empresa"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__communication_company_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__communication_company_id
msgid "Company used for communication"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__completed
msgid "Completed"
msgstr "Concluído"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document
msgid "Completed Document"
msgstr "Documento Concluído"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document_attachment_ids
msgid "Completed Documents"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_closed
msgid "Completed Signatures"
msgstr "Assinaturas Concluídas"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completion_date
msgid "Completion Date"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_res_config_settings
msgid "Config Settings"
msgstr "Definições de Configuração"

#. module: sign
#: model:ir.ui.menu,name:sign.menu_sign_configuration
msgid "Configuration"
msgstr "Configuração"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Configure the field types that can be used to sign documents (placeholder, "
"auto-completion, ...), as well as the values for selection fields in "
"signable documents."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
msgid "Confirm"
msgstr "Confirmar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Congrats, your signature is ready to be submitted!"
msgstr ""

#. module: sign
#: model_terms:web_tour.tour,rainbow_man_message:sign.sign_tour
msgid "Congratulations, you signed your first document!"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_res_partner
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__partner_id
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Contact"
msgstr "Contacto"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts"
msgstr "Contactos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts in copy"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__cc_partner_ids
msgid ""
"Contacts in copy will be notified by email once the document is either fully"
" signed or refused."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__cc_partner_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__cc_partner_ids
msgid "Copy to"
msgstr ""

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Create Sign Tags"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Create date"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_date
#: model:ir.model.fields,field_description:sign.field_sign_log__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_date
msgid "Created on"
msgstr "Criado em"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__create
msgid "Creation"
msgstr "Criação"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Current status of the signature request"
msgstr ""

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_customer
msgid "Customer"
msgstr "Cliente"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__access_url
msgid "Customer Portal URL"
msgstr "URL do Portal do Cliente"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: model:sign.item.type,name:sign.sign_item_type_date
#: model:sign.item.type,placeholder:sign.sign_item_type_date
msgid "Date"
msgstr "Data"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Date (UTC)"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__default
msgid "Default"
msgstr "Predefinição"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_height
msgid "Default Height"
msgstr "Altura Predefinida"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__default_sign_template_id
msgid "Default Signature Template"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_width
msgid "Default Width"
msgstr "Largura Predefinida"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__sequence
msgid "Default order"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign item"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Deliver one-time codes by SMS to identify signatories when signing a "
"document."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Action"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo IP"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Latitude"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Longitude"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Partner"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Details"
msgstr "Detalhes"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials
msgid "Digital Initials"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials_frame
msgid "Digital Initials Frame"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature
msgid "Digital Signature"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature_frame
msgid "Digital Signature Frame"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_users_view_form
#: model_terms:ir.ui.view,arch_db:sign.view_users_form_simple_modif
msgid "Digital Signatures"
msgstr "Assinaturas Digitais"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Display"
msgstr "Mostrar"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_option__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_role__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__display_name
#: model:ir.model.fields,field_description:sign.field_sign_log__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__display_name
msgid "Display Name"
msgstr "Nome a Exibir"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_preview_ready
msgid "Display sign preview button"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Document"
msgstr "Documento"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Document <i class=\"fa fa-download\"/>"
msgstr "Documento <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Document Details"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference
#: model:ir.model.fields,field_description:sign.field_sign_request_item__reference
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "Document Name"
msgstr "Nome do Documento"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__page
msgid "Document Page"
msgstr "Página do Documento"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__template_id
msgid "Document Template"
msgstr "Modelo de Documento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Document saved as Template."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Document to send for signature or to sign yourself."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Document(s) to sign"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Document/Signer"
msgstr ""

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_action
#: model:ir.ui.menu,name:sign.sign_request_menu
msgid "Documents"
msgstr "Documentos"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Download"
msgstr "Transferir"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
msgid "Download Certificate"
msgstr "Transferir Certificado"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Download Document"
msgstr "Transferir Documento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
msgid "Drag & Drop a field in the PDF"
msgstr "Arrastar e Largar um campo no PDF"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Drag and drop"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Draw your most beautiful signature!<br>You can also create one automatically"
" or load a signature from your computer."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Dropdown menu"
msgstr "Menu dropdown"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "ERROR: Invalid PDF file!"
msgstr ""

#. module: sign
#: model:ir.actions.report,name:sign.sign_report_green_savings_action
msgid "Ecological Savings by using Electronic Signatures"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit"
msgstr "Editar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Edit Template"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit field types"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit selection values"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit template name"
msgstr "Editar nome do modelo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signer_email
#: model:sign.item.type,name:sign.sign_item_type_email
#: model:sign.item.type,placeholder:sign.sign_item_type_email
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email"
msgstr "E-mail"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__subject
msgid "Email Subject"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email Verification"
msgstr ""

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_employee
msgid "Employee"
msgstr "Funcionário"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Energy"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Enter the code received through SMS to complete your signature"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Error"
msgstr "Erro"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Error 404"
msgstr "Erro 404"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Existing sign items are not allowed to be changed"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__expired
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__expired
msgid "Expired"
msgstr "Expirado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Expiring Soon"
msgstr "Expira em breve"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__favorited_ids
msgid "Favorite of"
msgstr "Favorito de"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__favorited_ids
msgid "Favorited Users"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__name
msgid "Field Name"
msgstr "Nome do Campo"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_type_menu
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Field Types"
msgstr "Tipos de Campo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Fields"
msgstr "Campos"

#. module: sign
#: model:ir.model,name:sign.model_sign_item
msgid "Fields to be sign on Document"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__datas
msgid "File Content (base64)"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/hooks.js:0
msgid "File Error"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_pdf
msgid "File name"
msgstr "Nome do ficheiro"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__filename
msgid "Filename"
msgstr "Nome de ficheiro"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Filled by"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Final Validation"
msgstr "Validação Final"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Follow the guide to sign the document."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Parceiros)"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ícone do Font awesome ex. fa-tasks"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"For 1000kg of paper usage, with 10% of recycled paper, environmental savings"
" are based on"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Frame"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_has_hash
msgid "Frame Has Hash"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__frame_hash
msgid "Frame Hash"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_value
msgid "Frame Value"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__signed
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Fully Signed"
msgstr "Totalmente Assinado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Future Activities"
msgstr "Atividades Futuras"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid "Generate PDF"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Geolocation"
msgstr "Geolocalização"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_report_green_savings
msgid "Green Savings"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Green Savings Report"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_report_sign_green_savings_report
msgid "Green Savings Report model"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Green Savings Summary"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Group By"
msgstr "Agrupar Por"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_1
msgid "HR"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_ir_http
msgid "HTTP Routing"
msgstr "Roteamento HTTP"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__has_default_template
msgid "Has Default Template"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__has_message
msgid "Has Message"
msgstr "Tem Mensagem"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__has_sign_requests
msgid "Has Sign Requests"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__height
msgid "Height"
msgstr "Altura"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Hello"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__tip
msgid "Hint displayed in the signing hint"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Home"
msgstr "Início"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "How are these results calculated?"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "How do we calculate?"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "I've got a total weight, and now?"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__id
#: model:ir.model.fields,field_description:sign.field_sign_item__id
#: model:ir.model.fields,field_description:sign.field_sign_item_option__id
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__id
#: model:ir.model.fields,field_description:sign.field_sign_item_role__id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__id
#: model:ir.model.fields,field_description:sign.field_sign_log__id
#: model:ir.model.fields,field_description:sign.field_sign_request__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__id
#: model:ir.model.fields,field_description:sign.field_sign_template__id
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__id
msgid "ID"
msgstr "Id."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "IP"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "IP Address"
msgstr "Endereço IP"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__ip
msgid "IP address of the visitor"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__icon
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_icon
msgid "Icon"
msgstr "Ícone"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ícone para indicar uma exceção na atividade."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__module_sign_itsme
msgid "Identify with itsme®"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se marcado, há novas mensagens que requerem a sua atenção."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,help:sign.field_sign_request_item__change_authorized
msgid ""
"If checked, recipient of a document with this role can be changed after "
"having sent the request. Useful to replace a signatory who is out of office,"
" etc."
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error
#: model:ir.model.fields,help:sign.field_sign_request__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se estiver marcado, algumas mensagens têm um erro na entrega."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you do not want to receive these notifications anymore, you can disable the extra-authentication step in the\n"
"                        <code>\n"
"                            Sign &gt; Configuration &gt; Roles\n"
"                        </code>\n"
"                        menu."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "If you do not wish to receive future reminders about this document,"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you wish, you can request the document again after buying more credits "
"for the operation."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__in_progress_count
msgid "In Progress Count"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "In favorites, remove it"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_hash
msgid "Inalterability Hash"
msgstr "Hash de inalterabilidade"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Include a visual security frame around your signature"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Information"
msgstr "Informação"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__initial
msgid "Initial"
msgstr "Inicial"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_initial
#: model:sign.item.type,placeholder:sign.sign_item_type_initial
msgid "Initials"
msgstr "Iniciais"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Insert your terms & conditions here..."
msgstr "Insira os seus termos e condições aqui..."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__integrity
msgid "Integrity of the Sign request"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_is_follower
msgid "Is Follower"
msgstr "É Seguidor"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__is_mail_sent
msgid "Is Mail Sent"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__is_sharing
msgid "Is Sharing"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Is Signing"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__is_user_signer
msgid "Is User Signer"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "It's signed!"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Doe"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Smith"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "L of water saved"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_action_date
msgid "Last Action Date"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_date
#: model:ir.model.fields,field_description:sign.field_sign_log__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_reminder
msgid "Last reminder"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Late Activities"
msgstr "Atividades em Atraso"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__latitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__latitude
msgid "Latitude"
msgstr "Latitude"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__validity
msgid "Leave empty for requests without expiration."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Left"
msgstr "Esquerda"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's <b>prepare & sign</b> our first document."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's send the request by email."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url_text
msgid "Link Label"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__activity_id
msgid "Linked Activity"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference_doc
msgid "Linked To"
msgstr "Vinculado A"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reference_doc
msgid "Linked to"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_date
msgid "Log Date"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be deleted!"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be modified!"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Logo"
msgstr "Logótipo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__sign_log_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_log_view_tree
msgid "Logs"
msgstr "Registos"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__longitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__longitude
msgid "Longitude"
msgstr "Longitude"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__mail_sent_order
msgid "Mail Sent Order"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update_mail
msgid "Mail Update"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Malformed expression: %(exp)s"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__group_manage_template_access
#: model:res.groups,name:sign.manage_template_access
msgid "Manage template access"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Mandatory field"
msgstr "Campo obrigatório"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message
msgid "Message"
msgstr "Mensagem"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error
msgid "Message Delivery error"
msgstr "Erro na Entrega de Mensagem"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message_cc
msgid "Message to be sent to contacts in copy of the signed document"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message
msgid "Message to be sent to signers of the specified document"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "Missing Password"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_number
msgid "Mobile"
msgstr "Telemóvel"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Modify Template"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__textarea
#: model:sign.item.type,name:sign.sign_item_type_multiline_text
#: model:sign.item.type,placeholder:sign.sign_item_type_multiline_text
msgid "Multiline Text"
msgstr "Texto Múltiplas Linhas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Multiple Signature Requests"
msgstr "Pedidos de Múltiplas Assinatura"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Prazo da Minha Atividade"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_request_my_documents
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Documents"
msgstr "Meus documentos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Favorites"
msgstr "Os Meus Favoritos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Requests"
msgstr "Os Meus Pedidos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Templates"
msgstr ""

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_2
msgid "NDA"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__name
#: model:ir.model.fields,field_description:sign.field_sign_template__name
#: model:sign.item.type,name:sign.sign_item_type_name
#: model:sign.item.type,placeholder:sign.sign_item_type_name
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Name"
msgstr "Nome"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_role_name_uniq
msgid "Name already exists!"
msgstr "O Nome já existe!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Name for the file"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Name of the file"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Nearly there, keep going!"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__need_my_signature
msgid "Need My Signature"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/PDF_iframe.js:0
msgid "Need a valid PDF to add signature fields!"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Need to sign documents?"
msgstr ""

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_send_request
msgid "New Signature Request"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_template
msgid "New Template Name"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Newest"
msgstr "Mais Recente"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Próximo Evento do Calendário de Atividades"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data Limite Para a Próxima Atividade"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_summary
msgid "Next Activity Summary"
msgstr "Resumo da Próxima Atividade"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo da Atividade Seguinte"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
msgid "Next Document"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Next signatory ("
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "No attachment was provided"
msgstr "Nenhum anexo foi fornecido"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "No document yet"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "No specified reason"
msgstr ""

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "No template yet"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "None"
msgstr "Nenhum(a)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, add it"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, set it"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Ações"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__num_options
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__num_options
msgid "Number of Radio Button options"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents in progress for this template."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents signed for this template."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error_counter
msgid "Number of errors"
msgstr "Número de erros"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensagens que requerem uma ação"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com um erro na entrega"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__num_pages
msgid "Number of pages"
msgstr "Número de páginas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Odoo Sign"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One can weighs 15 g"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One liter of gas fuel will produce 8.9 kg of CO²"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One or more selection items have no associated options"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One uploaded file cannot be read. Is it a valid PDF?"
msgstr "Um ficheiro enviado não pode ser lido. É um PDF válido?"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/controllers/terms.py:0
msgid "Oops"
msgstr "Ops"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "Operation successful"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__value
msgid "Option"
msgstr "Opção"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_option
msgid "Option of a selection Field"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Optional Message..."
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url
msgid "Optional link for redirection after signature"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url_text
msgid "Optional text to display on the button link"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Options"
msgstr "Opções"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__original_template_id
msgid "Original File"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "PDF is encrypted"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Paper Savings"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Participants"
msgstr "Participantes"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__partner_id
msgid "Partner"
msgstr "Parceiro"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
msgid "Password is incorrect."
msgstr ""

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_phone
#: model:sign.item.type,placeholder:sign.sign_item_type_phone
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Phone"
msgstr "Telefone"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Phone Number"
msgstr "Número de Telefone"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item_type__placeholder
msgid "Placeholder"
msgstr "Espaço Reservado"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Please configure senders'(%s) email addresses"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Please select recipients for the following roles: %(roles)s"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_url
msgid "Portal Access URL"
msgstr "URL de Acesso ao Portal"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posX
msgid "Position X"
msgstr "Posição X"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posY
msgid "Position Y"
msgstr "Posição Y"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Preview"
msgstr "Pré-visualizar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Printed on"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__progress
msgid "Progress"
msgstr "Progresso"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Public User"
msgstr "Utilizador Público"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__radio
#: model:sign.item.type,name:sign.sign_item_type_radio
msgid "Radio Buttons"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__radio_items
msgid "Radio Items"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__radio_set_id
msgid "Radio button options"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_item_radio_set
msgid "Radio button set for keeping radio button items together"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__rating_ids
msgid "Ratings"
msgstr "Classificações"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Re-send SMS"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url
msgid "Redirect Link"
msgstr "URL de redirecionamento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__refuse
msgid "Refuse"
msgstr "Recusar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Refuse Document"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Refuse to sign"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__refused
msgid "Refused Signature"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Reminder"
msgstr "Lembrete"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder_enabled
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder_enabled
msgid "Reminder Enabled"
msgstr ""

#. module: sign
#: model:ir.ui.menu,name:sign.sign_reports
msgid "Reports"
msgstr "Relatórios"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_infos
msgid "Request Item Infos"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_patch.xml:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.xml:0
#: model:ir.model.fields.selection,name:sign.selection__mail_activity_type__category__sign_request
#: model:mail.activity.type,name:sign.mail_activity_data_signature_request
msgid "Request Signature"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Request expiration date must be set in the future."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_total
msgid "Requested Signatures"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__required
msgid "Required"
msgstr "Obrigatório"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Resend"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resend the invitation"
msgstr "Reenviar o convite"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resent!"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__responsible_id
#: model:ir.model.fields,field_description:sign.field_sign_template__user_id
msgid "Responsible"
msgstr "Responsável"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__responsible_count
msgid "Responsible Count"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_user_id
msgid "Responsible User"
msgstr "Utilizador Responsável"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Restore"
msgstr "Restaurar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Right"
msgstr "Direita"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__role_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__role_id
msgid "Role"
msgstr "Função"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_tree
msgid "Role Name"
msgstr ""

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_role_menu
msgid "Roles"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_iframe.js:0
msgid "Rotate Clockwise"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erro de Envio de SMS"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "SMS Sent"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_token
msgid "SMS Token"
msgstr "Código de SMS"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_3
msgid "Sales"
msgstr "Vendas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__save
msgid "Save"
msgstr "Guardar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Save as Template"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Saved"
msgstr "Guardado"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Search <span class=\"nolabel\"> (in Document)</span>"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__access_token
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_token
msgid "Security Token"
msgstr "Código (token) de Segurança"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_selection
msgid "Select an option"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Select the contact who should sign, according to their role.<br>In this "
"example, select your own contact to sign the document yourself."
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__selection
#: model:sign.item.type,name:sign.sign_item_type_selection
#: model:sign.item.type,placeholder:sign.sign_item_type_selection
msgid "Selection"
msgstr "Seleção"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__option_ids
msgid "Selection options"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Send"
msgstr "Enviar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Send SMS"
msgstr "Enviar SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_id
msgid "Send To"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "Send a new link"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Send a reminder"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Send the invitation"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Sent"
msgstr "Enviado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_wait
msgid "Sent Requests"
msgstr "Pedidos Enviados"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sent by"
msgstr "Enviado por"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_settings_action
#: model:ir.ui.menu,name:sign.sign_item_settings_menu
msgid "Settings"
msgstr "Configurações"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Share"
msgstr "Partilhar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Share & Close"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Share Document by Link"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__share_link
msgid "Share Link"
msgstr "Partilhar Hiperligação"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Shareable"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/models/sign_template.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__shared
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__shared
msgid "Shared"
msgstr "Partilhado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Show all records which has next action date is before today"
msgstr "Mostrar todos os registos cuja data de ação é anterior à atual"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Show standard terms & conditions on signature requests"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: model:ir.ui.menu,name:sign.menu_document
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Sign"
msgstr "Assinar"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__use_sign_terms
msgid "Sign Default Terms & Conditions"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms
msgid "Sign Default Terms and Conditions"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_html
msgid "Sign Default Terms and Conditions as a Web page"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_duplicate_template_pdf
msgid "Sign Duplicate Template with new PDF"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "Sign Next Document"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Sign Now"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__mail_sent_order
msgid "Sign Order"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_id
msgid "Sign Request"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_item_id
msgid "Sign Request Item"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__sign_send_request_id
msgid "Sign Send Request"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Sign Settings"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_template_tag
msgid "Sign Template Tag"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms
msgid "Sign Terms & Conditions"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_html
msgid "Sign Terms & Conditions as a Web page"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_type
msgid "Sign Terms & Conditions format"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Sign all"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Sign document"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sign now"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Sign requests"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_log
msgid "Sign requests access history"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request
msgid "Sign send request"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request_signer
msgid "Sign send request signer"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"Sign up for Odoo Sign to manage your own documents and signature requests!"
msgstr ""

#. module: sign
#: model:ir.actions.server,name:sign.sign_reminder_cron_ir_actions_server
msgid "Sign: Send mail reminder"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory's hash:"
msgstr ""

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__sign
#: model:sign.item.type,name:sign.sign_item_type_signature
#: model:sign.item.type,placeholder:sign.sign_item_type_signature
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signature"
msgstr "Assinatura"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signature Date"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_item_id
msgid "Signature Item"
msgstr "Item de Assinatura"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_option_action
msgid "Signature Item Options"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_item_role
msgid "Signature Item Party"
msgstr ""

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_role_action
msgid "Signature Item Role"
msgstr ""

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_type_action
#: model:ir.model,name:sign.model_sign_item_type
msgid "Signature Item Type"
msgstr "Tipo de Item de Assinatura"

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item_value
msgid "Signature Item Value"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_item_ids
msgid "Signature Items"
msgstr "Itens de Assinatura"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_model_patch.js:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.js:0
#: model:ir.model,name:sign.model_sign_request
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_request_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_id
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Signature Request"
msgstr "Pedido de Assinatura"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %(file_name)s"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %s"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item
msgid "Signature Request Item"
msgstr ""

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_item_action
msgid "Signature Request Items"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_item_id
msgid "Signature Request item"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_request_ids
msgid "Signature Requests"
msgstr "Pedidos de Assinatura"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__start_sign
msgid "Signature Started"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_sign_template
msgid "Signature Template"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Signature configuration"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid ""
"Signature requested for template: %(template)s\n"
"Signatories: %(signatories)s"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid "Signature(s)"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signature:"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_menu_sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signatures"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Signed"
msgstr "Assinado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__signed_count
msgid "Signed Count"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Signed Documents"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signed_without_extra_auth
msgid "Signed Without Extra Authentication"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "Signed document"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signing_date
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signed on"
msgstr "Assinado em"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__partner_id
msgid "Signer"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Signer One 2 Many"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Signers"
msgstr "Signatários"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signers_count
msgid "Signers Count"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Signing Date"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signing Events"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__set_sign_order
msgid "Signing Order"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Since you're the one signing this document, you can do it directly within "
"Odoo.<br>External users can use the link provided by email."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Some fields have still to be completed"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some required items are not filled"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some unauthorised items are filled"
msgstr ""

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Somebody is already filling a document which uses this template"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Sorry, an error occurred, please try to fill the document again."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Sorry, you cannot refuse this document"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__set_sign_order
msgid ""
"Specify the order for each signer. The signature request only gets sent to                                     the next signers in the sequence when all signers from the previous level have                                     signed the document.\n"
"                                    "
msgstr ""

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_default
msgid "Standard"
msgstr "Padrão"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__state
#: model:ir.model.fields,field_description:sign.field_sign_request_item__state
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "State"
msgstr "Estado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__request_state
msgid "State of the request on action log"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Status"
msgstr "Estado"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estados baseados nas atividades\n"
"Vencida: Ultrapassada a data planeada\n"
"Hoje: Data da atividade é a de hoje\n"
"Planeado: Atividades futuras."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Stop Sharing"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__subject
msgid "Subject"
msgstr "Assunto"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Summary"
msgstr "Sumário"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__name
msgid "Tag Name"
msgstr "Nome de Etiqueta"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_template_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Já existe uma etiqueta com este nome!"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_template_tag_action
#: model:ir.model.fields,field_description:sign.field_sign_request__template_tags
#: model:ir.model.fields,field_description:sign.field_sign_template__tag_ids
#: model:ir.ui.menu,name:sign.sign_template_tag_menu
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Tags"
msgstr "Etiquetas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Tags:"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__auto_field
msgid ""
"Technical name of the field on the partner model to auto-complete this "
"signature field at the time of signature."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__template_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__template_id
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template"
msgstr "Template"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.js:0
#: code:addons/sign/static/src/views/hooks.js:0
msgid "Template %s"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Template Properties"
msgstr ""

#. module: sign
#: model:ir.actions.server,name:sign.sign_template_tour_trigger_action
msgid "Template Sample Contract.pdf trigger"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template or Tag"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.act_window,name:sign.sign_template_action
#: model:ir.ui.menu,name:sign.sign_template_menu
msgid "Templates"
msgstr "Modelos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Terms &amp; Conditions"
msgstr "Termos &amp; Condições"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__html
msgid "Terms as Web Page"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__plain
msgid "Terms in Email"
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,help:sign.field_res_config_settings__sign_terms_type
msgid ""
"Terms in Email - The text will be displayed at the bottom of every signature request email.\n"
"\n"
"        Terms as Web Page - A link will be pasted at the bottom of every signature request email, leading to your content.\n"
"        "
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__text
#: model:sign.item.type,name:sign.sign_item_type_text
#: model:sign.item.type,placeholder:sign.sign_item_type_text
msgid "Text"
msgstr "Texto"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
msgid "Thank You!"
msgstr "Obrigado!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"That's it, all done!<br>The document is signed, and a copy has been sent by "
"email to all participants, along with a traceability report."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"The Odoo Sign document you are trying to reach does not exist. The signature"
" request might have been deleted or modified."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "The PDF's password is required to generate the final document."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The completed document cannot be created because the sign request is not "
"fully signed"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "The computation is based on the website"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The contact of %(role)s has been changed from %(old_partner)s to "
"%(new_partner)s."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "The document"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document %s has been fully signed."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document (%s) has been rejected by one of the signers"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "The document has been refused"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document has been signed by a signer and cannot be edited"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"The final document and this completion history have been sent by email "
"on&amp;nbsp;"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"The integrity of the document's history cannot be verified. This could mean "
"that signature values or the underlying PDF document may have been modified "
"after the fact."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid ""
"The mail address of %(partner)s has been updated. The request will be "
"automatically resent."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The mail has been sent to contacts in copy: %(contacts)s"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/terms.py:0
msgid "The requested page is invalid, or doesn't exist anymore."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "The role %s is required by the Sign application and cannot be deleted."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The sign request has not been fully signed"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "The signature has been canceled by %(partner)s(%(role)s)"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature has been refused by %(partner)s(%(role)s)"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature mail has been sent to: "
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__is_mail_sent
msgid "The signature mail has been sent."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "The signature request has been cancelled"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature request has been edited by: %s."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
msgid "The template doesn't exist anymore."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid ""
"The template has more pages than the current file, it can't be applied."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"The total of sheets you saved is based on: the number of sent sign requests "
"x number of sheets in the document x (number of contacts who need to sign + "
"number of contacts in copy if the sign request is signed) = total of pages. "
"We assume that one page weights 0.005 kilograms."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid "The uploaded file is not a valid PDF. Please upload a valid PDF file."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "There are no signatures request."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "There are other documents waiting for your signature:"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"There was an issue downloading your document. Please contact an "
"administrator."
msgstr ""
"Ocorreu um problema ao transferir o seu documento. Por favor, contacte um "
"administrador."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"There's no reason to panic, <br/>\n"
"                        you can still sign your document in a few clicks!"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "These files cannot be read, they may be corrupted or encrypted."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "They will be removed from the uploaded files"
msgstr "Eles serão removidos dos ficheiros enviados"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This function can only be called with sudo."
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__reference
#: model:ir.model.fields,help:sign.field_sign_request_item__reference
msgid "This is how the document will be named in the mail"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "This link has expired."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be refused"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be signed"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request is not valid anymore"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be filled"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be refused"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be signed"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"This will keep all the already completed signature of this request and "
"disable every sent access, are you sure?"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__tip
msgid "Tip"
msgstr "Gorjeta"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__sent
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__sent
msgid "To Sign"
msgstr "Para Assinar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "To produce 1000 kg of wood, we have to cut 12 trees"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "To sign"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Today Activities"
msgstr "Atividades do Dia"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__transaction_id
msgid "Transaction"
msgstr "Transação"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Try Odoo Sign"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Try a sample contract"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Try our sample document"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Try out this sample contract."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__type_id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__item_type
msgid "Type"
msgstr "Tipo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Type a name or email..."
msgstr ""

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividade excecional registada."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
msgid "Type tag name here"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "UTC"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Unable to send the SMS, please contact the sender of the document."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"Unable to sign the document due to missing required data. Please contact an "
"administrator."
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_role__auth_method__sms
msgid "Unique Code via SMS"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update
msgid "Update"
msgstr "Atualizar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload"
msgstr "Enviar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload PDF"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Upload a PDF"
msgstr "Enviar um PDF"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a PDF & Sign"
msgstr ""

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "Upload a PDF file or use an existing template to begin."
msgstr "Para começar envie um ficheiro PDF ou utilize um modelo existente."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload a PDF file to create a reusable template."
msgstr ""

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Upload a PDF file to create your first template"
msgstr "Envie um ficheiro PDF para criar o seu primeiro modelo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a pdf that you want to sign directly"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Use Layout"
msgstr ""

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Use Tags to manage your Sign Templates and Sign Requests"
msgstr ""

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_duplicate_template_with_pdf
msgid "Use the layout of fields on a new PDF"
msgstr ""

#. module: sign
#: model:ir.model,name:sign.model_res_users
#: model:ir.model.fields,field_description:sign.field_sign_log__user_id
#: model:sign.item.role,name:sign.sign_item_role_user
msgid "User"
msgstr "Utilizador"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__token
msgid "User token"
msgstr "Código do utilizador"

#. module: sign
#: model:res.groups,name:sign.group_sign_user
msgid "User: Own Templates"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__validity
#: model:ir.model.fields,field_description:sign.field_sign_send_request__validity
msgid "Valid Until"
msgstr "Válida Até"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Validate"
msgstr "Validar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Validate & Send"
msgstr "Validar e Enviar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/edit_while_signing_signable_pdf_iframe.js:0
msgid "Validate & the next signatory is “%s”"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
msgid "Validate &amp; Send Completed Document"
msgstr "Validar e Enviar Documento Concluído"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Validation Code"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_item_value_ids
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__value
msgid "Value"
msgstr "Valor"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_option_value_uniq
msgid "Value already exists!"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Verify"
msgstr "Verificar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "View Document"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "View document"
msgstr ""

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__open
msgid "View/Download"
msgstr "Ver/Transferir"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Viewed/downloaded by"
msgstr "Visto/transferido por"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for me"
msgstr "Aguardando por mim"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for others"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Warning"
msgstr "Aviso"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Waste"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Water"
msgstr "Água"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"We can only send reminders in the future - as soon as we find a way to send reminders in the past we'll notify you.\n"
"In the mean time, please make sure to input a positive number of days for the reminder interval."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "We couldn't find the signature request you're looking for!"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"We display to you a ratio based on the saved weight versus 1000 kg of paper "
"usage."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "We will send you this document by email once everyone has signed."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid ""
"We'll send an email to warn other contacts in copy & signers with the reason"
" you provided."
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__website_message_ids
msgid "Website Messages"
msgstr "Mensagens do Website"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__website_message_ids
msgid "Website communication history"
msgstr "Histórico de comunicação do website"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Well done, your document is ready!<br>Let's send it to get our first "
"signature."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "What about those conversions I see?"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
msgid "Why do you refuse to sign this document?"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__width
msgid "Width"
msgstr "Largura"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Wood"
msgstr "Madeira"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Write email or search contact..."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "Wrong password"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "XYZ123456"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"You can contact the person who invited you to sign the document by email for"
" help."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You can only add new items for the current role"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"You can request a new link to access your document and sign it, it will  be "
"delivered in your inbox right away."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You can't delete a template for which signature requests exist but you can "
"archive it instead."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You cannot reassign this signatory"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You cannot share this document by link, because it has fields to be filled "
"by different roles. Use Send button instead."
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"You do not have access to these documents, please contact a Sign "
"Administrator."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "You have refused the document"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "You have until"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You must specify one signer for each role of your sign template"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You need to define a signatory"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You should select at least one document to download."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "You will get the signed document by email."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "You won't receive any notification for this signature request anymore."
msgstr ""

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "You're one click away from automating your signature process!"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Your Information"
msgstr ""

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Your confirmation code is %s"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your email"
msgstr "O seu e-mail"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid ""
"Your file is encrypted, PDF's password is required to generate final "
"document. The final document will be encrypted with the same password."
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your name"
msgstr "O seu nome"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Your signature has been saved. Next signatory is"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid ""
"Your signature was not submitted. Ensure the SMS validation code is correct."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "and"
msgstr "e"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "and the process will continue"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "and:"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"because you don't have enough credits for this operation.\n"
"                    The signatory was able to finish signing, but was not asked to authenticate fully."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "can"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "cans"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "click here to cancel it."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "days."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. +1 415 555 0100"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. 314159"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_form
msgid "e.g. Employee"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "e.g. Non-disclosure agreement"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "every"
msgstr ""

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_checkbox
#: model:sign.item.type,tip:sign.sign_item_type_company
#: model:sign.item.type,tip:sign.sign_item_type_date
#: model:sign.item.type,tip:sign.sign_item_type_email
#: model:sign.item.type,tip:sign.sign_item_type_multiline_text
#: model:sign.item.type,tip:sign.sign_item_type_name
#: model:sign.item.type,tip:sign.sign_item_type_phone
#: model:sign.item.type,tip:sign.sign_item_type_radio
#: model:sign.item.type,tip:sign.sign_item_type_text
msgid "fill in"
msgstr "preencher"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been completed and signed by"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been edited, completed and signed by"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "has been signed by"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hour of computer use"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hours of computer use"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "https://c.environmentalpaper.org/"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ip"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "is free, forever, with unlimited users - and it's fun to use!"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kWh of energy saved"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of reduced carbon emissions"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of waste prevented"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of wood saved"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liter of car fuel"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liters of car fuel"
msgstr ""

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_initial
msgid "mark it"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "next"
msgstr ""

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "on"
msgstr "de"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "or"
msgstr "ou"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "sheets of paper saved"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "shower"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "showers"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "sign"
msgstr ""

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_signature
msgid "sign it"
msgstr "assiná-lo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message
msgid "sign.message"
msgstr ""

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_cc
msgid "sign.message_cc"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "tags"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "that's"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "to sign the document."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "to:"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "tree"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "trees"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "we've got a list of consumption for 1000 kg of paper usage."
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "without the requested extra-authentification step ("
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "you"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}}"
msgstr ""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}} Summary"
msgstr ""
