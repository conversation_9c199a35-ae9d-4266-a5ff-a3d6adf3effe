# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_partner__signature_count
#: model:ir.model.fields,field_description:sign.field_res_users__signature_count
msgid "# Signatures"
msgstr "Número de firmas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "#7898678"
msgstr "#7898678"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"%(partner)s validated the signature by SMS with the phone number "
"%(phone_number)s."
msgstr ""
"%(partner)s validó la firma por SMS con el número de teléfono "
"%(phone_number)s."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "%s couldn't sign the document due to an insufficient credit error."
msgstr "%s no pudo firmar el documento por falta de crédito."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been edited and signed"
msgstr "%s se editó y se firmó"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "%s has been linked to this sign request."
msgstr "%s se vinculó a esta solicitud de firma."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been signed"
msgstr "%s se firmó"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s: missing credits for extra-authentication"
msgstr "%s: faltan créditos para la autenticación adicional"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "(UTC)"
msgstr "(UTC)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
")\n"
"            has refused the document"
msgstr ""
")\n"
"            rechazó el documento"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
")\n"
"        has requested your signature on the document"
msgstr ""
")\n"
"        solicitó su firma en el documento"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "******1234"
msgstr "******1234"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid ""
"- <em>\n"
"                                            Waiting Signature\n"
"                                        </em>"
msgstr ""
"- <em>\n"
"                                            En espera de firma\n"
"                                        </em>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "- <em>Cancelled</em>"
msgstr "- <em>Cancelado</em>"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "- or -"
msgstr "- o - "

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "1001"
msgstr "1001"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "192.168.1.1"
msgstr "192.168.1.1"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "20-03-2000"
msgstr "20-03-2000"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18"
msgstr "08-18-2023"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18 - 12:30:45"
msgstr "18-08-2023 - 12:30:45"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created by:</b>"
msgstr "<b>Creado por:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created on:</b>"
msgstr "<b>Creado el:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Creation IP Address:</b>"
msgstr "<b>Dirección IP de creación:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Document ID:</b>"
msgstr "<b>ID del documento:</b>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "<b>Drag & drop “Signature”</b> into the bottom of the document."
msgstr "<b>Arrastre y suelte la \"Firma\"</b> al final del documento."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Gas:</b> 18,700 Pounds of CO² = 18,700*0.4536 = 8482.32 kg"
msgstr "<b>Combustible:</b> 18,700 libras de CO² = 18,700*0.4536 = 8482.32 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signature:</b>"
msgstr "<b>Firma:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signers:</b>"
msgstr "<b>Signatarios:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Solid Waste:</b> 1290 Pounds = 1290*0.4536 = 585.14 kg"
msgstr "<b>Residuos sólidos:</b> 1290 libras= 1290*0.4536 = 585.14 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Total Energy:</b> 27 Millions BTU = 27,000,000*0.0002931 = 7.91 kWh"
msgstr ""
"<b>Energía total:</b> 27 millones BTU = 27,000,000*0.0002931 = 7.91 kWh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Water:</b> 23,400 Gallons = 23,400*3.78541 = 88578.59 L"
msgstr "<b>Agua:</b> 23,400 galones = 23,400*3.78541 = 88578.59 L"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Wood Use:</b> 4 US Short Tons = 4*907.18474 = 3628.73 kg"
msgstr "<b>Uso de madera:</b> 4 toneladas cortas = 4*907.18474 = 3628.73 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<br/>(the email access has not been sent)"
msgstr "<br/> (no se ha enviado el acceso por correo electrónico)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "<i class=\"fa fa-check\"/> A fresh link has just been sent to your inbox!"
msgstr ""
"<i class=\"fa fa-check\"/> Acaba de recibir un nuevo enlace en su bandeja de"
" entrada."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-check\"/> The document's integrity is valid."
msgstr "<i class=\"fa fa-check\"/> La integridad del documento es válida."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> The document's integrity could not "
"be verified."
msgstr ""
"<i class=\"fa fa-exclamation-circle\"/> No se pudo verificar la integridad "
"del documento."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-globe\"/> View"
msgstr "<i class=\"fa fa-globe\"/> Ver"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"<i class=\"fa fa-info-circle\"/> Links sent via email expire after a set "
"delay to increase security."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Los enlaces enviados por correo electrónico"
" caducan después de un tiempo determinado para aumentar la seguridad."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to settings"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Volver a ajustes"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Preview"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Vista previa"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Cancelled </i></small>"
msgstr "<small><i> Cancelado </i></small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Waiting Signature </i></small>"
msgstr "<small><i> En espera de firma </i></small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>Email Verification: The signatory has confirmed control of their "
"email inbox by clicking on a unique link</small>"
msgstr ""
"<small>Verificación del correo electrónico: el signatario aceptó la gestión "
"de su bandeja de entrada de correo electrónico al hacer clic en un enlace "
"único</small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>SMS: The signatory has confirmed their control of the phone number "
"using a unique code sent by SMS</small>"
msgstr ""
"<small>SMS: el signatario aceptó la gestión del número de teléfono mediante "
"un código único que se envió por SMS</small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_partner_view_form
msgid "<span class=\"o_stat_text\">Signature Requested</span>"
msgstr "<span class=\"o_stat_text\">Firma solicitada</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "<span class=\"o_stat_text\">Signed Document</span>"
msgstr "<span class=\"o_stat_text\">Documento firmado</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid ""
"<span class=\"text-muted\" invisible=\"not share_link\">Sharing will create "
"a copy of the file to sign. That file can be reached by the link below. "
"Every public user using the link will generate a document when the Signature"
" is complete. The link is private, only those that receive the link will be "
"able to sign it.</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"not share_link\">Compartir creará una"
" copia del archivo para firmar y puede acceder a ese archivo a través del "
"siguiente enlace. Cada usuario público que utilice el enlace generará un "
"documento cuando se haya firmado. El enlace es privado y solo quienes "
"reciban el enlace podrán firmarlo.</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<span class=\"text-muted\">Not available</span>"
msgstr "<span class=\"text-muted\">No disponible</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_type_view_form
msgid "<span>(1.0 = full page size)</span>"
msgstr "<span>(1.0 = tamaño de la página completa)</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<span>This is a preview of your Terms &amp; Conditions.</span>"
msgstr "<span>Esta es una vista previa de sus términos y condiciones.</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid ""
"<span>You won't receive any notification for this signature request "
"anymore.</span>"
msgstr ""
"<span>Ya no recibirá notificaciones para esta solicitud de firma.</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<strong>Creation Date:</strong>"
msgstr "<strong>Fecha de creación:</strong>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>"
msgstr ""
"<strong>Advertencia:</strong> ¡no reenvíe este correo electrónico a otras "
"personas!<br/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>\n"
"            They will be able to access this document and sign it as yourself.<br/>\n"
"            <span>Your IP address and localization are associated to your signature to ensure traceability.</span>"
msgstr ""
"<strong>Advertencia:</strong> ¡no reenvíe este correo electrónico a otras personas!<br/>\n"
"            Podrán acceder al documento y firmarlo como si fueran usted.<br/>\n"
"            <span>Usamos su dirección IP y su ubicación para asegurar la trazabilidad de su firma.</span>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid ""
"A SMS will be sent to the following phone number. Please update it if it's "
"not relevant."
msgstr ""
"Se enviará un SMS al siguiente número de teléfono. Actualícelo si ya no es "
"su número."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.message_signature_link
msgid "A document has been signed and a copy attached to"
msgstr "Se firmó un documento y se adjuntó una copia"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A non-shared sign request's should not have any signer with an empty partner"
msgstr ""
"Una solicitud de firma no compartida no debería tener ningún signatario con "
"un contacto vacío"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A shared sign request should only have one signer with an empty partner"
msgstr ""
"Una solicitud de firma compartida solo debe tener un signatario con un "
"contacto vacío"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "A shower uses approximately 65 L of water"
msgstr "Una regadera consume aproximadamente 65 L de agua"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "A signature request has been linked to this document: %s"
msgstr "Se vinculó una solicitud de firma a este documento: %s"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "A valid sign request needs at least one sign request item"
msgstr ""
"Una solicitud de firma válida necesita al menos un elemento de solicitud de "
"firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ABCD1234"
msgstr "ABCD1234"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Access Logs"
msgstr "Registros de acceso"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_warning
msgid "Access warning"
msgstr "Advertencia de acceso"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Access your signed documents"
msgstr "Acceda a sus documentos firmados"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_via_link
msgid "Accessed Through Token"
msgstr "Acceso a través del token"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__category
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Action"
msgstr "Acción"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction
msgid "Action Needed"
msgstr "Se requiere una acción"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__action
msgid "Action Performed"
msgstr "Acción realizada"

#. module: sign
#: model:ir.model.fields,help:sign.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Las acciones pueden activar comportamientos específicos, como abrir la vista"
" de calendario o marcar la actividad como hecha de forma automática al subir"
" un documento"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__active
#: model:ir.model.fields,field_description:sign.field_sign_template__active
msgid "Active"
msgstr "Activo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de la actividad de excepción"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.report,name:sign.action_sign_request_print_logs
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Activity Logs"
msgstr "Registros de actividad"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: sign
#: model:ir.model,name:sign.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipo de actividad"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono del tipo de actividad"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__sign_log_ids
msgid "Activity logs linked to this request"
msgstr "Registros de actividad vinculados a esta solicitud"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initials_all_pages_dialog.js:0
msgid "Add Initials"
msgstr "Agregar iniciales"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add Once"
msgstr "Agregar una vez"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Add document tags here"
msgstr "Agregue las etiquetas de los documentos aquí"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add to all pages"
msgstr "Agregar a todas las páginas"

#. module: sign
#: model:res.groups,name:sign.group_sign_manager
msgid "Administrator"
msgstr "Administrador"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.js:0
msgid "Adopt Your Signature"
msgstr "Adapte su firma"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__signed
msgid "After Signature"
msgstr "Después de la firma"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__alignment
msgid "Alignment"
msgstr "Alineación"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "All"
msgstr "Todos"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_all_request_action
#: model:ir.ui.menu,name:sign.sign_request_documents
msgid "All Documents"
msgstr "Todos los documentos"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "All signers must have valid email addresses"
msgstr ""
"Todos los signatarios deberán contar con direcciones de correo electrónico "
"válidas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow signatories to provide their identity using itsme® (available in "
"Belgium and the Netherlands)."
msgstr ""
"Permitir que los signatarios se identifiquen mediante itsme® (disponible en "
"Bélgica y los Países Bajos)."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow users to define the users or groups which have access to the template."
msgstr ""
"Permitir que los usuarios definan los usuarios o grupos que tienen acceso a "
"la plantilla."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "An average computer will consume 750 Wh"
msgstr "Una computadora promedio consume 750 Wh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Antwrep"
msgstr "Antwrep"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Archive"
msgstr "Archivar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Archived"
msgstr "Archivado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Are you sure you want to cancel the sign request?"
msgstr "¿Está seguro de que desea cancelar la solicitud de firma?"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__start_sign
msgid "At least one signer has signed the document."
msgstr "Al menos un signatario ha firmado el documento."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Attach a file"
msgstr "Adjuntar un archivo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__attachment_id
msgid "Attachment"
msgstr "Archivo adjunto"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__attachment_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__attachment_ids
msgid "Attachments"
msgstr "Archivos adjuntos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Authenticate by SMS"
msgstr "Autenticar por SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__group_ids
msgid "Authorized Groups"
msgstr "Grupos autorizados"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Groups:"
msgstr "Grupos autorizados:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__authorized_ids
msgid "Authorized Users"
msgstr "Usuarios autorizados"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Users:"
msgstr "Usuarios autorizados:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__auto_field
msgid "Auto-fill Partner Field"
msgstr "Autocompletar el campo del contacto"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__available
msgid "Available in new templates"
msgstr "Disponible en las nuevas plantillas"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Back to %s"
msgstr "Regresar a %s"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on"
msgstr "Según"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on various websites, here are our comparisons:"
msgstr "Basándonos en varios sitios web, estas son nuestras comparaciones:"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__sent
msgid "Before Signature"
msgstr "Antes de la firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "By"
msgstr "Por"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid ""
"By clicking Adopt & Sign, I agree that the chosen signature/initials will be"
" a valid electronic representation of my hand-written signature/initials for"
" all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"Al hacer clic en adoptar y firmar, estoy de acuerdo que la firma o iniciales"
" elegidas serán una representación electrónica válida de mi firma o "
"iniciales escritas a mano para cualquier propósito, cuando se utilicen en "
"documentos, incluyendo contratos legalmente vinculantes."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message_cc
msgid "CC Message"
msgstr "CC de mensaje"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__cancel
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Cancel Sign Request"
msgstr "Cancelar solicitud de firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Canceled"
msgstr "Cancelada"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__canceled
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Cancelled"
msgstr "Cancelado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Carbon Emissions"
msgstr "Emisiones de carbono"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Center"
msgstr "Centro"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Certificate <i class=\"fa fa-download\"/>"
msgstr "Certificado <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Certificate of Completion<br/>"
msgstr "Certificado de conclusión <br/>"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,field_description:sign.field_sign_request_item__change_authorized
msgid "Change Authorized"
msgstr "Cambio autorizado"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__checkbox
#: model:sign.item.type,name:sign.sign_item_type_checkbox
msgid "Checkbox"
msgstr "Casilla"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__is_sharing
msgid "Checked if this template has created a shared document for you"
msgstr "Marcado si esta plantilla creó un documento compartido para usted"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_item_navigator.js:0
msgid "Click to start"
msgstr "Haga clic para comenzar"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:sign.template,redirect_url_text:sign.template_sign_1
#: model:sign.template,redirect_url_text:sign.template_sign_2
#: model:sign.template,redirect_url_text:sign.template_sign_3
#: model:sign.template,redirect_url_text:sign.template_sign_4
#: model:sign.template,redirect_url_text:sign.template_sign_5
#: model:sign.template,redirect_url_text:sign.template_sign_tour
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "Close"
msgstr "Cerrar"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__color
#: model:ir.model.fields,field_description:sign.field_sign_request__color
#: model:ir.model.fields,field_description:sign.field_sign_request_item__color
#: model:ir.model.fields,field_description:sign.field_sign_template__color
msgid "Color"
msgstr "Color"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__color
msgid "Color Index"
msgstr "Índice de color"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Communication history"
msgstr "Historial de comunicación"

#. module: sign
#: model:ir.model,name:sign.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_company
#: model:sign.item.type,placeholder:sign.sign_item_type_company
msgid "Company"
msgstr "Empresa"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__communication_company_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__communication_company_id
msgid "Company used for communication"
msgstr "Empresa utilizada para la comunicación"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__completed
msgid "Completed"
msgstr "Terminado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document
msgid "Completed Document"
msgstr "Documento terminado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document_attachment_ids
msgid "Completed Documents"
msgstr "Documentos completos"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_closed
msgid "Completed Signatures"
msgstr "Firmas realizadas"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completion_date
msgid "Completion Date"
msgstr "Fecha de finalización"

#. module: sign
#: model:ir.model,name:sign.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: sign
#: model:ir.ui.menu,name:sign.menu_sign_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Configure the field types that can be used to sign documents (placeholder, "
"auto-completion, ...), as well as the values for selection fields in "
"signable documents."
msgstr ""
"Configure los tipos de campo que se pueden utilizar para firmar documentos "
"(marcador de posición, autocompletado, entre otros), así como los valores de"
" los campos de selección en los documentos firmables."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
msgid "Confirm"
msgstr "Confirmar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Congrats, your signature is ready to be submitted!"
msgstr "¡Felicidades, su firma está lista para enviarse!"

#. module: sign
#: model_terms:web_tour.tour,rainbow_man_message:sign.sign_tour
msgid "Congratulations, you signed your first document!"
msgstr "¡Felicidades, firmó su primer documento!"

#. module: sign
#: model:ir.model,name:sign.model_res_partner
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__partner_id
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Contact"
msgstr "Contacto"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts"
msgstr "Contactos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts in copy"
msgstr "Contactos en la copia"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__cc_partner_ids
msgid ""
"Contacts in copy will be notified by email once the document is either fully"
" signed or refused."
msgstr ""
"Una vez que el documento esté firmado en su totalidad o sea rechazado, se "
"notificará por correo electrónico a los contactos en copia."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__cc_partner_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__cc_partner_ids
msgid "Copy to"
msgstr "Copiar a"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Create Sign Tags"
msgstr "Crear etiquetas de firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Create date"
msgstr "Fecha de creación"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_date
#: model:ir.model.fields,field_description:sign.field_sign_log__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_date
msgid "Created on"
msgstr "Creado el"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__create
msgid "Creation"
msgstr "Creación"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Current status of the signature request"
msgstr "Estado actual de la solicitud de firma"

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_customer
msgid "Customer"
msgstr "Cliente"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__access_url
msgid "Customer Portal URL"
msgstr "URL del portal de cliente"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: model:sign.item.type,name:sign.sign_item_type_date
#: model:sign.item.type,placeholder:sign.sign_item_type_date
msgid "Date"
msgstr "Fecha"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Date (UTC)"
msgstr "Fecha (UTC)"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__default
msgid "Default"
msgstr "Predeterminado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_height
msgid "Default Height"
msgstr "Altura predeterminada"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__default_sign_template_id
msgid "Default Signature Template"
msgstr "Plantilla de firma predeterminada"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_width
msgid "Default Width"
msgstr "Ancho predeterminado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__sequence
msgid "Default order"
msgstr "Orden predeterminado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign"
msgstr "Eliminar firma"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign item"
msgstr "Eliminar elemento de firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Deliver one-time codes by SMS to identify signatories when signing a "
"document."
msgstr ""
"Enviar códigos de un solo uso por SMS para identificar a los signatarios al "
"firmar un documento."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Action"
msgstr "Acción de demostración"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo IP"
msgstr "IP de demostración"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Latitude"
msgstr "Latitud de demostración"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Longitude"
msgstr "Longitud de demostración"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Partner"
msgstr "Contacto de demostración"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Details"
msgstr "Detalles"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials
msgid "Digital Initials"
msgstr "Iniciales digitales"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials_frame
msgid "Digital Initials Frame"
msgstr "Cuadro de las iniciales digitales"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature
msgid "Digital Signature"
msgstr "Firma digital"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature_frame
msgid "Digital Signature Frame"
msgstr "Cuadro de la firma digital"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_users_view_form
#: model_terms:ir.ui.view,arch_db:sign.view_users_form_simple_modif
msgid "Digital Signatures"
msgstr "Firmas digitales"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Display"
msgstr "Mostrar"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_option__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_role__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__display_name
#: model:ir.model.fields,field_description:sign.field_sign_log__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_preview_ready
msgid "Display sign preview button"
msgstr "Mostrar el botón de vista previa de la firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Document"
msgstr "Documento"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Document <i class=\"fa fa-download\"/>"
msgstr "Documento <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Document Details"
msgstr "Detalles del documento"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference
#: model:ir.model.fields,field_description:sign.field_sign_request_item__reference
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "Document Name"
msgstr "Nombre del documento"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__page
msgid "Document Page"
msgstr "Página del documento"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__template_id
msgid "Document Template"
msgstr "Plantilla del documento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Document saved as Template."
msgstr "Documento guardado como plantilla."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Document to send for signature or to sign yourself."
msgstr "Documento a enviar para su firma o que debe firmar usted."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Document(s) to sign"
msgstr "Documentos a firmar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Document/Signer"
msgstr "Documento/Signatario"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_action
#: model:ir.ui.menu,name:sign.sign_request_menu
msgid "Documents"
msgstr "Documentos"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Download"
msgstr "Descargar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
msgid "Download Certificate"
msgstr "Descargar certificado"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Download Document"
msgstr "Descargar documento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
msgid "Drag & Drop a field in the PDF"
msgstr "Arrastre y suelte un campo en el PDF"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Drag and drop"
msgstr "Arrastrar y soltar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Draw your most beautiful signature!<br>You can also create one automatically"
" or load a signature from your computer."
msgstr ""
"Trace su firma más presentable. <br> También puede crear una de manera "
"automática o cargar una firma desde su computadora."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "ERROR: Invalid PDF file!"
msgstr "ERROR: el archivo PDF no es válido"

#. module: sign
#: model:ir.actions.report,name:sign.sign_report_green_savings_action
msgid "Ecological Savings by using Electronic Signatures"
msgstr "Ahorro ecológico mediante el uso de la firma electrónica"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit"
msgstr "Editar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Edit Template"
msgstr "Editar plantilla"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit field types"
msgstr "Editar tipos de campo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit selection values"
msgstr "Editar valores de selección"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit template name"
msgstr "Editar el nombre de la plantilla"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signer_email
#: model:sign.item.type,name:sign.sign_item_type_email
#: model:sign.item.type,placeholder:sign.sign_item_type_email
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email"
msgstr "Correo electrónico"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__subject
msgid "Email Subject"
msgstr "Asunto del correo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email Verification"
msgstr "Verificación de correo electrónico"

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_employee
msgid "Employee"
msgstr "Empleado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Energy"
msgstr "Energía"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Enter the code received through SMS to complete your signature"
msgstr "Introduzca el código que recibió por SMS para completar su firma"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Error"
msgstr "Error"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Error 404"
msgstr "Error 404"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Existing sign items are not allowed to be changed"
msgstr "No se permite modificar los elementos de firma existentes"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__expired
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__expired
msgid "Expired"
msgstr "Vencido"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Expiring Soon"
msgstr "Expirará pronto"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr "Paso adicional de autenticación"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__favorited_ids
msgid "Favorite of"
msgstr "Favorito de"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__favorited_ids
msgid "Favorited Users"
msgstr "Usuarios favoritos"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__name
msgid "Field Name"
msgstr "Nombre del campo"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_type_menu
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Field Types"
msgstr "Tipos de campo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Fields"
msgstr "Campos"

#. module: sign
#: model:ir.model,name:sign.model_sign_item
msgid "Fields to be sign on Document"
msgstr "Campos a firmar en el documento"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__datas
msgid "File Content (base64)"
msgstr "Contenido del archivo (base64)"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/hooks.js:0
msgid "File Error"
msgstr "Error en archivo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_pdf
msgid "File name"
msgstr "Nombre del archivo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__filename
msgid "Filename"
msgstr "Nombre del archivo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Filled by"
msgstr "Completado por"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Final Validation"
msgstr "Validación final"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Follow the guide to sign the document."
msgstr "Siga la guía para firmar el documento."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (contactos)"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome, por ejemplo, fa-tasks"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"For 1000kg of paper usage, with 10% of recycled paper, environmental savings"
" are based on"
msgstr ""
"El ahorro medioambiental para 1000 kg de papel utilizado, con un 10% de "
"papel reciclado, se basa en"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr ""
"Hacer que el signatario se identifique con un segundo método de "
"autenticación."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Frame"
msgstr "Cuadro"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_has_hash
msgid "Frame Has Hash"
msgstr "El cuadro tiene hash"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__frame_hash
msgid "Frame Hash"
msgstr "Hash del cuadro"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_value
msgid "Frame Value"
msgstr "Valor del cuadro"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__signed
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Fully Signed"
msgstr "Completamente firmado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid "Generate PDF"
msgstr "Generar PDF"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Geolocation"
msgstr "Geolocalización"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_report_green_savings
msgid "Green Savings"
msgstr "Ahorro ecológico"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Green Savings Report"
msgstr "Reporte del ahorro ecológico"

#. module: sign
#: model:ir.model,name:sign.model_report_sign_green_savings_report
msgid "Green Savings Report model"
msgstr "Modelo de reporte del ahorro ecológico"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Green Savings Summary"
msgstr "Resumen del ahorro ecológico"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_1
msgid "HR"
msgstr "RR. HH."

#. module: sign
#: model:ir.model,name:sign.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__has_default_template
msgid "Has Default Template"
msgstr "Tiene una plantilla predeterminada"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__has_sign_requests
msgid "Has Sign Requests"
msgstr "Tiene solicitudes de firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__height
msgid "Height"
msgstr "Altura"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Hello"
msgstr "Hola"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__tip
msgid "Hint displayed in the signing hint"
msgstr "Sugerencia mostrada en la sugerencia de firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Home"
msgstr "Inicio"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "How are these results calculated?"
msgstr "¿Cómo se calculan estos resultados?"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "How do we calculate?"
msgstr "¿Cómo calculamos?"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "I've got a total weight, and now?"
msgstr "Ya tengo un peso total, ¿y ahora?"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__id
#: model:ir.model.fields,field_description:sign.field_sign_item__id
#: model:ir.model.fields,field_description:sign.field_sign_item_option__id
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__id
#: model:ir.model.fields,field_description:sign.field_sign_item_role__id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__id
#: model:ir.model.fields,field_description:sign.field_sign_log__id
#: model:ir.model.fields,field_description:sign.field_sign_request__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__id
#: model:ir.model.fields,field_description:sign.field_sign_template__id
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__id
msgid "ID"
msgstr "ID"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "IP"
msgstr "IP"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "IP Address"
msgstr "Dirección IP"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__ip
msgid "IP address of the visitor"
msgstr "Dirección IP del visitante"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__icon
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono que indica una actividad de excepción."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__module_sign_itsme
msgid "Identify with itsme®"
msgstr "Identificarse con itsme®"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,help:sign.field_sign_request_item__change_authorized
msgid ""
"If checked, recipient of a document with this role can be changed after "
"having sent the request. Useful to replace a signatory who is out of office,"
" etc."
msgstr ""
"Si se encuentra seleccionado, el destinatario de un documento con esta "
"función puede cambiarse después de haber enviado la solicitud. Resulta útil "
"para sustituir a un signatario que no se encuentra en la oficina, etc."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error
#: model:ir.model.fields,help:sign.field_sign_request__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you do not want to receive these notifications anymore, you can disable the extra-authentication step in the\n"
"                        <code>\n"
"                            Sign &gt; Configuration &gt; Roles\n"
"                        </code>\n"
"                        menu."
msgstr ""
"Si ya no desea recibir estas notificaciones, puede deshabilitar el paso adicional de autenticación en el menú de\n"
"                        <code>\n"
"                            Firma electrónica &gt; Configuración &gt; Funciones.\n"
"                        </code> \n"
"                        "

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "If you do not wish to receive future reminders about this document,"
msgstr "Si no desea recibir recordatorios futuros sobre este documento,"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you wish, you can request the document again after buying more credits "
"for the operation."
msgstr ""
"Si lo desea, puede volver a solicitar el documento después de comprar más "
"créditos."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__in_progress_count
msgid "In Progress Count"
msgstr "Conteo en progreso"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "In favorites, remove it"
msgstr "En favoritos, elimínelo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_hash
msgid "Inalterability Hash"
msgstr "Hash de inalterabilidad"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Include a visual security frame around your signature"
msgstr "Incluya un marco de seguridad visual alrededor de su firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Information"
msgstr "Información"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__initial
msgid "Initial"
msgstr "Inicial"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_initial
#: model:sign.item.type,placeholder:sign.sign_item_type_initial
msgid "Initials"
msgstr "Iniciales"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Insert your terms & conditions here..."
msgstr "Inserte sus términos y condiciones aquí..."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__integrity
msgid "Integrity of the Sign request"
msgstr "Integridad de la solicitud de la firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__is_mail_sent
msgid "Is Mail Sent"
msgstr "Se envió el correo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__is_sharing
msgid "Is Sharing"
msgstr "Está compartiendo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Is Signing"
msgstr "Está firmando"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__is_user_signer
msgid "Is User Signer"
msgstr "Es usuario signatario"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "It's signed!"
msgstr "Está firmado."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Doe"
msgstr "Juan Pérez"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Smith"
msgstr "Juan Pérez"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "L of water saved"
msgstr "litros de agua ahorrados"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_action_date
msgid "Last Action Date"
msgstr "Fecha de la última acción"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_date
#: model:ir.model.fields,field_description:sign.field_sign_log__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_reminder
msgid "Last reminder"
msgstr "Último recordatorio"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Late Activities"
msgstr "Actividades atrasadas"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__latitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__latitude
msgid "Latitude"
msgstr "Latitud"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__validity
msgid "Leave empty for requests without expiration."
msgstr "Déjelo vacío para las solicitudes sin vencimiento."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Left"
msgstr "Izquierda"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's <b>prepare & sign</b> our first document."
msgstr "Vamos a <b>preparar y firmar</b> nuestro primer documento."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's send the request by email."
msgstr "Enviemos la solicitud por correo electrónico."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url_text
msgid "Link Label"
msgstr "Etiqueta del enlace"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__activity_id
msgid "Linked Activity"
msgstr "Actividad vinculada"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference_doc
msgid "Linked To"
msgstr "Vinculado a"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reference_doc
msgid "Linked to"
msgstr "Vinculado a"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_date
msgid "Log Date"
msgstr "Fecha de registro"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be deleted!"
msgstr "No se puede eliminar el historial de solicitudes de firma."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be modified!"
msgstr "No se puede modificar el historial de solicitudes de firma."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Logo"
msgstr "Logo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__sign_log_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_log_view_tree
msgid "Logs"
msgstr "Registros"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__longitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__longitude
msgid "Longitude"
msgstr "Longitud"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__mail_sent_order
msgid "Mail Sent Order"
msgstr "Orden enviada por correo"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update_mail
msgid "Mail Update"
msgstr "Actualización del correo"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Malformed expression: %(exp)s"
msgstr "Expresión mal formada: %(exp)s"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__group_manage_template_access
#: model:res.groups,name:sign.manage_template_access
msgid "Manage template access"
msgstr "Gestione el acceso a las plantillas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Mandatory field"
msgstr "Campo obligatorio"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message
msgid "Message"
msgstr "Mensaje"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error
msgid "Message Delivery error"
msgstr "Error al enviar el mensaje"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message_cc
msgid "Message to be sent to contacts in copy of the signed document"
msgstr "Mensaje a enviar a los contactos en copia del documento firmado"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message
msgid "Message to be sent to signers of the specified document"
msgstr "Mensaje a enviar a los signatarios del documento específico"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "Missing Password"
msgstr "Falta la contraseña"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_number
msgid "Mobile"
msgstr "Celular"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Modify Template"
msgstr "Modificar la plantilla"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__textarea
#: model:sign.item.type,name:sign.sign_item_type_multiline_text
#: model:sign.item.type,placeholder:sign.sign_item_type_multiline_text
msgid "Multiline Text"
msgstr "Texto multilínea"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Multiple Signature Requests"
msgstr "Solicitudes de varias firmas"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_request_my_documents
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Documents"
msgstr "Mis documentos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Favorites"
msgstr "Mis favoritos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Requests"
msgstr "Mis solicitudes"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Templates"
msgstr "Mis plantillas"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_2
msgid "NDA"
msgstr "ADC"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__name
#: model:ir.model.fields,field_description:sign.field_sign_template__name
#: model:sign.item.type,name:sign.sign_item_type_name
#: model:sign.item.type,placeholder:sign.sign_item_type_name
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Name"
msgstr "Nombre"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_role_name_uniq
msgid "Name already exists!"
msgstr "Este nombre ya existe"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Name for the file"
msgstr "Nombre del archivo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Name of the file"
msgstr "Nombre del archivo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Nearly there, keep going!"
msgstr "Ya casi acaba, ¡ánimo!"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__need_my_signature
msgid "Need My Signature"
msgstr "Necesita mi firma"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/PDF_iframe.js:0
msgid "Need a valid PDF to add signature fields!"
msgstr "Necesita un PDF válido para agregar campos de firma"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Need to sign documents?"
msgstr "¿Necesita firmar documentos?"

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_send_request
msgid "New Signature Request"
msgstr "Nueva solicitud de firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_template
msgid "New Template Name"
msgstr "Nombre de la nueva plantilla"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Newest"
msgstr "Más reciente"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Siguiente fecha límite de la actividad"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
msgid "Next Document"
msgstr "Siguiente documento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Next signatory ("
msgstr "Siguiente signatario ("

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "No attachment was provided"
msgstr "No se proporcionó ningún archivo adjunto"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "No document yet"
msgstr "Todavía no hay documentos."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "No specified reason"
msgstr "Sin motivo específico"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "No template yet"
msgstr "Todavía no hay plantillas"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "None"
msgstr "Ninguno"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, add it"
msgstr "No está en favoritos, agréguelo "

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, set it"
msgstr "No está en favoritos, configúrelo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__num_options
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__num_options
msgid "Number of Radio Button options"
msgstr "Número de opciones de botones de radio"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents in progress for this template."
msgstr "Número de documentos en progreso para esta plantilla."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents signed for this template."
msgstr "Número de documentos firmados para esta plantilla."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__num_pages
msgid "Number of pages"
msgstr "Número de páginas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Odoo Sign"
msgstr "Firma electrónica de Odoo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One can weighs 15 g"
msgstr "Una puede pesar 15 g"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One liter of gas fuel will produce 8.9 kg of CO²"
msgstr "Un litro de combustible producirá 8.9 kg de CO²"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One or more selection items have no associated options"
msgstr ""
"Hay uno o más elementos de selección que no cuentan con opciones asociadas"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One uploaded file cannot be read. Is it a valid PDF?"
msgstr "No se puede abrir un archivo, ¿está seguro de que es un PDF válido?"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/controllers/terms.py:0
msgid "Oops"
msgstr "Uy"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "Operation successful"
msgstr "Operación exitosa"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__value
msgid "Option"
msgstr "Opción"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_option
msgid "Option of a selection Field"
msgstr "Opción de un campo de selección"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Optional Message..."
msgstr "Mensaje opcional..."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url
msgid "Optional link for redirection after signature"
msgstr "Enlace opcional para redireccionar después de firmar"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url_text
msgid "Optional text to display on the button link"
msgstr "Texto opcional para mostrar en el botón de enlace"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Options"
msgstr "Opciones"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__original_template_id
msgid "Original File"
msgstr "Archivo original"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "PDF is encrypted"
msgstr "El PDF está encriptado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Paper Savings"
msgstr "Ahorro de papel"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Participants"
msgstr "Participantes"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
msgid "Password is incorrect."
msgstr "La contraseña es incorrecta."

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_phone
#: model:sign.item.type,placeholder:sign.sign_item_type_phone
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Phone"
msgstr "Teléfono"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Phone Number"
msgstr "Número de teléfono"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item_type__placeholder
msgid "Placeholder"
msgstr "Marcador de posición"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Please configure senders'(%s) email addresses"
msgstr "Configure la dirección de correo electrónico del remitente (%s)"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Please select recipients for the following roles: %(roles)s"
msgstr "Seleccione personas para las siguientes funciones: %(roles)s"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_url
msgid "Portal Access URL"
msgstr "URL de acceso al portal"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posX
msgid "Position X"
msgstr "Posición X"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posY
msgid "Position Y"
msgstr "Posición Y"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Preview"
msgstr "Vista previa"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Printed on"
msgstr "Impreso en"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__progress
msgid "Progress"
msgstr "Progreso"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Public User"
msgstr "Usuario público"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__radio
#: model:sign.item.type,name:sign.sign_item_type_radio
msgid "Radio Buttons"
msgstr "Botones de opción"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__radio_items
msgid "Radio Items"
msgstr "Elementos de opción"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__radio_set_id
msgid "Radio button options"
msgstr "Opciones de botón de radio"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_radio_set
msgid "Radio button set for keeping radio button items together"
msgstr ""
"Botón de opción configurado para mantener los elementos de opción juntos"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Re-send SMS"
msgstr "Reenviar SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url
msgid "Redirect Link"
msgstr "Enlace de redireccionamiento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__refuse
msgid "Refuse"
msgstr "Rechazar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Refuse Document"
msgstr "Rechazar documento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Refuse to sign"
msgstr "Rechazar firma"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__refused
msgid "Refused Signature"
msgstr "Firma rechazada"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Reminder"
msgstr "Recordatorio"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder_enabled
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder_enabled
msgid "Reminder Enabled"
msgstr "Recordatorio activado"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_reports
msgid "Reports"
msgstr "Reportes"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_infos
msgid "Request Item Infos"
msgstr "Información sobre el elemento de la solicitud"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_patch.xml:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.xml:0
#: model:ir.model.fields.selection,name:sign.selection__mail_activity_type__category__sign_request
#: model:mail.activity.type,name:sign.mail_activity_data_signature_request
msgid "Request Signature"
msgstr "Solicitar firma"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Request expiration date must be set in the future."
msgstr ""
"La fecha de vencimiento de la solicitud debe establecerse en el futuro."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_total
msgid "Requested Signatures"
msgstr "Firmas solicitadas"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__required
msgid "Required"
msgstr "Requerido"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Resend"
msgstr "Reenviar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resend the invitation"
msgstr "Reenviar la invitación"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resent!"
msgstr "Reenviado"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__responsible_id
#: model:ir.model.fields,field_description:sign.field_sign_template__user_id
msgid "Responsible"
msgstr "Responsable"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__responsible_count
msgid "Responsible Count"
msgstr "Número de responsables"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Restore"
msgstr "Restaurar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Right"
msgstr "Derecha"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__role_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__role_id
msgid "Role"
msgstr "Función"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_tree
msgid "Role Name"
msgstr "Nombre de la función"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_role_menu
msgid "Roles"
msgstr "Funciones"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_iframe.js:0
msgid "Rotate Clockwise"
msgstr "Rotar en el sentido de las agujas del reloj"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error en el envío del SMS"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "SMS Sent"
msgstr "SMS enviado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_token
msgid "SMS Token"
msgstr "Token del SMS"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_3
msgid "Sales"
msgstr "Ventas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__save
msgid "Save"
msgstr "Guardar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Save as Template"
msgstr "Guardar como plantilla"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Saved"
msgstr "Guardado"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Search <span class=\"nolabel\"> (in Document)</span>"
msgstr "Buscar <span class=\"nolabel\"> (en documento)</span>"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__access_token
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_token
msgid "Security Token"
msgstr "Token de seguridad"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_selection
msgid "Select an option"
msgstr "Seleccione una opción"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Select the contact who should sign, according to their role.<br>In this "
"example, select your own contact to sign the document yourself."
msgstr ""
"Seleccione el contacto que debe firmar, de acuerdo con su función. <br>En "
"este ejemplo, seleccione su propio contacto para que usted firme el "
"documento."

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__selection
#: model:sign.item.type,name:sign.sign_item_type_selection
#: model:sign.item.type,placeholder:sign.sign_item_type_selection
msgid "Selection"
msgstr "Selección"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__option_ids
msgid "Selection options"
msgstr "Opciones de selección"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Send"
msgstr "Enviar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Send SMS"
msgstr "Enviar SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_id
msgid "Send To"
msgstr "Enviar a"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "Send a new link"
msgstr "Enviar un enlace nuevo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Send a reminder"
msgstr "Enviar recordatorio"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Send the invitation"
msgstr "Envíe la invitación"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Sent"
msgstr "Enviado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_wait
msgid "Sent Requests"
msgstr "Solicitudes enviadas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sent by"
msgstr "Enviar por"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_settings_action
#: model:ir.ui.menu,name:sign.sign_item_settings_menu
msgid "Settings"
msgstr "Ajustes"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Share"
msgstr "Compartir"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Share & Close"
msgstr "Compartir y cerrar"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Share Document by Link"
msgstr "Compartir documento por enlace"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__share_link
msgid "Share Link"
msgstr "Compartir enlace"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Shareable"
msgstr "Se puede compartir "

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/models/sign_template.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__shared
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__shared
msgid "Shared"
msgstr "Compartido"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros cuya próxima fecha de acción es antes de la "
"fecha de hoy."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Show standard terms & conditions on signature requests"
msgstr ""
"Mostrar los términos y condiciones generales en las solicitudes de firma"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: model:ir.ui.menu,name:sign.menu_document
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Sign"
msgstr "Firma electrónica"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__use_sign_terms
msgid "Sign Default Terms & Conditions"
msgstr "Firmar los términos y condiciones predeterminados"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms
msgid "Sign Default Terms and Conditions"
msgstr "Firmar los términos y condiciones predeterminados"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_html
msgid "Sign Default Terms and Conditions as a Web page"
msgstr "Firmar los términos y condiciones predeterminados como una página web"

#. module: sign
#: model:ir.model,name:sign.model_sign_duplicate_template_pdf
msgid "Sign Duplicate Template with new PDF"
msgstr "Firmar la plantilla duplicada como un PDF nuevo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "Sign Next Document"
msgstr "Firmar siguiente documento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Sign Now"
msgstr "Firmar ahora"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__mail_sent_order
msgid "Sign Order"
msgstr "Orden de firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_id
msgid "Sign Request"
msgstr "Solicitud de firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_item_id
msgid "Sign Request Item"
msgstr "Elemento de la solicitud de firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__sign_send_request_id
msgid "Sign Send Request"
msgstr "Solicitud de envío de firmas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Sign Settings"
msgstr "Ajustes de firma"

#. module: sign
#: model:ir.model,name:sign.model_sign_template_tag
msgid "Sign Template Tag"
msgstr "Etiqueta de la plantilla de firmas"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms
msgid "Sign Terms & Conditions"
msgstr "Firmar términos y condiciones"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_html
msgid "Sign Terms & Conditions as a Web page"
msgstr "Firmar los términos y condiciones como una página web"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_type
msgid "Sign Terms & Conditions format"
msgstr "Firmar el formato de términos y condiciones"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Sign all"
msgstr "Firmar todo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Sign document"
msgstr "Firmar documento"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sign now"
msgstr "Firmar ahora"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Sign requests"
msgstr "Solicitudes de firmas"

#. module: sign
#: model:ir.model,name:sign.model_sign_log
msgid "Sign requests access history"
msgstr "Historial de acceso a las solicitudes de firma"

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request
msgid "Sign send request"
msgstr "Firmar la solicitud de envío"

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request_signer
msgid "Sign send request signer"
msgstr "Firmar la solicitud de envío del signatario"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"Sign up for Odoo Sign to manage your own documents and signature requests!"
msgstr ""
"Regístrese en la aplicación Firma electrónica de Odoo para gestionar sus "
"propios documentos y solicitudes de firma."

#. module: sign
#: model:ir.actions.server,name:sign.sign_reminder_cron_ir_actions_server
msgid "Sign: Send mail reminder"
msgstr "Firma electrónica: enviar recordatorio por correo electrónico"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory"
msgstr "Signatario"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory's hash:"
msgstr "Hash del signatario:"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__sign
#: model:sign.item.type,name:sign.sign_item_type_signature
#: model:sign.item.type,placeholder:sign.sign_item_type_signature
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signature"
msgstr "Firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signature Date"
msgstr "Fecha de la firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_item_id
msgid "Signature Item"
msgstr "Elemento de la firma"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_option_action
msgid "Signature Item Options"
msgstr "Opciones del elemento de firma"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_role
msgid "Signature Item Party"
msgstr "Parte del elemento de la firma"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_role_action
msgid "Signature Item Role"
msgstr "Función del elemento signatario"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_type_action
#: model:ir.model,name:sign.model_sign_item_type
msgid "Signature Item Type"
msgstr "Tipo del elemento de la firma"

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item_value
msgid "Signature Item Value"
msgstr "Valor del elemento de la firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_item_ids
msgid "Signature Items"
msgstr "Elementos de la firma"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_model_patch.js:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.js:0
#: model:ir.model,name:sign.model_sign_request
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_request_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_id
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Signature Request"
msgstr "Solicitud de firma"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %(file_name)s"
msgstr "Solicitud de firma - %(file_name)s"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %s"
msgstr "Solicitud de firma - %s"

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item
msgid "Signature Request Item"
msgstr "Elemento de solicitud de firma"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_item_action
msgid "Signature Request Items"
msgstr "Elementos de solicitud de firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_item_id
msgid "Signature Request item"
msgstr "Elemento de solicitud de firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_request_ids
msgid "Signature Requests"
msgstr "Solicitudes de firma"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__start_sign
msgid "Signature Started"
msgstr "Proceso de firma iniciado"

#. module: sign
#: model:ir.model,name:sign.model_sign_template
msgid "Signature Template"
msgstr "Plantilla de firma"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Signature configuration"
msgstr "Configuración de la firma"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid ""
"Signature requested for template: %(template)s\n"
"Signatories: %(signatories)s"
msgstr ""
"Firma solicitada para la plantilla: %(template)s\n"
"Signatarios: %(signatories)s"

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid "Signature(s)"
msgstr "Firmas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signature:"
msgstr "Firma:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_menu_sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signatures"
msgstr "Firmas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Signed"
msgstr "Firmado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__signed_count
msgid "Signed Count"
msgstr "Número de firmas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Signed Documents"
msgstr "Documentos firmados"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signed_without_extra_auth
msgid "Signed Without Extra Authentication"
msgstr "Firmado sin autenticación adicional"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "Signed document"
msgstr "Documento firmado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signing_date
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signed on"
msgstr "Firmado el"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__partner_id
msgid "Signer"
msgstr "Signatario"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Signer One 2 Many"
msgstr "Signatario One 2 Many"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Signers"
msgstr "Signatarios"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signers_count
msgid "Signers Count"
msgstr "Número de signatarios"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Signing Date"
msgstr "Fecha de la firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signing Events"
msgstr "Eventos de firmas"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__set_sign_order
msgid "Signing Order"
msgstr "Orden de firma"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Since you're the one signing this document, you can do it directly within "
"Odoo.<br>External users can use the link provided by email."
msgstr ""
"Ya que usted es la persona que firmará este documento, puede hacerlo desde "
"Odoo. <br> Los usuarios externos pueden usar el enlace enviado por correo "
"electrónico."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Some fields have still to be completed"
msgstr "Faltan campos por completar"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some required items are not filled"
msgstr "Algunos elementos obligatorios están vacíos"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some unauthorised items are filled"
msgstr "Algunos elementos no autorizados se completaron"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Somebody is already filling a document which uses this template"
msgstr "Alguien ya está llenando un documento que utiliza esta plantilla"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Sorry, an error occurred, please try to fill the document again."
msgstr "Lo sentimos, ocurrió un error. Intente llenar el documento de nuevo."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Sorry, you cannot refuse this document"
msgstr "Lo sentimos, no puede rechazar este documento."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__set_sign_order
msgid ""
"Specify the order for each signer. The signature request only gets sent to                                     the next signers in the sequence when all signers from the previous level have                                     signed the document.\n"
"                                    "
msgstr ""
"Especifique el orden para cada signatario. La solicitud de firma solo se enviará a los signatarios posteriores de la secuencia cuando todos los signatarios del nivel anterior hayan firmado el documento.\n"
"                                    "

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_default
msgid "Standard"
msgstr "Estándar"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__state
#: model:ir.model.fields,field_description:sign.field_sign_request_item__state
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "State"
msgstr "Estado"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__request_state
msgid "State of the request on action log"
msgstr "Estado de la solicitud en el registro de acciones"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Status"
msgstr "Estado"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado según las actividades\n"
"Vencida: ya pasó la fecha límite\n"
"Hoy: hoy es la fecha de la actividad\n"
"Planeada: futuras actividades."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Stop Sharing"
msgstr "Dejar de compartir"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__subject
msgid "Subject"
msgstr "Asunto"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Summary"
msgstr "Resumen"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__name
msgid "Tag Name"
msgstr "Nombre de la etiqueta"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_template_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Ya hay una etiqueta con este nombre."

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_template_tag_action
#: model:ir.model.fields,field_description:sign.field_sign_request__template_tags
#: model:ir.model.fields,field_description:sign.field_sign_template__tag_ids
#: model:ir.ui.menu,name:sign.sign_template_tag_menu
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Tags"
msgstr "Etiquetas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Tags:"
msgstr "Etiquetas:"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__auto_field
msgid ""
"Technical name of the field on the partner model to auto-complete this "
"signature field at the time of signature."
msgstr ""
"Nombre técnico del campo en el modelo del contacto para completar en "
"automático este campo de firma al momento de la firma."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__template_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__template_id
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template"
msgstr "Plantilla"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.js:0
#: code:addons/sign/static/src/views/hooks.js:0
msgid "Template %s"
msgstr "Plantilla %s"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Template Properties"
msgstr "Propiedades de la plantilla"

#. module: sign
#: model:ir.actions.server,name:sign.sign_template_tour_trigger_action
msgid "Template Sample Contract.pdf trigger"
msgstr "Activador de la plantilla de muestra de contrato.pdf "

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template or Tag"
msgstr "Plantilla o etiqueta"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.act_window,name:sign.sign_template_action
#: model:ir.ui.menu,name:sign.sign_template_menu
msgid "Templates"
msgstr "Plantillas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Terms &amp; Conditions"
msgstr "Términos y condiciones"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__html
msgid "Terms as Web Page"
msgstr "Términos como página web"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__plain
msgid "Terms in Email"
msgstr "Términos en el correo electrónico"

#. module: sign
#: model:ir.model.fields,help:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,help:sign.field_res_config_settings__sign_terms_type
msgid ""
"Terms in Email - The text will be displayed at the bottom of every signature request email.\n"
"\n"
"        Terms as Web Page - A link will be pasted at the bottom of every signature request email, leading to your content.\n"
"        "
msgstr ""
"Términos en el correo electrónico: el texto aparecerá al final de cada solicitud de firma por correo electrónico.\n"
"\n"
"Términos como página web: un enlace se pegará al final de cada solicitud de firma por correo electrónico, este estará vinculado a su contenido."

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__text
#: model:sign.item.type,name:sign.sign_item_type_text
#: model:sign.item.type,placeholder:sign.sign_item_type_text
msgid "Text"
msgstr "Texto"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
msgid "Thank You!"
msgstr "Gracias"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"That's it, all done!<br>The document is signed, and a copy has been sent by "
"email to all participants, along with a traceability report."
msgstr ""
"¡Todo listo! <br> Ya se firmó el documento y se envió una copia por correo "
"electrónico a todos los participantes junto con un reporte de trazabilidad."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"The Odoo Sign document you are trying to reach does not exist. The signature"
" request might have been deleted or modified."
msgstr ""
"El documento en la aplicación Firma electrónica de Odoo al que intenta "
"acceder no existe. La solicitud de firma se pudo haber eliminado o "
"modificado."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "The PDF's password is required to generate the final document."
msgstr ""
"Es necesario contar con la contraseña del PDF para generar el documento "
"final."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The completed document cannot be created because the sign request is not "
"fully signed"
msgstr "No se puede crear el documento completo, hacen falta firmas."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "The computation is based on the website"
msgstr "El cálculo está basado en el sitio web"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The contact of %(role)s has been changed from %(old_partner)s to "
"%(new_partner)s."
msgstr "El contacto %(role)s cambió de %(old_partner)s a %(new_partner)s."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "The document"
msgstr "El documento"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document %s has been fully signed."
msgstr "El documento %s ha sido firmado por completo."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document (%s) has been rejected by one of the signers"
msgstr "Uno de los signatarios rechazó el documento (%s)"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "The document has been refused"
msgstr "Se ha rechazado el documento"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document has been signed by a signer and cannot be edited"
msgstr ""
"Un signatario ya firmó este documento y por lo tanto no se puede editar."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"The final document and this completion history have been sent by email "
"on&amp;nbsp;"
msgstr ""
"El documento final y este historial de finalización se enviaron por correo "
"electrónico el&amp;nbsp;"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"The integrity of the document's history cannot be verified. This could mean "
"that signature values or the underlying PDF document may have been modified "
"after the fact."
msgstr ""
"No se puede verificar la integridad del historial del documento. Esto podría"
" significar que los valores de la firma o el documento PDF correspondiente "
"se hayan modificado después."

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid ""
"The mail address of %(partner)s has been updated. The request will be "
"automatically resent."
msgstr ""
"Se actualizó la dirección de correo de %(partner)s. La solicitud de firma se"
" volverá a enviar de manera automática."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The mail has been sent to contacts in copy: %(contacts)s"
msgstr "El correo se envió a los contactos en copia: %(contacts)s"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/terms.py:0
msgid "The requested page is invalid, or doesn't exist anymore."
msgstr "La página solicitada no es válida o ya no existe."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "The role %s is required by the Sign application and cannot be deleted."
msgstr ""
"La aplicación Firma electrónica necesita la función %s, por lo que no se "
"puede eliminar."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The sign request has not been fully signed"
msgstr "Faltan firmas en la solicitud de firma."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "The signature has been canceled by %(partner)s(%(role)s)"
msgstr "%(partner)s (%(role)s) canceló la firma"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature has been refused by %(partner)s(%(role)s)"
msgstr "%(partner)s (%(role)s) rechazó la firma"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature mail has been sent to: "
msgstr "Se envió un correo de firma a:"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__is_mail_sent
msgid "The signature mail has been sent."
msgstr "Se envió el correo de firma."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "The signature request has been cancelled"
msgstr "La solicitud de firma fue cancelada"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature request has been edited by: %s."
msgstr "%s editó la solicitud de firma."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
msgid "The template doesn't exist anymore."
msgstr "La plantilla ya no existe."

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid ""
"The template has more pages than the current file, it can't be applied."
msgstr ""
"La plantilla tiene más páginas que el documento actual y no puede aplicarse."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"The total of sheets you saved is based on: the number of sent sign requests "
"x number of sheets in the document x (number of contacts who need to sign + "
"number of contacts in copy if the sign request is signed) = total of pages. "
"We assume that one page weights 0.005 kilograms."
msgstr ""
"El total de hojas ahorradas se calcula tomando en cuenta el número de "
"solicitudes de firma enviadas x el número de hojas en el documento x (el "
"número de contactos que necesitan firmarlo + el número de contactos en copia"
" si se firma la solicitud de firma). Consideramos que una hoja pesa 0.005 "
"kg."

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid "The uploaded file is not a valid PDF. Please upload a valid PDF file."
msgstr "El archivo PDF que subió no es válido, suba otro PDF."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "There are no signatures request."
msgstr "No hay solicitud de firmas."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "There are other documents waiting for your signature:"
msgstr "Hay otros documentos en espera de su firma:"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"There was an issue downloading your document. Please contact an "
"administrator."
msgstr ""
"Ocurrió un error al descargar su documento, póngase en contacto con un "
"administrador."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"There's no reason to panic, <br/>\n"
"                        you can still sign your document in a few clicks!"
msgstr ""
"No se preocupe, <br/>\n"
"                        podrá seguir firmando su documento en unos pocos clics."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "These files cannot be read, they may be corrupted or encrypted."
msgstr "No se pueden leer estos archivos, pueden estar dañados o encriptados."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "They will be removed from the uploaded files"
msgstr "Se eliminarán de los archivos cargados"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This function can only be called with sudo."
msgstr "Esta función solo se puede llamar con sudo."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__reference
#: model:ir.model.fields,help:sign.field_sign_request_item__reference
msgid "This is how the document will be named in the mail"
msgstr "Así es como se llamará el documento en el correo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "This link has expired."
msgstr "Este enlace expiró."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be refused"
msgstr "No se puede rechazar esta solicitud de firma"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be signed"
msgstr "No se puede firmar esta solicitud de firma"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request is not valid anymore"
msgstr "Esta solicitud de firma ya no es válida"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be filled"
msgstr "No se puede completar este elemento de la solicitud de firma"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be refused"
msgstr "No se puede rechazar este elemento de la solicitud de firma"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be signed"
msgstr "No se puede firmar este elemento de la solicitud de firma"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"This will keep all the already completed signature of this request and "
"disable every sent access, are you sure?"
msgstr ""
"Esto mantendrá todas las firmas de esta solicitud y desactivará todos los "
"accesos enviados, ¿está seguro?"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__tip
msgid "Tip"
msgstr "Consejo"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__sent
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__sent
msgid "To Sign"
msgstr "Por firmar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "To produce 1000 kg of wood, we have to cut 12 trees"
msgstr "Para producir 1000 kg de madera se deben cortar 12 árboles"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "To sign"
msgstr "Por firmar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__transaction_id
msgid "Transaction"
msgstr "Transacción"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Try Odoo Sign"
msgstr "Pruebe la aplicación Firma electrónica de Odoo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Try a sample contract"
msgstr "Intente con un contrato de prueba"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Try our sample document"
msgstr "Pruebe nuestro documento de prueba"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Try out this sample contract."
msgstr "Pruebe este ejemplo de contrato."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__type_id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__item_type
msgid "Type"
msgstr "Tipo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Type a name or email..."
msgstr "Escriba un nombre o correo electrónico..."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de la actividad de excepción registrada."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
msgid "Type tag name here"
msgstr "Escriba aquí el nombre de la etiqueta"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "UTC"
msgstr "UTC"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Unable to send the SMS, please contact the sender of the document."
msgstr ""
"No se puede enviar el SMS, póngase en contacto con el remitente del "
"documento."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"Unable to sign the document due to missing required data. Please contact an "
"administrator."
msgstr ""
"No es posible firmar el documento, hacen falta datos necesarios. Póngase en "
"contacto con un administrador."

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_role__auth_method__sms
msgid "Unique Code via SMS"
msgstr "Código único por SMS"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update
msgid "Update"
msgstr "Actualizar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload"
msgstr "Subir"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload PDF"
msgstr "Subir PDF"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Upload a PDF"
msgstr "Subir un PDF"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a PDF & Sign"
msgstr "Subir un PDF y firmarlo"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "Upload a PDF file or use an existing template to begin."
msgstr "Suba un archivo PDF o utilice una plantilla existente para comenzar."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload a PDF file to create a reusable template."
msgstr "Suba un archivo PDF para crear una plantilla reutilizable."

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Upload a PDF file to create your first template"
msgstr "Suba un archivo PDF para crear su primera plantilla"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a pdf that you want to sign directly"
msgstr "Suba un PDF que desee firmar directamente"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Use Layout"
msgstr "Usar diseño"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Use Tags to manage your Sign Templates and Sign Requests"
msgstr ""
"Utilice las etiquetas para gestionar sus plantillas y solicitudes de firmas"

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_duplicate_template_with_pdf
msgid "Use the layout of fields on a new PDF"
msgstr "Usar el diseño de campos en un PDF nuevo"

#. module: sign
#: model:ir.model,name:sign.model_res_users
#: model:ir.model.fields,field_description:sign.field_sign_log__user_id
#: model:sign.item.role,name:sign.sign_item_role_user
msgid "User"
msgstr "Usuario"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__token
msgid "User token"
msgstr "Token de usuario"

#. module: sign
#: model:res.groups,name:sign.group_sign_user
msgid "User: Own Templates"
msgstr "Usuario: plantillas propias"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__validity
#: model:ir.model.fields,field_description:sign.field_sign_send_request__validity
msgid "Valid Until"
msgstr "Válido hasta"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Validate"
msgstr "Validar "

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Validate & Send"
msgstr "Validar y enviar"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/edit_while_signing_signable_pdf_iframe.js:0
msgid "Validate & the next signatory is “%s”"
msgstr "Validar, el siguiente signatario es \"%s\""

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
msgid "Validate &amp; Send Completed Document"
msgstr "Validar y enviar el documento completado"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Validation Code"
msgstr "Código de validación"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_item_value_ids
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__value
msgid "Value"
msgstr "Valor"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_option_value_uniq
msgid "Value already exists!"
msgstr "¡El valor ya existe!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Verify"
msgstr "Verificar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "View Document"
msgstr "Ver documento"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "View document"
msgstr "Ver documento"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__open
msgid "View/Download"
msgstr "Ver/Descargar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Viewed/downloaded by"
msgstr "Visto/descargado por"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for me"
msgstr "Esperándome"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for others"
msgstr "Esperando a otros"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Warning"
msgstr "Advertencia"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Waste"
msgstr "Residuos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Water"
msgstr "Agua"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"We can only send reminders in the future - as soon as we find a way to send reminders in the past we'll notify you.\n"
"In the mean time, please make sure to input a positive number of days for the reminder interval."
msgstr ""
"Solo puede configurar recordatorios para fechas futuras.\n"
"Asegúrese que el número de días para el intervalo de recordatorio sea positivo."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "We couldn't find the signature request you're looking for!"
msgstr "No pudimos encontrar la solicitud de firma que busca."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"We display to you a ratio based on the saved weight versus 1000 kg of paper "
"usage."
msgstr ""
"Le mostramos una proporción basada en el peso ahorrado en comparación con "
"los 1000 kg de uso de papel."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "We will send you this document by email once everyone has signed."
msgstr ""
"Le enviaremos este documento por correo electrónico una vez que todos hayan "
"firmado."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid ""
"We'll send an email to warn other contacts in copy & signers with the reason"
" you provided."
msgstr ""
"Enviaremos un correo electrónico para avisar a los contactos en copia y "
"signatarios del motivo que nos ha proporcionado."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Well done, your document is ready!<br>Let's send it to get our first "
"signature."
msgstr ""
"Muy bien, su documento está listo.<br> Ahora hay que enviarlo para obtener "
"la primera firma."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "What about those conversions I see?"
msgstr "¿Y esas conversiones que veo?"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
msgid "Why do you refuse to sign this document?"
msgstr "¿Por qué rechaza firmar este documento?"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__width
msgid "Width"
msgstr "Ancho"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Wood"
msgstr "Madera"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Write email or search contact..."
msgstr "Escriba un correo electrónico o busque un contacto..."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "Wrong password"
msgstr "Contraseña incorrecta"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "XYZ123456"
msgstr "XYZ123456"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"You can contact the person who invited you to sign the document by email for"
" help."
msgstr ""
"Puede contactar a la persona que le invitó a firmar el documento por correo "
"electrónico para que le ayude."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You can only add new items for the current role"
msgstr "Solo puede agregar nuevos elementos a la función actual"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"You can request a new link to access your document and sign it, it will  be "
"delivered in your inbox right away."
msgstr ""
"Puede solicitar un nuevo enlace para acceder a su documento y firmarlo, lo "
"recibirá en su bandeja de entrada inmediatamente."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You can't delete a template for which signature requests exist but you can "
"archive it instead."
msgstr ""
"No puede eliminar una plantilla para la que existen solicitudes de firma, "
"pero sí puede archivarla."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You cannot reassign this signatory"
msgstr "No puede cambiar de signatario"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You cannot share this document by link, because it has fields to be filled "
"by different roles. Use Send button instead."
msgstr ""
"No puede compartir este documento mediante un enlace. Tiene campos que deben"
" llenarse por distintos roles, utilice el botón Enviar."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"You do not have access to these documents, please contact a Sign "
"Administrator."
msgstr ""
"No tiene acceso a estos documentos, póngase en contacto con el administrador"
" de Firma electrónica."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "You have refused the document"
msgstr "Ha rechazado el documento"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "You have until"
msgstr "Tiene hasta el"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You must specify one signer for each role of your sign template"
msgstr ""
"Debe especificar un signatario para cada función de su plantilla de firmas"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You need to define a signatory"
msgstr "Debe definir un signatario"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You should select at least one document to download."
msgstr "Debe seleccionar al menos un documento a descargar."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "You will get the signed document by email."
msgstr "Recibirá un documento firmado por correo electrónico"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "You won't receive any notification for this signature request anymore."
msgstr "Ya no recibirá ninguna notificación para esta solicitud de firma."

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "You're one click away from automating your signature process!"
msgstr "Está a un solo clic de automatizar su proceso de firma."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Your Information"
msgstr "Su información"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Your confirmation code is %s"
msgstr "Su código de confirmación es %s"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your email"
msgstr "Su correo electrónico"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid ""
"Your file is encrypted, PDF's password is required to generate final "
"document. The final document will be encrypted with the same password."
msgstr ""
"Su archivo está protegido, es necesario contar con la contraseña del PDF "
"para generar el documento final. El documento final se cifrará con la misma "
"contraseña."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your name"
msgstr "Su nombre"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Your signature has been saved. Next signatory is"
msgstr "Se ha guardado su firma. El siguiente signatario es"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid ""
"Your signature was not submitted. Ensure the SMS validation code is correct."
msgstr ""
"No se ha enviado su firma. Asegúrese de que el código de validación del SMS "
"sea correcto."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "and"
msgstr "y"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "and the process will continue"
msgstr "y el proceso continuará"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "and:"
msgstr "y:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"because you don't have enough credits for this operation.\n"
"                    The signatory was able to finish signing, but was not asked to authenticate fully."
msgstr ""
"porque no tiene los créditos suficientes para realizar esta operación.\n"
"                    El signatario terminó de firmar, pero no se le pidió que realizara una autenticación completa."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "can"
msgstr "lata"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "cans"
msgstr "latas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "click here to cancel it."
msgstr "haga clic aquí para cancelarla."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "days."
msgstr "días."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. +1 415 555 0100"
msgstr "Por ejemplo, +1 415 555 0100"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. 314159"
msgstr "Por ejemplo, 314159"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_form
msgid "e.g. Employee"
msgstr "Por ejemplo, empleado"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "e.g. Non-disclosure agreement"
msgstr "Por ejemplo, acuerdo de confidencialidad"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "every"
msgstr "cada"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_checkbox
#: model:sign.item.type,tip:sign.sign_item_type_company
#: model:sign.item.type,tip:sign.sign_item_type_date
#: model:sign.item.type,tip:sign.sign_item_type_email
#: model:sign.item.type,tip:sign.sign_item_type_multiline_text
#: model:sign.item.type,tip:sign.sign_item_type_name
#: model:sign.item.type,tip:sign.sign_item_type_phone
#: model:sign.item.type,tip:sign.sign_item_type_radio
#: model:sign.item.type,tip:sign.sign_item_type_text
msgid "fill in"
msgstr "completar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been completed and signed by"
msgstr "lo completó y firmó"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been edited, completed and signed by"
msgstr "lo editó, completó y firmo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "has been signed by"
msgstr "lo firmó"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hour of computer use"
msgstr "hora de uso de la computadora"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hours of computer use"
msgstr "horas de uso de la computadora"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "https://c.environmentalpaper.org/"
msgstr "https://c.environmentalpaper.org/"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ip"
msgstr "ip"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "is free, forever, with unlimited users - and it's fun to use!"
msgstr ""
"es gratis, para siempre, con usuarios ilimitados. Además, ¡es muy divertido "
"de usar!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kWh of energy saved"
msgstr "kWh de energía ahorrada"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of reduced carbon emissions"
msgstr "kg de emisiones de carbono reducidas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of waste prevented"
msgstr "kg de residuos evitados"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of wood saved"
msgstr "kg de madera ahorrada"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liter of car fuel"
msgstr "litro de combustible para vehículos"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liters of car fuel"
msgstr "litros de combustible para vehículos"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_initial
msgid "mark it"
msgstr "marcarlo"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "next"
msgstr "siguiente"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "on"
msgstr "en"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "or"
msgstr "o"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "sheets of paper saved"
msgstr "hojas de papel ahorradas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "shower"
msgstr "ducha"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "showers"
msgstr "duchas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "sign"
msgstr "firmar"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_signature
msgid "sign it"
msgstr "firmarlo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message
msgid "sign.message"
msgstr "sign.message"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_cc
msgid "sign.message_cc"
msgstr "sign.message_cc"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "tags"
msgstr "etiquetas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "that's"
msgstr "eso equivale a"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "to sign the document."
msgstr "para firmar el documento."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "to:"
msgstr "a:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "tree"
msgstr "árbol"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "trees"
msgstr "árboles"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "we've got a list of consumption for 1000 kg of paper usage."
msgstr "tenemos una lista de consumo para 1000 kg de uso de papel."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "without the requested extra-authentification step ("
msgstr "sin el paso de autentificación adicional solicitado ("

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "you"
msgstr "usted"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}}"
msgstr "{{green_report_el_title}}"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}} Summary"
msgstr "Resumen {{green_report_el_title}}"
