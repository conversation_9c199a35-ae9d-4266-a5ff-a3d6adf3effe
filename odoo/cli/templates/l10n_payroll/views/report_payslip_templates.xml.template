<odoo>
<template id="l10n_{{code}}_report_payslip_{{code}}" inherit_id="hr_payroll.report_payslip" primary="True">

</template>

<template id="l10n_{{code}}_report_payslip_{{code}}_lang">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-set="o" t-value="o.with_context(lang=o.employee_id.sudo().lang or o.env.lang)"/>
            <t t-call="l10n_{{code}}_hr_payroll.report_payslip_{{code}}" t-lang="o.env.lang"/>
        </t>
    </t>
</template>
</odoo>
