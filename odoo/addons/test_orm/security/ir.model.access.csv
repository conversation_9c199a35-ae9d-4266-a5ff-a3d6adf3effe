"id","name","model_id:id","group_id:id","perm_read","perm_write","perm_create","perm_unlink"
access_category,test_orm_category,test_orm.model_test_orm_category,base.group_system,1,1,1,1
access_decimal_precision_test_all,decimal.precision.test,model_decimal_precision_test,base.group_system,1,1,1,1
access_discussion,test_orm_discussion,test_orm.model_test_orm_discussion,base.group_user,1,1,1,1
access_message,test_orm_message,test_orm.model_test_orm_message,base.group_user,1,1,1,1
access_emailmessage,test_orm_emailmessage,test_orm.model_test_orm_emailmessage,base.group_system,1,1,1,1
access_multi,test_orm_multi,test_orm.model_test_orm_multi,base.group_system,1,1,1,1
access_multi_line,test_orm_multi_line,test_orm.model_test_orm_multi_line,base.group_user,1,1,1,1
access_multi_line2,test_orm_multi_line2,test_orm.model_test_orm_multi_line2,base.group_user,1,1,1,1
access_multi_tag,test_orm_multi_tag,test_orm.model_test_orm_multi_tag,base.group_user,1,1,1,1
access_creativework_edition,test_orm_creativework_edition,model_test_orm_creativework_edition,base.group_system,1,1,1,1
access_creativework_book,test_orm_creativework_book,model_test_orm_creativework_book,base.group_system,1,1,1,1
access_creativework_movie,test_orm_creativework_movie,model_test_orm_creativework_movie,base.group_system,1,1,1,1
access_mixed,test_orm_mixed,test_orm.model_test_orm_mixed,base.group_user,1,1,1,1
access_domain_bool,access_domain_bool,model_domain_bool,base.group_system,1,1,1,1
access_test_orm_foo,access_test_orm_foo,model_test_orm_foo,base.group_system,1,1,1,1
access_test_orm_bar,access_test_orm_bar,model_test_orm_bar,base.group_system,1,1,1,1
access_test_orm_related,access_test_orm_related,model_test_orm_related,base.group_user,1,1,1,1
access_test_orm_related_foo,access_test_orm_related_foo,model_test_orm_related_foo,base.group_user,1,1,1,1
access_test_orm_related_bar,access_test_orm_related_bar,model_test_orm_related_bar,base.group_user,1,1,1,1
access_test_orm_related_inherits,access_test_orm_related_inherits,model_test_orm_related_inherits,base.group_user,1,1,1,1
access_test_orm_company,access_test_orm_company,model_test_orm_company,base.group_user,1,1,1,1
access_test_orm_company_attr,access_test_orm_company_attr,model_test_orm_company_attr,base.group_user,1,1,1,1
access_test_orm_compute_inverse,access_test_orm_compute_inverse,model_test_orm_compute_inverse,base.group_system,1,1,1,1
access_test_orm_compute_readonly,access_test_orm_compute_readonly,model_test_orm_compute_readonly,base.group_system,1,1,1,1
access_test_orm_multi_compute_inverse,access_test_orm_multi_compute_inverse,model_test_orm_multi_compute_inverse,base.group_system,1,1,1,1
access_test_orm_recursive,access_test_orm_recursive,model_test_orm_recursive,base.group_system,1,1,1,1
access_test_orm_recursive_tree,access_test_orm_recursive_tree,model_test_orm_recursive_tree,base.group_system,1,1,1,1
access_test_orm_recursive_order,access_test_orm_recursive_order,model_test_orm_recursive_order,base.group_system,1,1,1,1
access_test_orm_recursive_line,access_test_orm_recursive_line,model_test_orm_recursive_line,base.group_system,1,1,1,1
access_test_orm_recursive_task,access_test_orm_recursive_task,model_test_orm_recursive_task,base.group_system,1,1,1,1
access_test_orm_compute_sudo,access_test_orm_compute_sudo,test_orm.model_test_orm_compute_sudo,base.group_user,1,1,1,1
access_test_orm_cascade,access_test_orm_cascade,model_test_orm_cascade,base.group_system,1,1,1,1
access_test_orm_compute_readwrite,access_test_orm_compute_readwrite,model_test_orm_compute_readwrite,base.group_system,1,1,1,1
access_test_orm_compute_onchange,access_test_orm_compute_onchange,model_test_orm_compute_onchange,base.group_system,1,1,1,1
access_test_orm_compute_onchange_line,access_test_orm_compute_onchange_line,model_test_orm_compute_onchange_line,base.group_system,1,1,1,1
access_test_orm_compute_dynamic_depends,access_test_orm_compute_dynamic_depends,model_test_orm_compute_dynamic_depends,base.group_system,1,1,1,1
access_test_orm_compute_unassigned,access_test_orm_compute_unassigned,model_test_orm_compute_unassigned,base.group_user,1,1,1,1
access_test_orm_one2many,access_test_orm_one2many,model_test_orm_one2many,base.group_system,1,1,1,1
access_test_orm_one2many_line,access_test_orm_one2many_line,model_test_orm_one2many_line,base.group_system,1,1,1,1
access_test_orm_binary_svg,access_test_orm_binary_svg,model_test_orm_binary_svg,base.group_user,1,1,1,1
access_test_orm_monetary_base,access_test_orm_monetary_base,model_test_orm_monetary_base,base.group_system,1,1,1,1
access_test_orm_monetary_related,access_test_orm_monetary_related,model_test_orm_monetary_related,base.group_system,1,1,1,1
access_test_orm_monetary_custom,access_test_orm_monetary_custom,model_test_orm_monetary_custom,base.group_system,1,1,1,1
access_test_orm_monetary_inherits,access_test_orm_monetary_inherits,model_test_orm_monetary_inherits,base.group_system,1,1,1,1
access_test_orm_field_with_caps,access_test_orm_field_with_caps,model_test_orm_field_with_caps,base.group_system,1,1,1,1
access_test_orm_req_m2o,access_test_orm_req_m2o,model_test_orm_req_m2o,base.group_system,1,1,1,1
access_test_orm_selection,access_test_orm_selection,model_test_orm_selection,base.group_system,1,1,1,1
access_test_orm_attachment,access_test_orm_attachment,model_test_orm_attachment,base.group_system,1,1,1,1
access_test_orm_attachment_host,access_test_orm_attachment_host,model_test_orm_attachment_host,base.group_system,1,1,1,1
access_test_orm_model_image,access_test_orm_model_image,model_test_orm_model_image,base.group_system,1,1,1,1
access_test_orm_model_a,access_test_orm_model_a,model_test_orm_model_a,base.group_system,1,1,1,1
access_test_orm_model_b,access_test_orm_model_b,model_test_orm_model_b,base.group_system,1,1,1,1
access_test_orm_model_parent,access_test_orm_model_parent,model_test_orm_model_parent,base.group_system,1,1,1,1
access_test_orm_model_child,access_test_orm_model_child,model_test_orm_model_child,base.group_system,1,1,1,1
access_test_orm_model_child_nocheck,access_test_orm_model_child_nocheck,model_test_orm_model_child_nocheck,base.group_system,1,1,1,1
access_test_orm_display,access_test_orm_display,model_test_orm_display,base.group_system,1,1,1,1
access_test_orm_model_active_field,access_test_orm_model_active_field,model_test_orm_model_active_field,base.group_system,1,1,1,1
access_test_orm_model_many2one_reference,access_test_orm_model_many2one_reference,model_test_orm_model_many2one_reference,base.group_system,1,1,1,1
access_test_orm_inverse_m2o_ref,access_test_orm_inverse_m2o_ref,model_test_orm_inverse_m2o_ref,base.group_system,1,1,1,1
access_test_orm_model_binary,access_test_orm_model_binary,model_test_orm_model_binary,base.group_system,1,1,1,1
access_test_orm_model_child_m2o,access_test_orm_model_child_m2o,model_test_orm_model_child_m2o,base.group_system,1,1,1,1
access_test_orm_model_parent_m2o,access_test_orm_model_parent_m2o,model_test_orm_model_parent_m2o,base.group_system,1,1,1,1
access_test_orm_partner,access_test_orm_partner,test_orm.model_test_orm_partner,base.group_system,1,1,1,1
access_test_orm_req_m2o_transient,access_test_orm_req_m2o_transient,model_test_orm_req_m2o_transient,base.group_system,0,0,0,0
access_test_orm_country,access_test_orm_country,model_test_orm_country,base.group_user,1,1,1,1
access_test_orm_city,access_test_orm_city,model_test_orm_city,base.group_user,1,1,1,1
access_test_orm_monetary_order,access_test_orm_monetary_order,model_test_orm_monetary_order,base.group_system,1,1,1,1
access_test_orm_monetary_order_line,access_test_orm_monetary_order_line,model_test_orm_monetary_order_line,base.group_system,1,1,1,1
access_test_orm_model_selection_base,access_test_orm_model_selection_base,model_test_orm_model_selection_base,base.group_system,1,1,1,1
access_test_orm_model_selection_required,access_test_orm_model_selection_required,model_test_orm_model_selection_required,base.group_system,1,1,1,1
access_test_orm_model_selection_non_stored,access_test_orm_model_selection_non_stored,model_test_orm_model_selection_non_stored,base.group_system,1,1,1,1
access_test_orm_model_selection_required_for_write_override,access_test_orm_model_selection_required_for_write_override,model_test_orm_model_selection_required_for_write_override,base.group_system,1,1,1,1
access_test_orm_model_selection_related,access_test_orm_model_selection_related,test_orm.model_test_orm_model_selection_related,base.group_user,1,1,1,1
access_test_orm_model_selection_related_updatable,access_test_orm_model_selection_related_updatable,test_orm.model_test_orm_model_selection_related_updatable,base.group_user,1,1,1,1
access_test_orm_move,access_test_orm_move,model_test_orm_move,base.group_system,1,1,1,1
access_test_orm_move_line,access_test_orm_move_line,model_test_orm_move_line,base.group_user,1,1,1,1
access_test_orm_payment,access_test_orm_payment,model_test_orm_payment,base.group_system,1,1,1,1
access_test_orm_order,access_test_orm_order,model_test_orm_order,base.group_system,1,1,1,1
access_test_orm_order_line,access_test_orm_order_line,model_test_orm_order_line,base.group_system,1,1,1,1
access_test_orm_model_shared_cache_compute_parent,access_test_orm.model_shared_cache_compute_parent,model_test_orm_model_shared_cache_compute_parent,base.group_user,1,1,1,1
access_test_orm_model_shared_cache_compute_line,access_test_orm.model_shared_cache_compute_line,model_test_orm_model_shared_cache_compute_line,base.group_user,1,1,1,1
access_test_orm_compute_container,access_test_orm_compute_container,model_test_orm_compute_container,base.group_system,1,1,1,1
access_test_orm_compute_member,access_test_orm_compute_member,model_test_orm_compute_member,base.group_system,1,1,1,1
access_test_orm_user,access_test_orm_user,model_test_orm_user,base.group_system,1,1,1,1
access_test_orm_group,access_test_orm_group,model_test_orm_group,base.group_system,1,1,1,1
access_test_orm_compute_editable,access_test_orm_compute_editable,model_test_orm_compute_editable,base.group_system,1,1,1,1
access_test_orm_compute_editable_line,access_test_orm_compute_editable_line,model_test_orm_compute_editable_line,base.group_system,1,1,1,1
access_test_orm_model_constrained_unlinks,access_test_orm_model_constrained_unlinks,model_test_orm_model_constrained_unlinks,base.group_system,1,1,1,1
access_test_orm_trigger_left,access_test_orm_trigger_left,model_test_orm_trigger_left,base.group_system,1,1,1,1
access_test_orm_trigger_middle,access_test_orm_trigger_middle,model_test_orm_trigger_middle,base.group_system,1,1,1,1
access_test_orm_trigger_right,access_test_orm_trigger_right,model_test_orm_trigger_right,base.group_system,1,1,1,1
access_test_orm_crew,access_test_orm_crew,model_test_orm_crew,base.group_user,1,1,1,1
access_test_orm_ship,access_test_orm_ship,model_test_orm_ship,base.group_user,1,1,1,1
access_test_orm_pirate,access_test_orm_pirate,model_test_orm_pirate,base.group_user,1,1,1,1
access_test_orm_prisoner,access_test_orm_prisoner,model_test_orm_prisoner,base.group_user,1,1,1,1
access_test_orm_precompute,access_test_orm_precompute,model_test_orm_precompute,base.group_system,1,0,0,0
access_test_orm_precompute_line,access_test_orm_precompute_line,model_test_orm_precompute_line,base.group_system,1,0,0,0
access_test_orm_precompute_combo,access_test_orm_precompute_combo,model_test_orm_precompute_combo,base.group_system,1,0,0,0
access_test_orm_precompute_editable,access_test_orm_precompute_editable,model_test_orm_precompute_editable,base.group_system,1,0,0,0
access_test_orm_precompute_readonly,access_test_orm_precompute_readonly,model_test_orm_precompute_readonly,base.group_system,1,0,0,0
access_test_orm_precompute_required,access_test_orm_precompute_required,model_test_orm_precompute_required,base.group_system,1,0,0,0
access_test_orm_precompute_monetary,access_test_orm_precompute_monetary,model_test_orm_precompute_monetary,base.group_system,1,0,0,0
access_test_orm_prefetch,access_test_orm_prefetch,model_test_orm_prefetch,base.group_system,1,0,0,0
access_test_orm_prefetch_line,access_test_orm_prefetch_line,model_test_orm_prefetch_line,base.group_system,1,0,0,0
access_test_orm_modified,access_test_orm_modified,model_test_orm_modified,base.group_user,1,1,1,1
access_test_orm_modified_line,access_test_orm_modified_line,model_test_orm_modified_line,base.group_user,1,1,1,1
access_test_orm_related_translation_1,access_test_orm_related_translation_1,model_test_orm_related_translation_1,base.group_system,1,1,1,1
access_test_orm_related_translation_2,access_test_orm_related_translation_2,model_test_orm_related_translation_2,base.group_system,1,1,1,1
access_test_orm_related_translation_3,access_test_orm_related_translation_3,model_test_orm_related_translation_3,base.group_system,1,1,1,1
access_test_orm_indexed_translation,access_test_orm_indexed_translation,model_test_orm_indexed_translation,base.group_system,1,1,1,1
access_test_orm_empty_char,access_test_orm_empty_char,model_test_orm_empty_char,base.group_system,1,1,1,1
access_test_orm_empty_int,access_test_orm_empty_int,model_test_orm_empty_int,base.group_system,1,1,1,1
access_test_orm_course,access_test_orm_course,model_test_orm_course,base.group_user,1,1,1,1
access_test_orm_lesson,access_test_orm_lesson,model_test_orm_lesson,base.group_user,1,1,1,1
access_test_orm_person,access_test_orm_person,model_test_orm_person,base.group_user,1,1,1,1
access_test_orm_employer,access_test_orm_employer,model_test_orm_employer,base.group_user,1,1,1,1
access_test_orm_person_account,access_test_orm_person_account,model_test_orm_person_account,base.group_user,1,1,1,1
public_access_test_orm_course,access_test_orm_course,model_test_orm_course,base.group_public,1,0,0,0
access_test_orm_team,access_test_orm_team,model_test_orm_team,base.group_user,1,0,0,0
access_test_orm_team_member,access_test_orm_team_member,model_test_orm_team_member,base.group_user,1,0,0,0
access_test_orm_unsearchable_o2m_employee,access_test_orm_unsearchable_o2m,model_test_orm_unsearchable_o2m,base.group_user,1,1,1,1
access_test_orm_any_parent,access_test_orm_any_parent,model_test_orm_any_parent,base.group_user,1,0,0,0
access_test_orm_any_child,access_test_orm_any_child,model_test_orm_any_child,base.group_user,1,0,0,0
access_test_orm_any_tag,access_test_orm_any_tag,model_test_orm_any_tag,base.group_user,1,0,0,0
access_test_orm_hierarchy_head,access_test_orm_hierarchy_head,model_test_orm_hierarchy_head,base.group_user,1,1,1,0
access_test_orm_hierarchy_node,access_test_orm_hierarchy_node,model_test_orm_hierarchy_node,base.group_user,1,1,1,0
access_test_orm_transient_model,access_test_orm_transient_model,model_test_orm_transient_model,base.group_user,1,1,1,1
access_test_orm_custom_view,access_test_orm_custom_view,model_test_orm_custom_view,base.group_user,1,0,0,0
access_test_orm_custom_table_query,access_test_orm_custom_table_query,model_test_orm_custom_table_query,base.group_user,1,0,0,0
access_test_orm_custom_table_query_sql,access_test_orm_custom_table_query_sql,model_test_orm_custom_table_query_sql,base.group_user,1,0,0,0
access_test_orm_autovacuumed,access_test_orm_autovacuumed,model_test_orm_autovacuumed,base.group_user,1,0,0,0
access_test_orm_shared_compute,access_test_orm_shared_compute,model_test_orm_shared_compute,base.group_user,1,0,0,0
access_test_orm_model_no_access,access_test_orm_model_no_access,model_test_orm_model_no_access,,0,0,0,0
access_test_orm_model_all_access_public,access_test_orm_model_all_access_public,model_test_orm_model_all_access,base.group_public,1,0,0,0
access_test_orm_model_all_access_portal,access_test_orm_model_all_access_portal,model_test_orm_model_all_access,base.group_portal,1,0,0,0
access_test_orm_model_all_access_user,access_test_orm_model_all_access_user,model_test_orm_model_all_access,base.group_user,1,0,0,0
access_test_orm_model_all_access_system,access_test_orm_model_all_access_system,model_test_orm_model_all_access,base.group_system,1,0,0,0
access_test_orm_model_some_access,access_test_orm_model_some_access,model_test_orm_model_some_access,base.group_public,1,0,0,0
access_test_orm_model_some_access_system,access_test_orm_model_some_access_system,model_test_orm_model_some_access,base.group_system,1,0,0,0
access_test_orm_model2_some_access,access_test_orm_model2_some_access,model_test_orm_model2_some_access,base.group_multi_company,1,0,0,0
access_test_orm_model3_some_access,access_test_orm_model3_some_access,model_test_orm_model3_some_access,base.group_multi_company,1,0,0,0
access_test_orm_computed_modifier,access_test_orm_computed_modifier_user,model_test_orm_computed_modifier,base.group_user,1,0,0,0
access_test_orm_view_str_id,access_test_orm_view_str_id,test_orm.model_test_orm_view_str_id,base.group_user,1,0,0,0
access_test_orm_create_performance,access_test_orm_create_performance,test_orm.model_test_orm_create_performance,base.group_user,1,0,0,0
access_test_orm_create_performance_line,access_test_orm_create_performance_line,test_orm.model_test_orm_create_performance_line,base.group_user,1,0,0,0
access_test_orm_onchange_partial_view,access_test_orm_onchange_partial_view,model_test_orm_onchange_partial_view,base.group_user,1,0,0,0
access_test_performance_base,access_test_performance_base,model_test_performance_base,base.group_user,1,1,1,1
access_test_performance_line,access_test_performance_line,model_test_performance_line,base.group_user,1,1,1,1
access_test_performance_tag,access_test_performance_tag,model_test_performance_tag,base.group_user,1,1,1,1
access_test_performance_bacon,access_test_performance_bacon,model_test_performance_bacon,base.group_system,1,1,1,1
access_test_performance_eggs,access_test_performance_eggs,model_test_performance_eggs,base.group_system,1,1,1,1
access_test_performance_mozzarella,access_test_performance_mozzarella,model_test_performance_mozzarella,base.group_system,1,1,1,1
access_test_performance_simple_minded,access_test_performance_simple_minded,model_test_performance_simple_minded,base.group_system,1,1,1,1