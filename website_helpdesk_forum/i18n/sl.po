# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_forum
#
# Translators:
# <AUTHOR> <EMAIL>, 2022
# J<PERSON><PERSON>ur <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:50+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"Language: sl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid ", by"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "<i class=\"fa fa-3x rounded bg-secondary m-3 fa-comments fa-3x rounded bg-secondary m-3\">​</i>"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "<i class=\"fa fa-fw fa-2x fa-comments\" title=\"Forum Post\"/>"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.question_dropdown
msgid ""
"<i class=\"fa fa-life-ring fa-fw text-muted\"/>\n"
"                View Ticket"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "<span style=\"font-size: 18px;\">Ask questions and discuss tips with fellow members in a forum</span>"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "<span style=\"font-weight: bolder; font-size: 18px;\">Community</span>"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "Answers"
msgstr "Odgovori"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.footer
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.knowledge_base_forum_card
msgid "Ask the Community"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket__can_share_forum
msgid "Can Share Forum"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Create Post"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Create and View Post"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__description
msgid "Description"
msgstr "Opis"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Discard"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__forum_id
msgid "Forum"
msgstr "Forum"

#. module: website_helpdesk_forum
#: model:ir.model,name:website_helpdesk_forum.model_forum_post
msgid "Forum Post"
msgstr "Objava foruma"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket__forum_post_count
msgid "Forum Post Count"
msgstr ""

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/wizards/helpdesk_ticket_select_forum.py:0
msgid "Forum Post created"
msgstr ""

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/helpdesk.py:0
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket__forum_post_ids
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_view_form_inherit_website_helpdesk_forum
msgid "Forum Posts"
msgstr "Objave foruma"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_team__website_forum_ids
msgid "Forums"
msgstr "Forumi"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket__use_website_helpdesk_forum
msgid "Help Center Active"
msgstr ""

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/helpdesk.py:0
msgid "Help Center not active for this team."
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model,name:website_helpdesk_forum.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Skupina za pomoč uporabnikom"

#. module: website_helpdesk_forum
#: model:ir.model,name:website_helpdesk_forum.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Zahtevek za pomoč uporabnikom"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__id
msgid "ID"
msgstr "ID"

#. module: website_helpdesk_forum
#: model:ir.model.fields,help:website_helpdesk_forum.field_helpdesk_team__website_forum_ids
msgid "In the help center, customers will only be able to see posts from the selected forums."
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/models/helpdesk.py:0
msgid "No posts associated to this ticket."
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_view_form
msgid "Select Forum"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.actions.act_window,name:website_helpdesk_forum.helpdesk_ticket_select_forum_wizard_action
#: model:ir.model,name:website_helpdesk_forum.model_helpdesk_ticket_select_forum_wizard
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.helpdesk_ticket_view_form_inherit_website_helpdesk_forum
msgid "Share on Forum"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_team__show_knowledge_base_forum
msgid "Show Knowledge Base Forum"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_forum_post__show_ticket
msgid "Show Ticket"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "Solved"
msgstr ""

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__tag_ids
msgid "Tags"
msgstr "Ključne besede"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_forum_post__ticket_id
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__ticket_id
msgid "Ticket"
msgstr "Zahtevek"

#. module: website_helpdesk_forum
#: model:ir.model.fields,field_description:website_helpdesk_forum.field_helpdesk_ticket_select_forum_wizard__title
msgid "Title"
msgstr "Naslov"

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "Views"
msgstr "Prikazi"

#. module: website_helpdesk_forum
#. odoo-python
#: code:addons/website_helpdesk_forum/wizards/helpdesk_ticket_select_forum.py:0
msgid "You must select a forum to share the ticket"
msgstr ""

#. module: website_helpdesk_forum
#: model_terms:ir.ui.view,arch_db:website_helpdesk_forum.search_result
msgid "| 0 Answer"
msgstr ""
