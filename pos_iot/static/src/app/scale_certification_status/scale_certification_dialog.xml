<?xml version="1.0"?>
<templates>
    <t t-name="pos_iot.ScaleCertificationDialog">
        <Dialog>
            <t t-set-slot="header">
                <h4 class="modal-title text-break w-100 fw-bolder">
                    Warning
                </h4>
                <button type="button" class="btn-close" aria-label="Close" tabindex="-1" t-on-click="props.close"/>
            </t>

            <p class="fw-bolder">Your configurations are not compliant with the European regulation. In order to use a certified POS with your scale, you should address the following:</p>
            <ul class="m-4 mt-3">
                <li style="list-style-type: disc;" t-foreach="props.errors" t-as="error" t-key="error" t-out="error"/>
            </ul>
            <p>
                Odoo can automatically apply the right modification for you.
                Be aware that the modification will change global rounding settings, and can affect more than just this Point of Sale.
            </p>

            <t t-set-slot="footer">
                <button class="btn btn-primary" t-on-click="onClickTracked.call" t-att-disabled="onClickTracked.status === 'loading'">
                    Apply changes
                </button>
                <button class="btn btn-secondary" t-on-click="props.close" t-att-disabled="onClickTracked.status === 'loading'">Discard</button>
            </t>
        </Dialog>
    </t>
</templates>
