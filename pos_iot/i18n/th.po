# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_iot
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:45+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with a Worldline payment terminal"
msgstr "รับชำระเงินด้วยเครื่องชำระเงิน Worldline"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with an Ingenico payment terminal"
msgstr "รับชำระเงินด้วยสถานีชำระเงิน Ingenico"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Access your"
msgstr "เข้าถึง"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.js:0
msgid "An error occurred while attempting to fix certification issues - %s"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Apply changes"
msgstr "ใช้การเปลี่ยนแปลง"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Barcode Scanners/Card Readers"
msgstr "เครื่องสแกนบาร์โค้ด/เครื่องอ่านบัตร"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "ข้ามการพิมพ์ด้วยเบราว์เซอร์และพิมพ์ผ่านพร็อกซีฮาร์ดแวร์"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Cashdrawer"
msgstr "ลิ้นชักเก็บเงิน"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "คลิกที่ ขั้นสูง/แสดงรายละเอียด/รายละเอียด/ข้อมูลเพิ่มเติม"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"คลิกที่ ให้ดำเนินการ .../เพิ่มข้อยกเว้น/เยี่ยมชมเว็บไซต์นี้/ไปที่หน้าเว็บ"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Close"
msgstr "ปิด"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Close this window and try again"
msgstr "ปิดหน้าต่างและลองใหม่อีกครั้ง"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Configuration of payment terminal failed"
msgstr "การกำหนดค่าสถานีชำระเงินล้มเหลว"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Connection to IoT Box failed"
msgstr "การเชื่อมต่อกับกล่องไอโอทีล้มเหลว"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Connection to terminal failed"
msgstr "การเชื่อมต่อสถานีชำระเงินล้มเหลว"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Customer Display"
msgstr "แสดงลูกค้า"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.js:0
msgid "Decimal accuracy is less than 3 decimal places"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Discard"
msgstr "ละทิ้ง"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Electronic Scale"
msgstr "เครื่องชั่งอิเล็กทรอนิกส์"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"เปิดใช้งานการสแกนบาร์โค้ดด้วยเครื่องสแกนบาร์โค้ดที่เชื่อมต่อจากระยะไกลและการรูดบัตรด้วยเครื่องอ่านบัตร"
" Vantiv"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "เปิดใช้งานการผสานรวมเครื่องชั่งอิเล็กทรอนิกส์"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr "Firefox เท่านั้น: คลิกยืนยันข้อยกเว้นด้านความปลอดภัย"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_iot_device
msgid "IOT Device"
msgstr "อุปกรณ์ไอโอที"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_identifier
msgid "Identifier"
msgstr "ตัวระบุ"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"หากคุณอยู่บนเซิร์ฟเวอร์ที่ปลอดภัย (HTTPS) "
"ให้ตรวจสอบว่าคุณยอมรับการรับรองหรือไม่:"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_display_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_display_id
msgid "Iface Display"
msgstr "หน้าจอแสดงผล Iface"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_printer_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_printer_id
msgid "Iface Printer"
msgstr "เครื่องพิมพ์ Iface "

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scale_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scale_id
msgid "Iface Scale"
msgstr "สเกล Iface"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid "Iface Scanner"
msgstr "เครื่องสแกน Iface"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Ingenico (BENELUX)"
msgstr "Ingenico (BENELUX)"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__ingenico_payment_terminal
msgid "Ingenico Payment Terminal"
msgstr "สถานีชำระเงิน Ingenico"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_iot_box
msgid "IoT Box"
msgstr "กล่อง IoT "

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "IoT Box Homepage"
msgstr "โฮมเพจกล่องไอโอที"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_id
msgid "IoT Device"
msgstr "อุปกรณ์ไอโอที"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "IoT Devices"
msgstr "อุปกรณ์ไอโอที"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iot_device_ids
msgid "Iot Device"
msgstr "อุปกรณ์ไอโอที"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Network Error"
msgstr "ข้อผิดพลาดเครือข่าย"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid ""
"Odoo can automatically apply the right modification for you.\n"
"                Be aware that the modification will change global rounding settings, and can affect more than just this Point of Sale."
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__payment_terminal_ids
msgid "Payment Terminal"
msgstr "สถานีการชำระเงิน"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__payment_terminal_device_ids
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__iot_device_id
msgid "Payment Terminal Device"
msgstr "อุปกรณ์สถานีการชำระเงิน"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Payment terminal error"
msgstr "ข้อผิดพลาดสถานีการชำระเงิน"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Please check if the IoT Box is still connected."
msgstr "โปรดตรวจสอบว่ากล่องไอโอทียังคงเชื่อมต่ออยู่หรือไม่"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Please check if the terminal is still connected."
msgstr "โปรดตรวจสอบว่าสถานียังเชื่อมต่ออยู่หรือไม่"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid ""
"Please check the network connection and then check the status of the last "
"transaction manually."
msgstr ""
"โปรดตรวจสอบการเชื่อมต่อเครือข่ายแล้วตรวจสอบสถานะของธุรกรรมล่าสุดด้วยตนเอง"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/overrides/models/pos_store.js:0
msgid "Please process or cancel the current transaction."
msgstr "โปรดดำเนินการหรือยกเลิกธุรกรรมปัจจุบัน"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_config
msgid "Point of Sale Configuration"
msgstr "กำหนดค่าการขายหน้าร้าน"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "วิธีการชำระเงินการขายหน้าร้าน"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_printer
msgid "Point of Sale Printer"
msgstr "เครื่องพิมพ์ ณ จุดขาย"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_session
msgid "Point of Sale Session"
msgstr "เซสชั่นการขายหน้าร้าน"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "พิมพ์ผ่าน Proxy"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "พร็อกซี่ที่อยู่ IP "

#. module: pos_iot
#. odoo-python
#: code:addons/pos_iot/models/pos_printer.py:0
msgid "Proxy IP cannot be empty."
msgstr "IP พร็อกซีไม่สามารถเว้นว่างได้"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Receipt Printer"
msgstr "เครื่องพิมพ์ใบเสร็จ"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "สแกนผ่าน Proxy"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Scanner"
msgstr "เครื่องสแกน"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Terminal Disconnected"
msgstr "ไม่เชื่อมต่อสถานี"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "ที่อยู่ IP หรือชื่อโฮสต์ของพร็อกซีฮาร์ดแวร์เครื่องพิมพ์"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.js:0
msgid "The following units of measure have insufficient rounding accuracy: %s"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Ingenico. Set your Ingenico device on the "
"related payment method."
msgstr ""
"ธุรกรรมจะถูกประมวลผลโดย Ingenico ตั้งค่าอุปกรณ์ Ingenico "
"ของคุณในวิธีการชำระเงินที่เกี่ยวข้อง"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__worldline_payment_terminal
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Worldline. Set your Worldline device on "
"the related payment method."
msgstr ""
"ธุรกรรมจะถูกประมวลผลโดย Worldline ตั้งค่าอุปกรณ์ Worldline "
"ของคุณตามวิธีการชำระเงินที่เกี่ยวข้อง"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.xml:0
msgid "This Point of Sale is EU Certified for weighing scales."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.xml:0
msgid "This Point of Sale is uncertified for weighing scales."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Transaction could not be cancelled"
msgstr "ไม่สามารถยกเลิกการทำธุรกรรมได้"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/overrides/models/pos_store.js:0
msgid "Transaction in progress"
msgstr "อยู่ระหว่างการดำเนินการธุรกรรม"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Warning"
msgstr "คำเตือน"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Worldline (BENELUX)"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__worldline_payment_terminal
msgid "Worldline Payment Terminal"
msgstr "สถานีชำระเงิน Worldline"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "You must select a payment terminal in your POS config."
msgstr "คุณต้องเลือกสถานนีชำระเงินในการกำหนดค่า POS ของคุณ"

#. module: pos_iot
#. odoo-python
#: code:addons/pos_iot/models/pos_config.py:0
msgid ""
"You must set a display device for an IOT-connected screen. You'll find the "
"field under the 'IoT Box' option."
msgstr ""
"คุณต้องตั้งค่าอุปกรณ์แสดงผลสำหรับหน้าจอที่เชื่อมต่อกับ IoT "
"คุณจะพบช่องภายใต้ตัวเลือก 'IoT Box'"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid ""
"Your configurations are not compliant with the European regulation. In order"
" to use a certified POS with your scale, you should address the following:"
msgstr ""
