# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_iot
# 
# Translators:
# Wil <PERSON>doo, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:45+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with a Worldline payment terminal"
msgstr "Accepter les paiements avec un terminal de paiement Worldline"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with an Ingenico payment terminal"
msgstr "Accepter des paiements avec un terminal de paiement Ingenico"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Access your"
msgstr "Accéder à votre"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.js:0
msgid "An error occurred while attempting to fix certification issues - %s"
msgstr ""
"Une erreur s'est produite lors de la tentative de correction des problèmes "
"de certification - %s"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Apply changes"
msgstr "Appliquer les changements"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Barcode Scanners/Card Readers"
msgstr "Lecteurs de codes-barres/Lecteurs de cartes"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr ""
"Contourner l'impression via le navigateur pour imprimer via le proxy "
"matériel."

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Cashdrawer"
msgstr "Tiroir caisse"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "Cliquez sur Avancé/Afficher les détails/Détails/Plus d'informations"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"Cliquer sur Procéder à .../Ajouter exception/Visiter ce site web/Continuer "
"sur la page web"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Close"
msgstr "Fermer"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Close this window and try again"
msgstr "Fermez cette fenêtre et réessayez"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Configuration of payment terminal failed"
msgstr "Echec de la configuration du terminal de paiement"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Connection to IoT Box failed"
msgstr "Echec de la connexion à l'IoT Box"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Connection to terminal failed"
msgstr "Échec de la connexion au terminal"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Customer Display"
msgstr "Affichage client"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.js:0
msgid "Decimal accuracy is less than 3 decimal places"
msgstr "La précision décimale est inférieure à 3 décimales."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Discard"
msgstr "Ignorer"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Electronic Scale"
msgstr "Balance électronique"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"Permet d'activer la lecture de codes-barres depuis un lecteur connecté à "
"distance et le glissement de cartes avec un lecteur de cartes Vantiv."

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "Permet d'activer l'intégration d'une balance électronique."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr "Firefox uniquement : Cliquez sur Confirmer l'exception de sécurité"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_iot_device
msgid "IOT Device"
msgstr "Périphérique IOT"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_identifier
msgid "Identifier"
msgstr "Identifiant"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"Si vous êtes sur un serveur sécurisé (HTTPS), vérifiez si vous avez accepté "
"le certificat :"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_display_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_display_id
msgid "Iface Display"
msgstr "Affichage Iface"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_printer_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_printer_id
msgid "Iface Printer"
msgstr "Imprimante Iface"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scale_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scale_id
msgid "Iface Scale"
msgstr "Balance Iface"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid "Iface Scanner"
msgstr "Lecteur Iface"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Ingenico (BENELUX)"
msgstr "Ingenico (BENELUX)"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__ingenico_payment_terminal
msgid "Ingenico Payment Terminal"
msgstr "Terminal de paiement Ingenico"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_iot_box
msgid "IoT Box"
msgstr "IoT Box"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "IoT Box Homepage"
msgstr "Page d'accueil de l'IoT Box"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_id
msgid "IoT Device"
msgstr "Périphérique IoT"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "IoT Devices"
msgstr "Appareils IoT"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iot_device_ids
msgid "Iot Device"
msgstr "Appareil IoT"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Network Error"
msgstr "Erreur réseau"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid ""
"Odoo can automatically apply the right modification for you.\n"
"                Be aware that the modification will change global rounding settings, and can affect more than just this Point of Sale."
msgstr ""
"Odoo peut appliquer automatiquement la modification appropriée pour vous.\n"
"                Sachez que cette modification modifiera les paramètres globaux d'arrondi et pourra affecter plus que ce seul point de vente."

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__payment_terminal_ids
msgid "Payment Terminal"
msgstr "Terminal de paiement"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__payment_terminal_device_ids
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__iot_device_id
msgid "Payment Terminal Device"
msgstr "Terminal de paiement"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Payment terminal error"
msgstr "Erreur terminal de paiement"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Please check if the IoT Box is still connected."
msgstr "Veuillez vérifier si l'IoT Box est toujours connectée."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Please check if the terminal is still connected."
msgstr "Veuillez vérifier si le terminal est toujours connecté."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid ""
"Please check the network connection and then check the status of the last "
"transaction manually."
msgstr ""
"Veuillez vérifier la connexion réseau, puis vérifier manuellement le statut "
"de la dernière transaction."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/overrides/models/pos_store.js:0
msgid "Please process or cancel the current transaction."
msgstr "Veuillez traiter ou annuler la transaction en cours."

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuration du point de vente"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Modes de paiement du point de vente"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_printer
msgid "Point of Sale Printer"
msgstr "Imprimante point de vente"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_session
msgid "Point of Sale Session"
msgstr "Session du point de vente"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Imprimer via un proxy"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "Adresse IP du proxy"

#. module: pos_iot
#. odoo-python
#: code:addons/pos_iot/models/pos_printer.py:0
msgid "Proxy IP cannot be empty."
msgstr "L'adresse IP du proxy ne peut pas être vide."

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Receipt Printer"
msgstr "Imprimante de reçu"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Scanner via Proxy"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Scanner"
msgstr "Scanner"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Terminal Disconnected"
msgstr "Terminal déconnecté"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "L'adresse IP ou le nom d'hôte du proxy de l'imprimante"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.js:0
msgid "The following units of measure have insufficient rounding accuracy: %s"
msgstr ""
"Les unités de mesure suivantes présentent une précision d'arrondi "
"insuffisante : %s"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Ingenico. Set your Ingenico device on the "
"related payment method."
msgstr ""
"Les transactions sont traitées par Ingenico. Configurez votre appareil "
"Ingenico sur le mode de paiement correspondant."

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__worldline_payment_terminal
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Worldline. Set your Worldline device on "
"the related payment method."
msgstr ""
"Les transactions sont traitées par Worldline. Configurez votre appareil "
"Worldline sur le mode de paiement associé."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.xml:0
msgid "This Point of Sale is EU Certified for weighing scales."
msgstr "Ce point de vente est certifié par l'UE pour les balances."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.xml:0
msgid "This Point of Sale is uncertified for weighing scales."
msgstr "Ce point de vente n'est pas certifié pour les balances."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Transaction could not be cancelled"
msgstr "La transaction n'a pas pu être annulée"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/overrides/models/pos_store.js:0
msgid "Transaction in progress"
msgstr "Transaction en cours"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Warning"
msgstr "Avertissement"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Worldline (BENELUX)"
msgstr "Worldline (BENELUX)"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__worldline_payment_terminal
msgid "Worldline Payment Terminal"
msgstr "Terminal de paiement Worldline"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "You must select a payment terminal in your POS config."
msgstr ""
"Vous devez sélectionner un terminal de paiement dans la configuration de "
"votre point de vente."

#. module: pos_iot
#. odoo-python
#: code:addons/pos_iot/models/pos_config.py:0
msgid ""
"You must set a display device for an IOT-connected screen. You'll find the "
"field under the 'IoT Box' option."
msgstr ""
"Vous devez définir un dispositif d'affichage pour un écran connecté à l'IoT."
" Vous trouverez ce champ sous l'option « IoT Box »."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid ""
"Your configurations are not compliant with the European regulation. In order"
" to use a certified POS with your scale, you should address the following:"
msgstr ""
"Vos configurations ne sont pas conformes à la réglementation européenne. "
"Pour utiliser un point de vente certifié avec votre balance, vous devez "
"corriger les points suivants :"
