# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_iot
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"Language: is\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with a Worldline payment terminal"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with an Ingenico payment terminal"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
msgid "Access your"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/LastTransactionStatus.xml:0
msgid "Action identifier:"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/LastTransactionStatus.xml:0
msgid "Amount:"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Barcode Scanners/Card Readers"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Cashdrawer"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
msgid "Click on Proceed to .../Add Exception/Visit this website/Go on to the webpage"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
msgid "Close this window and try again"
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/payment.js:0
msgid "Configuration of payment terminal failed"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/iot_longpolling.js:0
#: code:addons/pos_iot/static/src/js/payment.js:0
msgid "Connection to IoT Box failed"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/payment.js:0
msgid "Connection to terminal failed"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/ScaleScreen.js:0
msgid "Could not connect to IoT scale"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Customer Display"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Electronic Scale"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/LastTransactionStatus.js:0
msgid "Electronic payment in progress"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid "Enable barcode scanning with a remotely connected barcode scanner and card swiping with a Vantiv card reader."
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/ScaleScreen.xml:0
msgid "Get Weight"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_identifier
msgid "Identifier"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
msgid "If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_display_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_display_id
msgid "Iface Display"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_printer_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_printer_id
msgid "Iface Printer"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scale_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scale_id
msgid "Iface Scale"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid "Iface Scanner"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Ingenico (BENELUX)"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__ingenico_payment_terminal
msgid "Ingenico Payment Terminal"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
msgid "IoT Box Homepage"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_id
msgid "IoT Device"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "IoT Devices"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iot_device_ids
msgid "Iot Device"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/models.js:0
msgid ""
"It seems that no scale was detected.\n"
"Make sure that the scale is connected and visible in the IoT app."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/LastTransactionStatus.xml:0
msgid "Last Transaction Status"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/models.js:0
msgid "No Scale Detected"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/LastTransactionStatus.xml:0
msgid "Ok"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__payment_terminal_ids
msgid "Payment Terminal"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__payment_terminal_device_ids
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__iot_device_id
msgid "Payment Terminal Device"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/payment.js:0
msgid "Payment terminal error"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/payment.js:0
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
msgid "Please check if the IoT Box is still connected."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/payment.js:0
msgid "Please check if the terminal is still connected."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/payment.js:0
msgid "Please check the network connection and then check the status of the last transaction manually."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/pos_store.js:0
msgid "Please process or cancel the current transaction."
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Uppsetning kassakerfis"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_printer
msgid "Point of Sale Printer"
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_session
msgid "Point of Sale Session"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Receipt Printer"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/payment.js:0
msgid "Terminal Disconnected"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/ScaleScreen.js:0
msgid "The IoT scale is not responding. You should check your connection."
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "The transactions are processed by Ingenico. Set your Ingenico device on the related payment method."
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__worldline_payment_terminal
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "The transactions are processed by Worldline. Set your Worldline device on the related payment method."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/LastTransactionStatus.xml:0
msgid "Time:"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/payment.js:0
msgid "Transaction could not be cancelled"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/pos_store.js:0
msgid "Transaction in progress"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Worldline"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/xml/LastTransactionStatus.xml:0
msgid "Worldline - Last Transaction Status"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__worldline_payment_terminal
msgid "Worldline Payment Terminal"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/LastTransactionStatus.js:0
msgid "You cannot check the status of the last transaction when a payment in in progress."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/js/payment.js:0
msgid "You must select a payment terminal in your POS config."
msgstr ""
