# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_iot
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <pernillek<PERSON><EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:45+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with a Worldline payment terminal"
msgstr "Modtag betalinger med en Worldline betalingsterminal"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with an Ingenico payment terminal"
msgstr "Accepter betalinger med en Ingenico betalingsterminal"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Access your"
msgstr "Tilgå din(e)"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.js:0
msgid "An error occurred while attempting to fix certification issues - %s"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Apply changes"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Barcode Scanners/Card Readers"
msgstr "Stregkode Scannere/Kortlæsere"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "Omgå browser printning og udprintning via hardware proxy'en."

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Cashdrawer"
msgstr "Åbn kasseapparatet"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "Klik på Avanceret/Vis detaljer/Detaljer/Mere information"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"Klik på Fortsæt til.../Tilføj undtagelse/Besøg denne hjemmeside/Gå videre "
"til hjemmesiden"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Close"
msgstr "Luk"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Close this window and try again"
msgstr "Luk dette vindue og prøv igen"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Configuration of payment terminal failed"
msgstr "Konfiguration af betalingsterminalen mislykkedes"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Connection to IoT Box failed"
msgstr "Forbindelse til IoT boks slog fejl"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Connection to terminal failed"
msgstr "Forbindelse til terminal mislykkedes"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Customer Display"
msgstr "Kunde visning"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.js:0
msgid "Decimal accuracy is less than 3 decimal places"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Discard"
msgstr "Kassér"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Electronic Scale"
msgstr "Elektronisk vægt"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"Aktiver stregkode scanning med en fjernforbundet stregkode scanner og kort "
"aflæsning med en Vantiv kortlæser."

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "Aktiverer elektronisk vægt integration."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr "Kun Firefox: Klik på Godkend Sikkerheds Undtagelse"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_iot_device
msgid "IOT Device"
msgstr "IoT Enhed"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_identifier
msgid "Identifier"
msgstr "Identifikator"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"Hvis du er på en sikker server (HTTPS), bør du tjekke at du accepterede "
"certifikatet:"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_display_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_display_id
msgid "Iface Display"
msgstr "Iface Displayet"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_printer_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_printer_id
msgid "Iface Printer"
msgstr "Iface Printer"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scale_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scale_id
msgid "Iface Scale"
msgstr "Iface vægt"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid "Iface Scanner"
msgstr "Iface Scanner"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Ingenico (BENELUX)"
msgstr "Ingenico (BENELUX)"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__ingenico_payment_terminal
msgid "Ingenico Payment Terminal"
msgstr "Ingenico betalingsterminal"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_iot_box
msgid "IoT Box"
msgstr "IoT Boks"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "IoT Box Homepage"
msgstr "IoT Box startside"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_id
msgid "IoT Device"
msgstr "IoT Enhed"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "IoT Devices"
msgstr "IoT enheder"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iot_device_ids
msgid "Iot Device"
msgstr "IoT enhed"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Network Error"
msgstr "Netværksfejl"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid ""
"Odoo can automatically apply the right modification for you.\n"
"                Be aware that the modification will change global rounding settings, and can affect more than just this Point of Sale."
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__payment_terminal_ids
msgid "Payment Terminal"
msgstr "Betalingsterminal"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__payment_terminal_device_ids
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__iot_device_id
msgid "Payment Terminal Device"
msgstr "Betalingsterminal enhed"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Payment terminal error"
msgstr "Betalingsterminal fejl"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Please check if the IoT Box is still connected."
msgstr "Tjek venligst om IoT boksen stadig er forbundet."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Please check if the terminal is still connected."
msgstr "Tjek venligst om terminalen stadig er forbundet."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid ""
"Please check the network connection and then check the status of the last "
"transaction manually."
msgstr ""
"Venligst kontrollér netværksforbindelsen og derefter status på den sidste "
"transaktion manuelt."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/overrides/models/pos_store.js:0
msgid "Please process or cancel the current transaction."
msgstr "Venligst gennemfør eller annullér den nuværende transaktion."

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS konfiguration"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Point of Sale betalingsmetoder"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_printer
msgid "Point of Sale Printer"
msgstr "Point of Sale Printer"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_session
msgid "Point of Sale Session"
msgstr "PoS session"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Print via Proxy"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "Proxy IP Adresse"

#. module: pos_iot
#. odoo-python
#: code:addons/pos_iot/models/pos_printer.py:0
msgid "Proxy IP cannot be empty."
msgstr "Proxy IP kan ikke være tom."

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Receipt Printer"
msgstr "Udskrivning af kvittering"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Scan via Proxy"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Scanner"
msgstr "Skanner"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Terminal Disconnected"
msgstr "Terminal ikke forbundet"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "IP adresse eller værtsnavn for printerens hardware proxy"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.js:0
msgid "The following units of measure have insufficient rounding accuracy: %s"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Ingenico. Set your Ingenico device on the "
"related payment method."
msgstr ""
"Transaktionerne behandles af Ingenico. Angiv din Ingenico enhed på den "
"relaterede betalingsmetode."

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__worldline_payment_terminal
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Worldline. Set your Worldline device on "
"the related payment method."
msgstr ""
"Transaktionerne behandles af Worldline. Indstil din Worldline-enhed på den "
"relaterede betalingsmetode."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.xml:0
msgid "This Point of Sale is EU Certified for weighing scales."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.xml:0
msgid "This Point of Sale is uncertified for weighing scales."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Transaction could not be cancelled"
msgstr "Transaktion kunne ikke annulleres"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/overrides/models/pos_store.js:0
msgid "Transaction in progress"
msgstr "Transaktion i gang"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Warning"
msgstr "Advarsel"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Worldline (BENELUX)"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__worldline_payment_terminal
msgid "Worldline Payment Terminal"
msgstr "Worldline betalingsterminal"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "You must select a payment terminal in your POS config."
msgstr "Du skal vælge en betalingsterminal i din POS konfiguration."

#. module: pos_iot
#. odoo-python
#: code:addons/pos_iot/models/pos_config.py:0
msgid ""
"You must set a display device for an IOT-connected screen. You'll find the "
"field under the 'IoT Box' option."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid ""
"Your configurations are not compliant with the European regulation. In order"
" to use a certified POS with your scale, you should address the following:"
msgstr ""
