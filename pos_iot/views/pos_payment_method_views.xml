<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_payment_method_pos_iot_user_form" model="ir.ui.view">
        <field name="name">pos.payment.method.iot.form.inherit</field>
        <field name="model">pos.payment.method</field>
        <field name="inherit_id" ref="point_of_sale.pos_payment_method_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='use_payment_terminal']" position="after">
                <field name="payment_terminal_ids" invisible="1"/>
                <field name="iot_device_id" domain="[('id', 'in', payment_terminal_ids)]"
                    invisible="use_payment_terminal != 'ingenico'"
                    required="use_payment_terminal == 'ingenico'"/>
                <field name="iot_device_id" domain="[('id', 'in', payment_terminal_ids)]"
                    invisible="use_payment_terminal != 'worldline'"
                    required="use_payment_terminal == 'worldline'"/>
            </xpath>
        </field>
    </record>
</odoo>
