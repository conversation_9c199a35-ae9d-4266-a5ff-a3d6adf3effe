<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="room_office_view_form" model="ir.ui.view">
        <field name="name">room.office.view.form</field>
        <field name="model">room.office</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1>
                            <field name="name" placeholder="Office Name"/>
                        </h1>
                    </div>
                    <group>
                        <group colspan="2">
                            <field name="company_id" domain="[('id', 'in', allowed_company_ids)]"
                                options="{'no_create': True}" groups="base.group_multi_company"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
