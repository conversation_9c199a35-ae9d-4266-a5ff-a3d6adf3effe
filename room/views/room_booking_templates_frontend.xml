<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="room_booking" name="Room Booking" inherit_id="web.layout" primary="True">
        <xpath expr="//html" position="before">
            <t t-set="html_data" t-value="{'lang': lang}"/>
        </xpath>
        <xpath expr="//head/meta[last()]" position="after">
            <meta name="viewport" content="width=device-width, initial-scale=1"/>
            <meta name="apple-mobile-web-app-capable" content="yes" />
            <meta name="mobile-web-app-capable" content="yes" />
        </xpath>
        <xpath expr="//head/link[last()]" position="after">
            <link rel="preload" href="/web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0" as="font" crossorigin=""/>
            <t t-call-assets="room.assets_room_booking" t-js="false"/>
        </xpath>
        <xpath expr="//head/script[@id='web.layout.odooscript']" position="after">
            <script t-nocache="Session information should always be up to date." type="text/javascript">
                odoo.__session_info__ = <t t-out="json.dumps(request.env['ir.http'].get_frontend_session_info())"/>;
                    if (!/(^|;\s)tz=/.test(document.cookie)) {
                    const userTZ = Intl.DateTimeFormat().resolvedOptions().timeZone;
                    document.cookie = `tz=${userTZ}; path=/`;
                }
            </script>
            <t t-set="title" t-value="room.name"/>
            <t t-set="body_classname" t-value="'o_room_booking_frontend'"/>
            <t t-call-assets="web.assets_frontend_minimal" t-css="false" defer_load="True"/>
            <t t-call="web.conditional_assets_tests">
                <t t-set="ignore_missing_deps" t-value="True"/>
            </t>
            <t t-call-assets="web.assets_frontend_lazy" t-css="false" lazy_load="True"/>
            <t t-call-assets="room.assets_room_booking" t-css="false" lazy_load="True"/>
        </xpath>
        <xpath expr="//t[@t-out='0']" position="replace">
            <main>
                <owl-component name="room.room_booking_view" t-att-props="json.dumps({
                    'id': room.id,
                    'description': room.description,
                    'name': room.name,
                    'accessToken': room.access_token,
                    'bookableBgColor': room.bookable_background_color,
                    'bookedBgColor': room.booked_background_color,
                    })"/>
            </main>
        </xpath>
    </template>
</odoo>
