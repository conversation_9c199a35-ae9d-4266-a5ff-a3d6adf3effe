<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">
    <record id="room_office_main_office" model="room.office">
        <field name="name">Main Office</field>
        <field name="company_id" ref="base.main_company"/>
    </record>

    <record id="room_office_head_office" model="room.office">
        <field name="name">Head Office</field>
        <field name="company_id" ref="base.main_company"/>
    </record>
</data>
</odoo>
