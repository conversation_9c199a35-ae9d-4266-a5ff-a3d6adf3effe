# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* room
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# ievaput<PERSON>a <ievai.putnin<PERSON>@gmail.com>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 12:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "1 hour"
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "15 min"
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "30 min"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<i class=\"fa fa-long-arrow-right mx-1\" title=\"Until\"/>"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Until\"/>"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span class=\"o_stat_text\">Next Meeting</span>"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span invisible=\"next_booking_start\">No Upcoming Meetings</span>"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Date: </strong>"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<strong>Organizer: </strong>"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Room: </strong>"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__access_token
msgid "Access Token"
msgstr "Piekļuves atslēga"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction
msgid "Action Needed"
msgstr "Nepieciešama darbība"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__active
msgid "Active"
msgstr "Aktīvs"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Add"
msgstr "Pievienot"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Add a Booking"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__description
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Amenities"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Appearance"
msgstr "Izskats"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Archived"
msgstr "Arhivēts"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to delete this booking?"
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to end the current booking ?"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_attachment_count
#: model:ir.model.fields,field_description:room.field_room_room__message_attachment_count
msgid "Attachment Count"
msgstr "Pielikumu skaits"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Available"
msgstr "Pieejams"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__bookable_background_color
msgid "Available Background Color"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_background_image
msgid "Background Image"
msgstr "Fona attēls"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booked"
msgstr "Rezervēts"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__booked_background_color
msgid "Booked Background Color"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Booking"
msgstr "Rezervācija"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model:ir.model.fields,field_description:room.field_room_booking__name
msgid "Booking Name"
msgstr "Rezervācijas nosaukums"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booking Now"
msgstr ""

#. module: room
#. odoo-python
#: code:addons/room/models/room_room.py:0
#: model:ir.actions.act_window,name:room.room_booking_action
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_ids
#: model:ir.ui.menu,name:room.room_booking_menu
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "Bookings"
msgstr "Rezervācijas"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Busy"
msgstr "Aizņemts"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_3
msgid "Capacity: 6 people"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__company_id
#: model:ir.model.fields,field_description:room.field_room_office__company_id
#: model:ir.model.fields,field_description:room.field_room_room__company_id
msgid "Company"
msgstr "Uzņēmums"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "Create Rooms"
msgstr ""

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Create a Room"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_uid
#: model:ir.model.fields,field_description:room.field_room_office__create_uid
#: model:ir.model.fields,field_description:room.field_room_room__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_date
#: model:ir.model.fields,field_description:room.field_room_office__create_date
#: model:ir.model.fields,field_description:room.field_room_room__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Date"
msgstr "Datums"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Delete"
msgstr "Izdzēst"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Discard"
msgstr "Atmest"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__display_name
#: model:ir.model.fields,field_description:room.field_room_office__display_name
#: model:ir.model.fields,field_description:room.field_room_room__display_name
msgid "Display Name"
msgstr "Nosaukums"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "End Booking"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__stop_datetime
msgid "End Datetime"
msgstr "Beigu datums un laiks"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_follower_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_follower_ids
msgid "Followers"
msgstr "Sekotāji"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_partner_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekotāji (Partneri)"

#. module: room
#: model:ir.model,name:room.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP maršrutēšana"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__has_message
#: model:ir.model.fields,field_description:room.field_room_room__has_message
msgid "Has Message"
msgstr "Ir ziņojums"

#. module: room
#: model:room.office,name:room.room_office_head_office
msgid "Head Office"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__id
#: model:ir.model.fields,field_description:room.field_room_office__id
#: model:ir.model.fields,field_description:room.field_room_room__id
msgid "ID"
msgstr "ID"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction
#: model:ir.model.fields,help:room.field_room_room__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ja atzīmēts, jums jāpievērš uzmanība jauniem ziņojumiem."

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error
#: model:ir.model.fields,help:room.field_room_room__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Ja atzīmēts, daži ziņojumi satur piegādes kļūdu."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Install Application"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_list
msgid "Is Currently Available"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_is_follower
#: model:ir.model.fields,field_description:room.field_room_room__message_is_follower
msgid "Is Follower"
msgstr "Ir sekotājs"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__is_available
msgid "Is Room Currently Available"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_uid
#: model:ir.model.fields,field_description:room.field_room_office__write_uid
#: model:ir.model.fields,field_description:room.field_room_room__write_uid
msgid "Last Updated by"
msgstr "Pēdējo reizi atjaunoja"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_date
#: model:ir.model.fields,field_description:room.field_room_office__write_date
#: model:ir.model.fields,field_description:room.field_room_room__write_date
msgid "Last Updated on"
msgstr "Pēdējās izmaiņas"

#. module: room
#: model:room.office,name:room.room_office_main_office
msgid "Main Office"
msgstr ""

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid ""
"Make meeting room sharing easy by centralizing all meetings in this App"
msgstr ""

#. module: room
#: model:res.groups,name:room.group_room_manager
msgid "Manage Rooms"
msgstr ""

#. module: room
#: model:ir.ui.menu,name:room.room_menu_root
msgid "Meeting Rooms"
msgstr "Sapulču istabas"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Meeting name"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error
msgid "Message Delivery error"
msgstr "Ziņojuma piegādes kļūda"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_ids
msgid "Messages"
msgstr "Ziņojumi"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "My Meetings"
msgstr "Manas tikšanās"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_list
msgid "Name"
msgstr "Nosaukums"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__next_booking_start
msgid "Next Booking Start"
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Next Week"
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings Planned"
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings for Today"
msgstr ""

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid "No Bookings found. Let’s schedule one!"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction_counter
msgid "Number of Actions"
msgstr "Darbību skaits"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error_counter
msgid "Number of errors"
msgstr "Kļūdu skaits"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,help:room.field_room_room__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Ziņojumu skaits, kuriem nepieciešama darbība"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,help:room.field_room_room__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Ziņojumu skaits, kas satur piegādes kļūdu"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__office_id
#: model:ir.model.fields,field_description:room.field_room_room__office_id
msgid "Office"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__name
#: model_terms:ir.ui.view,arch_db:room.room_office_view_form
msgid "Office Name"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Open"
msgstr "Atvērt"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Or"
msgstr "Or"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__organizer_id
msgid "Organizer"
msgstr "Organizators"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Previous Week"
msgstr ""

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_2
msgid "Projector"
msgstr ""

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_1
msgid "Projector, whiteboard"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_properties
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Properties"
msgstr "Īpašības"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Public Booking"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__rating_ids
#: model:ir.model.fields,field_description:room.field_room_room__rating_ids
msgid "Ratings"
msgstr "Reitingi"

#. module: room
#: model:ir.actions.act_window,name:room.room_room_action
#: model:ir.model,name:room.model_room_room
#: model:ir.model.fields,field_description:room.field_room_booking__room_id
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Room"
msgstr ""

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Room %(room_name)s is already booked during the selected time slot."
msgstr ""

#. module: room
#: model:ir.model,name:room.model_room_booking
msgid "Room Booking"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_url
msgid "Room Link"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__name
msgid "Room Name"
msgstr ""

#. module: room
#: model:ir.model,name:room.model_room_office
msgid "Room Office"
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__room_properties_definition
msgid "Room Properties"
msgstr ""

#. module: room
#: model:ir.ui.menu,name:room.room_room_menu
msgid "Rooms"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Select a Room..."
msgstr ""

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__short_code
msgid "Short Code"
msgstr "Īsais kods"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__start_datetime
msgid "Start Datetime"
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Start now"
msgstr ""

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_access_token
msgid "The access token must be unique"
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "The booking you were editing has been updated or deleted."
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "The next booking is starting soon."
msgstr ""

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_short_code
msgid "The short code must be unique."
msgstr ""

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "The start date of %(booking_name)s must be earlier than the end date."
msgstr ""

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "They can then be booked for meetings from a Tablet or the back-end."
msgstr ""

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "This room is as free as a fish in a sea of endless possibilities"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Until"
msgstr "Līdz"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Update Meeting"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Blue Meeting Room"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Headquarters"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "e.g. Team Meeting"
msgstr ""

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. projector, capacity 6 people"
msgstr ""
