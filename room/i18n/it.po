# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* room
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 12:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "1 hour"
msgstr "1 ora"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "15 min"
msgstr "15 min"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "30 min"
msgstr "30 min"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<i class=\"fa fa-long-arrow-right mx-1\" title=\"Until\"/>"
msgstr "<i class=\"fa fa-long-arrow-right mx-1\" title=\"Until\"/>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Until\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Until\"/>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span class=\"o_stat_text\">Next Meeting</span>"
msgstr "<span class=\"o_stat_text\">Prossima riunione</span>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span invisible=\"next_booking_start\">No Upcoming Meetings</span>"
msgstr "<span invisible=\"next_booking_start\">Nessuna riunione imminente</span>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Date: </strong>"
msgstr "<strong>Data: </strong>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<strong>Organizer: </strong>"
msgstr "<strong>Organizzatore:</strong>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Room: </strong>"
msgstr "<strong>Stanza:</strong>"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__access_token
msgid "Access Token"
msgstr "Token di accesso"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__active
msgid "Active"
msgstr "Attivo"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Add"
msgstr "Aggiungi"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Add a Booking"
msgstr "Aggiungi prenotazione"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__description
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Amenities"
msgstr "Servizi"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Appearance"
msgstr "Aspetto"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Archived"
msgstr "In archivio"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to delete this booking?"
msgstr "Sei sicuro di voler eliminare la prenotazione?"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to end the current booking ?"
msgstr "Sei sicuro di voler terminare la prenotazione attuale?"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_attachment_count
#: model:ir.model.fields,field_description:room.field_room_room__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Available"
msgstr "Disponibile"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__bookable_background_color
msgid "Available Background Color"
msgstr "Colore sfondo disponibile"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_background_image
msgid "Background Image"
msgstr "Immagine di sfondo"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booked"
msgstr "Prenotato"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__booked_background_color
msgid "Booked Background Color"
msgstr "Colore sfondo prenotato"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Booking"
msgstr "Prenotazione"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model:ir.model.fields,field_description:room.field_room_booking__name
msgid "Booking Name"
msgstr "Nome prenotazione"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booking Now"
msgstr "Prenota ora"

#. module: room
#. odoo-python
#: code:addons/room/models/room_room.py:0
#: model:ir.actions.act_window,name:room.room_booking_action
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_ids
#: model:ir.ui.menu,name:room.room_booking_menu
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "Bookings"
msgstr "Prenotazioni"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Busy"
msgstr "Occupato"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_3
msgid "Capacity: 6 people"
msgstr "Capacità: 6 persone"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__company_id
#: model:ir.model.fields,field_description:room.field_room_office__company_id
#: model:ir.model.fields,field_description:room.field_room_room__company_id
msgid "Company"
msgstr "Azienda"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "Create Rooms"
msgstr "Crea stanze"

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Create a Room"
msgstr "Crea una stanza"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_uid
#: model:ir.model.fields,field_description:room.field_room_office__create_uid
#: model:ir.model.fields,field_description:room.field_room_room__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_date
#: model:ir.model.fields,field_description:room.field_room_office__create_date
#: model:ir.model.fields,field_description:room.field_room_room__create_date
msgid "Created on"
msgstr "Creato il"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Date"
msgstr "Data"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Delete"
msgstr "Elimina"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Discard"
msgstr "Abbandona"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__display_name
#: model:ir.model.fields,field_description:room.field_room_office__display_name
#: model:ir.model.fields,field_description:room.field_room_room__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "End Booking"
msgstr "Termina prenotazione"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__stop_datetime
msgid "End Datetime"
msgstr "Orario data di fine"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_follower_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_follower_ids
msgid "Followers"
msgstr "Seguaci"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_partner_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguaci (partner)"

#. module: room
#: model:ir.model,name:room.model_ir_http
msgid "HTTP Routing"
msgstr "Routing HTTP"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__has_message
#: model:ir.model.fields,field_description:room.field_room_room__has_message
msgid "Has Message"
msgstr "Ha messaggio"

#. module: room
#: model:room.office,name:room.room_office_head_office
msgid "Head Office"
msgstr "Sede principale"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__id
#: model:ir.model.fields,field_description:room.field_room_office__id
#: model:ir.model.fields,field_description:room.field_room_room__id
msgid "ID"
msgstr "ID"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction
#: model:ir.model.fields,help:room.field_room_room__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error
#: model:ir.model.fields,help:room.field_room_room__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Install Application"
msgstr "Installa applicazione"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_list
msgid "Is Currently Available"
msgstr "È attualmente disponibile"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_is_follower
#: model:ir.model.fields,field_description:room.field_room_room__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__is_available
msgid "Is Room Currently Available"
msgstr "La stanza è attualmente disponibile"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_uid
#: model:ir.model.fields,field_description:room.field_room_office__write_uid
#: model:ir.model.fields,field_description:room.field_room_room__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_date
#: model:ir.model.fields,field_description:room.field_room_office__write_date
#: model:ir.model.fields,field_description:room.field_room_room__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: room
#: model:room.office,name:room.room_office_main_office
msgid "Main Office"
msgstr "Ufficio principale"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid ""
"Make meeting room sharing easy by centralizing all meetings in this App"
msgstr ""
"Rendi semplice la condivisione delle stanze per riunioni "
"centralizzandole nell'app"

#. module: room
#: model:res.groups,name:room.group_room_manager
msgid "Manage Rooms"
msgstr "Gestisci stanze"

#. module: room
#: model:ir.ui.menu,name:room.room_menu_root
msgid "Meeting Rooms"
msgstr "Sale riunioni"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Meeting name"
msgstr "Nome riunione"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error
msgid "Message Delivery error"
msgstr "Errore consegna messaggio"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "My Meetings"
msgstr "I miei appuntamenti"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_list
msgid "Name"
msgstr "Nome"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__next_booking_start
msgid "Next Booking Start"
msgstr "Inizio prossima prenotazione"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Next Week"
msgstr "Prossima settimana"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings Planned"
msgstr "Nessuna prenotazione pianificata"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings for Today"
msgstr "Nessuna prenotazione per oggi"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid "No Bookings found. Let’s schedule one!"
msgstr "Nessuna prenotazione trovata. Programmane uno!"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di azioni"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,help:room.field_room_room__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messagig che richiedono un'azione"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,help:room.field_room_room__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__office_id
#: model:ir.model.fields,field_description:room.field_room_room__office_id
msgid "Office"
msgstr "Ufficio"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__name
#: model_terms:ir.ui.view,arch_db:room.room_office_view_form
msgid "Office Name"
msgstr "Nome ufficio"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Open"
msgstr "Apri"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Or"
msgstr "Oppure"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__organizer_id
msgid "Organizer"
msgstr "Organizzatore"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Previous Week"
msgstr "Settimana precedente"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_2
msgid "Projector"
msgstr "Proiettore"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_1
msgid "Projector, whiteboard"
msgstr "Proiettore, lavagna"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_properties
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Properties"
msgstr "Proprietà"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Public Booking"
msgstr "Prenotazione pubblica"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__rating_ids
#: model:ir.model.fields,field_description:room.field_room_room__rating_ids
msgid "Ratings"
msgstr "Valutazioni"

#. module: room
#: model:ir.actions.act_window,name:room.room_room_action
#: model:ir.model,name:room.model_room_room
#: model:ir.model.fields,field_description:room.field_room_booking__room_id
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Room"
msgstr "Stanza"

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Room %(room_name)s is already booked during the selected time slot."
msgstr ""
"La stanza %(room_name)s è stata già prenotata durante la fascia oraria "
"selezionata."

#. module: room
#: model:ir.model,name:room.model_room_booking
msgid "Room Booking"
msgstr "Prenotazione stanza"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_url
msgid "Room Link"
msgstr "Link stanza"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__name
msgid "Room Name"
msgstr "Nome stanza"

#. module: room
#: model:ir.model,name:room.model_room_office
msgid "Room Office"
msgstr "Ufficio stanza"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__room_properties_definition
msgid "Room Properties"
msgstr "Proprietà stanza"

#. module: room
#: model:ir.ui.menu,name:room.room_room_menu
msgid "Rooms"
msgstr "Stanze"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Select a Room..."
msgstr "Seleziona una stanza..."

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__short_code
msgid "Short Code"
msgstr "Codice breve"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__start_datetime
msgid "Start Datetime"
msgstr "Inizia data e ora"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Start now"
msgstr "Inizia ora"

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_access_token
msgid "The access token must be unique"
msgstr "Il token di accesso deve essere unico"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "The booking you were editing has been updated or deleted."
msgstr "La prenotazione che stavi modificando è stata aggiornata o eliminata."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "The next booking is starting soon."
msgstr "La prossima prenotazione inizia a breve."

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_short_code
msgid "The short code must be unique."
msgstr "Il codice breve deve essere unico."

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "The start date of %(booking_name)s must be earlier than the end date."
msgstr ""
"La data di inizio di %(booking_name)s deve essere precedente alla data di "
"fine."

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "They can then be booked for meetings from a Tablet or the back-end."
msgstr ""
"In seguito, possono essere prenotate per riunioni dal tablet o dal backend."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "This room is as free as a fish in a sea of endless possibilities"
msgstr ""
"Questa stanza è libera come un pesce in un mare di possibilità infinite"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Until"
msgstr "Fino a"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Update Meeting"
msgstr "Aggiorna riunione"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Blue Meeting Room"
msgstr "ad es. Stanza riunioni blu"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Headquarters"
msgstr "ad es. Sede principale"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "e.g. Team Meeting"
msgstr "ad es. Riunione team"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. projector, capacity 6 people"
msgstr "ad es. proiettore, capienza 6 persone"
