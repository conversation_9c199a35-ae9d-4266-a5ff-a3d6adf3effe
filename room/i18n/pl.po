# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* room
# 
# Translators:
# Interactive AV-IT, 2024
# Wil Odoo, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 12:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "1 hour"
msgstr "1 godzina"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "15 min"
msgstr "15 min"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "30 min"
msgstr "30 min"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<i class=\"fa fa-long-arrow-right mx-1\" title=\"Until\"/>"
msgstr "<i class=\"fa fa-long-arrow-right mx-1\" title=\"Do\"/>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Until\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Do\"/>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span class=\"o_stat_text\">Next Meeting</span>"
msgstr "<span class=\"o_stat_text\">Następne spotkanie</span>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span invisible=\"next_booking_start\">No Upcoming Meetings</span>"
msgstr "<span invisible=\"next_booking_start\">Brak nadchodzących spotkań</span>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Date: </strong>"
msgstr "<strong>Data: </strong>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<strong>Organizer: </strong>"
msgstr "<strong>Organizator: </strong>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Room: </strong>"
msgstr "<strong>Pokój: </strong>"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__access_token
msgid "Access Token"
msgstr "Token dostępu"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction
msgid "Action Needed"
msgstr "Wymagane działanie"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__active
msgid "Active"
msgstr "Aktywne"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Add"
msgstr "Dodaj"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Add a Booking"
msgstr "Dodaj rezerwację"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__description
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Amenities"
msgstr "Udogodnienia"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Appearance"
msgstr "Wygląd"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Archived"
msgstr "Zarchiwizowane"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to delete this booking?"
msgstr "Czy na pewno chcesz usunąć tę rezerwację?"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to end the current booking ?"
msgstr "Czy na pewno chcesz zakończyć aktualną rezerwację?"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_attachment_count
#: model:ir.model.fields,field_description:room.field_room_room__message_attachment_count
msgid "Attachment Count"
msgstr "Liczba załączników"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Available"
msgstr "Dostępny"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__bookable_background_color
msgid "Available Background Color"
msgstr "Dostępny kolor tła"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_background_image
msgid "Background Image"
msgstr "Obrazek tła"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booked"
msgstr "Zarezerwowane"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__booked_background_color
msgid "Booked Background Color"
msgstr "Kolor tła - Zarezerwowane"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Booking"
msgstr "Rezerwacja"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model:ir.model.fields,field_description:room.field_room_booking__name
msgid "Booking Name"
msgstr "Nazwa rezerwacji"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booking Now"
msgstr "Rezerwacja w toku"

#. module: room
#. odoo-python
#: code:addons/room/models/room_room.py:0
#: model:ir.actions.act_window,name:room.room_booking_action
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_ids
#: model:ir.ui.menu,name:room.room_booking_menu
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "Bookings"
msgstr "Rezerwacje"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Busy"
msgstr "Zajęty"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_3
msgid "Capacity: 6 people"
msgstr "Pojemność: 6 osób"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__company_id
#: model:ir.model.fields,field_description:room.field_room_office__company_id
#: model:ir.model.fields,field_description:room.field_room_room__company_id
msgid "Company"
msgstr "Firma"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "Create Rooms"
msgstr "Twórz pokoje"

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Create a Room"
msgstr "Stwórz Pokój"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_uid
#: model:ir.model.fields,field_description:room.field_room_office__create_uid
#: model:ir.model.fields,field_description:room.field_room_room__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_date
#: model:ir.model.fields,field_description:room.field_room_office__create_date
#: model:ir.model.fields,field_description:room.field_room_room__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Date"
msgstr "Data"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Delete"
msgstr "Usuń"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Discard"
msgstr "Odrzuć"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__display_name
#: model:ir.model.fields,field_description:room.field_room_office__display_name
#: model:ir.model.fields,field_description:room.field_room_room__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "End Booking"
msgstr "Zakończ rezerwację"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__stop_datetime
msgid "End Datetime"
msgstr "Data i czas końca"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_follower_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_follower_ids
msgid "Followers"
msgstr "Obserwatorzy"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_partner_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_partner_ids
msgid "Followers (Partners)"
msgstr "Obserwatorzy (partnerzy)"

#. module: room
#: model:ir.model,name:room.model_ir_http
msgid "HTTP Routing"
msgstr "Wytyczanie HTTP"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__has_message
#: model:ir.model.fields,field_description:room.field_room_room__has_message
msgid "Has Message"
msgstr "Ma wiadomość"

#. module: room
#: model:room.office,name:room.room_office_head_office
msgid "Head Office"
msgstr "Siedziba główna"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__id
#: model:ir.model.fields,field_description:room.field_room_office__id
#: model:ir.model.fields,field_description:room.field_room_room__id
msgid "ID"
msgstr "ID"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction
#: model:ir.model.fields,help:room.field_room_room__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jeśli zaznaczone, nowe wiadomości wymagają twojej uwagi."

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error
#: model:ir.model.fields,help:room.field_room_room__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Jeśli zaznaczone, niektóre wiadomości napotkały błędy podczas doręczenia."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Install Application"
msgstr "Zainstaluj aplikację "

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_list
msgid "Is Currently Available"
msgstr "Jest aktualnie dostępny"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_is_follower
#: model:ir.model.fields,field_description:room.field_room_room__message_is_follower
msgid "Is Follower"
msgstr "Jest obserwatorem"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__is_available
msgid "Is Room Currently Available"
msgstr "Ten pokój jest aktualnie dostępny"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_uid
#: model:ir.model.fields,field_description:room.field_room_office__write_uid
#: model:ir.model.fields,field_description:room.field_room_room__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_date
#: model:ir.model.fields,field_description:room.field_room_office__write_date
#: model:ir.model.fields,field_description:room.field_room_room__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: room
#: model:room.office,name:room.room_office_main_office
msgid "Main Office"
msgstr "Główne biuro"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid ""
"Make meeting room sharing easy by centralizing all meetings in this App"
msgstr ""
"Spraw by udostępnianie pokoi było łatwiejsze, dzięki centralizacji "
"wszystkich spotkań w tej aplikacji"

#. module: room
#: model:res.groups,name:room.group_room_manager
msgid "Manage Rooms"
msgstr "Zarządzaj pokojami"

#. module: room
#: model:ir.ui.menu,name:room.room_menu_root
msgid "Meeting Rooms"
msgstr "Pokoje Spotkań"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Meeting name"
msgstr "Nazwa spotkania"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error
msgid "Message Delivery error"
msgstr "Błąd doręczenia wiadomości"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_ids
msgid "Messages"
msgstr "Wiadomości"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "My Meetings"
msgstr "Moje spotkania"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_list
msgid "Name"
msgstr "Nazwa"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__next_booking_start
msgid "Next Booking Start"
msgstr "Rozpoczęcie następnej rezerwacji"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Next Week"
msgstr "Następny tydzień"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings Planned"
msgstr "Nie ma zaplanowanych rezerwacji"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings for Today"
msgstr "Nie ma rezerwacji na dziś"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid "No Bookings found. Let’s schedule one!"
msgstr "Nie znaleziono żadnych rezerwacji. Zaplanujmy jakąś!"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction_counter
msgid "Number of Actions"
msgstr "Liczba akcji"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error_counter
msgid "Number of errors"
msgstr "Liczba błędów"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,help:room.field_room_room__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Liczba wiadomości wymagających akcji"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,help:room.field_room_room__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Liczba wiadomości z błędami przy doręczeniu"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__office_id
#: model:ir.model.fields,field_description:room.field_room_room__office_id
msgid "Office"
msgstr "Biuro"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__name
#: model_terms:ir.ui.view,arch_db:room.room_office_view_form
msgid "Office Name"
msgstr "Nazwa biura"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Open"
msgstr "Otwarta"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Or"
msgstr "Lub"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__organizer_id
msgid "Organizer"
msgstr "Organizator"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Previous Week"
msgstr "Tydzień poprzedni"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_2
msgid "Projector"
msgstr "Projektant"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_1
msgid "Projector, whiteboard"
msgstr "Projektor, tablica"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_properties
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Properties"
msgstr "Właściwości"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Public Booking"
msgstr "Publiczna rezerwacja"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__rating_ids
#: model:ir.model.fields,field_description:room.field_room_room__rating_ids
msgid "Ratings"
msgstr "Oceny"

#. module: room
#: model:ir.actions.act_window,name:room.room_room_action
#: model:ir.model,name:room.model_room_room
#: model:ir.model.fields,field_description:room.field_room_booking__room_id
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Room"
msgstr "Pokój"

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Room %(room_name)s is already booked during the selected time slot."
msgstr ""
"Pokój %(room_name)s jest już zarezerwowany w wybranym przedziale czasowym."

#. module: room
#: model:ir.model,name:room.model_room_booking
msgid "Room Booking"
msgstr "Rezerwacja pokoju"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_url
msgid "Room Link"
msgstr "Link pokoju"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__name
msgid "Room Name"
msgstr "Nazwa pokoju"

#. module: room
#: model:ir.model,name:room.model_room_office
msgid "Room Office"
msgstr "Pokój biurowy"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__room_properties_definition
msgid "Room Properties"
msgstr "Właściwości pokoju"

#. module: room
#: model:ir.ui.menu,name:room.room_room_menu
msgid "Rooms"
msgstr "Pokoje"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Select a Room..."
msgstr "Wybierz pokój..."

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__short_code
msgid "Short Code"
msgstr "Krótki kod"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__start_datetime
msgid "Start Datetime"
msgstr "Data i czas rozpoczęcia"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Start now"
msgstr "Zacznij teraz"

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_access_token
msgid "The access token must be unique"
msgstr "Token dostępu musi być unikalny"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "The booking you were editing has been updated or deleted."
msgstr "Rezerwacja, którą edytowałeś została zaktualizowana lub usunięta."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "The next booking is starting soon."
msgstr "Następna rezerwacja zaczyna się za chwilę."

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_short_code
msgid "The short code must be unique."
msgstr "Ten krótki kod musi być unikalny."

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "The start date of %(booking_name)s must be earlier than the end date."
msgstr ""
"Data rozpoczęcia %(booking_name)s musi być wcześniejsza niż data "
"zakończenia."

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "They can then be booked for meetings from a Tablet or the back-end."
msgstr ""
"Mogą one być następnie rezerwowane na spotkania z tabletu lub z backendu"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "This room is as free as a fish in a sea of endless possibilities"
msgstr "Ten pokój jest wolny jak ptak!"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Until"
msgstr "Do"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Update Meeting"
msgstr "Zaktualizuj spotkanie"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Blue Meeting Room"
msgstr "np. Niebieski pokój spotkań"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Headquarters"
msgstr "np. Siedziba główna"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "e.g. Team Meeting"
msgstr "np. Spotkanie zespołu"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. projector, capacity 6 people"
msgstr "np. projektor, miejsce dla 6 osób"
