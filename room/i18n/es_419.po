# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* room
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 12:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "1 hour"
msgstr "1 hora"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "15 min"
msgstr "15 minutos"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "30 min"
msgstr "30 minutos"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<i class=\"fa fa-long-arrow-right mx-1\" title=\"Until\"/>"
msgstr "<i class=\"fa fa-long-arrow-right mx-1\" title=\"Hasta\"/>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Until\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Hasta\"/>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span class=\"o_stat_text\">Next Meeting</span>"
msgstr "<span class=\"o_stat_text\">Siguiente reunión</span>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span invisible=\"next_booking_start\">No Upcoming Meetings</span>"
msgstr "<span invisible=\"next_booking_start\">No hay más reuniones</span>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Date: </strong>"
msgstr "<strong>Fecha: </strong>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<strong>Organizer: </strong>"
msgstr "<strong>Organizador: </strong>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Room: </strong>"
msgstr "<strong>Sala: </strong>"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction
msgid "Action Needed"
msgstr "Se requiere una acción"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__active
msgid "Active"
msgstr "Activo"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Add"
msgstr "Agregar"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Add a Booking"
msgstr "Agregar una reservación"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__description
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Amenities"
msgstr "Servicios"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Appearance"
msgstr "Apariencia"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Archived"
msgstr "Archivado"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to delete this booking?"
msgstr "¿Está seguro de que desea eliminar esta reservación?"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to end the current booking ?"
msgstr "¿Está seguro de que desea finalizar la reservación actual?"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_attachment_count
#: model:ir.model.fields,field_description:room.field_room_room__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Available"
msgstr "Disponible"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__bookable_background_color
msgid "Available Background Color"
msgstr "Color de fondo cuando está disponible"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_background_image
msgid "Background Image"
msgstr "Imagen de fondo"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booked"
msgstr "Reservada"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__booked_background_color
msgid "Booked Background Color"
msgstr "Color de fondo cuando está reservada"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Booking"
msgstr "Reservación "

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model:ir.model.fields,field_description:room.field_room_booking__name
msgid "Booking Name"
msgstr "Nombre de la reservación"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booking Now"
msgstr "Reservar ahora"

#. module: room
#. odoo-python
#: code:addons/room/models/room_room.py:0
#: model:ir.actions.act_window,name:room.room_booking_action
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_ids
#: model:ir.ui.menu,name:room.room_booking_menu
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "Bookings"
msgstr "Reservaciones"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Busy"
msgstr "Ocupada"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_3
msgid "Capacity: 6 people"
msgstr "Capacidad: seis personas"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__company_id
#: model:ir.model.fields,field_description:room.field_room_office__company_id
#: model:ir.model.fields,field_description:room.field_room_room__company_id
msgid "Company"
msgstr "Empresa"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "Create Rooms"
msgstr "Crear salas"

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Create a Room"
msgstr "Crear sala"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_uid
#: model:ir.model.fields,field_description:room.field_room_office__create_uid
#: model:ir.model.fields,field_description:room.field_room_room__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_date
#: model:ir.model.fields,field_description:room.field_room_office__create_date
#: model:ir.model.fields,field_description:room.field_room_room__create_date
msgid "Created on"
msgstr "Creado el"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Date"
msgstr "Fecha"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Delete"
msgstr "Eliminar"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Discard"
msgstr "Descartar"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__display_name
#: model:ir.model.fields,field_description:room.field_room_office__display_name
#: model:ir.model.fields,field_description:room.field_room_room__display_name
msgid "Display Name"
msgstr "Mostrar nombre"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "End Booking"
msgstr "Finalizar reservación"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__stop_datetime
msgid "End Datetime"
msgstr "Fecha y hora de finalización"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_follower_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_partner_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (contactos)"

#. module: room
#: model:ir.model,name:room.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__has_message
#: model:ir.model.fields,field_description:room.field_room_room__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: room
#: model:room.office,name:room.room_office_head_office
msgid "Head Office"
msgstr "Sede"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__id
#: model:ir.model.fields,field_description:room.field_room_office__id
#: model:ir.model.fields,field_description:room.field_room_room__id
msgid "ID"
msgstr "ID"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction
#: model:ir.model.fields,help:room.field_room_room__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error
#: model:ir.model.fields,help:room.field_room_room__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Install Application"
msgstr "Instalar aplicación"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_list
msgid "Is Currently Available"
msgstr "¿Se encuentra disponible por el momento?"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_is_follower
#: model:ir.model.fields,field_description:room.field_room_room__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__is_available
msgid "Is Room Currently Available"
msgstr "¿La sala se encuentra disponible por el momento?"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_uid
#: model:ir.model.fields,field_description:room.field_room_office__write_uid
#: model:ir.model.fields,field_description:room.field_room_room__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_date
#: model:ir.model.fields,field_description:room.field_room_office__write_date
#: model:ir.model.fields,field_description:room.field_room_room__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: room
#: model:room.office,name:room.room_office_main_office
msgid "Main Office"
msgstr "Oficina principal"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid ""
"Make meeting room sharing easy by centralizing all meetings in this App"
msgstr ""
"Haga que compartir una sala de juntas sea fácil y centralice todas las "
"reuniones en esta aplicación"

#. module: room
#: model:res.groups,name:room.group_room_manager
msgid "Manage Rooms"
msgstr "Gestionar salas"

#. module: room
#: model:ir.ui.menu,name:room.room_menu_root
msgid "Meeting Rooms"
msgstr "Salas de reunión"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Meeting name"
msgstr "Nombre de la reunión"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error
msgid "Message Delivery error"
msgstr "Error al enviar el mensaje"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "My Meetings"
msgstr "Mis reuniones"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_list
msgid "Name"
msgstr "Nombre"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__next_booking_start
msgid "Next Booking Start"
msgstr "Inicio de la siguiente reservación"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Next Week"
msgstr "Próxima semana"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings Planned"
msgstr "Sin reservaciones planificadas"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings for Today"
msgstr "Sin reservaciones para el día de hoy"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid "No Bookings found. Let’s schedule one!"
msgstr "No se encontraron reservaciones. Programemos una."

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,help:room.field_room_room__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,help:room.field_room_room__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__office_id
#: model:ir.model.fields,field_description:room.field_room_room__office_id
msgid "Office"
msgstr "Encargado"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__name
#: model_terms:ir.ui.view,arch_db:room.room_office_view_form
msgid "Office Name"
msgstr "Nombre de la oficina"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Open"
msgstr "Abrir"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Or"
msgstr "O"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__organizer_id
msgid "Organizer"
msgstr "Organizador"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Previous Week"
msgstr "Semana anterior"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_2
msgid "Projector"
msgstr "Proyector"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_1
msgid "Projector, whiteboard"
msgstr "Proyector, pizarrón"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_properties
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Properties"
msgstr "Propiedades"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Public Booking"
msgstr "Reservación pública"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__rating_ids
#: model:ir.model.fields,field_description:room.field_room_room__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: room
#: model:ir.actions.act_window,name:room.room_room_action
#: model:ir.model,name:room.model_room_room
#: model:ir.model.fields,field_description:room.field_room_booking__room_id
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Room"
msgstr "Sala"

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Room %(room_name)s is already booked during the selected time slot."
msgstr ""
"La sala %(room_name)s ya está reservada durante el horario seleccionado."

#. module: room
#: model:ir.model,name:room.model_room_booking
msgid "Room Booking"
msgstr "Reservación de sala"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_url
msgid "Room Link"
msgstr "Enlace de la sala"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__name
msgid "Room Name"
msgstr "Nombre de la sala"

#. module: room
#: model:ir.model,name:room.model_room_office
msgid "Room Office"
msgstr "Oficina de la sala"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__room_properties_definition
msgid "Room Properties"
msgstr "Propiedades de la sala"

#. module: room
#: model:ir.ui.menu,name:room.room_room_menu
msgid "Rooms"
msgstr "Salas"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Select a Room..."
msgstr "Seleccione una sala..."

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__short_code
msgid "Short Code"
msgstr "Código corto"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__start_datetime
msgid "Start Datetime"
msgstr "Fecha y hora de inicio"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Start now"
msgstr "Empezar ahora"

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_access_token
msgid "The access token must be unique"
msgstr "El token de acceso debe ser único"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "The booking you were editing has been updated or deleted."
msgstr "La reservación que estaba editando ha sido actualizada o eliminada."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "The next booking is starting soon."
msgstr "La siguiente reservación inicia pronto."

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_short_code
msgid "The short code must be unique."
msgstr "El código corto debe ser único."

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "The start date of %(booking_name)s must be earlier than the end date."
msgstr ""
"La fecha de inicio de %(booking_name)s debe ser antes de la fecha final."

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "They can then be booked for meetings from a Tablet or the back-end."
msgstr ""
"Luego se pueden reservar para reuniones desde una tableta o el backend."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "This room is as free as a fish in a sea of endless possibilities"
msgstr "Esta sala está disponible"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Until"
msgstr "Hasta"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Update Meeting"
msgstr "Actualizar reunión"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Blue Meeting Room"
msgstr "Por ejemplo, Sala de juntas azul"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Headquarters"
msgstr "Por ejemplo, Oficinas principales"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "e.g. Team Meeting"
msgstr "Por ejemplo, Reunión de equipo"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. projector, capacity 6 people"
msgstr "Por ejemplo, proyector, con capacidad para seis personas"
