# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* room
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 12:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "1 hour"
msgstr "1 heure"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "15 min"
msgstr "15 min"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "30 min"
msgstr "30 min"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<i class=\"fa fa-long-arrow-right mx-1\" title=\"Until\"/>"
msgstr "<i class=\"fa fa-long-arrow-right mx-1\" title=\"Jusqu'au\"/>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Until\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Jusqu'au\"/>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span class=\"o_stat_text\">Next Meeting</span>"
msgstr "<span class=\"o_stat_text\">Réunion suivante</span>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "<span invisible=\"next_booking_start\">No Upcoming Meetings</span>"
msgstr "<span invisible=\"next_booking_start\">Pas de réunion planifiée</span>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Date: </strong>"
msgstr "<strong>Date : </strong>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "<strong>Organizer: </strong>"
msgstr "<strong>Organisateur :</strong>"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_kanban
msgid "<strong>Room: </strong>"
msgstr "<strong>Salle :</strong>"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__access_token
msgid "Access Token"
msgstr "Jeton d'accès"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__active
msgid "Active"
msgstr "Actif"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Add"
msgstr "Ajouter"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Add a Booking"
msgstr "Ajouter une réservation"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__description
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Amenities"
msgstr "Commodités"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Appearance"
msgstr "Apparence"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Archived"
msgstr "Archivé"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to delete this booking?"
msgstr "Êtes-vous sûr de vouloir supprimer cette réservation ?"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Are you sure you want to end the current booking ?"
msgstr "Êtes-vous sûr de vouloir interrompre la réservation en cours ?"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_attachment_count
#: model:ir.model.fields,field_description:room.field_room_room__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Available"
msgstr "Disponible"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__bookable_background_color
msgid "Available Background Color"
msgstr "Couleur d'arrière-plan disponible"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_background_image
msgid "Background Image"
msgstr "Image de fond"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booked"
msgstr "Réservé"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__booked_background_color
msgid "Booked Background Color"
msgstr "Couleur d'arrière-plan de réservation"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Booking"
msgstr "Réservation"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
#: model:ir.model.fields,field_description:room.field_room_booking__name
msgid "Booking Name"
msgstr "Nom de la réservation"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Booking Now"
msgstr "Réserver maintenant"

#. module: room
#. odoo-python
#: code:addons/room/models/room_room.py:0
#: model:ir.actions.act_window,name:room.room_booking_action
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_ids
#: model:ir.ui.menu,name:room.room_booking_menu
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_gantt
msgid "Bookings"
msgstr "Réservations"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Busy"
msgstr "Occupé"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_3
msgid "Capacity: 6 people"
msgstr "Capacité : 6 personnes"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__company_id
#: model:ir.model.fields,field_description:room.field_room_office__company_id
#: model:ir.model.fields,field_description:room.field_room_room__company_id
msgid "Company"
msgstr "Société"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "Create Rooms"
msgstr "Créer les salles"

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Create a Room"
msgstr "Créer une salle"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_uid
#: model:ir.model.fields,field_description:room.field_room_office__create_uid
#: model:ir.model.fields,field_description:room.field_room_room__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__create_date
#: model:ir.model.fields,field_description:room.field_room_office__create_date
#: model:ir.model.fields,field_description:room.field_room_room__create_date
msgid "Created on"
msgstr "Créé le"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Date"
msgstr "Date"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Delete"
msgstr "Supprimer"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Discard"
msgstr "Ignorer"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__display_name
#: model:ir.model.fields,field_description:room.field_room_office__display_name
#: model:ir.model.fields,field_description:room.field_room_room__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "End Booking"
msgstr "Finaliser la réservation"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__stop_datetime
msgid "End Datetime"
msgstr "Date de fin"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_follower_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_partner_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: room
#: model:ir.model,name:room.model_ir_http
msgid "HTTP Routing"
msgstr "Routage HTTP"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__has_message
#: model:ir.model.fields,field_description:room.field_room_room__has_message
msgid "Has Message"
msgstr "A un message"

#. module: room
#: model:room.office,name:room.room_office_head_office
msgid "Head Office"
msgstr "Siège social"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__id
#: model:ir.model.fields,field_description:room.field_room_office__id
#: model:ir.model.fields,field_description:room.field_room_room__id
msgid "ID"
msgstr "ID"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction
#: model:ir.model.fields,help:room.field_room_room__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error
#: model:ir.model.fields,help:room.field_room_room__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Install Application"
msgstr "Installer l'application"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_list
msgid "Is Currently Available"
msgstr "Est actuellement disponible"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_is_follower
#: model:ir.model.fields,field_description:room.field_room_room__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__is_available
msgid "Is Room Currently Available"
msgstr "La salle est actuellement disponible"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_uid
#: model:ir.model.fields,field_description:room.field_room_office__write_uid
#: model:ir.model.fields,field_description:room.field_room_room__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__write_date
#: model:ir.model.fields,field_description:room.field_room_office__write_date
#: model:ir.model.fields,field_description:room.field_room_room__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: room
#: model:room.office,name:room.room_office_main_office
msgid "Main Office"
msgstr "Bureau principal"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid ""
"Make meeting room sharing easy by centralizing all meetings in this App"
msgstr ""
"Facilitez le partage des salles de réunion en centralisant toutes les "
"réunions dans cette application."

#. module: room
#: model:res.groups,name:room.group_room_manager
msgid "Manage Rooms"
msgstr "Gérer les salles"

#. module: room
#: model:ir.ui.menu,name:room.room_menu_root
msgid "Meeting Rooms"
msgstr "Salles de réunion"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "Meeting name"
msgstr "Nom de la réunion"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_ids
#: model:ir.model.fields,field_description:room.field_room_room__message_ids
msgid "Messages"
msgstr "Messages"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_search
msgid "My Meetings"
msgstr "Mes réunions"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_list
msgid "Name"
msgstr "Nom"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__next_booking_start
msgid "Next Booking Start"
msgstr "Début de la prochaine réservation"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Next Week"
msgstr "Semaine prochaine"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings Planned"
msgstr "Pas de réunion planifiée"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "No Bookings for Today"
msgstr "Pas de réunion pour aujourd'hui"

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_booking_action
msgid "No Bookings found. Let’s schedule one!"
msgstr "Aucune réservation trouvée. Programmons-en une !"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,field_description:room.field_room_room__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_needaction_counter
#: model:ir.model.fields,help:room.field_room_room__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: room
#: model:ir.model.fields,help:room.field_room_booking__message_has_error_counter
#: model:ir.model.fields,help:room.field_room_room__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__office_id
#: model:ir.model.fields,field_description:room.field_room_room__office_id
msgid "Office"
msgstr "Bureau"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__name
#: model_terms:ir.ui.view,arch_db:room.room_office_view_form
msgid "Office Name"
msgstr "Nom du bureau"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "Open"
msgstr "Ouvert"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Or"
msgstr "Ou"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__organizer_id
msgid "Organizer"
msgstr "Organisateur"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Previous Week"
msgstr "Semaine précédente"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_2
msgid "Projector"
msgstr "Projecteur"

#. module: room
#: model_terms:room.room,description:room.room_room_main_office_room_1
msgid "Projector, whiteboard"
msgstr "Projecteur, tableau"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_properties
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Properties"
msgstr "Propriétés"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "Public Booking"
msgstr "Réservation publique"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__rating_ids
#: model:ir.model.fields,field_description:room.field_room_room__rating_ids
msgid "Ratings"
msgstr "Évaluations"

#. module: room
#: model:ir.actions.act_window,name:room.room_room_action
#: model:ir.model,name:room.model_room_room
#: model:ir.model.fields,field_description:room.field_room_booking__room_id
#: model_terms:ir.ui.view,arch_db:room.room_room_view_search
msgid "Room"
msgstr "Salle"

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "Room %(room_name)s is already booked during the selected time slot."
msgstr ""
"La salle %(room_name)s est déjà réservée pendant la période sélectionnée."

#. module: room
#: model:ir.model,name:room.model_room_booking
msgid "Room Booking"
msgstr "Réservation de salle"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__room_booking_url
msgid "Room Link"
msgstr "Lien de la salle"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__name
msgid "Room Name"
msgstr "Nom de la salle"

#. module: room
#: model:ir.model,name:room.model_room_office
msgid "Room Office"
msgstr "Bureau de la salle"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_office__room_properties_definition
msgid "Room Properties"
msgstr "Propriétés de la salle"

#. module: room
#: model:ir.ui.menu,name:room.room_room_menu
msgid "Rooms"
msgstr "Salles"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "Select a Room..."
msgstr "Sélectionner une salle.."

#. module: room
#: model:ir.model.fields,field_description:room.field_room_room__short_code
msgid "Short Code"
msgstr "Code"

#. module: room
#: model:ir.model.fields,field_description:room.field_room_booking__start_datetime
msgid "Start Datetime"
msgstr "Date de début"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "Start now"
msgstr "Démarrer maintenant"

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_access_token
msgid "The access token must be unique"
msgstr "Le jeton d'accès doit être unique"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.js:0
msgid "The booking you were editing has been updated or deleted."
msgstr ""
"La réservation que vous étiez en train de modifier a été mise à jour ou "
"supprimée."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "The next booking is starting soon."
msgstr "La prochaine réservation commence bientôt."

#. module: room
#: model:ir.model.constraint,message:room.constraint_room_room_uniq_short_code
msgid "The short code must be unique."
msgstr "Le code doit être unique."

#. module: room
#. odoo-python
#: code:addons/room/models/room_booking.py:0
msgid "The start date of %(booking_name)s must be earlier than the end date."
msgstr ""
"La date de début de %(booking_name)s doit être antérieure à la date de fin."

#. module: room
#: model_terms:ir.actions.act_window,help:room.room_room_action
msgid "They can then be booked for meetings from a Tablet or the back-end."
msgstr ""
"Ils peuvent alors être réservés pour des réunions à partir d'une tablette ou"
" du back-end."

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_view/room_booking_view.xml:0
msgid "This room is as free as a fish in a sea of endless possibilities"
msgstr ""
"Cette salle est aussi libre qu'un poisson dans une mer aux possibilités "
"infinies."

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_kanban
msgid "Until"
msgstr "Jusqu'au"

#. module: room
#. odoo-javascript
#: code:addons/room/static/src/room_booking/room_booking_form/room_booking_form.xml:0
msgid "Update Meeting"
msgstr "Mise à jour de la réunion"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Blue Meeting Room"
msgstr "par ex. Blue Meeting Room"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. Headquarters"
msgstr "par ex. quartiers généraux"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_booking_view_form
msgid "e.g. Team Meeting"
msgstr "par ex. Réunion de groupe"

#. module: room
#: model_terms:ir.ui.view,arch_db:room.room_room_view_form
msgid "e.g. projector, capacity 6 people"
msgstr "par ex. projecteur, capacité 6 personnes"
