# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_usps
#
# Translators:
# 425fe09b3064b9f906f637fff94056ae_a00ea56 <0fa3588fa89906bfcb3a354600956e0e_308047>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__abandon
msgid "Abandon"
msgstr "Abandonar"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_account_validated
msgid "Account Validated"
msgstr "Conta Validada"

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier__usps_account_validated
msgid "Check this box if your account is validated by USPS"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "Company phone number is invalid. Please insert a US phone number."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_content_type
msgid "Content Type"
msgstr "Tipo de Conteúdo"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_delivery_nature
msgid "Delivery Nature"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__documents
msgid "Documents"
msgstr "Documentos"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_delivery_nature__domestic
msgid "Domestic"
msgstr "Domestic"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
msgid ""
"Error:\n"
"%s"
msgstr ""
"Erro:\n"
"%s"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__express
msgid "Express"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__first_class
msgid "First Class"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__flat
msgid "Flat"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__flatrate
msgid "Flat Rate"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__flat_rate_box
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__flatratebox
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__flatratebox
msgid "Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__flatrateenv
msgid "Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__gift
msgid "Gift"
msgstr "Oferta"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_delivery_nature__international
msgid "International"
msgstr "Internacional"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "Label Format"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_size_container__large
msgid "Large"
msgstr "Grande"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__largeenvelope
msgid "Large Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__lg_flat_rate_box
msgid "Large Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__legal_flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__legalflatrateenv
msgid "Legal Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__letter
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__letter
msgid "Letter"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_machinable
msgid "Machinable"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__md_flat_rate_box
msgid "Medium Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__merchandise
msgid "Merchandise"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_intl_non_delivery_option
msgid "Non delivery option"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__nonrectangular
msgid "Non-rectangular"
msgstr ""

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "Options"
msgstr "Opções"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_label_file_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__package
msgid "Package"
msgstr "Pacote"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_girth
msgid "Package Girth"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_height
msgid "Package Height"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_length
msgid "Package Length"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__package_service
msgid "Package Service"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_width
msgid "Package Width"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__padded_flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__paddedflatrateenv
msgid "Padded Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__parcel
msgid "Parcel"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier__usps_machinable
msgid "Please check on USPS website to ensure that your package is machinable."
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "Please choose another service (maximum weight of this service is 4 pounds)"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "Please enter a valid ZIP code in recipient address"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "Please enter a valid ZIP code in your Company address"
msgstr "Por favor, insira um código postal válido na morada da sua empresa."

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "Please set country U.S.A in your company address, Service is only available for U.S.A"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__postcard
msgid "Postcard"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__priority
msgid "Priority"
msgstr "Prioridade"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Provedor"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "Quantity for each move line should be less than 1000."
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "Recipient address cannot be found. Please check the address exists."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__rectangular
msgid "Rectangular"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__redirect
msgid "Redirect"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_redirect_partner_id
msgid "Redirect Partner"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_size_container__regular
msgid "Regular"
msgstr "Normal"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__variable
msgid "Regular < 12 inch"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__return
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__return
msgid "Return"
msgstr "Devolução"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__sample
msgid "Sample"
msgstr "Amostra"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
msgid "Shipment #%s has been cancelled"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
msgid "Shipment created into USPS"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
msgid "Shipment created into USPS <br/> <b>Tracking Number: </b>%s"
msgstr ""

#. module: delivery_usps
#: model:ir.model,name:delivery_usps.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Métodos de Envio"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__sm_flat_rate_box
msgid "Small Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__sm_flat_rate_envelope
msgid "Small Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_label_file_type__tif
msgid "TIF"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid ""
"The address of your company is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid ""
"The recipient address is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "The selected USPS service (%s) cannot be used to deliver this package."
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
msgid "Tracking Number:"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_international_regular_container
msgid "Type of USPS International regular container"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_domestic_regular_container
msgid "Type of USPS domestic regular container"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_container
msgid "Type of container"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__delivery_type__usps
msgid "USPS"
msgstr "USPS"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "USPS Configuration"
msgstr ""

#. module: delivery_usps
#: model:delivery.carrier,name:delivery_usps.delivery_carrier_usps_domestic
#: model:product.template,name:delivery_usps.product_product_delivery_usps_domestic_product_template
msgid "USPS Domestic Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "USPS Domestic is used only to ship inside of the U.S.A. Please change the delivery method into USPS International."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_first_class_mail_type
msgid "USPS First Class Mail Type"
msgstr ""

#. module: delivery_usps
#: model:delivery.carrier,name:delivery_usps.delivery_carrier_usps_international
#: model:product.template,name:delivery_usps.product_product_delivery_usps_international_product_template
msgid "USPS International Flat Rate Box"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "USPS International is used only to ship outside of the U.S.A. Please change the delivery method into USPS Domestic."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_label_file_type
msgid "USPS Label File Type"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_mail_type
msgid "USPS Mail Type"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_service
msgid "USPS Service"
msgstr ""

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_stock
msgid "USPS Shipping Methods"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_username
msgid "USPS User ID"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_size_container
msgid "Usps Size Container"
msgstr ""

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
msgid "Your company or recipient ZIP code is incorrect."
msgstr ""

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "inch"
msgstr ""
