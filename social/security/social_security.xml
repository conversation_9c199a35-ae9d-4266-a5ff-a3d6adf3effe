<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <record id="group_social_user" model="res.groups">
        <field name="name">Social User</field>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="category_id" ref="base.module_category_marketing_social_marketing"/>
    </record>

    <record id="group_social_manager" model="res.groups">
        <field name="name">Social Manager</field>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        <field name="category_id" ref="base.module_category_marketing_social_marketing"/>
        <field name="implied_ids" eval="[(4, ref('group_social_user'))]"/>
    </record>

    <data noupdate="1">

    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4, ref('social.group_social_manager'))]"/>
    </record>

    <record id="social_post_rule_social_manager" model="ir.rule">
        <field name="name">Social: manager: all</field>
        <field name="model_id" ref="social.model_social_post"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_social_manager'))]"/>
        <field name="perm_unlink" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
    </record>

    <record id="social_post_rule_social_user_read_write" model="ir.rule">
        <field name="name">Social: user: read/create/write all posts</field>
        <field name="model_id" ref="social.model_social_post"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_social_user'))]"/>
        <field name="perm_unlink" eval="0"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
    </record>

    <record id="social_post_rule_social_user_cwu" model="ir.rule">
        <field name="name">Social: user: unlink own posts only</field>
        <field name="model_id" ref="social.model_social_post"/>
        <field name="domain_force">[('create_uid', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_social_user'))]"/>
        <field name="perm_unlink" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_read" eval="0"/>
        <field name="perm_create" eval="0"/>
    </record>

    <record id="social_live_post_rule_social_manager" model="ir.rule">
        <field name="name">Social: manager: all</field>
        <field name="model_id" ref="social.model_social_live_post"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_social_manager'))]"/>
        <field name="perm_unlink" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
    </record>

    <record id="social_live_post_rule_social_user_rcwu" model="ir.rule">
        <field name="name">Social: user: read/create/write/unlink own live posts only</field>
        <field name="model_id" ref="social.model_social_live_post"/>
        <field name="domain_force">[('create_uid', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_social_user'))]"/>
        <field name="perm_unlink" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
    </record>

    <record id="social_stream_rule_social_manager" model="ir.rule">
        <field name="name">Social: manager: all</field>
        <field name="model_id" ref="social.model_social_stream"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_social_manager'))]"/>
        <field name="perm_unlink" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
    </record>

    <record id="social_stream_rule_social_user_rcwu" model="ir.rule">
        <field name="name">Social: user: create/write/unlink own streams only</field>
        <field name="model_id" ref="social.model_social_stream"/>
        <field name="domain_force">[('create_uid', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_social_user'))]"/>
        <field name="perm_unlink" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_read" eval="0"/>
        <field name="perm_create" eval="1"/>
    </record>


    <!-- Multi-company rules -->
    <record id="social_account_rule_company" model="ir.rule">
        <field name="name">Social Account - Multi-Company</field>
        <field name="model_id" ref="model_social_account"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="social_post_rule_company" model="ir.rule">
        <field name="name">Social Post - Multi-Company</field>
        <field name="model_id" ref="model_social_post"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="social_live_post_rule_company" model="ir.rule">
        <field name="name">Social Live Post - Multi-Company</field>
        <field name="model_id" ref="model_social_live_post"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="social_stream_rule_company" model="ir.rule">
        <field name="name">Social Stream - Multi-Company</field>
        <field name="model_id" ref="model_social_stream"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="social_stream_post_rule_company" model="ir.rule">
        <field name="name">Social Stream Post - Multi-Company</field>
        <field name="model_id" ref="model_social_stream_post"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    </data>
</odoo>
