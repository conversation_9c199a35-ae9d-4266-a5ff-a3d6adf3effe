# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__accounts_count
msgid "# Accounts"
msgstr "# Cuentas"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "%(media_name)s (max %(max_chars)s chars)"
msgstr "%(media_name)s (máximo %(max_chars)s caracteres)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ", you must first link a social media."
msgstr ", primero debe vincular una red social."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<b>An error occurred while trying to link your account</b>"
msgstr "<b>Ocurrió un error al intentar vincular su cuenta</b>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr "<i class=\"fa fa-calendar me-1 small fw-bold\" title=\"Calendario\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar-check-o me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr "<i class=\"fa fa-calendar-check-o me-1 small fw-bold\" title=\"Calendario\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "<i class=\"fa fa-globe ms-1\" title=\"See the post\"/>"
msgstr "<i class=\"fa fa-globe ms-1\" title=\"Ver publicación\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<i class=\"oi oi-arrow-left\"/> Go back to Odoo"
msgstr "<i class=\"oi oi-arrow-left\"/> Volver a Odoo"

#. module: social
#: model_terms:web_tour.tour,rainbow_man_message:social.social_tour
msgid ""
"<strong>Congrats! Come back in a few minutes to check your "
"statistics.</strong>"
msgstr ""
"<strong>¡Felicidades! Regrese en unos minutos para revisar sus "
"estadísticas.</strong>"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction
msgid "Action Needed"
msgstr "Se requiere una acción"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__active
msgid "Active"
msgstr "Activo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de la actividad de excepción"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono del tipo de actividad"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add"
msgstr "Agregar"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Add Post"
msgstr "Agregar publicación"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add Stream"
msgstr "Agregar un flujo"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/add_stream_modal.js:0
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Add a Stream"
msgstr "Agregar un flujo"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "Add a stream"
msgstr "Agregar un flujo"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add an image"
msgstr "Agregar una imagen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "All Companies"
msgstr "Todas las empresas"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Todas las publicaciones relacionadas en las redes sociales"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__account_allowed_ids
msgid "Allowed Accounts"
msgstr "Cuentas permitidas"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Archived"
msgstr "Archivado"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_active_accounts
#: model:ir.model.fields,field_description:social.field_social_post_template__has_active_accounts
msgid "Are Accounts Available?"
msgstr "¿Las cuentas están disponibles?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__image_ids
msgid "Attach Images"
msgstr "Adjuntar imágenes"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__image_ids
msgid "Attached Images"
msgstr "Imágenes adjuntas"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_attachment_count
#: model:ir.model.fields,field_description:social.field_social_post__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__audience
msgid "Audience"
msgstr "Audiencia"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__audience_trend
msgid "Audience Trend"
msgstr "Tendencia de la audiencia"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_link
msgid "Author Link"
msgstr "Enlace del autor"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_name
msgid "Author Name"
msgstr "Nombre del autor"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_link
msgid "Author link to the external social.media (ex: link to the X Account)."
msgstr ""
"Enlace del autor a una red social externa (por ejemplo, enlace a una cuenta "
"de X)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Before posting, links will be converted to be trackable."
msgstr "Los enlaces se harán rasteables antes de publicar."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "By Stream"
msgstr "Por flujo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__csrf_token
msgid "CSRF Token"
msgstr "Token CSRF"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__calendar_date
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Calendar Date"
msgstr "Fecha del calendario"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form_quick_create_social
msgid "Campaign"
msgstr "Campaña"

#. module: social
#: model:ir.actions.act_window,name:social.action_view_utm_campaigns
#: model:ir.ui.menu,name:social.menu_social_campaign
msgid "Campaigns"
msgstr "Campañas"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Las campañas se usan para centralizar sus esfuerzos de marketing y seguir "
"sus resultados."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__can_link_accounts
msgid "Can link accounts?"
msgstr "¿Puede vincular cuentas?"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Choose which <b>account</b> you would like to link first."
msgstr "Elija qué <b>cuenta</b> quiere vincular primero."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Click to refresh."
msgstr "De clic para volver a cargar."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Clicks"
msgstr "Clics"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Clicks:"
msgstr "Clics:"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Comment Image"
msgstr "Comentar en la imagen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__company_id
#: model:ir.model.fields,field_description:social.field_social_live_post__company_id
#: model:ir.model.fields,field_description:social.field_social_post__company_id
#: model:ir.model.fields,field_description:social.field_social_stream__company_id
#: model:ir.model.fields,field_description:social.field_social_stream_post__company_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Company"
msgstr "Empresa"

#. module: social
#: model:ir.model,name:social.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Connecting Problem"
msgstr "Problema de conexión"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__social_account_handle
msgid ""
"Contains the social media handle of the person that created this account. "
"E.g: '@odoo.official' for the 'Odoo' X account"
msgstr ""
"Contiene el nombre de usuario en redes sociales de la persona que creó esta "
"cuenta. Por ejemplo \"@odoo.official\" para la cuenta de X de Odoo."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__message
msgid ""
"Content of the social post message that is post-processed (links are "
"shortened, UTMs, ...)"
msgstr ""
"Contenido de la publicación que ya se procesó (los enlaces se acortaron, "
"UTM, ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__has_streams
msgid "Controls if social streams are handled on this social media."
msgstr ""
"Controla si los flujos de redes sociales se deben gestionar en esta red "
"social."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__can_link_accounts
msgid "Controls if we can link accounts or not."
msgstr "Controla si podemos vincular cuentas o no."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid "Create a Campaign"
msgstr "Crear una campaña"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid "Create a Post"
msgstr "Crear una publicación"

#. module: social
#. odoo-python
#: code:addons/social/models/social_account.py:0
msgid ""
"Create other accounts for %(media_names)s for this company or ask "
"%(company_names)s to share their accounts"
msgstr ""
"Crear otras cuentas para %(media_names)s para esta empresa o solicítele a "
"%(company_names)s que comparta sus cuentas"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_media__create_uid
#: model:ir.model.fields,field_description:social.field_social_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_date
#: model:ir.model.fields,field_description:social.field_social_live_post__create_date
#: model:ir.model.fields,field_description:social.field_social_media__create_date
#: model:ir.model.fields,field_description:social.field_social_post__create_date
#: model:ir.model.fields,field_description:social.field_social_post_template__create_date
#: model:ir.model.fields,field_description:social.field_social_stream__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_account_stats
msgid ""
"Defines whether this account has Audience/Engagements/Stories stats.\n"
"        Account with stats are displayed on the dashboard."
msgstr ""
"Define si esta cuenta tiene estadísticas de audiencia/interacciones/historias.\n"
"Las cuentas se muestran en el tablero."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_trends
msgid "Defines whether this account has statistics tends or not."
msgstr "Define si la cuenta tiene estadísticas de las tendencias o no."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Delete"
msgstr "Eliminar"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Delete Comment"
msgstr "Eliminar comentario"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Delete Post"
msgstr "Eliminar publicación"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Demo Mode"
msgstr "Modo de demostración"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__media_description
msgid "Description"
msgstr "Descripción"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Developer Accounts"
msgstr "Cuentas de desarrollador"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Discard"
msgstr "Descartar"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__display_message
#: model:ir.model.fields,field_description:social.field_social_post_template__display_message
msgid "Display Message"
msgstr "Mostrar mensaje"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__display_name
#: model:ir.model.fields,field_description:social.field_social_live_post__display_name
#: model:ir.model.fields,field_description:social.field_social_media__display_name
#: model:ir.model.fields,field_description:social.field_social_post__display_name
#: model:ir.model.fields,field_description:social.field_social_post_template__display_name
#: model:ir.model.fields,field_description:social.field_social_stream__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_type__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Do you really want to delete this %s?"
msgstr "¿Está seguro de que desea eliminar %s?"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Do you really want to delete this Post ?"
msgstr "¿Está seguro de que desea eliminar esta publicación?"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__draft
msgid "Draft"
msgstr "Borrador"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Due to length restrictions, the following posts cannot be posted:\n"
" %s"
msgstr ""
"Debido a restricciones de caracteres, no es posible realizar las siguientes publicaciones:\n"
"%s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit"
msgstr "Editar"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit Your Post"
msgstr "Editar su publicación"

#. module: social
#: model:ir.model.fields,field_description:social.field_res_config_settings__module_social_demo
msgid "Enable Demo Mode"
msgstr "Activar el modo de demostración"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Enable this option and load demo data to test the social module. This must "
"never be used on a production database!"
msgstr ""
"Active esta opción y cargue los datos de demostración para probar el módulo "
"de marketing social, estos nunca se deben usar en una base de datos de "
"producción."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__engagement
#: model:ir.model.fields,field_description:social.field_social_live_post__engagement
#: model:ir.model.fields,field_description:social.field_social_post__engagement
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Engagement"
msgstr "Interacciones"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__engagement_trend
msgid "Engagement Trend"
msgstr "Tendencia de interacciones"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Engagement:"
msgstr "Interacciones:"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__utm_medium_id
msgid ""
"Every time an account is created, a utm.medium is also created and linked to"
" the account"
msgstr ""
"Siempre que se crea una cuenta, también se crea un utm.medium que está "
"vinculado a la cuenta"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__failed
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Failed"
msgstr "Error"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__failure_reason
msgid "Failure Reason"
msgstr "Motivo del error"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream_post
#: model:ir.ui.menu,name:social.menu_social_stream_post
msgid "Feed"
msgstr "Feed"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Feed Posts"
msgstr "Publicaciones en el feed"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__stream_posts_count
msgid "Feed Posts Count"
msgstr "Número de publicaciones en el feed"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_follower_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_partner_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (contactos)"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome, por ejemplo, fa-tasks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__formatted_published_date
msgid "Formatted Published Date"
msgstr "Formato de fecha de publicación"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience
msgid ""
"General audience of the Social Account (Page Likes, Account Follows, ...)."
msgstr ""
"Audiencia general de la cuenta social (me gusta en páginas, seguidores de la"
" cuenta...)"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "Go to the"
msgstr "Vaya al"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__social_account_handle
msgid "Handle / Short Name"
msgstr "Nombre de usuario"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Happy with the result? Let's post it!"
msgstr "¿Le gusta el resultado? ¡Publiquémoslo!"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_account_stats
msgid "Has Account Stats"
msgstr "Tiene estadísticas de la cuenta"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_message
#: model:ir.model.fields,field_description:social.field_social_post__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_trends
msgid "Has Trends?"
msgstr "¿Tiene tendencias?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_hatched
msgid "Hatched"
msgstr "Creado"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__id
#: model:ir.model.fields,field_description:social.field_social_live_post__id
#: model:ir.model.fields,field_description:social.field_social_media__id
#: model:ir.model.fields,field_description:social.field_social_post__id
#: model:ir.model.fields,field_description:social.field_social_post_template__id
#: model:ir.model.fields,field_description:social.field_social_stream__id
#: model:ir.model.fields,field_description:social.field_social_stream_post__id
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__id
#: model:ir.model.fields,field_description:social.field_social_stream_type__id
msgid "ID"
msgstr "ID"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono que indica una actividad de excepción."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction
#: model:ir.model.fields,help:social.field_social_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error
#: model:ir.model.fields,help:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,help:social.field_social_post__message_has_error
#: model:ir.model.fields,help:social.field_social_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__image
#: model:ir.model.fields,field_description:social.field_social_media__image
msgid "Image"
msgstr "Imagen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__image_url
msgid "Image URL"
msgstr "URL de la imagen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Image Url"
msgstr "URL de la imagen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_urls
#: model:ir.model.fields,field_description:social.field_social_post_template__image_urls
msgid "Images URLs"
msgstr "URL de las imágenes"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__stream_post_image_ids
msgid "Images that were shared with this post."
msgstr "Imágenes que se compartieron con esta publicación."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Insights"
msgstr "Información"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__is_author
msgid "Is Author"
msgstr "Es autor"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_is_follower
#: model:ir.model.fields,field_description:social.field_social_post__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"It appears there is an issue with the Social Media link, click here to link "
"the account again"
msgstr ""
"Parece que hay un problema con el enlace de red social, haga clic aquí para "
"volver a vincular la cuenta"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "It will appear in the Feed once it has posts to display."
msgstr "Aparecerá en el feed cuando tenga publicaciones que mostrar."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_media__write_uid
#: model:ir.model.fields,field_description:social.field_social_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_date
#: model:ir.model.fields,field_description:social.field_social_live_post__write_date
#: model:ir.model.fields,field_description:social.field_social_media__write_date
#: model:ir.model.fields,field_description:social.field_social_post__write_date
#: model:ir.model.fields,field_description:social.field_social_post_template__write_date
#: model:ir.model.fields,field_description:social.field_social_stream__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's <b>connect</b> to Facebook, LinkedIn or X."
msgstr "<b>Conectémonos</b> a Facebook, LinkedIn o X."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's create your own <b>social media</b> dashboard."
msgstr "Creemos su propio tablero de <b>red social</b>."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's start posting."
msgstr "Comencemos a publicar."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Like"
msgstr "Me gusta"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Likes"
msgstr "Me gusta"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_description
msgid "Link Description"
msgstr "Descripción del enlace"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Link Image"
msgstr "Enlace de la imagen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_image_url
msgid "Link Image URL"
msgstr "URL del enlace de la imagen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_title
msgid "Link Title"
msgstr "Título del enlace"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_url
msgid "Link URL"
msgstr "URL del enlace"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Link a new account"
msgstr "Vincule una cuenta nueva"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
msgid "Link account"
msgstr "Vincular cuenta"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "Link an Account"
msgstr "Vincular una cuenta"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__company_id
#: model:ir.model.fields,help:social.field_social_live_post__company_id
#: model:ir.model.fields,help:social.field_social_stream__company_id
#: model:ir.model.fields,help:social.field_social_stream_post__company_id
msgid ""
"Link an account to a company to restrict its usage or keep empty to let all "
"companies use it."
msgstr ""
"Vincule una cuenta a una empresa para restringir su uso o manténgalo vacío "
"para permitir que todas las empresas lo usen."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__live_post_link
msgid "Link of the live post on the target media."
msgstr "Enlace de la publicación en vivo en el medio objetivo."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Link social accounts"
msgstr "Vincular redes sociales"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stats_link
msgid "Link to the external Social Account statistics"
msgstr "Vincular a las estadísticas externas de red social"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__is_media_disconnected
msgid "Link with external Social Media is broken"
msgstr "El enlace con la red social externa está roto"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_allowed_ids
msgid "List of the accounts which can be selected for this post."
msgstr "Lista de cuentas que se pueden seleccionar para esta publicación."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_posts_by_media
msgid "Live Posts by Social Media"
msgstr "Publicaciones en vivo por red social"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Load more comments..."
msgstr "Cargar más comentarios..."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__max_post_length
msgid "Max Post Length"
msgstr "Longitud máxima de la publicación"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__media_type
#: model:ir.model.fields,field_description:social.field_social_post__media_ids
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Media"
msgstr "Medios"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__media_count
#: model:ir.model.fields,field_description:social.field_social_post_template__media_count
msgid "Media Count"
msgstr "Conteo de medios"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__media_type
#: model:ir.model.fields,field_description:social.field_social_media__media_type
msgid "Media Type"
msgstr "Tipo de medios"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__message
#: model:ir.model.fields,field_description:social.field_social_post__message
#: model:ir.model.fields,field_description:social.field_social_post_template__message
#: model:ir.model.fields,field_description:social.field_social_stream_post__message
msgid "Message"
msgstr "Mensaje"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error
msgid "Message Delivery error"
msgstr "Error al enviar el mensaje"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Message posted"
msgstr "Mensaje publicado"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Message posted partially. These are the ones that couldn't be posted:%s"
msgstr "Mensaje publicado de forma parcial. Estos no se pudieron publicar: %s"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__state
msgid ""
"Most social.live.posts directly go from Ready to Posted/Failed since they result of a single call to the third party API.\n"
"        A 'Posting' state is also available for those that are sent through batching (like push notifications)."
msgstr ""
"La mayoría de las publicaciones en vivo de redes sociales van directo a Publicado/Error ya que son el resultado de una sola llamada a la API externa.\n"
"También está disponible un estado de \"publicando\" para todas las que se envían por lotes (como las notificaciones push)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "My Posts"
msgstr "Mis publicaciones "

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "My Streams"
msgstr "Mis flujos"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__name
#: model:ir.model.fields,field_description:social.field_social_media__name
#: model:ir.model.fields,field_description:social.field_social_post__name
#: model:ir.model.fields,field_description:social.field_social_stream_type__name
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Name"
msgstr "Nombre"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New Post"
msgstr "Nueva publicación"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New content available"
msgstr "Nuevo contenido disponible"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Next"
msgstr "Siguiente"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Siguiente fecha límite de la actividad"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "No Social Account yet!"
msgstr "Todavía no registra ninguna cuenta de red social"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "No Social Streams yet!"
msgstr "Todavía no hay flujos sociales"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "No Stream added yet!"
msgstr "No hay ningún flujo todavía"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "No comments yet."
msgstr "Todavía no hay comentarios."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "No social accounts configured, please contact your administrator."
msgstr "No hay cuentas sociales configuradas, contacte a un administrador."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Number of Followers of your channel"
msgstr "Número de seguidores de su canal"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__click_count
msgid "Number of clicks"
msgstr "Número de clics"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Número de interacciones (me gusta, compartir, comentarios...) en las "
"publicaciones en redes sociales"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,help:social.field_social_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,help:social.field_social_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__engagement
#: model:ir.model.fields,help:social.field_social_post__engagement
msgid "Number of people engagements with the post (Likes, comments...)"
msgstr ""
"Número de veces que la gente interactuó con las publicaciones (me gusta, "
"comentarios...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement
msgid "Number of people engagements with your posts (Likes, Comments, ...)."
msgstr ""
"Número de veces que la gente interactuó con sus publicaciones (me gusta, "
"comentarios...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories
msgid "Number of stories created from your posts (Shares, Reposts, ...)."
msgstr ""
"Número de historias que se crearon de sus publicaciones (compartido, "
"reposteos...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people have engaged with your posts (likes, comments, "
"shares,...)"
msgstr ""
"Número de veces que la gente ha interactuado con sus publicaciones, (me "
"gusta, comentarios, compartidos...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people who have engaged with your channel have created "
"stories on their friends' or followers' feed (Shares, Reposts...)"
msgstr ""
"Número de veces que la gente que interactuó con su canal ha creado historias"
" en el feed de sus amigos o seguidores (compartidos, reposteos...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Or add"
msgstr "O agregue"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience_trend
msgid "Percentage of increase/decrease of the audience over a defined period."
msgstr ""
"Porcentaje de audiencia que aumentó o disminuyó a lo largo de un periodo de "
"tiempo definido. "

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement_trend
msgid ""
"Percentage of increase/decrease of the engagement over a defined period."
msgstr ""
"Porcentaje de interacción que aumentó o disminuyó a lo largo de un periodo "
"de tiempo definido. "

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories_trend
msgid "Percentage of increase/decrease of the stories over a defined period."
msgstr ""
"Porcentaje de historias que aumentó o disminuyó a lo largo de un periodo de "
"tiempo definido. "

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Please specify at least one account to post into (for post ID(s) %s)."
msgstr ""
"Especifique al menos una cuenta en la que publicar (para los ID de las "
"publicaciones %s)."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Please specify either a message or upload some images."
msgstr "Especifique ya sea un mensaje o suba algunas imágenes."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post"
msgstr "Publicar"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Post Image"
msgstr "Publicar imagen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.js:0
#: code:addons/social/static/src/js/post_kanban_view.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/js/stream_post_kanban_renderer.js:0
msgid "Post Images"
msgstr "Publicar imágenes"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__live_post_link
#: model:ir.model.fields,field_description:social.field_social_stream_post__post_link
msgid "Post Link"
msgstr "Publicar enlace"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "Post Message"
msgstr "Publicar mensaje"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post Now"
msgstr "Publicar ahora"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__post_link
msgid ""
"Post link to the external social.media (ex: link to the actual Facebook "
"Post)."
msgstr ""
"Publicar enlace a una red social externa (p. ej. enlace a la publicación de "
"Facebook como tal)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Post on"
msgstr "Publicar en"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posted
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posted
msgid "Posted"
msgstr "Publicado"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posting
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posting
msgid "Posting"
msgstr "Publicando"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_post_ids
#: model:ir.ui.menu,name:social.menu_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_kanban
msgid "Posts"
msgstr "Publicaciones"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_post_ids
msgid "Posts By Account"
msgstr "Publicaciones por cuenta"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Posts Preview"
msgstr "Previsualización de publicaciones"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to edit. Press Shift+Enter to insert a Line Break."
msgstr ""
"Presione enter para editar. Presione shift+enter para insertar un salto de "
"línea."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to post. Press Shift+Enter to insert a Line Break."
msgstr ""
"Presione enter para publicar. Presione shift+enter para insertar un salto de"
" línea."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Preview your post"
msgstr "Previsualice su publicación"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Previous"
msgstr "Anterior"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__post_method
msgid "Publish your post immediately or schedule it at a later time."
msgstr "Haga su publicación al momento o prográmela para después."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Published By"
msgstr "Publicado por"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__published_date
msgid "Published Date"
msgstr "Fecha de publicación"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__published_date
msgid "Published date"
msgstr "Fecha de publicación"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__rating_ids
#: model:ir.model.fields,field_description:social.field_social_post__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__ready
msgid "Ready"
msgstr "Listo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__media_type
msgid "Related Social Media"
msgstr "Red social relacionada"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_id
msgid "Related Social Media (Facebook, X, ...)."
msgstr "Red social relacionada (Facebook, X, ...)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__account_id
msgid "Related social Account"
msgstr "Cuenta de red social relacionada"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Reply"
msgstr "Responder"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Retry"
msgstr "Volver a intentar"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error en el envío del SMS"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Schedule"
msgstr "Programar"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__scheduled
msgid "Schedule later"
msgstr "Programar después"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__scheduled
msgid "Scheduled"
msgstr "Planeado"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__scheduled_date
msgid "Scheduled Date"
msgstr "Fecha programada"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Selected accounts (%(account_list)s) do not match the selected company "
"(%(company)s)"
msgstr ""
"Las cuentas seleccionadas (%(account_list)s) no coinciden con la empresa "
"seleccionada (%(company)s)"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__now
msgid "Send now"
msgstr "Enviar ahora"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream__sequence
msgid "Sequence used to order streams (mainly for the 'Feed' kanban view)"
msgstr ""
"Secuencia que se usa para ordenar flujos (principalmente para la vista "
"kanban del feed)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__max_post_length
msgid ""
"Set a maximum number of characters can be posted in post. 0 for no limit."
msgstr ""
"Establezca un número de caracteres máximos para publicar. Si no tiene "
"límite, ponga 0."

#. module: social
#: model:ir.actions.act_window,name:social.action_social_global_settings
#: model:ir.ui.menu,name:social.menu_social_global_settings
msgid "Settings"
msgstr "Ajustes"

#. module: social
#: model:ir.model,name:social.model_social_account
#: model:ir.model.fields,field_description:social.field_social_live_post__account_id
#: model:ir.model.fields,field_description:social.field_social_stream__account_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
msgid "Social Account"
msgstr "Cuenta de red social"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_account
#: model:ir.model.fields,field_description:social.field_social_media__account_ids
#: model:ir.model.fields,field_description:social.field_social_post__account_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__account_ids
#: model:ir.ui.menu,name:social.menu_social_account
#: model_terms:ir.ui.view,arch_db:social.social_account_view_list
msgid "Social Accounts"
msgstr "Cuentas de red social"

#. module: social
#: model:ir.model,name:social.model_social_live_post
msgid "Social Live Post"
msgstr "Publicación en redes sociales en vivo"

#. module: social
#: model:res.groups,name:social.group_social_manager
msgid "Social Manager"
msgstr "Gerente de redes sociales"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_global
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Social Marketing"
msgstr "Marketing social"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.actions.act_window,name:social.action_social_media
#: model:ir.model,name:social.model_social_media
#: model:ir.model.fields,field_description:social.field_social_account__media_id
#: model:ir.model.fields,field_description:social.field_social_stream__media_id
#: model:ir.model.fields,field_description:social.field_social_stream_type__media_id
#: model:ir.ui.menu,name:social.menu_social_media
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Social Media"
msgstr "Redes sociales"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Publicaciones de redes sociales"

#. module: social
#: model:ir.model,name:social.model_social_post
#: model:ir.model.fields,field_description:social.field_social_live_post__post_id
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
msgid "Social Post"
msgstr "Publicación en redes sociales"

#. module: social
#: model:ir.model,name:social.model_social_post_template
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Social Post Template"
msgstr "Plantilla de publicación en redes sociales"

#. module: social
#: model:ir.actions.act_window,name:social.social_post_template_action
msgid "Social Post Templates"
msgstr "Plantillas de publicaciones para redes sociales"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_calendar
#: model_terms:ir.ui.view,arch_db:social.social_post_view_pivot
msgid "Social Posts"
msgstr "Publicaciones en redes sociales"

#. module: social
#: model:ir.model,name:social.model_social_stream
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_id
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_form
msgid "Social Stream"
msgstr "Flujo social"

#. module: social
#: model:ir.model,name:social.model_social_stream_post
#: model:ir.model,name:social.model_social_stream_type
msgid "Social Stream Post"
msgstr "Publicación en el flujo social"

#. module: social
#: model:ir.model,name:social.model_social_stream_post_image
msgid "Social Stream Post Image Attachment"
msgstr "Imagen adjunta a la publicación del flujo social"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream
#: model:ir.ui.menu,name:social.menu_social_stream
msgid "Social Streams"
msgstr "Flujos sociales"

#. module: social
#: model:res.groups,name:social.group_social_user
msgid "Social User"
msgstr "Usuario social"

#. module: social
#: model:ir.actions.server,name:social.ir_cron_post_scheduled_ir_actions_server
msgid "Social: Publish Scheduled Posts"
msgstr "Social: hacer las publicaciones programadas"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments_reply.js:0
msgid "Something went wrong while posting the comment."
msgstr "Ocurrió un error al publicar el comentario."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_dashboard.js:0
msgid ""
"Sorry, you're not allowed to re-link this account, please contact your "
"administrator."
msgstr ""
"Lo sentimos, no tiene permitido volver a vincular esta cuenta, contacte a su"
" administrador."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__source_id
msgid "Source"
msgstr "Origen"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_posts_by_media
msgid ""
"Special technical field that holds a dict containing the live posts names by"
" media ids (used for kanban view)."
msgstr ""
"Campo especial técnico que contiene un diccionario con los títulos de las "
"publicaciones en vivo en los ID de medios (se usa para la vista kanban)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_post_split_per_media.xml:0
msgid "Split Per Media"
msgstr "Dividir por medios"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_split_per_media
#: model:ir.model.fields,field_description:social.field_social_post_template__is_split_per_media
msgid "Split Per Network"
msgstr "Dividir por red social"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stats_link
msgid "Stats Link"
msgstr "Estadísticas del enlace"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__state
#: model:ir.model.fields,field_description:social.field_social_post__state
msgid "Status"
msgstr "Estado"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado según las actividades\n"
"Vencida: ya pasó la fecha límite\n"
"Hoy: hoy es la fecha de la actividad\n"
"Planeada: futuras actividades."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__stories
msgid "Stories"
msgstr "Historias"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stories_trend
msgid "Stories Trend"
msgstr "Tendencias de historias"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Stream Added (%s)"
msgstr "Se agregó el flujo (%s)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__stream_post_id
msgid "Stream Post"
msgstr "Publicación del flujo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_ids
msgid "Stream Post Images"
msgstr "Imágenes de la publicación del flujo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_urls
msgid "Stream Post Images URLs"
msgstr "URL de las imágenes de la publicación del flujo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__stream_type_ids
msgid "Stream Types"
msgstr "Tipos de flujo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_type
#: model:ir.model.fields,field_description:social.field_social_stream_type__stream_type
msgid "Stream type name (technical)"
msgstr "Nombre del tipo de flujo (técnico)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_streams
msgid "Streams Enabled"
msgstr "Permitir flujos"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_post_ids
msgid "Sub-posts that will be published on each selected social accounts."
msgstr ""
"Publicaciones subordinadas que se harán en cada una de las cuentas de redes "
"sociales seleccionadas."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Sync"
msgstr "Sincronizar"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_ids
#: model:ir.model.fields,help:social.field_social_post_template__account_ids
msgid "The accounts on which this post will be published."
msgstr "Las cuentas en las que se hará la publicación."

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_name
msgid ""
"The post author name based on third party information (ex: 'John Doe')."
msgstr ""
"El nombre del autor de la publicación según la información externa (por "
"ejemplo, \"Juan Pérez\")"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__state
msgid ""
"The post is considered as 'Posted' when all its sub-posts (one per social "
"account) are either 'Failed' or 'Posted'"
msgstr ""
"Se cuenta como \"publicado\" cuando todas las publicaciones subordinadas (en"
" cada red social) están en 'Error' o 'Publicado'"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__published_date
msgid "The post published date based on third party information."
msgstr "La fecha de publicación de acuerdo con información externa."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__failure_reason
msgid ""
"The reason why a post is not successfully posted on the Social Media (eg: "
"connection error, duplicated post, ...)."
msgstr ""
"Motivo por el cual no se pudo publicar algo en las redes sociales (por "
"ejemplo, error de conectividad, publicación duplicada, ...)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__media_image
msgid "The related Social Media's image"
msgstr "La imagen de la red social relacionada"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__media_ids
msgid "The social medias linked to the selected social accounts."
msgstr "Las redes sociales vinculadas a las cuentas seleccionadas."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_post_errors
msgid "There are post errors on sub-posts"
msgstr "Hay errores de publicación en publicaciones subordinadas"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__csrf_token
msgid ""
"This token can be used to verify that an incoming request from a social "
"provider has not been forged."
msgstr ""
"Este token se usa para verificar que una solicitud entrante de un proveedor "
"social no se falsificó."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__name
msgid "Title"
msgstr "Título"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "To add a stream"
msgstr "Para agregar un flujo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_id
msgid "Type"
msgstr "Tipo"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de la actividad de excepción registrada."

#. module: social
#: model:ir.model,name:social.model_utm_campaign
msgid "UTM Campaign"
msgstr "Campaña UTM "

#. module: social
#: model:ir.model,name:social.model_utm_medium
#: model:ir.model.fields,field_description:social.field_social_account__utm_medium_id
msgid "UTM Medium"
msgstr "Medio UTM"

#. module: social
#: model:ir.model,name:social.model_utm_source
msgid "UTM Source"
msgstr "Fuente UTM"

#. module: social
#. odoo-python
#: code:addons/social/controllers/main.py:0
msgid "Uh-oh! It looks like this message has been deleted from X."
msgstr "Parece que este mensaje fue eliminado de X."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Unknown error"
msgstr "Error desconocido"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Uploaded file does not seem to be a valid image."
msgstr "El archivo que subió no es una imagen válida."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Use your own Developer Accounts on our Social app. Those credentials are "
"provided in the developer section of your professional social media account."
msgstr ""
"Use sus propias cuentas de desarrollador en nuestra aplicación Marketing "
"social. Las credenciales las podrá obtener en la sección de desarrollador en"
" su cuenta de red social."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_type
#: model:ir.model.fields,help:social.field_social_live_post__media_type
#: model:ir.model.fields,help:social.field_social_media__media_type
#: model:ir.model.fields,help:social.field_social_stream_post__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""
"Se utiliza para hacer comparaciones cuando necesitamos restringir algunas "
"funciones para una red social específica (\"Facebook\", \"X\", entre otras)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "View"
msgstr "Vista"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__website_message_ids
#: model:ir.model.fields,field_description:social.field_social_post__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__website_message_ids
#: model:ir.model.fields,help:social.field_social_post__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__post_method
msgid "When"
msgstr "Cuando"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__published_date
msgid ""
"When the global post was published. The actual sub-posts published dates may"
" be different depending on the media."
msgstr ""
"Cuándo se hizo la publicación global. Las fechas de publicación de las "
"publicaciones subordinadas pueden ser distintas dependiendo de la red "
"social."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__image_ids
#: model:ir.model.fields,help:social.field_social_post_template__image_ids
msgid "Will attach images to your posts (if the social media supports it)."
msgstr ""
"Adjuntará imágenes a sus publicaciones (si es compatible con la red social)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a comment..."
msgstr "Escriba un comentario..."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Write a message or upload an image"
msgstr "Escriba un mensaje o suba una imagen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Write a message to get a preview of your post."
msgstr "Escriba un mensaje para obtener una vista previa de su publicación."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a reply..."
msgstr "Escriba una respuesta..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid ""
"Write an enticing post, add images and schedule it to be posted later on "
"multiple platforms at once."
msgstr ""
"Escriba una publicación atractiva, agregue imágenes y prográmelas para que "
"se publiquen después en diferentes plataformas al mismo tiempo."

#. module: social
#. odoo-python
#: code:addons/social/models/utm_medium.py:0
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following social accounts in Social:\n"
"%(social_accounts)s"
msgstr ""
"No puede eliminar estos medios UTM ya que están vinculados a las siguientes redes sociales en Marketing social:\n"
"%(social_accounts)s"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to social posts in Social:\n"
"%(utm_sources)s"
msgstr ""
"No puede eliminar estas fuentes UTM ya que están vinculadas a publicaciones en Marketing social:\n"
"%(utm_sources)s"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot reschedule a post that has already been posted."
msgstr "No puede reprogramar una publicación que ya se publicó."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot schedule a post in the past."
msgstr "No puede programar una publicación en una fecha que ya ocurrió."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Your Post"
msgstr "Su publicación."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "a Stream from an existing account"
msgstr "un flujo de una cuenta existente."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "before posting."
msgstr "antes de publicar."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "comment/reply"
msgstr "comentar/responder"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "dashboard"
msgstr "tablero"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "for"
msgstr "para"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "replies..."
msgstr "respuestas..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "to keep an eye on your own posts and monitor all social activities."
msgstr ""
"para consultar sus publicaciones y monitorear las actividades de sus redes "
"sociales."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "to link your accounts and start posting."
msgstr "para vincular sus cuentas y empezar a publicar."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "to start posting from Odoo."
msgstr "para empezar a publicar desde Odoo."
