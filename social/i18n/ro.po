# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__accounts_count
msgid "# Accounts"
msgstr "# Conturi"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "%(media_name)s (max %(max_chars)s chars)"
msgstr "%(media_name)s (maximum %(max_chars)s caractere)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ", you must first link a social media."
msgstr ", trebuie să atașați un cont social"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<b>An error occurred while trying to link your account</b>"
msgstr "<b>A apărut o eroare la conectarea contul</b>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar-check-o me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "<i class=\"fa fa-globe ms-1\" title=\"See the post\"/>"
msgstr "<i class=\"fa fa-globe ms-1\" title=\"Vizualizați postarea\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<i class=\"oi oi-arrow-left\"/> Go back to Odoo"
msgstr "<i class=\"oi oi-arrow-left\"/> Reveniți la Odoo"

#. module: social
#: model_terms:web_tour.tour,rainbow_man_message:social.social_tour
msgid ""
"<strong>Congrats! Come back in a few minutes to check your "
"statistics.</strong>"
msgstr ""
"<strong>Felicitări! Reveniți mai târziu pentru a vă verifica "
"statisticile</strong>"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction
msgid "Action Needed"
msgstr "Acțiune necesară"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__active
msgid "Active"
msgstr "Activ"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_ids
msgid "Activities"
msgstr "Activități"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorator Excepție Activitate"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_state
msgid "Activity State"
msgstr "Stare activitate"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Pictograma tipului de activitate"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add"
msgstr "Adaugă"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Add Post"
msgstr "Adaugă postare"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add Stream"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/add_stream_modal.js:0
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Add a Stream"
msgstr "Adaugă flux"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "Add a stream"
msgstr "Adaugă flux"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add an image"
msgstr "Adaugă Imagine"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "All Companies"
msgstr "Toate companiile"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Toate postările de social media corelate"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__account_allowed_ids
msgid "Allowed Accounts"
msgstr "Conturi permise"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Archived"
msgstr "Arhivat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_active_accounts
#: model:ir.model.fields,field_description:social.field_social_post_template__has_active_accounts
msgid "Are Accounts Available?"
msgstr "Sunt disponibile conturile?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__image_ids
msgid "Attach Images"
msgstr "Atașament imagini"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__image_ids
msgid "Attached Images"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_attachment_count
#: model:ir.model.fields,field_description:social.field_social_post__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__audience
msgid "Audience"
msgstr "Audiență"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__audience_trend
msgid "Audience Trend"
msgstr "Tendință Audiență"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_link
msgid "Author Link"
msgstr "Link Autor"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_name
msgid "Author Name"
msgstr "Nume autor"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_link
msgid "Author link to the external social.media (ex: link to the X Account)."
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Before posting, links will be converted to be trackable."
msgstr ""
"Înainte de postare, link-urile vor fi convertite pentru a putea fi urmărite."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "By Stream"
msgstr "După Stream"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__csrf_token
msgid "CSRF Token"
msgstr "Token CSRF"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__calendar_date
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Calendar Date"
msgstr "Dată Calendar"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form_quick_create_social
msgid "Campaign"
msgstr "Campanie"

#. module: social
#: model:ir.actions.act_window,name:social.action_view_utm_campaigns
#: model:ir.ui.menu,name:social.menu_social_campaign
msgid "Campaigns"
msgstr "Campanii"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Campaniile sunt folosite pentru a vă centraliza eforturile de marketing și "
"pentru a putea vizualiza rezultatele acestora."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__can_link_accounts
msgid "Can link accounts?"
msgstr "Poate atașa conturi?"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Cancel"
msgstr "Anulează"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Choose which <b>account</b> you would like to link first."
msgstr "Alegeți la care <b>cont</b> ați dori să faceți legătura mai întâi."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Click to refresh."
msgstr "Clic pentru reîmprospătare."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Clicks"
msgstr "Clicuri"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Clicks:"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Comment Image"
msgstr "Imagine comentariu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__company_id
#: model:ir.model.fields,field_description:social.field_social_live_post__company_id
#: model:ir.model.fields,field_description:social.field_social_post__company_id
#: model:ir.model.fields,field_description:social.field_social_stream__company_id
#: model:ir.model.fields,field_description:social.field_social_stream_post__company_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Company"
msgstr "Companie"

#. module: social
#: model:ir.model,name:social.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_configuration
msgid "Configuration"
msgstr "Configurare"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Connecting Problem"
msgstr "Problemă de conectare"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__social_account_handle
msgid ""
"Contains the social media handle of the person that created this account. "
"E.g: '@odoo.official' for the 'Odoo' X account"
msgstr ""

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__message
msgid ""
"Content of the social post message that is post-processed (links are "
"shortened, UTMs, ...)"
msgstr ""
"Conținutul mesajului din postarea socială care este post-procesat (linkurile"
" sunt scurtate, UTM-uri, ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__has_streams
msgid "Controls if social streams are handled on this social media."
msgstr ""
"Controlează dacă fluxurile sociale sunt gestionate pe acest social media."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__can_link_accounts
msgid "Controls if we can link accounts or not."
msgstr "Controlează dacă Odoo poate conecta conturi sau nu."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid "Create a Campaign"
msgstr "Creați o campanie"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid "Create a Post"
msgstr "Creați o postare"

#. module: social
#. odoo-python
#: code:addons/social/models/social_account.py:0
msgid ""
"Create other accounts for %(media_names)s for this company or ask "
"%(company_names)s to share their accounts"
msgstr ""
"Creați alte conturi pentru %(media_names)s pentru această companie sau "
"cereți companiei/companiilor %(company_names)s să le partajeze conturile "

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_media__create_uid
#: model:ir.model.fields,field_description:social.field_social_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_date
#: model:ir.model.fields,field_description:social.field_social_live_post__create_date
#: model:ir.model.fields,field_description:social.field_social_media__create_date
#: model:ir.model.fields,field_description:social.field_social_post__create_date
#: model:ir.model.fields,field_description:social.field_social_post_template__create_date
#: model:ir.model.fields,field_description:social.field_social_stream__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_account_stats
msgid ""
"Defines whether this account has Audience/Engagements/Stories stats.\n"
"        Account with stats are displayed on the dashboard."
msgstr ""
"Definește dacă acest cont are statistici pentru audiență / implicare / story-uri.\n"
"        Conturile cu statistici le afișează pe tabloul de bord."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_trends
msgid "Defines whether this account has statistics tends or not."
msgstr "Definește dacă acest cont are statistici sau nu."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Delete"
msgstr "Șterge"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Delete Comment"
msgstr "Ștergere Comentariu"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Delete Post"
msgstr ""

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Demo Mode"
msgstr "Mod Demo"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__media_description
msgid "Description"
msgstr "Descriere"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Developer Accounts"
msgstr "Conturi de dezvoltator"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Discard"
msgstr "Abandonează"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__display_message
#: model:ir.model.fields,field_description:social.field_social_post_template__display_message
msgid "Display Message"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__display_name
#: model:ir.model.fields,field_description:social.field_social_live_post__display_name
#: model:ir.model.fields,field_description:social.field_social_media__display_name
#: model:ir.model.fields,field_description:social.field_social_post__display_name
#: model:ir.model.fields,field_description:social.field_social_post_template__display_name
#: model:ir.model.fields,field_description:social.field_social_stream__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_type__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Do you really want to delete this %s?"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Do you really want to delete this Post ?"
msgstr ""

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__draft
msgid "Draft"
msgstr "Ciornă"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Due to length restrictions, the following posts cannot be posted:\n"
" %s"
msgstr ""
"Din cauza restricțiilor de dimensiune, următoarele postări nu pot fi postate: \n"
"%s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit"
msgstr "Editare"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit Your Post"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_res_config_settings__module_social_demo
msgid "Enable Demo Mode"
msgstr "Activare Mod Demo"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Enable this option and load demo data to test the social module. This must "
"never be used on a production database!"
msgstr ""
"Activați această opțiune și încărcați datele demo pentru a testa modulul social.<br/>\n"
"                                    Acestă opțiune nu trebuie folosită niciodată într-un sistem productiv!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__engagement
#: model:ir.model.fields,field_description:social.field_social_live_post__engagement
#: model:ir.model.fields,field_description:social.field_social_post__engagement
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Engagement"
msgstr "Implicare"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__engagement_trend
msgid "Engagement Trend"
msgstr "Tendință de implicare"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Engagement:"
msgstr ""

#. module: social
#: model:ir.model.fields,help:social.field_social_account__utm_medium_id
msgid ""
"Every time an account is created, a utm.medium is also created and linked to"
" the account"
msgstr ""
"De fiecare dată când se creează un cont, un utm.medium este de asemenea "
"creat și legat de cont"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__failed
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Failed"
msgstr "Eșuat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__failure_reason
msgid "Failure Reason"
msgstr "Motiv eșuare"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream_post
#: model:ir.ui.menu,name:social.menu_social_stream_post
msgid "Feed"
msgstr "Feed"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Feed Posts"
msgstr "Postări Feed"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__stream_posts_count
msgid "Feed Posts Count"
msgstr "Număr Postări Feed"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_follower_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_follower_ids
msgid "Followers"
msgstr "Urmăritori"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_partner_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Pictogramă Font awesome, de ex. fa-sarcini"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__formatted_published_date
msgid "Formatted Published Date"
msgstr "Data publicării formatată"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience
msgid ""
"General audience of the Social Account (Page Likes, Account Follows, ...)."
msgstr ""
"Publicul general al contului social (Like-urile paginii, Urmăritori cont, "
"...)."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "Go to the"
msgstr "Navigare la"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Group By"
msgstr "Grupează după"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__social_account_handle
msgid "Handle / Short Name"
msgstr "Identificator / Nume scurt"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Happy with the result? Let's post it!"
msgstr "Mulțumit de rezultat? Să-l postăm!"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_account_stats
msgid "Has Account Stats"
msgstr "Are statistici de cont"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_message
#: model:ir.model.fields,field_description:social.field_social_post__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_trends
msgid "Has Trends?"
msgstr "Are tendințe?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_hatched
msgid "Hatched"
msgstr "Hatched"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__id
#: model:ir.model.fields,field_description:social.field_social_live_post__id
#: model:ir.model.fields,field_description:social.field_social_media__id
#: model:ir.model.fields,field_description:social.field_social_post__id
#: model:ir.model.fields,field_description:social.field_social_post_template__id
#: model:ir.model.fields,field_description:social.field_social_stream__id
#: model:ir.model.fields,field_description:social.field_social_stream_post__id
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__id
#: model:ir.model.fields,field_description:social.field_social_stream_type__id
msgid "ID"
msgstr "ID"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_icon
msgid "Icon"
msgstr "Pictogramă"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Pictograma care indică o activitate de excepție."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction
#: model:ir.model.fields,help:social.field_social_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Dacă este bifat, mesaje noi necesită atenția dumneavoastră."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error
#: model:ir.model.fields,help:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,help:social.field_social_post__message_has_error
#: model:ir.model.fields,help:social.field_social_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, există mesaje cu eroare de livrare."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__image
#: model:ir.model.fields,field_description:social.field_social_media__image
msgid "Image"
msgstr "Imagine"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__image_url
msgid "Image URL"
msgstr "URL Imagine "

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Image Url"
msgstr "URL Imagine "

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_urls
#: model:ir.model.fields,field_description:social.field_social_post_template__image_urls
msgid "Images URLs"
msgstr "URL-uri Imagini"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__stream_post_image_ids
msgid "Images that were shared with this post."
msgstr "Imagini ale aceastei postări."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Insights"
msgstr "informații"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__is_author
msgid "Is Author"
msgstr "Este autor"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_is_follower
#: model:ir.model.fields,field_description:social.field_social_post__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"It appears there is an issue with the Social Media link, click here to link "
"the account again"
msgstr ""
"A apărut o problemă la conectarea contului social, vă rugăm să reîncercați"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "It will appear in the Feed once it has posts to display."
msgstr "Va apărea în feed când are postări de afișat."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_media__write_uid
#: model:ir.model.fields,field_description:social.field_social_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_date
#: model:ir.model.fields,field_description:social.field_social_live_post__write_date
#: model:ir.model.fields,field_description:social.field_social_media__write_date
#: model:ir.model.fields,field_description:social.field_social_post__write_date
#: model:ir.model.fields,field_description:social.field_social_post_template__write_date
#: model:ir.model.fields,field_description:social.field_social_stream__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's <b>connect</b> to Facebook, LinkedIn or X."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's create your own <b>social media</b> dashboard."
msgstr "Crează-ți propriul tău dashboard de <b>social media</b> "

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's start posting."
msgstr "Începeți să postați!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Like"
msgstr "Apreciere"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Likes"
msgstr "Aprecieri"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_description
msgid "Link Description"
msgstr "Descriere Link "

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Link Image"
msgstr "Imagine link"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_image_url
msgid "Link Image URL"
msgstr "URL imagine link"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_title
msgid "Link Title"
msgstr "Link Titlu"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_url
msgid "Link URL"
msgstr "Link URL"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Link a new account"
msgstr "Conectați un cont nou"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
msgid "Link account"
msgstr "Link Cont"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "Link an Account"
msgstr "Conectați un cont"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__company_id
#: model:ir.model.fields,help:social.field_social_live_post__company_id
#: model:ir.model.fields,help:social.field_social_stream__company_id
#: model:ir.model.fields,help:social.field_social_stream_post__company_id
msgid ""
"Link an account to a company to restrict its usage or keep empty to let all "
"companies use it."
msgstr ""
"Conectați un cont la o companie pentru a restricționa utilizarea acestuia.\n"
"Când câmpul este gol, toate companiile pot folosi contul."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__live_post_link
msgid "Link of the live post on the target media."
msgstr "Link al postării live pe audiența dorită."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Link social accounts"
msgstr "Link Conturi Sociale"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stats_link
msgid "Link to the external Social Account statistics"
msgstr "Link extern către statisticile contului social"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__is_media_disconnected
msgid "Link with external Social Media is broken"
msgstr "Conexiunea cu contul social extern nu funcționează"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_allowed_ids
msgid "List of the accounts which can be selected for this post."
msgstr "Lista conturilor selectabile pentru această postare."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_posts_by_media
msgid "Live Posts by Social Media"
msgstr "Postări Live de Social Media"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Load more comments..."
msgstr "Încarcă mai multe comentarii ..."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__max_post_length
msgid "Max Post Length"
msgstr "Lungime maximă postare"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__media_type
#: model:ir.model.fields,field_description:social.field_social_post__media_ids
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Media"
msgstr "Media"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__media_count
#: model:ir.model.fields,field_description:social.field_social_post_template__media_count
msgid "Media Count"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__media_type
#: model:ir.model.fields,field_description:social.field_social_media__media_type
msgid "Media Type"
msgstr "Tip Media"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__message
#: model:ir.model.fields,field_description:social.field_social_post__message
#: model:ir.model.fields,field_description:social.field_social_post_template__message
#: model:ir.model.fields,field_description:social.field_social_stream_post__message
msgid "Message"
msgstr "Mesaj"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error
msgid "Message Delivery error"
msgstr "Eroare de livrare a mesajului"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Message posted"
msgstr "Mesaj postat"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Message posted partially. These are the ones that couldn't be posted:%s"
msgstr "Mesaj postat parțial. Mesaje care nu au putut fi postate: %s"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__state
msgid ""
"Most social.live.posts directly go from Ready to Posted/Failed since they result of a single call to the third party API.\n"
"        A 'Posting' state is also available for those that are sent through batching (like push notifications)."
msgstr ""
"Majoritatea posturilor social.live.post trec direct de la Pregătit la Postat / Eșuat, deoarece rezultă dintr-un singur apel către API-ul third party.\n"
"       O stare de „Postare” este de asemenea disponibilă pentru postări batch (cum ar fi notificările push)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Data limită a activității mele"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "My Posts"
msgstr "Postările mele"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "My Streams"
msgstr "Fluxurile Mele"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__name
#: model:ir.model.fields,field_description:social.field_social_media__name
#: model:ir.model.fields,field_description:social.field_social_post__name
#: model:ir.model.fields,field_description:social.field_social_stream_type__name
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Name"
msgstr "Nume"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New Post"
msgstr "Postare Nouă"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New content available"
msgstr "Conținut nou disponibil "

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Next"
msgstr "Următorul"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Următoarea activitate din calendar"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data limită pentru următoarea activitate"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_summary
msgid "Next Activity Summary"
msgstr "Sumarul următoarei activități"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_id
msgid "Next Activity Type"
msgstr "Următorul tip de activitate"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "No Social Account yet!"
msgstr "Nu există conturi sociale."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "No Social Streams yet!"
msgstr "Nu există fluxuri sociale."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "No Stream added yet!"
msgstr "Nu există fluxuri adăugate."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "No comments yet."
msgstr "Niciun comentariu încă."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "No social accounts configured, please contact your administrator."
msgstr ""
"Nu este configurat niciun cont social, vă rugăm să contactați "
"administratorul."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Number of Followers of your channel"
msgstr "Numărul de urmăritori ai canalului tău"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__click_count
msgid "Number of clicks"
msgstr "Numărul de click-uri"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error_counter
msgid "Number of errors"
msgstr "Număr de erori"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Număr de interacțiuni (like-uri, share-uri, comentarii ...) cu postările "
"sociale"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,help:social.field_social_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numărul de mesaje care necesită acțiune"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,help:social.field_social_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__engagement
#: model:ir.model.fields,help:social.field_social_post__engagement
msgid "Number of people engagements with the post (Likes, comments...)"
msgstr "Număr interacțiuni cu postarea (aprecieri, comentarii ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement
msgid "Number of people engagements with your posts (Likes, Comments, ...)."
msgstr "Număr de interacțiuni cu postările dvs. (aprecieri, comentarii, ...)."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories
msgid "Number of stories created from your posts (Shares, Reposts, ...)."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people have engaged with your posts (likes, comments, "
"shares,...)"
msgstr ""
"De câte ori au interacționat utilizatorii cu postările dvs. (aprecieri, "
"comentarii, distribuții, ...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people who have engaged with your channel have created "
"stories on their friends' or followers' feed (Shares, Reposts...)"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Or add"
msgstr "Sau adaugă"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience_trend
msgid "Percentage of increase/decrease of the audience over a defined period."
msgstr ""
"Procentul de creștere / scădere a audienței pe o perioadă determinată."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement_trend
msgid ""
"Percentage of increase/decrease of the engagement over a defined period."
msgstr ""
"Procentul de creștere / scădere a interacțiunilor pe o perioadă determinată."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories_trend
msgid "Percentage of increase/decrease of the stories over a defined period."
msgstr ""
"Procentul de creștere / scădere a poveștilor într-o perioadă definită."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Please specify at least one account to post into (for post ID(s) %s)."
msgstr ""
"Vă rugăm să specificați cel puțin un cont pentru postare (pentru post ID-ul "
"%s)"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Please specify either a message or upload some images."
msgstr ""

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post"
msgstr "Postează"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Post Image"
msgstr "Imagine postare"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.js:0
#: code:addons/social/static/src/js/post_kanban_view.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/js/stream_post_kanban_renderer.js:0
msgid "Post Images"
msgstr "Imagini postare"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__live_post_link
#: model:ir.model.fields,field_description:social.field_social_stream_post__post_link
msgid "Post Link"
msgstr "Link postare"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "Post Message"
msgstr "Mesaj postare"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post Now"
msgstr "Postează"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__post_link
msgid ""
"Post link to the external social.media (ex: link to the actual Facebook "
"Post)."
msgstr ""
"Publicați un link către social.media extern (ex: link către Facebook Post)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Post on"
msgstr "Postează pe"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posted
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posted
msgid "Posted"
msgstr "Postat"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posting
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posting
msgid "Posting"
msgstr "Postare"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_post_ids
#: model:ir.ui.menu,name:social.menu_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_kanban
msgid "Posts"
msgstr "Postări"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_post_ids
msgid "Posts By Account"
msgstr "Postări după Cont"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Posts Preview"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to edit. Press Shift+Enter to insert a Line Break."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to post. Press Shift+Enter to insert a Line Break."
msgstr ""
"Apăsați Enter pentru a posta. Apăsați Shift + Enter pentru a insera un "
"Enter."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Preview your post"
msgstr "Previzualizați postarea dvs."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Previous"
msgstr "Anterior"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__post_method
msgid "Publish your post immediately or schedule it at a later time."
msgstr "Publicați imediat postarea sau programați-o pe dată fixă."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Published By"
msgstr "Publicat de"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__published_date
msgid "Published Date"
msgstr "Dată publicată"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__published_date
msgid "Published date"
msgstr "Dată pubicare"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__rating_ids
#: model:ir.model.fields,field_description:social.field_social_post__rating_ids
msgid "Ratings"
msgstr "Ratings"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__ready
msgid "Ready"
msgstr "Pregătit"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__media_type
msgid "Related Social Media"
msgstr "Social media aferent"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_id
msgid "Related Social Media (Facebook, X, ...)."
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__account_id
msgid "Related social Account"
msgstr "Cont social aferent"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Reply"
msgstr "Răspunde"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_user_id
msgid "Responsible User"
msgstr "Utilizator responsabil"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Retry"
msgstr "Reîncearcă"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Schedule"
msgstr "Programare"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__scheduled
msgid "Schedule later"
msgstr "Programează mai târziu"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__scheduled
msgid "Scheduled"
msgstr "Programat"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__scheduled_date
msgid "Scheduled Date"
msgstr "Dată planificată"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Selected accounts (%(account_list)s) do not match the selected company "
"(%(company)s)"
msgstr ""
"Contul selectat (%(account_list)s) nu corespunde companiei selectate "
"(%(company)s)"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__now
msgid "Send now"
msgstr "Trimite acum"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream__sequence
msgid "Sequence used to order streams (mainly for the 'Feed' kanban view)"
msgstr ""
"Secvență utilizată pentru a ordona fluxuri (în principal pentru vizualizarea"
" kanban „Feed”)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__max_post_length
msgid ""
"Set a maximum number of characters can be posted in post. 0 for no limit."
msgstr "Alegeți o limită decaractere pentru postări. 0 pentru nelimitat."

#. module: social
#: model:ir.actions.act_window,name:social.action_social_global_settings
#: model:ir.ui.menu,name:social.menu_social_global_settings
msgid "Settings"
msgstr "Setări"

#. module: social
#: model:ir.model,name:social.model_social_account
#: model:ir.model.fields,field_description:social.field_social_live_post__account_id
#: model:ir.model.fields,field_description:social.field_social_stream__account_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
msgid "Social Account"
msgstr "Cont Social"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_account
#: model:ir.model.fields,field_description:social.field_social_media__account_ids
#: model:ir.model.fields,field_description:social.field_social_post__account_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__account_ids
#: model:ir.ui.menu,name:social.menu_social_account
#: model_terms:ir.ui.view,arch_db:social.social_account_view_list
msgid "Social Accounts"
msgstr "Conturi Sociale"

#. module: social
#: model:ir.model,name:social.model_social_live_post
msgid "Social Live Post"
msgstr "Postare socială live"

#. module: social
#: model:res.groups,name:social.group_social_manager
msgid "Social Manager"
msgstr "Manager social"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_global
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Social Marketing"
msgstr "Marketing social"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.actions.act_window,name:social.action_social_media
#: model:ir.model,name:social.model_social_media
#: model:ir.model.fields,field_description:social.field_social_account__media_id
#: model:ir.model.fields,field_description:social.field_social_stream__media_id
#: model:ir.model.fields,field_description:social.field_social_stream_type__media_id
#: model:ir.ui.menu,name:social.menu_social_media
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Social Media"
msgstr "Rețelele de socializare"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Postări în rețele sociale"

#. module: social
#: model:ir.model,name:social.model_social_post
#: model:ir.model.fields,field_description:social.field_social_live_post__post_id
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
msgid "Social Post"
msgstr "Postare socială"

#. module: social
#: model:ir.model,name:social.model_social_post_template
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Social Post Template"
msgstr "Șablon postare socială"

#. module: social
#: model:ir.actions.act_window,name:social.social_post_template_action
msgid "Social Post Templates"
msgstr "Șablon postare socială"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_calendar
#: model_terms:ir.ui.view,arch_db:social.social_post_view_pivot
msgid "Social Posts"
msgstr "Postări Sociale"

#. module: social
#: model:ir.model,name:social.model_social_stream
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_id
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_form
msgid "Social Stream"
msgstr "Flux social"

#. module: social
#: model:ir.model,name:social.model_social_stream_post
#: model:ir.model,name:social.model_social_stream_type
msgid "Social Stream Post"
msgstr "Postare flux social"

#. module: social
#: model:ir.model,name:social.model_social_stream_post_image
msgid "Social Stream Post Image Attachment"
msgstr "Imagine postare flux social atașată"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream
#: model:ir.ui.menu,name:social.menu_social_stream
msgid "Social Streams"
msgstr "Fluxuri sociale"

#. module: social
#: model:res.groups,name:social.group_social_user
msgid "Social User"
msgstr "Utilizator rețea socială"

#. module: social
#: model:ir.actions.server,name:social.ir_cron_post_scheduled_ir_actions_server
msgid "Social: Publish Scheduled Posts"
msgstr "Social: publicați postări programate"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments_reply.js:0
msgid "Something went wrong while posting the comment."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_dashboard.js:0
msgid ""
"Sorry, you're not allowed to re-link this account, please contact your "
"administrator."
msgstr ""
"Nu puteți reconecta acest cont, vă rugăm sa contactați administratorul."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__source_id
msgid "Source"
msgstr "Sursă"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_posts_by_media
msgid ""
"Special technical field that holds a dict containing the live posts names by"
" media ids (used for kanban view)."
msgstr ""
"Câmp tehnic special care deține un dict ce conține numele postărilor live "
"după ID-uri media (utilizat pentru vizualizarea kanban)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_post_split_per_media.xml:0
msgid "Split Per Media"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_split_per_media
#: model:ir.model.fields,field_description:social.field_social_post_template__is_split_per_media
msgid "Split Per Network"
msgstr ""

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stats_link
msgid "Stats Link"
msgstr "Link Statistici"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__state
#: model:ir.model.fields,field_description:social.field_social_post__state
msgid "Status"
msgstr "Stare"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stare bazată pe activități\n"
"Întârziată: data activitații este deja trecută\n"
"Astăzi: data activității este astăzi\n"
"Planificate: activități viitoare."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__stories
msgid "Stories"
msgstr "Povești"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stories_trend
msgid "Stories Trend"
msgstr "Trend povești"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Stream Added (%s)"
msgstr "Flux adăugat (%s)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__stream_post_id
msgid "Stream Post"
msgstr "Postare flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_ids
msgid "Stream Post Images"
msgstr "Imagini postare flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_urls
msgid "Stream Post Images URLs"
msgstr "URL imagini postare flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__stream_type_ids
msgid "Stream Types"
msgstr "Tip flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_type
#: model:ir.model.fields,field_description:social.field_social_stream_type__stream_type
msgid "Stream type name (technical)"
msgstr "Nume tip flux (tehnic)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_streams
msgid "Streams Enabled"
msgstr "Fluxuri activate"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_post_ids
msgid "Sub-posts that will be published on each selected social accounts."
msgstr "Postări copil de publicat pe toate conturile sociale selectate."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Sync"
msgstr ""

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_ids
#: model:ir.model.fields,help:social.field_social_post_template__account_ids
msgid "The accounts on which this post will be published."
msgstr "Conturile pe care vor fi publicate postările."

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_name
msgid ""
"The post author name based on third party information (ex: 'John Doe')."
msgstr ""
"Numele autorul postării bazat pe informații de la terți (ex: 'Andrei Mihai')"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__state
msgid ""
"The post is considered as 'Posted' when all its sub-posts (one per social "
"account) are either 'Failed' or 'Posted'"
msgstr ""
"Această postare este considerată 'Postată' când toate sub-postările (unul "
"pentru fiecare cont) este 'Eșuat' sau 'Postat'  "

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__published_date
msgid "The post published date based on third party information."
msgstr "Data publicării postării bazată pe informații de la terț."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__failure_reason
msgid ""
"The reason why a post is not successfully posted on the Social Media (eg: "
"connection error, duplicated post, ...)."
msgstr ""
"Motivul pentru care o postare nu este postată cu succes pe Social Media (ex:"
" eroare de conectare, postare duplicată, ...). "

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__media_image
msgid "The related Social Media's image"
msgstr "Imaginea aferentă mediului social"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__media_ids
msgid "The social medias linked to the selected social accounts."
msgstr "Rețelele de socializare conectate la conturile sociale selectate."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_post_errors
msgid "There are post errors on sub-posts"
msgstr "Există erori în postările copil"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__csrf_token
msgid ""
"This token can be used to verify that an incoming request from a social "
"provider has not been forged."
msgstr ""
"Acest token poate fi folosit pentru a confirma dacă o cerere primită de la "
"providerul de rețea este credibil."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__name
msgid "Title"
msgstr "Titlu"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "To add a stream"
msgstr "Pentru a insera un flux"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_id
msgid "Type"
msgstr "Tip"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipul de activitate de excepție înregistrată."

#. module: social
#: model:ir.model,name:social.model_utm_campaign
msgid "UTM Campaign"
msgstr "Campanie UTM"

#. module: social
#: model:ir.model,name:social.model_utm_medium
#: model:ir.model.fields,field_description:social.field_social_account__utm_medium_id
msgid "UTM Medium"
msgstr "Mediu UTM"

#. module: social
#: model:ir.model,name:social.model_utm_source
msgid "UTM Source"
msgstr "Sursă UTM"

#. module: social
#. odoo-python
#: code:addons/social/controllers/main.py:0
msgid "Uh-oh! It looks like this message has been deleted from X."
msgstr ""

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Unknown error"
msgstr "Eroare necunoscută"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Uploaded file does not seem to be a valid image."
msgstr "Fișierele încărcate nu conțin o imagine validă."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Use your own Developer Accounts on our Social app. Those credentials are "
"provided in the developer section of your professional social media account."
msgstr ""
"Use your own Developer Accounts on our Social app. Those credentials are "
"provided in the developer section of your professional social media account."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_type
#: model:ir.model.fields,help:social.field_social_live_post__media_type
#: model:ir.model.fields,help:social.field_social_media__media_type
#: model:ir.model.fields,help:social.field_social_stream_post__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "View"
msgstr "Afișare"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__website_message_ids
#: model:ir.model.fields,field_description:social.field_social_post__website_message_ids
msgid "Website Messages"
msgstr "Mesaje de pe site-ul web"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__website_message_ids
#: model:ir.model.fields,help:social.field_social_post__website_message_ids
msgid "Website communication history"
msgstr "Istoricul comunicării pe site-ul web"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__post_method
msgid "When"
msgstr "Când"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__published_date
msgid ""
"When the global post was published. The actual sub-posts published dates may"
" be different depending on the media."
msgstr ""
"Când a fost publicată postarea globală. Datele reale de publicare a "
"subpostărilor pot fi diferite în funcție de tip."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__image_ids
#: model:ir.model.fields,help:social.field_social_post_template__image_ids
msgid "Will attach images to your posts (if the social media supports it)."
msgstr ""
"Vor fi atașate imaginile la postare (Dacă rețeaua de socializare acceptă)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a comment..."
msgstr "Scrie un comentariu..."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Write a message or upload an image"
msgstr ""

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Write a message to get a preview of your post."
msgstr "Scrieți un mesaj pentru a avea o previzualizare a postării."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a reply..."
msgstr "Scrie un răspuns..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid ""
"Write an enticing post, add images and schedule it to be posted later on "
"multiple platforms at once."
msgstr ""
"Scrieți o postare interesantă, adăugați imagini și programați-o să fie "
"postată mai târziu pe mai multe platforme simultan."

#. module: social
#. odoo-python
#: code:addons/social/models/utm_medium.py:0
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following social accounts in Social:\n"
"%(social_accounts)s"
msgstr ""
"Nu puteți șterge aceste medii UTM, deoarece sunt ocnectate la următoarele conturi sociale:\n"
"%(social_accounts)s"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to social posts in Social:\n"
"%(utm_sources)s"
msgstr ""
"Nu puteți șterge aceste medii UTM, deoarece sunt ocnectate la următoarele postări sociale:\n"
"%(utm_sources)s"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot reschedule a post that has already been posted."
msgstr "Nu puteți reprograma o postare care deja a fost publicată."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot schedule a post in the past."
msgstr "Nu puteți reprograma o postare în trecut."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Your Post"
msgstr "Postarea dvs."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "a Stream from an existing account"
msgstr "un flux de pe un cont existent"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "before posting."
msgstr "înainte de a publica."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "comment/reply"
msgstr "comentariu/răspuns"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "dashboard"
msgstr "tablou de bord"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "for"
msgstr "pentru"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "replies..."
msgstr "răspunsuri..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "to keep an eye on your own posts and monitor all social activities."
msgstr ""
"pentru a urmării propriile postări și monitoriza toate activitațile sociale."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "to link your accounts and start posting."
msgstr "pentru a atașa conturile și să puteți posta."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "to start posting from Odoo."
msgstr "pentru a putea publica din Odoo."
