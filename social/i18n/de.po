# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__accounts_count
msgid "# Accounts"
msgstr "# Konten"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "%(media_name)s (max %(max_chars)s chars)"
msgstr "%(media_name)s (max. %(max_chars)s Zeichen)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ", you must first link a social media."
msgstr ", müssen Sie zunächst eine Social-Media-Plattform verknüpfen."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<b>An error occurred while trying to link your account</b>"
msgstr ""
"<b>Beim Versuch Ihr Konto zu verknüpfen, ist ein Fehler aufgetreten</b>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr "<i class=\"fa fa-calendar me-1 small fw-bold\" title=\"Kalender\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar-check-o me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr "<i class=\"fa fa-calendar-check-o me-1 small fw-bold\" title=\"Kalender\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "<i class=\"fa fa-globe ms-1\" title=\"See the post\"/>"
msgstr "<i class=\"fa fa-globe ms-1\" title=\"See the post\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<i class=\"oi oi-arrow-left\"/> Go back to Odoo"
msgstr "<i class=\"oi oi-arrow-left\"/> Zurück zu Odoo"

#. module: social
#: model_terms:web_tour.tour,rainbow_man_message:social.social_tour
msgid ""
"<strong>Congrats! Come back in a few minutes to check your "
"statistics.</strong>"
msgstr ""
"<strong>Herzlichen Glückwunsch! Kommen Sie in ein paar Minuten wieder, um "
"Ihre Statistiken zu überprüfen.</strong>"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__active
msgid "Active"
msgstr "Aktiv"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_ids
msgid "Activities"
msgstr "Aktivitäten"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitätsausnahme-Dekoration"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_state
msgid "Activity State"
msgstr "Status der Aktivität"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Symbol des Aktivitätstyps"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add"
msgstr "Hinzufügen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Add Post"
msgstr "Beitrag hinzufügen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add Stream"
msgstr "Stream hinzufügen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/add_stream_modal.js:0
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Add a Stream"
msgstr "Stream hinzufügen"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "Add a stream"
msgstr "Stream hinzufügen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add an image"
msgstr "Bild hinzufügen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "All Companies"
msgstr "Alle Unternehmen"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Ähnliche Social-Media-Beiträge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__account_allowed_ids
msgid "Allowed Accounts"
msgstr "Zulässige Konten"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Archived"
msgstr "Archiviert"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_active_accounts
#: model:ir.model.fields,field_description:social.field_social_post_template__has_active_accounts
msgid "Are Accounts Available?"
msgstr "Sind Konten verfügbar?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__image_ids
msgid "Attach Images"
msgstr "Bilder anhängen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__image_ids
msgid "Attached Images"
msgstr "Angehängte Bilder"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_attachment_count
#: model:ir.model.fields,field_description:social.field_social_post__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__audience
msgid "Audience"
msgstr "Publikum"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__audience_trend
msgid "Audience Trend"
msgstr "Publikumstrend"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_link
msgid "Author Link"
msgstr "Autor-Link"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_name
msgid "Author Name"
msgstr "Name des Autors"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_link
msgid "Author link to the external social.media (ex: link to the X Account)."
msgstr "Autor-Link zu externen social.media (bsp.: Link zu einem X-Konto)"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Before posting, links will be converted to be trackable."
msgstr ""
"Links werden vor der Veröffentlichung so umgewandelt, dass sie verfolgbar "
"sind."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "By Stream"
msgstr "Durch Stream"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__csrf_token
msgid "CSRF Token"
msgstr "CSRF-Token"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__calendar_date
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Calendar Date"
msgstr "Kalenderdatum"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form_quick_create_social
msgid "Campaign"
msgstr "Kampagne"

#. module: social
#: model:ir.actions.act_window,name:social.action_view_utm_campaigns
#: model:ir.ui.menu,name:social.menu_social_campaign
msgid "Campaigns"
msgstr "Kampagnen"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Kampagnen werden verwendet, um Ihre Marketingmaßnahmen zu zentralisieren und"
" deren Ergebnisse zu verfolgen."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__can_link_accounts
msgid "Can link accounts?"
msgstr "Konten verbindbar?"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Choose which <b>account</b> you would like to link first."
msgstr "Wählen Sie das <b>Konto</b>, das Sie zuerst verknüpfen möchten."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Click to refresh."
msgstr "Für Aktualisierung klicken."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Clicks"
msgstr "Klicks"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Clicks:"
msgstr "Klicks:"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Comment Image"
msgstr "Bild kommentieren"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__company_id
#: model:ir.model.fields,field_description:social.field_social_live_post__company_id
#: model:ir.model.fields,field_description:social.field_social_post__company_id
#: model:ir.model.fields,field_description:social.field_social_stream__company_id
#: model:ir.model.fields,field_description:social.field_social_stream_post__company_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Company"
msgstr "Unternehmen"

#. module: social
#: model:ir.model,name:social.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen "

#. module: social
#: model:ir.ui.menu,name:social.menu_social_configuration
msgid "Configuration"
msgstr "Konfiguration"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Connecting Problem"
msgstr "Verbindungsprobleme"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__social_account_handle
msgid ""
"Contains the social media handle of the person that created this account. "
"E.g: '@odoo.official' for the 'Odoo' X account"
msgstr ""
"Beinhaltet den Social-Media-Namen der Person, die dieses Konto erstellt hat."
" Z. B.: „@odoo.official“ für das „Odoo“-X-Konto."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__message
msgid ""
"Content of the social post message that is post-processed (links are "
"shortened, UTMs, ...)"
msgstr ""
"Inhalt des Social-Media-Beitrags, der nachbearbeitet wird (Links werden "
"gekürzt, UTMs ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__has_streams
msgid "Controls if social streams are handled on this social media."
msgstr "Kontrolliert, ob auf diesem Social-Media-Streams verwendet werden."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__can_link_accounts
msgid "Controls if we can link accounts or not."
msgstr "Kontrolliert, ob Konten verbunden werden können oder nicht."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid "Create a Campaign"
msgstr "Eine Kampagne erstellen"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid "Create a Post"
msgstr "Einen Beitrag erstellen"

#. module: social
#. odoo-python
#: code:addons/social/models/social_account.py:0
msgid ""
"Create other accounts for %(media_names)s for this company or ask "
"%(company_names)s to share their accounts"
msgstr ""
"Erstellen Sie weitere Konten für %(media_names)s für dieses Unternehmen oder"
" bitten Sie %(company_names)s um die Freigabe von Konten"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_media__create_uid
#: model:ir.model.fields,field_description:social.field_social_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_date
#: model:ir.model.fields,field_description:social.field_social_live_post__create_date
#: model:ir.model.fields,field_description:social.field_social_media__create_date
#: model:ir.model.fields,field_description:social.field_social_post__create_date
#: model:ir.model.fields,field_description:social.field_social_post_template__create_date
#: model:ir.model.fields,field_description:social.field_social_stream__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_account_stats
msgid ""
"Defines whether this account has Audience/Engagements/Stories stats.\n"
"        Account with stats are displayed on the dashboard."
msgstr ""
"Definiert, ob dieses Konto Statistiken über Publikum/Interaktionen/Storys hat.\n"
"        Konten mit Statistiken werden auf dem Dashboard angezeigt."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_trends
msgid "Defines whether this account has statistics tends or not."
msgstr ""
"Definiert, ob dieses Konto statistische Tendenzen aufweist oder nicht."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Delete"
msgstr "Löschen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Delete Comment"
msgstr "Kommentar löschen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Delete Post"
msgstr "Beitrag löschen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Demo Mode"
msgstr "Demo-Modus"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__media_description
msgid "Description"
msgstr "Beschreibung"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Developer Accounts"
msgstr "Entwicklerkonten"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Discard"
msgstr "Verwerfen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__display_message
#: model:ir.model.fields,field_description:social.field_social_post_template__display_message
msgid "Display Message"
msgstr "Nachricht anzeigen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__display_name
#: model:ir.model.fields,field_description:social.field_social_live_post__display_name
#: model:ir.model.fields,field_description:social.field_social_media__display_name
#: model:ir.model.fields,field_description:social.field_social_post__display_name
#: model:ir.model.fields,field_description:social.field_social_post_template__display_name
#: model:ir.model.fields,field_description:social.field_social_stream__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_type__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Do you really want to delete this %s?"
msgstr "Möchten Sie „%s“ wirklich löschen?"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Do you really want to delete this Post ?"
msgstr "Möchten Sie diesen Beitrag wirklich löschen?"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__draft
msgid "Draft"
msgstr "Entwurf"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Due to length restrictions, the following posts cannot be posted:\n"
" %s"
msgstr ""
"Wegen Längenbeschränkungen können folgende Beiträge nicht gepostet werden: "
"%s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit"
msgstr "Bearbeiten"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit Your Post"
msgstr "Ihren Beitrag bearbeiten"

#. module: social
#: model:ir.model.fields,field_description:social.field_res_config_settings__module_social_demo
msgid "Enable Demo Mode"
msgstr "Demo-Modus aktivieren"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Enable this option and load demo data to test the social module. This must "
"never be used on a production database!"
msgstr ""
"Aktivieren Sie diese Option und laden Sie Demodaten, um das Social-Modul zu "
"testen. Verwenden Sie diese Option auf keinen Fall in einer "
"Produktionsdatenbank!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__engagement
#: model:ir.model.fields,field_description:social.field_social_live_post__engagement
#: model:ir.model.fields,field_description:social.field_social_post__engagement
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Engagement"
msgstr "Interaktion"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__engagement_trend
msgid "Engagement Trend"
msgstr "Interaktionstrend"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Engagement:"
msgstr "Interaktion:"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__utm_medium_id
msgid ""
"Every time an account is created, a utm.medium is also created and linked to"
" the account"
msgstr ""
"Jedes Mal wenn ein Konto erstellt wird, wird auch ein utm.medium erstellt "
"und mit diesem Konto verbunden."

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__failed
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Failed"
msgstr "Fehlgeschlagen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__failure_reason
msgid "Failure Reason"
msgstr "Fehlerursache"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream_post
#: model:ir.ui.menu,name:social.menu_social_stream_post
msgid "Feed"
msgstr "Feed"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Feed Posts"
msgstr "Feed-Beiträge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__stream_posts_count
msgid "Feed Posts Count"
msgstr "Anzahl Feed-Beiträge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_follower_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_partner_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "FontAwesome-Icon, z. B. fa-tasks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__formatted_published_date
msgid "Formatted Published Date"
msgstr "Formatiertes Veröffentlichungsdatum"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience
msgid ""
"General audience of the Social Account (Page Likes, Account Follows, ...)."
msgstr ""
"Allgemeines Publikum des Social-Media-Kontos (Seiten-Likes, Konto-Follower "
"...)."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "Go to the"
msgstr "Gehen Sie zum"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__social_account_handle
msgid "Handle / Short Name"
msgstr "Alias/Kurzname"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Happy with the result? Let's post it!"
msgstr "Zufrieden mit dem Ergebnis? Posten Sie es!"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_account_stats
msgid "Has Account Stats"
msgstr "Hat Kontostatistiken"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_message
#: model:ir.model.fields,field_description:social.field_social_post__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_trends
msgid "Has Trends?"
msgstr "Hat Trends?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_hatched
msgid "Hatched"
msgstr "Schraffiert"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__id
#: model:ir.model.fields,field_description:social.field_social_live_post__id
#: model:ir.model.fields,field_description:social.field_social_media__id
#: model:ir.model.fields,field_description:social.field_social_post__id
#: model:ir.model.fields,field_description:social.field_social_post_template__id
#: model:ir.model.fields,field_description:social.field_social_stream__id
#: model:ir.model.fields,field_description:social.field_social_stream_post__id
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__id
#: model:ir.model.fields,field_description:social.field_social_stream_type__id
msgid "ID"
msgstr "ID"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon, um eine Ausnahmeaktivität anzuzeigen."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction
#: model:ir.model.fields,help:social.field_social_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error
#: model:ir.model.fields,help:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,help:social.field_social_post__message_has_error
#: model:ir.model.fields,help:social.field_social_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__image
#: model:ir.model.fields,field_description:social.field_social_media__image
msgid "Image"
msgstr "Bild"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__image_url
msgid "Image URL"
msgstr "Bild-URL"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Image Url"
msgstr "Bild-Url"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_urls
#: model:ir.model.fields,field_description:social.field_social_post_template__image_urls
msgid "Images URLs"
msgstr "Bilder-URLs"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__stream_post_image_ids
msgid "Images that were shared with this post."
msgstr "Bilder, die mit diesem Beitrag geteilt wurden."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Insights"
msgstr "Einblicke"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__is_author
msgid "Is Author"
msgstr "Ist Autor"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_is_follower
#: model:ir.model.fields,field_description:social.field_social_post__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"It appears there is an issue with the Social Media link, click here to link "
"the account again"
msgstr ""
"Es scheint, als gäbe es ein Problem mit dem Social-Media-Link, klicken Sie "
"hier um den Konto erneut zu verlinken."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "It will appear in the Feed once it has posts to display."
msgstr "Wird im Feed angezeigt, sobald es anzuzeigende Beiträge gibt."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_media__write_uid
#: model:ir.model.fields,field_description:social.field_social_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_date
#: model:ir.model.fields,field_description:social.field_social_live_post__write_date
#: model:ir.model.fields,field_description:social.field_social_media__write_date
#: model:ir.model.fields,field_description:social.field_social_post__write_date
#: model:ir.model.fields,field_description:social.field_social_post_template__write_date
#: model:ir.model.fields,field_description:social.field_social_stream__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's <b>connect</b> to Facebook, LinkedIn or X."
msgstr "<b>Verbinden</b> Sie sich mit Facebook, LinkedIn oder X."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's create your own <b>social media</b> dashboard."
msgstr "Gestalten Sie Ihr eigenes <b>Social-Media</b>-Dashboard."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's start posting."
msgstr "Beginnen Sie zu posten."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Like"
msgstr "Like"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Likes"
msgstr "Likes"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_description
msgid "Link Description"
msgstr "Link-Beschreibung"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Link Image"
msgstr "Link-Bild"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_image_url
msgid "Link Image URL"
msgstr "URL des Link-Bilds"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_title
msgid "Link Title"
msgstr "Link-Titel"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_url
msgid "Link URL"
msgstr "Link-URL"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Link a new account"
msgstr "Ein neues Konto verknüpfen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
msgid "Link account"
msgstr "Konto verknüpfen"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "Link an Account"
msgstr "Ein Konto verknüpfen"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__company_id
#: model:ir.model.fields,help:social.field_social_live_post__company_id
#: model:ir.model.fields,help:social.field_social_stream__company_id
#: model:ir.model.fields,help:social.field_social_stream_post__company_id
msgid ""
"Link an account to a company to restrict its usage or keep empty to let all "
"companies use it."
msgstr ""
"Verknüpfen Sie ein Konto mit einem Unternehmen, um seine Nutzung "
"einzuschränken, oder lassen Sie es leer, damit alle Unternehmen es nutzen "
"können."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__live_post_link
msgid "Link of the live post on the target media."
msgstr "Link des Live-Beitrags auf dem Zielmedium."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Link social accounts"
msgstr "Social-Media-Konten verbinden"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stats_link
msgid "Link to the external Social Account statistics"
msgstr "Link zu externen Statistiken der Social-Media-Konten"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__is_media_disconnected
msgid "Link with external Social Media is broken"
msgstr "Link mit externen Social-Media-Quellen funktioniert nicht"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_allowed_ids
msgid "List of the accounts which can be selected for this post."
msgstr "Liste der Konten, die für diesen Beitrag ausgewählt werden können."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_posts_by_media
msgid "Live Posts by Social Media"
msgstr "Live-Beiträge in sozialen Medien"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Load more comments..."
msgstr "Mehr Kommentare laden ..."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__max_post_length
msgid "Max Post Length"
msgstr "Max. Beitragslänge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__media_type
#: model:ir.model.fields,field_description:social.field_social_post__media_ids
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Media"
msgstr "Medien"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__media_count
#: model:ir.model.fields,field_description:social.field_social_post_template__media_count
msgid "Media Count"
msgstr "Anzahl Medien"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__media_type
#: model:ir.model.fields,field_description:social.field_social_media__media_type
msgid "Media Type"
msgstr "Medientyp"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__message
#: model:ir.model.fields,field_description:social.field_social_post__message
#: model:ir.model.fields,field_description:social.field_social_post_template__message
#: model:ir.model.fields,field_description:social.field_social_stream_post__message
msgid "Message"
msgstr "Nachricht"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Message posted"
msgstr "Nachricht gepostet"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Message posted partially. These are the ones that couldn't be posted:%s"
msgstr ""
"Nachrichten nur zum Teil gepostet. Das sind die Nachrichten, die nicht "
"gepostet werden konnten:%s"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__state
msgid ""
"Most social.live.posts directly go from Ready to Posted/Failed since they result of a single call to the third party API.\n"
"        A 'Posting' state is also available for those that are sent through batching (like push notifications)."
msgstr ""
"Der Status der meisten social.live.posts ändert sich direkt von „Bereit“ zu „Gepostet/Fehlgeschlagen“, da Sie meistens das Resultat einer einzelnen Anfrage zu der API des Dritten sind.\n"
"        Ein „Gepostet“-Status ist auch für jene verfügbar, die im Stapel gesendet worden sind (wie Push-Benachrichtigungen)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Frist für meine Aktivitäten"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "My Posts"
msgstr "Meine Beiträge"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "My Streams"
msgstr "Meine Streams"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__name
#: model:ir.model.fields,field_description:social.field_social_media__name
#: model:ir.model.fields,field_description:social.field_social_post__name
#: model:ir.model.fields,field_description:social.field_social_stream_type__name
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Name"
msgstr "Name"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New Post"
msgstr "Neuer Beitrag"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New content available"
msgstr "Neuer Inhalt verfügbar"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Next"
msgstr "Weiter"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nächstes Aktivitätskalenderereignis"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nächste Aktivitätsfrist"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "No Social Account yet!"
msgstr "Noch kein Social-Media-Konto!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "No Social Streams yet!"
msgstr "Noch keine Social-Media-Streams!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "No Stream added yet!"
msgstr "Noch kein Stream hinzugefügt!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "No comments yet."
msgstr "Noch keine Kommentare."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "No social accounts configured, please contact your administrator."
msgstr ""
"Keine Social-Media-Konten konfiguriert, bitte kontaktieren Sie Ihren "
"Administrator."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Number of Followers of your channel"
msgstr "Anzahl der Follower Ihres Kanals"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__click_count
msgid "Number of clicks"
msgstr "Anzahl Klicks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Anzahl der Interaktionen (Likes, Geteilt, Kommentare ...) mit den Social-"
"Media-Beiträgen"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,help:social.field_social_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,help:social.field_social_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__engagement
#: model:ir.model.fields,help:social.field_social_post__engagement
msgid "Number of people engagements with the post (Likes, comments...)"
msgstr "Anzahl der Interaktionen mit dem Beitrag (Likes, Kommentare ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement
msgid "Number of people engagements with your posts (Likes, Comments, ...)."
msgstr "Anzahl der Interaktionen mit Ihren Beiträgen (Likes, Kommentare ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories
msgid "Number of stories created from your posts (Shares, Reposts, ...)."
msgstr ""
"Anzahl der Storys, die anhand Ihres Beitrags kreiert worden sind (Geteilt, "
"Reposts ...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people have engaged with your posts (likes, comments, "
"shares,...)"
msgstr ""
"Anzahl der Male, die Personen auf Ihre Beiträge reagiert haben (Likes, "
"Kommentare, Geteilt ...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people who have engaged with your channel have created "
"stories on their friends' or followers' feed (Shares, Reposts...)"
msgstr ""
"Anzahl der Male, in denen Personen, die sich mit Ihrem Kanal beschäftigt "
"haben, Storys im Feed ihrer Freunde oder Follower erstellt haben (Shares, "
"Reposts ...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Or add"
msgstr "Oder fügen Sie"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience_trend
msgid "Percentage of increase/decrease of the audience over a defined period."
msgstr ""
"Prozent der Zunahme/Abnahme des Publikums über einen bestimmten Zeitraum."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement_trend
msgid ""
"Percentage of increase/decrease of the engagement over a defined period."
msgstr ""
"Prozent der Zunahme/Abnahme des Publikums über einen bestimmten Zeitraum."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories_trend
msgid "Percentage of increase/decrease of the stories over a defined period."
msgstr ""
"Prozent der Zunahme/Abnahme der Stories über einen bestimmten Zeitraum."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Please specify at least one account to post into (for post ID(s) %s)."
msgstr ""
"Bitte geben Sie mindestens ein Konto an, auf dem gepostet werden soll (für "
"Post-ID(s) %s)."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Please specify either a message or upload some images."
msgstr ""
"Bitte geben Sie entweder eine Nachricht ein oder laden Sie einige Bilder "
"hoch."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post"
msgstr "Posten"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Post Image"
msgstr "Bild posten"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.js:0
#: code:addons/social/static/src/js/post_kanban_view.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/js/stream_post_kanban_renderer.js:0
msgid "Post Images"
msgstr "Bilder posten"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__live_post_link
#: model:ir.model.fields,field_description:social.field_social_stream_post__post_link
msgid "Post Link"
msgstr "Link posten"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "Post Message"
msgstr "Nachricht posten"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post Now"
msgstr "Jetzt posten"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__post_link
msgid ""
"Post link to the external social.media (ex: link to the actual Facebook "
"Post)."
msgstr ""
"Beitragslink zu einem externen social.media (Bsp.: Link zum richtigen "
"Facebook-Beitrag)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Post on"
msgstr "Posten auf"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posted
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posted
msgid "Posted"
msgstr "Gepostet"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posting
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posting
msgid "Posting"
msgstr "Posting"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_post_ids
#: model:ir.ui.menu,name:social.menu_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_kanban
msgid "Posts"
msgstr "Beiträge"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_post_ids
msgid "Posts By Account"
msgstr "Beiträge per Konto"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Posts Preview"
msgstr "Vorschau der Beiträge"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to edit. Press Shift+Enter to insert a Line Break."
msgstr ""
"Drücken Sie Enter, um zu bearbeiten und Shift+Enter, um einen Zeilenumbruch "
"einzufügen."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to post. Press Shift+Enter to insert a Line Break."
msgstr ""
"Drücken Sie Enter, um zu posten und Shift+Enter, um einen Zeilenumbruch "
"einzufügen."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Preview your post"
msgstr "Vorschau Ihres Beitrags"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Previous"
msgstr "Zurück"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__post_method
msgid "Publish your post immediately or schedule it at a later time."
msgstr ""
"Veröffentlichen Sie Ihren Beitrag sofort oder bestimmen Sie einen späteren "
"Zeitpunkt."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Published By"
msgstr "Veröffentlicht von"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__published_date
msgid "Published Date"
msgstr "Veröffentlichungsdatum"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__published_date
msgid "Published date"
msgstr "Veröffentlichungsdatum"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__rating_ids
#: model:ir.model.fields,field_description:social.field_social_post__rating_ids
msgid "Ratings"
msgstr "Bewertungen"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__ready
msgid "Ready"
msgstr "Bereit"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__media_type
msgid "Related Social Media"
msgstr "Ähnliche soziale Medien"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_id
msgid "Related Social Media (Facebook, X, ...)."
msgstr "Ähnliche soziale Medien (Facebook, X ...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__account_id
msgid "Related social Account"
msgstr "Verknüpfte Social-Media-Konten"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Reply"
msgstr "Antworten"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Retry"
msgstr "Wiederholen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Schedule"
msgstr "Planen"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__scheduled
msgid "Schedule later"
msgstr "Für später planen"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__scheduled
msgid "Scheduled"
msgstr "Geplant"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__scheduled_date
msgid "Scheduled Date"
msgstr "Geplantes Datum"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Selected accounts (%(account_list)s) do not match the selected company "
"(%(company)s)"
msgstr ""
"Gewählte Konten (%(account_list)s) passen nicht zum gewählten Unternehmen "
"(%(company)s)"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__now
msgid "Send now"
msgstr "Jetzt senden"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__sequence
msgid "Sequence"
msgstr "Reihenfolge"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream__sequence
msgid "Sequence used to order streams (mainly for the 'Feed' kanban view)"
msgstr ""
"Reihenfolge, die zum Ordnen von Streams verwendet wird (hauptsächlich für "
"die Kanban-Ansicht „Feed“)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__max_post_length
msgid ""
"Set a maximum number of characters can be posted in post. 0 for no limit."
msgstr ""
"Legen Sie eine maximale Anzahl von Zeichen fest, die in einem Beitrag "
"gepostet werden können. 0 für keine Begrenzung."

#. module: social
#: model:ir.actions.act_window,name:social.action_social_global_settings
#: model:ir.ui.menu,name:social.menu_social_global_settings
msgid "Settings"
msgstr "Einstellungen"

#. module: social
#: model:ir.model,name:social.model_social_account
#: model:ir.model.fields,field_description:social.field_social_live_post__account_id
#: model:ir.model.fields,field_description:social.field_social_stream__account_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
msgid "Social Account"
msgstr "Social-Media-Konto"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_account
#: model:ir.model.fields,field_description:social.field_social_media__account_ids
#: model:ir.model.fields,field_description:social.field_social_post__account_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__account_ids
#: model:ir.ui.menu,name:social.menu_social_account
#: model_terms:ir.ui.view,arch_db:social.social_account_view_list
msgid "Social Accounts"
msgstr "Social-Media-Konten"

#. module: social
#: model:ir.model,name:social.model_social_live_post
msgid "Social Live Post"
msgstr "Live-Beitrag in Social Media"

#. module: social
#: model:res.groups,name:social.group_social_manager
msgid "Social Manager"
msgstr "Social-Media-Manager"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_global
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Social Marketing"
msgstr "Social Marketing"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.actions.act_window,name:social.action_social_media
#: model:ir.model,name:social.model_social_media
#: model:ir.model.fields,field_description:social.field_social_account__media_id
#: model:ir.model.fields,field_description:social.field_social_stream__media_id
#: model:ir.model.fields,field_description:social.field_social_stream_type__media_id
#: model:ir.ui.menu,name:social.menu_social_media
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Social Media"
msgstr "Social Media"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Social-Media-Beiträge"

#. module: social
#: model:ir.model,name:social.model_social_post
#: model:ir.model.fields,field_description:social.field_social_live_post__post_id
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
msgid "Social Post"
msgstr "Social-Media-Beitrag"

#. module: social
#: model:ir.model,name:social.model_social_post_template
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Social Post Template"
msgstr "Vorlage für Social-Media-Beiträge"

#. module: social
#: model:ir.actions.act_window,name:social.social_post_template_action
msgid "Social Post Templates"
msgstr "Vorlagen für Social-Media-Beiträge"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_calendar
#: model_terms:ir.ui.view,arch_db:social.social_post_view_pivot
msgid "Social Posts"
msgstr "Social-Media-Beiträge"

#. module: social
#: model:ir.model,name:social.model_social_stream
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_id
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_form
msgid "Social Stream"
msgstr "Social-Media-Stream"

#. module: social
#: model:ir.model,name:social.model_social_stream_post
#: model:ir.model,name:social.model_social_stream_type
msgid "Social Stream Post"
msgstr "Beitrag aus Social-Media-Stream"

#. module: social
#: model:ir.model,name:social.model_social_stream_post_image
msgid "Social Stream Post Image Attachment"
msgstr "Bildanhang eines Beitrags aus Social-Media-Stream"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream
#: model:ir.ui.menu,name:social.menu_social_stream
msgid "Social Streams"
msgstr "Social-Media-Streams"

#. module: social
#: model:res.groups,name:social.group_social_user
msgid "Social User"
msgstr "Social-Media-Benutzer"

#. module: social
#: model:ir.actions.server,name:social.ir_cron_post_scheduled_ir_actions_server
msgid "Social: Publish Scheduled Posts"
msgstr "Social: geplante Beiträge veröffentlichen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments_reply.js:0
msgid "Something went wrong while posting the comment."
msgstr "Beim Veröffentlichen des Kommentars ist etwas schiefgelaufen."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_dashboard.js:0
msgid ""
"Sorry, you're not allowed to re-link this account, please contact your "
"administrator."
msgstr ""
"Es ist Ihnen leider nicht gestattet, dieses Konto erneut zu verknüpfen. "
"Bitte wenden Sie sich an Ihren Administrator."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__source_id
msgid "Source"
msgstr "Quelle"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_posts_by_media
msgid ""
"Special technical field that holds a dict containing the live posts names by"
" media ids (used for kanban view)."
msgstr ""
"Spezielles technisches Feld, das ein dict enthält, das die Namen der Live-"
"Beiträge nach Medien-IDs enthält (wird für die Kanban-Ansicht verwendet)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_post_split_per_media.xml:0
msgid "Split Per Media"
msgstr "Je Medium getrennt"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_split_per_media
#: model:ir.model.fields,field_description:social.field_social_post_template__is_split_per_media
msgid "Split Per Network"
msgstr "Je Netzwerk getrennt"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stats_link
msgid "Stats Link"
msgstr "Statistik-Link"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__state
#: model:ir.model.fields,field_description:social.field_social_post__state
msgid "Status"
msgstr "Status"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basierend auf Aktivitäten\n"
"Überfällig: Fälligkeitsdatum bereits überschritten\n"
"Heute: Aktivitätsdatum ist heute\n"
"Geplant: anstehende Aktivitäten."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__stories
msgid "Stories"
msgstr "Stories"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stories_trend
msgid "Stories Trend"
msgstr "Storys-Trend"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Stream Added (%s)"
msgstr "Stream hinzugefügt (%s)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__stream_post_id
msgid "Stream Post"
msgstr "Stream-Beitrag"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_ids
msgid "Stream Post Images"
msgstr "Stream-Beitragsbilder"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_urls
msgid "Stream Post Images URLs"
msgstr "URLS der Stream-Beitragsbilder"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__stream_type_ids
msgid "Stream Types"
msgstr "Stream-Typen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_type
#: model:ir.model.fields,field_description:social.field_social_stream_type__stream_type
msgid "Stream type name (technical)"
msgstr "Stream-Typname (technisch)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_streams
msgid "Streams Enabled"
msgstr "Streams aktiviert"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_post_ids
msgid "Sub-posts that will be published on each selected social accounts."
msgstr ""
"Unterbeiträge, die auf jedem ausgewählten Social-Media-Konto veröffentlicht "
"werden."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Sync"
msgstr "Synchronisieren"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_ids
#: model:ir.model.fields,help:social.field_social_post_template__account_ids
msgid "The accounts on which this post will be published."
msgstr "Die Konten, auf welchen der Beitrag veröffentlicht wird."

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_name
msgid ""
"The post author name based on third party information (ex: 'John Doe')."
msgstr ""
"Name des Verfassers des Posts basierend auf Informationen Dritter (z. B. "
"„Lieschen Müller“)."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__state
msgid ""
"The post is considered as 'Posted' when all its sub-posts (one per social "
"account) are either 'Failed' or 'Posted'"
msgstr ""
"Der Beitrag gilt als „Gepostet“, wenn alle seine Unterbeiträge (einer pro "
"Social-Media-Konto) entweder „Fehlgeschlagen“ sind oder „Gepostet“ wurden."

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__published_date
msgid "The post published date based on third party information."
msgstr ""
"Das Veröffentlichungsdatum eines Beitrags basierend auf Informationen "
"Dritter."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__failure_reason
msgid ""
"The reason why a post is not successfully posted on the Social Media (eg: "
"connection error, duplicated post, ...)."
msgstr ""
"Der Grund, warum ein Beitrag nicht erfolgreich in den sozialen Medien "
"gepostet wurde (Bsp.: Verbindungsprobleme, doppelter Beitrag ...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__media_image
msgid "The related Social Media's image"
msgstr "Das Bild der verbundenen sozialen Medien."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__media_ids
msgid "The social medias linked to the selected social accounts."
msgstr "Die sozialen Medien, die mit den Social-Media-Konten verbunden sind."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_post_errors
msgid "There are post errors on sub-posts"
msgstr "Es gibt Beitragsfehler in Unterbeiträgen"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__csrf_token
msgid ""
"This token can be used to verify that an incoming request from a social "
"provider has not been forged."
msgstr ""
"Dieses Token kann verwendet werden, um zu überprüfen, dass eine eingehende "
"Anfrage von einem Social-Media-Anbieter nicht gefälscht wurde."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__name
msgid "Title"
msgstr "Titel"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "To add a stream"
msgstr "Um einen Stream hinzuzufügen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_id
msgid "Type"
msgstr "Typ"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ der Ausnahmeaktivität im Datensatz."

#. module: social
#: model:ir.model,name:social.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM-Kampagne"

#. module: social
#: model:ir.model,name:social.model_utm_medium
#: model:ir.model.fields,field_description:social.field_social_account__utm_medium_id
msgid "UTM Medium"
msgstr "UTM-Medium"

#. module: social
#: model:ir.model,name:social.model_utm_source
msgid "UTM Source"
msgstr "UTM-Quelle"

#. module: social
#. odoo-python
#: code:addons/social/controllers/main.py:0
msgid "Uh-oh! It looks like this message has been deleted from X."
msgstr "Oh oh! Diese Nachricht scheint auf X gelöscht worden zu sein."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Unknown error"
msgstr "Unbekannter Fehler"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Uploaded file does not seem to be a valid image."
msgstr "Die hochgeladene Datei scheint kein gültiges Bild zu sein."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Use your own Developer Accounts on our Social app. Those credentials are "
"provided in the developer section of your professional social media account."
msgstr ""
"Verwenden Sie Ihre eigenen Entwicklerkonten auf unseren Social-Apps. Diese "
"Anmeldedaten werden im Entwicklerabschnitt Ihres professionellen Social-"
"Media-Kontos zur Verfügung gestellt."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_type
#: model:ir.model.fields,help:social.field_social_live_post__media_type
#: model:ir.model.fields,help:social.field_social_media__media_type
#: model:ir.model.fields,help:social.field_social_stream_post__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""
"Wird verwendet, um Vergleiche anzustellen, wenn wir einige Funktionen auf "
"ein bestimmtes Medium beschränken müssen („Facebook“, „X“ ...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "View"
msgstr "Ansicht"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__website_message_ids
#: model:ir.model.fields,field_description:social.field_social_post__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__website_message_ids
#: model:ir.model.fields,help:social.field_social_post__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__post_method
msgid "When"
msgstr "Wann"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__published_date
msgid ""
"When the global post was published. The actual sub-posts published dates may"
" be different depending on the media."
msgstr ""
"Wann der globale Beitrag veröffentlicht wurde. Die tatsächlichen "
"Veröffentlichungsdaten der Unterbeiträge können je nach Medium "
"unterschiedlich sein."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__image_ids
#: model:ir.model.fields,help:social.field_social_post_template__image_ids
msgid "Will attach images to your posts (if the social media supports it)."
msgstr ""
"Hängt Bilder an Ihre Beiträge an (wenn die sozialen Medien dies "
"unterstützen)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a comment..."
msgstr "Einen Kommentar schreiben ..."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Write a message or upload an image"
msgstr "Eine Nachricht schreiben oder ein Bild hochladen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Write a message to get a preview of your post."
msgstr ""
"Schreiben Sie eine Nachricht, um eine Vorschau Ihres Beitrags zu erhalten."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a reply..."
msgstr "Schreiben Sie eine Antwort ..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid ""
"Write an enticing post, add images and schedule it to be posted later on "
"multiple platforms at once."
msgstr ""
"Schreiben Sie einen ansprechenden Beitrag, fügen Sie Bilder hinzu und planen"
" Sie ihn für eine spätere Veröffentlichung auf mehreren Plattformen "
"gleichzeitig."

#. module: social
#. odoo-python
#: code:addons/social/models/utm_medium.py:0
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following social accounts in Social:\n"
"%(social_accounts)s"
msgstr ""
"Sie können diese UTM-Medien nicht löschen, da sie mit den folgenden sozialen Konten in Social verknüpft sind:\n"
"%(social_accounts)s"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to social posts in Social:\n"
"%(utm_sources)s"
msgstr ""
"Sie können diese UTM-Quellen nicht löschen, da sie mit den folgenden Beiträgen in Social verknüpft sind:\n"
"%(utm_sources)s"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot reschedule a post that has already been posted."
msgstr "Sie können einen Beitrag, der bereits gepostet wurde, nicht umplanen."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot schedule a post in the past."
msgstr "Sie können einen Beitrag nicht in der Vergangenheit planen."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Your Post"
msgstr "Ihr Beitrag"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "a Stream from an existing account"
msgstr "einen Stream von einem existierenden Konto hinzu"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "before posting."
msgstr ", bevor Sie posten."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "comment/reply"
msgstr "kommentieren/antworten"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "dashboard"
msgstr "Dashboard"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "for"
msgstr "für"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "replies..."
msgstr "Antworten ..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "to keep an eye on your own posts and monitor all social activities."
msgstr ""
", um Ihre eigenen Beiträge im Auge und den Überblick über all Ihre Social-"
"Media-Aktivitäten zu behalten."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "to link your accounts and start posting."
msgstr ", Ihre Konten zu verknüpfen und mit dem Posten zu beginnen."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "to start posting from Odoo."
msgstr ", um damit zu beginnen, aus Odoo heraus zu posten."
