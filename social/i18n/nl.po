# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__accounts_count
msgid "# Accounts"
msgstr "# Accounts"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "%(media_name)s (max %(max_chars)s chars)"
msgstr "%(media_name)s (max %(max_chars)s tekens)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ", you must first link a social media."
msgstr ", moet je eerst een social media-account koppelen."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<b>An error occurred while trying to link your account</b>"
msgstr "<b>Er is een fout opgetreden bij het koppelen van je account.</b>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr "<i class=\"fa fa-calendar me-1 small fw-bold\" title=\"Calendar\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "<i class=\"fa fa-calendar-check-o me-1 small fw-bold\" title=\"Calendar\"/>"
msgstr "<i class=\"fa fa-calendar-check-o me-1 small fw-bold\" title=\"Calendar\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "<i class=\"fa fa-globe ms-1\" title=\"See the post\"/>"
msgstr "<i class=\"fa fa-globe ms-1\" title=\"See the post\"/>"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_http_error_view
msgid "<i class=\"oi oi-arrow-left\"/> Go back to Odoo"
msgstr "<i class=\"oi oi-arrow-left\"/> Terug naar Odoo"

#. module: social
#: model_terms:web_tour.tour,rainbow_man_message:social.social_tour
msgid ""
"<strong>Congrats! Come back in a few minutes to check your "
"statistics.</strong>"
msgstr ""
"<strong>Gefeliciteerd! Kom over een paar minuten terug om je statistieken te"
" controleren.</strong>"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__active
msgid "Active"
msgstr "Actief"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_ids
msgid "Activities"
msgstr "Activiteiten"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activiteit uitzondering decoratie"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_state
msgid "Activity State"
msgstr "Activiteitsfase"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Activiteitensoort icoon"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add"
msgstr "Toevoegen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Add Post"
msgstr "Bericht toevoegen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add Stream"
msgstr "Voeg een stream toe"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/add_stream_modal.js:0
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Add a Stream"
msgstr "Stream toevoegen"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "Add a stream"
msgstr "Stream toevoegen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Add an image"
msgstr "Voeg een afbeelding toe"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "All Companies"
msgstr "Alle bedrijven"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Alle gerelateerde socialmedia-posts"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__account_allowed_ids
msgid "Allowed Accounts"
msgstr "Toegestane accounts"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_active_accounts
#: model:ir.model.fields,field_description:social.field_social_post_template__has_active_accounts
msgid "Are Accounts Available?"
msgstr "Zijn accounts beschikbaar?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__image_ids
msgid "Attach Images"
msgstr "Afbeeldingen toevoegen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__image_ids
msgid "Attached Images"
msgstr "Bijgevoegde afbeeldingen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_attachment_count
#: model:ir.model.fields,field_description:social.field_social_post__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__audience
msgid "Audience"
msgstr "Publiek"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__audience_trend
msgid "Audience Trend"
msgstr "Trend publiek"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_link
msgid "Author Link"
msgstr "Link auteur"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__author_name
msgid "Author Name"
msgstr "Naam auteur"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_link
msgid "Author link to the external social.media (ex: link to the X Account)."
msgstr ""
"Auteurlink naar de externe social.media (bijvoorbeeld: link naar het "
"X-account)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Before posting, links will be converted to be trackable."
msgstr ""
"Voordat het wordt geplaatst, worden links omgezet om traceerbaar te zijn."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "By Stream"
msgstr "Per stream"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__csrf_token
msgid "CSRF Token"
msgstr "CSRF-token"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__calendar_date
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Calendar Date"
msgstr "Kalenderdatum"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form_quick_create_social
msgid "Campaign"
msgstr "Campagne"

#. module: social
#: model:ir.actions.act_window,name:social.action_view_utm_campaigns
#: model:ir.ui.menu,name:social.menu_social_campaign
msgid "Campaigns"
msgstr "Campagnes"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Campagnes worden gebruikt om je marketinginspanningen te centraliseren en de"
" resultaten daarvan bij te houden."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__can_link_accounts
msgid "Can link accounts?"
msgstr "Kan accounts koppelen?"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Cancel"
msgstr "Annuleren"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Choose which <b>account</b> you would like to link first."
msgstr "Kies welk <b>account</b> je als eerste wilt koppelen."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Click to refresh."
msgstr "Klik om te vernieuwen."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Clicks"
msgstr "Klikken"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Clicks:"
msgstr "Aantal klikken:"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Comment Image"
msgstr "Opmerking afbeelding"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__company_id
#: model:ir.model.fields,field_description:social.field_social_live_post__company_id
#: model:ir.model.fields,field_description:social.field_social_post__company_id
#: model:ir.model.fields,field_description:social.field_social_stream__company_id
#: model:ir.model.fields,field_description:social.field_social_stream_post__company_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Company"
msgstr "Bedrijf"

#. module: social
#: model:ir.model,name:social.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_configuration
msgid "Configuration"
msgstr "Configuratie"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Connecting Problem"
msgstr "Probleem met verbinden"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__social_account_handle
msgid ""
"Contains the social media handle of the person that created this account. "
"E.g: '@odoo.official' for the 'Odoo' X account"
msgstr ""
"Bevat de social media-handle van de persoon die dit account heeft "
"aangemaakt. Bijvoorbeeld: '@odoo.official' voor het 'Odoo' X-account"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__message
msgid ""
"Content of the social post message that is post-processed (links are "
"shortened, UTMs, ...)"
msgstr ""
"Inhoud van de social post bericht dat nabewerkt wordt (links worden "
"ingekort, UTM's, ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__has_streams
msgid "Controls if social streams are handled on this social media."
msgstr ""
"Bepaald of socialmedia-streams afgehandeld worden op dit social medium."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__can_link_accounts
msgid "Controls if we can link accounts or not."
msgstr "Bepaalt of we accounts kunnen koppelen of niet."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_view_utm_campaigns
msgid "Create a Campaign"
msgstr "Een campagne maken"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid "Create a Post"
msgstr "Maak een bericht"

#. module: social
#. odoo-python
#: code:addons/social/models/social_account.py:0
msgid ""
"Create other accounts for %(media_names)s for this company or ask "
"%(company_names)s to share their accounts"
msgstr ""
"Maak andere accounts voor %(media_names)s voor dit bedrijf of vraag "
"%(company_names)s om hun accounts te delen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_media__create_uid
#: model:ir.model.fields,field_description:social.field_social_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__create_date
#: model:ir.model.fields,field_description:social.field_social_live_post__create_date
#: model:ir.model.fields,field_description:social.field_social_media__create_date
#: model:ir.model.fields,field_description:social.field_social_post__create_date
#: model:ir.model.fields,field_description:social.field_social_post_template__create_date
#: model:ir.model.fields,field_description:social.field_social_stream__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__create_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_account_stats
msgid ""
"Defines whether this account has Audience/Engagements/Stories stats.\n"
"        Account with stats are displayed on the dashboard."
msgstr ""
"Bepaalt of dit account statistieken over doelgroep / betrokkenheid / verhalen heeft.\n"
"Accounts met statistieken worden weergegeven op het dashboard."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__has_trends
msgid "Defines whether this account has statistics tends or not."
msgstr "Bepaalt of dit account statistische trends heeft of niet."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Delete"
msgstr "Verwijderen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Delete Comment"
msgstr "Verwijder reactie"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Delete Post"
msgstr "Post verwijderen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Demo Mode"
msgstr "Demomodus"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__media_description
msgid "Description"
msgstr "Omschrijving"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Developer Accounts"
msgstr "Ontwikkelaarsaccounts"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Discard"
msgstr "Negeren"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__display_message
#: model:ir.model.fields,field_description:social.field_social_post_template__display_message
msgid "Display Message"
msgstr "Toon bericht"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__display_name
#: model:ir.model.fields,field_description:social.field_social_live_post__display_name
#: model:ir.model.fields,field_description:social.field_social_media__display_name
#: model:ir.model.fields,field_description:social.field_social_post__display_name
#: model:ir.model.fields,field_description:social.field_social_post_template__display_name
#: model:ir.model.fields,field_description:social.field_social_stream__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__display_name
#: model:ir.model.fields,field_description:social.field_social_stream_type__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "Do you really want to delete this %s?"
msgstr "Wil je %s echt verwijderen?"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments.js:0
msgid "Do you really want to delete this Post ?"
msgstr "Wil je deze post echt verwijderen?"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__draft
msgid "Draft"
msgstr "Concept"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Due to length restrictions, the following posts cannot be posted:\n"
" %s"
msgstr ""
"Vanwege lengtebeperkingen kunnen de volgende berichten niet worden geplaatst:\n"
" %s"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit"
msgstr "Bewerken"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Edit Your Post"
msgstr "Je post bewerken"

#. module: social
#: model:ir.model.fields,field_description:social.field_res_config_settings__module_social_demo
msgid "Enable Demo Mode"
msgstr "Demomodus inschakelen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Enable this option and load demo data to test the social module. This must "
"never be used on a production database!"
msgstr ""
"Schakel deze optie in en laad demogegevens om de sociale module te testen. "
"Dit mag nooit worden gebruikt op een productiedatabase!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__engagement
#: model:ir.model.fields,field_description:social.field_social_live_post__engagement
#: model:ir.model.fields,field_description:social.field_social_post__engagement
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Engagement"
msgstr "Betrokkenheid"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__engagement_trend
msgid "Engagement Trend"
msgstr "Betrokkenheid trend"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Engagement:"
msgstr "Betrokkenheid:"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__utm_medium_id
msgid ""
"Every time an account is created, a utm.medium is also created and linked to"
" the account"
msgstr ""
"Elke keer dat een account wordt gemaakt, wordt er ook een utm.medium gemaakt"
" en aan het account gekoppeld"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__failed
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Failed"
msgstr "Mislukt"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__failure_reason
msgid "Failure Reason"
msgstr "Reden mislukt"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream_post
#: model:ir.ui.menu,name:social.menu_social_stream_post
msgid "Feed"
msgstr "Feed"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Feed Posts"
msgstr "Feed posts"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__stream_posts_count
msgid "Feed Posts Count"
msgstr "Aantal feed posts"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_follower_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_partner_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Partners)"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icoon bijv. fa-tasks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__formatted_published_date
msgid "Formatted Published Date"
msgstr "Geformatteerde publicatiedatum"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience
msgid ""
"General audience of the Social Account (Page Likes, Account Follows, ...)."
msgstr ""
"Algemeen publiek van het sociale account (Pagina-likes, volgers, ...)."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "Go to the"
msgstr "Ga naar de"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_search
msgid "Group By"
msgstr "Groeperen op"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__social_account_handle
msgid "Handle / Short Name"
msgstr "Gebruikersnaam / korte naam"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Happy with the result? Let's post it!"
msgstr "Blij met het resultaat? Laten we het posten!"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_account_stats
msgid "Has Account Stats"
msgstr "Heeft account statistieken"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_message
#: model:ir.model.fields,field_description:social.field_social_post__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__has_trends
msgid "Has Trends?"
msgstr "Heeft trends?"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_hatched
msgid "Hatched"
msgstr "Gearceerd"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__id
#: model:ir.model.fields,field_description:social.field_social_live_post__id
#: model:ir.model.fields,field_description:social.field_social_media__id
#: model:ir.model.fields,field_description:social.field_social_post__id
#: model:ir.model.fields,field_description:social.field_social_post_template__id
#: model:ir.model.fields,field_description:social.field_social_stream__id
#: model:ir.model.fields,field_description:social.field_social_stream_post__id
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__id
#: model:ir.model.fields,field_description:social.field_social_stream_type__id
msgid "ID"
msgstr "ID"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_exception_icon
msgid "Icon"
msgstr "Icoon"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icoon om uitzondering op activiteit aan te geven."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction
#: model:ir.model.fields,help:social.field_social_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error
#: model:ir.model.fields,help:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,help:social.field_social_post__message_has_error
#: model:ir.model.fields,help:social.field_social_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Indien aangevinkt hebben sommige berichten een leveringsfout."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__image
#: model:ir.model.fields,field_description:social.field_social_media__image
msgid "Image"
msgstr "Afbeelding"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__image_url
msgid "Image URL"
msgstr "Afbeelding URL"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Image Url"
msgstr "Afbeelding URL"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__image_urls
#: model:ir.model.fields,field_description:social.field_social_post_template__image_urls
msgid "Images URLs"
msgstr "Afbeeldingen URL's"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__stream_post_image_ids
msgid "Images that were shared with this post."
msgstr "Afbeeldingen die gedeeld zijn met deze post."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Insights"
msgstr "Inzichten"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__is_author
msgid "Is Author"
msgstr "Is auteur"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_is_follower
#: model:ir.model.fields,field_description:social.field_social_post__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"It appears there is an issue with the Social Media link, click here to link "
"the account again"
msgstr ""
"Er lijkt een probleem te zijn met de socialmedia-link, klik hier om het "
"account opnieuw te koppelen"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "It will appear in the Feed once it has posts to display."
msgstr "Het verschijnt in de feed zodra het berichten heeft om weer te geven."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_uid
#: model:ir.model.fields,field_description:social.field_social_live_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_media__write_uid
#: model:ir.model.fields,field_description:social.field_social_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_post_template__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_uid
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__write_date
#: model:ir.model.fields,field_description:social.field_social_live_post__write_date
#: model:ir.model.fields,field_description:social.field_social_media__write_date
#: model:ir.model.fields,field_description:social.field_social_post__write_date
#: model:ir.model.fields,field_description:social.field_social_post_template__write_date
#: model:ir.model.fields,field_description:social.field_social_stream__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__write_date
#: model:ir.model.fields,field_description:social.field_social_stream_type__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's <b>connect</b> to Facebook, LinkedIn or X."
msgstr "Laten we <b>verbinding maken</b> met Facebook, LinkedIn of X."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's create your own <b>social media</b> dashboard."
msgstr "Laten we je eigen dashboard voor <b>sociale media</b> maken."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Let's start posting."
msgstr "Laten we beginnen met posten."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Like"
msgstr "Vind-ik-leuk"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Likes"
msgstr "Vind-ik-leuks"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_description
msgid "Link Description"
msgstr "Link omschrijving"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Link Image"
msgstr "Link afbeelding"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_image_url
msgid "Link Image URL"
msgstr "Link afbeelding URL"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_title
msgid "Link Title"
msgstr "Link titel"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__link_url
msgid "Link URL"
msgstr "Link URL"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Link a new account"
msgstr "Een nieuw account koppelen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
msgid "Link account"
msgstr "Account koppelen"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "Link an Account"
msgstr "Een account koppelen"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__company_id
#: model:ir.model.fields,help:social.field_social_live_post__company_id
#: model:ir.model.fields,help:social.field_social_stream__company_id
#: model:ir.model.fields,help:social.field_social_stream_post__company_id
msgid ""
"Link an account to a company to restrict its usage or keep empty to let all "
"companies use it."
msgstr ""
"Koppel een account aan een bedrijf om het gebruik ervan te beperken of houd "
"het leeg zodat alle bedrijven het kunnen gebruiken."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__live_post_link
msgid "Link of the live post on the target media."
msgstr "Link van de live post op de doelmedia."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Link social accounts"
msgstr "Socialmedia-accounts koppelen"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stats_link
msgid "Link to the external Social Account statistics"
msgstr "Link naar de externe socialmedia-account statistieken"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__is_media_disconnected
msgid "Link with external Social Media is broken"
msgstr "Link met externe sociale media is verbroken"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_allowed_ids
msgid "List of the accounts which can be selected for this post."
msgstr ""
"Lijst van de accounts die voor dit bericht kunnen worden geselecteerd."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_posts_by_media
msgid "Live Posts by Social Media"
msgstr "Live posts via socialmedia"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Load more comments..."
msgstr "Laad meer reacties..."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__max_post_length
msgid "Max Post Length"
msgstr "Maximale berichtlengte"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__media_type
#: model:ir.model.fields,field_description:social.field_social_post__media_ids
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
msgid "Media"
msgstr "Media"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__media_count
#: model:ir.model.fields,field_description:social.field_social_post_template__media_count
msgid "Media Count"
msgstr "Mediatelling"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__media_type
#: model:ir.model.fields,field_description:social.field_social_media__media_type
msgid "Media Type"
msgstr "Soort media"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__message
#: model:ir.model.fields,field_description:social.field_social_post__message
#: model:ir.model.fields,field_description:social.field_social_post_template__message
#: model:ir.model.fields,field_description:social.field_social_stream_post__message
msgid "Message"
msgstr "Bericht"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Message posted"
msgstr "Bericht geplaatst"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Message posted partially. These are the ones that couldn't be posted:%s"
msgstr ""
"Het bericht is gedeeltelijk gepubliceerd. Dit zijn de berichten die niet "
"gepubliceerd konden worden:%s"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_ids
#: model:ir.model.fields,field_description:social.field_social_post__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__state
msgid ""
"Most social.live.posts directly go from Ready to Posted/Failed since they result of a single call to the third party API.\n"
"        A 'Posting' state is also available for those that are sent through batching (like push notifications)."
msgstr ""
"De meeste social.live.posts gaan direct van Gereed naar Geplaatst / Mislukt omdat ze het resultaat zijn van een enkele oproep naar de API van derden.\n"
"Een status 'Verzenden' is ook beschikbaar voor diegenen die via batching worden verzonden (zoals pushmeldingen)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mijn activiteit deadline"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "My Posts"
msgstr "Mijn berichten"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "My Streams"
msgstr "Mijn streams"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__name
#: model:ir.model.fields,field_description:social.field_social_media__name
#: model:ir.model.fields,field_description:social.field_social_post__name
#: model:ir.model.fields,field_description:social.field_social_stream_type__name
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
#: model_terms:ir.ui.view,arch_db:social.social_account_view_search
msgid "Name"
msgstr "Naam"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New Post"
msgstr "Nieuwe post"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "New content available"
msgstr "Nieuwe inhoud beschikbaar"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Next"
msgstr "Volgende"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Volgende activiteitenafspraak"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Volgende activiteit deadline"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_summary
msgid "Next Activity Summary"
msgstr "Volgende activiteit overzicht"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_type_id
msgid "Next Activity Type"
msgstr "Volgende activiteit type"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "No Social Account yet!"
msgstr "Nog geen sociaal account!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "No Social Streams yet!"
msgstr "Nog geen socialmedia-streams!"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "No Stream added yet!"
msgstr "Nog geen stream toegevoegd!"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "No comments yet."
msgstr "Nog geen reacties."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "No social accounts configured, please contact your administrator."
msgstr ""
"Geen socialmedia-accounts geconfigureerd. Neem contact op met je beheerder."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Number of Followers of your channel"
msgstr "Aantal volgers van je kanaal"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__click_count
msgid "Number of clicks"
msgstr "Aantal klikken"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,field_description:social.field_social_post__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Aantal interacties (likes, shares, comments...) met de socialmedia-posts"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_needaction_counter
#: model:ir.model.fields,help:social.field_social_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Aantal berichten die actie vereisen"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__message_has_error_counter
#: model:ir.model.fields,help:social.field_social_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__engagement
#: model:ir.model.fields,help:social.field_social_post__engagement
msgid "Number of people engagements with the post (Likes, comments...)"
msgstr ""
"Aantal mensen dat interactie is aangegaan met de post (likes, comments, ...)"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement
msgid "Number of people engagements with your posts (Likes, Comments, ...)."
msgstr "Aantal mensenbetrokkenheid bij je berichten (Likes, Reacties, ...)."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories
msgid "Number of stories created from your posts (Shares, Reposts, ...)."
msgstr "Aantal verhalen gemaakt vanuit je posts (Gedeeld, reposts, ...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people have engaged with your posts (likes, comments, "
"shares,...)"
msgstr ""
"Aantal keren dat mensen betrokken zijn geweest bij je berichten (likes, "
"comments, shares, ...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid ""
"Number of times people who have engaged with your channel have created "
"stories on their friends' or followers' feed (Shares, Reposts...)"
msgstr ""
"Aantal keren dat mensen die betrokken zijn geweest met je kanaal verhalen "
"hebben gemaakt in de feed van hun vrienden of volgers (Shares, Reposts ...)"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Or add"
msgstr "Of voeg toe"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__audience_trend
msgid "Percentage of increase/decrease of the audience over a defined period."
msgstr ""
"Percentage toename / afname van het publiek gedurende een gedefinieerde "
"periode."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__engagement_trend
msgid ""
"Percentage of increase/decrease of the engagement over a defined period."
msgstr ""
"Percentage toename / afname van de betrokkenheid gedurende een gedefinieerde"
" periode."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__stories_trend
msgid "Percentage of increase/decrease of the stories over a defined period."
msgstr ""
"Percentage toename / afname van de verhalen gedurende een gedefinieerde "
"periode."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Please specify at least one account to post into (for post ID(s) %s)."
msgstr ""
"Geef ten minste één account op om naar te posten (voor bericht-ID('s) %s)."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Please specify either a message or upload some images."
msgstr "Geef een bericht op of upload een aantal afbeeldingen."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post"
msgstr "Publiceren"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.xml:0
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_kanban
msgid "Post Image"
msgstr "Plaats afbeelding"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_many2many_images.js:0
#: code:addons/social/static/src/js/post_kanban_view.js:0
#: code:addons/social/static/src/js/stream_post_comments.js:0
#: code:addons/social/static/src/js/stream_post_kanban_renderer.js:0
msgid "Post Images"
msgstr "Plaats afbeeldingen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__live_post_link
#: model:ir.model.fields,field_description:social.field_social_stream_post__post_link
msgid "Post Link"
msgstr "Plaats link"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
#: model_terms:ir.ui.view,arch_db:social.social_stream_post_view_search
msgid "Post Message"
msgstr "Plaats bericht"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Post Now"
msgstr "Plaats nu"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__post_link
msgid ""
"Post link to the external social.media (ex: link to the actual Facebook "
"Post)."
msgstr ""
"Plaats een link naar de externe social media (bijvoorbeeld: link naar de "
"eigenlijke Facebook-post)."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Post on"
msgstr "Publiceren op"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posted
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posted
msgid "Posted"
msgstr "Geplaatst"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__posting
#: model:ir.model.fields.selection,name:social.selection__social_post__state__posting
msgid "Posting"
msgstr "Plaatsen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_post_ids
#: model:ir.ui.menu,name:social.menu_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_kanban
msgid "Posts"
msgstr "Posts"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__live_post_ids
msgid "Posts By Account"
msgstr "Posts per account"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Posts Preview"
msgstr "Berichten Voorbeeld"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to edit. Press Shift+Enter to insert a Line Break."
msgstr ""
"Druk op Enter om te bewerken. Druk op Shift+Enter om een regeleinde in te "
"voegen."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Press Enter to post. Press Shift+Enter to insert a Line Break."
msgstr ""
"Druk op Enter om te posten. Druk op Shift+Enter om een regeleinde in te "
"voegen."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Preview your post"
msgstr "Bekijk voorvertoning van je post"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Previous"
msgstr "Vorige"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__post_method
msgid "Publish your post immediately or schedule it at a later time."
msgstr "Publiceer je bericht onmiddellijk of plan het op een later tijdstip."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_search
msgid "Published By"
msgstr "Gepubliceerd door"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__published_date
msgid "Published Date"
msgstr "Publicatiedatum"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__published_date
msgid "Published date"
msgstr "Datum gepubliceerd"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__rating_ids
#: model:ir.model.fields,field_description:social.field_social_post__rating_ids
msgid "Ratings"
msgstr "Beoordelingen"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_live_post__state__ready
msgid "Ready"
msgstr "Beschikbaar"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__media_type
msgid "Related Social Media"
msgstr "Gerelateerde social media"

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_id
msgid "Related Social Media (Facebook, X, ...)."
msgstr "Gerelateerde social media (Facebook, X, ...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__account_id
msgid "Related social Account"
msgstr "Gerelateerd social account"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Reply"
msgstr "Beantwoorden"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__activity_user_id
msgid "Responsible User"
msgstr "Verantwoordelijke gebruiker"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Retry"
msgstr "Opnieuw proberen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__message_has_sms_error
#: model:ir.model.fields,field_description:social.field_social_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS fout bij versturen"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_view_form
msgid "Schedule"
msgstr "Inplannen"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__scheduled
msgid "Schedule later"
msgstr "Later plannen"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__state__scheduled
msgid "Scheduled"
msgstr "Gepland"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__scheduled_date
msgid "Scheduled Date"
msgstr "Geplande datum"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid ""
"Selected accounts (%(account_list)s) do not match the selected company "
"(%(company)s)"
msgstr ""
"Geselecteerde accounts (%(account_list)s) komen niet overeen met het "
"geselecteerde bedrijf (%(company)s)"

#. module: social
#: model:ir.model.fields.selection,name:social.selection__social_post__post_method__now
msgid "Send now"
msgstr "Nu verzenden"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream__sequence
msgid "Sequence used to order streams (mainly for the 'Feed' kanban view)"
msgstr ""
"Volgorde gebruikt om streams te bestellen (voornamelijk voor de "
"kanbanweergave 'Feed')."

#. module: social
#: model:ir.model.fields,help:social.field_social_media__max_post_length
msgid ""
"Set a maximum number of characters can be posted in post. 0 for no limit."
msgstr ""
"Stel een maximum aantal tekens in dat in een bericht kan worden geplaatst. 0"
" voor geen limiet."

#. module: social
#: model:ir.actions.act_window,name:social.action_social_global_settings
#: model:ir.ui.menu,name:social.menu_social_global_settings
msgid "Settings"
msgstr "Instellingen"

#. module: social
#: model:ir.model,name:social.model_social_account
#: model:ir.model.fields,field_description:social.field_social_live_post__account_id
#: model:ir.model.fields,field_description:social.field_social_stream__account_id
#: model_terms:ir.ui.view,arch_db:social.social_account_view_form
msgid "Social Account"
msgstr "Social account"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_account
#: model:ir.model.fields,field_description:social.field_social_media__account_ids
#: model:ir.model.fields,field_description:social.field_social_post__account_ids
#: model:ir.model.fields,field_description:social.field_social_post_template__account_ids
#: model:ir.ui.menu,name:social.menu_social_account
#: model_terms:ir.ui.view,arch_db:social.social_account_view_list
msgid "Social Accounts"
msgstr "Socialmedia-accounts"

#. module: social
#: model:ir.model,name:social.model_social_live_post
msgid "Social Live Post"
msgstr "Socialmedia-livepost"

#. module: social
#: model:res.groups,name:social.group_social_manager
msgid "Social Manager"
msgstr "Social beheerder"

#. module: social
#: model:ir.ui.menu,name:social.menu_social_global
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid "Social Marketing"
msgstr "Social marketing"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.actions.act_window,name:social.action_social_media
#: model:ir.model,name:social.model_social_media
#: model:ir.model.fields,field_description:social.field_social_account__media_id
#: model:ir.model.fields,field_description:social.field_social_stream__media_id
#: model:ir.model.fields,field_description:social.field_social_stream_type__media_id
#: model:ir.ui.menu,name:social.menu_social_media
#: model_terms:ir.ui.view,arch_db:social.social_media_view_kanban
#: model_terms:ir.ui.view,arch_db:social.utm_campaign_view_form
msgid "Social Media"
msgstr "Social media"

#. module: social
#: model:ir.model.fields,field_description:social.field_utm_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Socialmedia-posts"

#. module: social
#: model:ir.model,name:social.model_social_post
#: model:ir.model.fields,field_description:social.field_social_live_post__post_id
#: model_terms:ir.ui.view,arch_db:social.social_live_post_view_form
msgid "Social Post"
msgstr "Socialmedia-post"

#. module: social
#: model:ir.model,name:social.model_social_post_template
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Social Post Template"
msgstr "Socialmedia-postsjabloon"

#. module: social
#: model:ir.actions.act_window,name:social.social_post_template_action
msgid "Social Post Templates"
msgstr "Socialmedia-postsjablonen"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_post
#: model_terms:ir.ui.view,arch_db:social.social_post_view_calendar
#: model_terms:ir.ui.view,arch_db:social.social_post_view_pivot
msgid "Social Posts"
msgstr "Socialmedia-posts"

#. module: social
#: model:ir.model,name:social.model_social_stream
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_id
#: model_terms:ir.ui.view,arch_db:social.social_stream_view_form
msgid "Social Stream"
msgstr "Socialmedia-stream"

#. module: social
#: model:ir.model,name:social.model_social_stream_post
#: model:ir.model,name:social.model_social_stream_type
msgid "Social Stream Post"
msgstr "Socialmedia-post"

#. module: social
#: model:ir.model,name:social.model_social_stream_post_image
msgid "Social Stream Post Image Attachment"
msgstr "Socialmedia-post afbeeldingbijlage"

#. module: social
#: model:ir.actions.act_window,name:social.action_social_stream
#: model:ir.ui.menu,name:social.menu_social_stream
msgid "Social Streams"
msgstr "Socialmedia-streams"

#. module: social
#: model:res.groups,name:social.group_social_user
msgid "Social User"
msgstr "Sociale gebruiker"

#. module: social
#: model:ir.actions.server,name:social.ir_cron_post_scheduled_ir_actions_server
msgid "Social: Publish Scheduled Posts"
msgstr "Social: Geplande berichten publiceren"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comments_reply.js:0
msgid "Something went wrong while posting the comment."
msgstr "Er is een fout opgetreden tijdens het plaatsen van de reactie."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_dashboard.js:0
msgid ""
"Sorry, you're not allowed to re-link this account, please contact your "
"administrator."
msgstr ""
"Sorry, je mag dit account niet opnieuw koppelen, neem contact op met je "
"beheerder."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__source_id
msgid "Source"
msgstr "Bron"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_posts_by_media
msgid ""
"Special technical field that holds a dict containing the live posts names by"
" media ids (used for kanban view)."
msgstr ""
"Speciaal technisch veld dat een dict bevat met de namen van live berichten "
"van media-id's (gebruikt voor kanban-weergave)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/fields/social_post_split_per_media.xml:0
msgid "Split Per Media"
msgstr "Splits per media"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__is_split_per_media
#: model:ir.model.fields,field_description:social.field_social_post_template__is_split_per_media
msgid "Split Per Network"
msgstr "Opsplitsen per netwerk"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stats_link
msgid "Stats Link"
msgstr "Statistieken link"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_live_post__state
#: model:ir.model.fields,field_description:social.field_social_post__state
msgid "Status"
msgstr "Status"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status gebaseerd op activiteiten\n"
"Te laat: Datum is al gepasseerd\n"
"Vandaag: Activiteit datum is vandaag\n"
"Gepland: Toekomstige activiteiten."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
#: model:ir.model.fields,field_description:social.field_social_account__stories
msgid "Stories"
msgstr "Verhalen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_account__stories_trend
msgid "Stories Trend"
msgstr "Verhalentrend"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_kanban_controller.js:0
msgid "Stream Added (%s)"
msgstr "Stream toegevoegd (%s)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post_image__stream_post_id
msgid "Stream Post"
msgstr "Stream post"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_ids
msgid "Stream Post Images"
msgstr "Stream post afbeeldingen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream_post__stream_post_image_urls
msgid "Stream Post Images URLs"
msgstr "Stream post afbeeldingen url's"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__stream_type_ids
msgid "Stream Types"
msgstr "Soorten streams"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_type
#: model:ir.model.fields,field_description:social.field_social_stream_type__stream_type
msgid "Stream type name (technical)"
msgstr "Soort stream naam (technisch)"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__has_streams
msgid "Streams Enabled"
msgstr "Streams ingeschakeld"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__live_post_ids
msgid "Sub-posts that will be published on each selected social accounts."
msgstr ""
"Subposts die worden gepubliceerd op elke geselecteerde socialmedia-account."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Sync"
msgstr "Sync"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__account_ids
#: model:ir.model.fields,help:social.field_social_post_template__account_ids
msgid "The accounts on which this post will be published."
msgstr "Het account waarop de post geplaatst wordt."

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__author_name
msgid ""
"The post author name based on third party information (ex: 'John Doe')."
msgstr ""
"De naam van de auteur van het bericht op basis van informatie van derden "
"(bijvoorbeeld: 'John Doe')."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__state
msgid ""
"The post is considered as 'Posted' when all its sub-posts (one per social "
"account) are either 'Failed' or 'Posted'"
msgstr ""
"Het bericht wordt beschouwd als 'Geplaatst' wanneer alle sub-berichten (één "
"per socialmedia account) 'Mislukt' of 'Geplaatst' zijn"

#. module: social
#: model:ir.model.fields,help:social.field_social_stream_post__published_date
msgid "The post published date based on third party information."
msgstr "De gepubliceerde datum op basis van informatie van derden."

#. module: social
#: model:ir.model.fields,help:social.field_social_live_post__failure_reason
msgid ""
"The reason why a post is not successfully posted on the Social Media (eg: "
"connection error, duplicated post, ...)."
msgstr ""
"De reden waarom een bericht niet succesvol op de sociale media is geplaatst "
"(bijvoorbeeld: verbindingsfout, dubbel bericht, ...)."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__media_image
msgid "The related Social Media's image"
msgstr "De gerelateerde socialmedia afbeelding"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__media_ids
msgid "The social medias linked to the selected social accounts."
msgstr ""
"De socialmedia die zijn gekoppeld aan de geselecteerde socialmedia-accounts."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__has_post_errors
msgid "There are post errors on sub-posts"
msgstr "Er zijn postfouten op subposts"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__csrf_token
msgid ""
"This token can be used to verify that an incoming request from a social "
"provider has not been forged."
msgstr ""
"Dit token kan worden gebruikt om te controleren of een binnenkomend verzoek "
"van een sociale provider niet is vervalst."

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__name
msgid "Title"
msgstr "Titel"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "To add a stream"
msgstr "Om een stream toevoegen"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_stream__stream_type_id
msgid "Type"
msgstr "Soort"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type van de geregistreerde uitzonderingsactiviteit."

#. module: social
#: model:ir.model,name:social.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM Campagne"

#. module: social
#: model:ir.model,name:social.model_utm_medium
#: model:ir.model.fields,field_description:social.field_social_account__utm_medium_id
msgid "UTM Medium"
msgstr "UTM Medium"

#. module: social
#: model:ir.model,name:social.model_utm_source
msgid "UTM Source"
msgstr "UTM Bron"

#. module: social
#. odoo-python
#: code:addons/social/controllers/main.py:0
msgid "Uh-oh! It looks like this message has been deleted from X."
msgstr "Uh-oh! Het lijkt erop dat dit bericht van X is verwijderd."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "Unknown error"
msgstr "Onbekende fout"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post_template.py:0
msgid "Uploaded file does not seem to be a valid image."
msgstr "Geüpload bestand lijkt geen geldige afbeelding."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.res_config_settings_view_form
msgid ""
"Use your own Developer Accounts on our Social app. Those credentials are "
"provided in the developer section of your professional social media account."
msgstr ""
"Gebruik je eigen ontwikkelaarsaccounts op onze Social-app. Die inloggegevens"
" kan je vinden in de ontwikkelaarsgedeelte van je professionele socialmedia-"
"account."

#. module: social
#: model:ir.model.fields,help:social.field_social_account__media_type
#: model:ir.model.fields,help:social.field_social_live_post__media_type
#: model:ir.model.fields,help:social.field_social_media__media_type
#: model:ir.model.fields,help:social.field_social_stream_post__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""
"Wordt gebruikt om vergelijkingen te maken wanneer we sommige functies moeten"
" beperken tot een specifiek medium ('facebook', 'X', ...)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "View"
msgstr "Bekijk"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_media__website_message_ids
#: model:ir.model.fields,field_description:social.field_social_post__website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: social
#: model:ir.model.fields,help:social.field_social_media__website_message_ids
#: model:ir.model.fields,help:social.field_social_post__website_message_ids
msgid "Website communication history"
msgstr "Website communicatie geschiedenis"

#. module: social
#: model:ir.model.fields,field_description:social.field_social_post__post_method
msgid "When"
msgstr "Wanneer"

#. module: social
#: model:ir.model.fields,help:social.field_social_post__published_date
msgid ""
"When the global post was published. The actual sub-posts published dates may"
" be different depending on the media."
msgstr ""
"Wanneer de algemene post werd gepubliceerd. De werkelijke gepubliceerde "
"datums voor sub-berichten kunnen verschillen, afhankelijk van de media."

#. module: social
#: model:ir.model.fields,help:social.field_social_post__image_ids
#: model:ir.model.fields,help:social.field_social_post_template__image_ids
msgid "Will attach images to your posts (if the social media supports it)."
msgstr ""
"Voegt afbeeldingen toe aan je posts (indien het socialmedia platform dit "
"ondersteund)."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a comment..."
msgstr "Schrijf een reactie..."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Write a message or upload an image"
msgstr "Schrijf een bericht of upload een afbeelding"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/tours/social.js:0
msgid "Write a message to get a preview of your post."
msgstr "Schrijf een bericht om een voorbeeld van je bericht te krijgen."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "Write a reply..."
msgstr "Schrijf een reactie..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_post
msgid ""
"Write an enticing post, add images and schedule it to be posted later on "
"multiple platforms at once."
msgstr ""
"Schrijf een aanlokkelijk bericht, voeg afbeeldingen toe en plan het later op"
" meerdere platforms tegelijk."

#. module: social
#. odoo-python
#: code:addons/social/models/utm_medium.py:0
msgid ""
"You cannot delete these UTM Mediums as they are linked to the following social accounts in Social:\n"
"%(social_accounts)s"
msgstr ""
"Je kunt deze UTM-mediums niet verwijderen omdat ze zijn gekoppeld aan de volgende socialmedia-accounts in Sociaal:\n"
"%(social_accounts)s"

#. module: social
#. odoo-python
#: code:addons/social/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to social posts in Social:\n"
"%(utm_sources)s"
msgstr ""
"Je kunt deze UTM-bronnen niet verwijderen omdat ze zijn gekoppeld aan sociale berichten in Sociaal:\n"
"%(utm_sources)s"

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot reschedule a post that has already been posted."
msgstr "Je kunt een reeds geplaatste post niet opnieuw inplannen."

#. module: social
#. odoo-python
#: code:addons/social/models/social_post.py:0
msgid "You cannot schedule a post in the past."
msgstr "Je kunt geen post in het verleden plannen."

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "Your Post"
msgstr "Je post"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "a Stream from an existing account"
msgstr "een stream van een bestaand account"

#. module: social
#: model_terms:ir.ui.view,arch_db:social.social_post_template_view_form
msgid "before posting."
msgstr "voordat je post."

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/js/stream_post_comment.js:0
msgid "comment/reply"
msgstr "opmerking/reactie"

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "dashboard"
msgstr "dashboard"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "for"
msgstr "voor"

#. module: social
#. odoo-javascript
#: code:addons/social/static/src/xml/social_templates.xml:0
msgid "replies..."
msgstr "reacties..."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream_post
msgid "to keep an eye on your own posts and monitor all social activities."
msgstr ""
"om je eigen berichten in de gaten te houden en alle sociale activiteiten te "
"volgen."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_stream
msgid "to link your accounts and start posting."
msgstr "om je accounts te koppelen en te beginnen met posten."

#. module: social
#: model_terms:ir.actions.act_window,help:social.action_social_account
msgid "to start posting from Odoo."
msgstr "om te beginnen met posten vanuit Odoo."
