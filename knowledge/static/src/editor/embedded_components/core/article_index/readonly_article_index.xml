<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="knowledge.ReadonlyEmbeddedArticleIndex">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="o_embedded_articles_index_label px-2 py-1 fw-bold">Index</div>
        </div>
        <div class="o_embedded_articles_index_content">
            <div t-if="(props.articles || []).length === 0" class="fst-italic p-1">No article to display</div>
            <ArticleIndexList t-else="" articles="props.articles"/>
        </div>
    </t>
</templates>
