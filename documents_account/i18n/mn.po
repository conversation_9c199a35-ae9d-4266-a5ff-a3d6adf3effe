# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_account
#
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# tser<PERSON><PERSON><PERSON> tsogtoo <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# hish, 2022
# Baskhu<PERSON>hu<PERSON> <<EMAIL>>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Baskhuu Lodoikhuu <<EMAIL>>, 2022\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"Language: mn\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_workflow_rule
msgid "A set of condition and actions which will be available to all attachments matching the conditions"
msgstr "Тухайн нөхцөл байдалд тохирсон бүх хавсралтад ашиглах боломжтой нөхцөл ба үйлдлүүдийн багц"

#. module: documents_account
#: model:ir.model.constraint,message:documents_account.constraint_documents_account_folder_setting_journal_unique
msgid "A setting already exists for this journal"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__documents_account_settings
msgid "Accounting "
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__account_folder
msgid "Accounting Workspace"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_ir_attachment
msgid "Attachment"
msgstr "Хавсралт"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_bank_statement
msgid "Bank Statement"
msgstr ""

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Centralize accounting files and documents"
msgstr ""

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "Check them"
msgstr "Түүнийг шалгах"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click on the <b>page separator</b>: we don't want to split these two pages as they belong to the same document."
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_id
msgid "Company"
msgstr "Компани"

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Үүсгэх"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.credit_note_rule
msgid "Create Customer Credit Note"
msgstr ""

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.customer_invoice_rule
msgid "Create Customer Invoice"
msgstr "Захиалагчийн нэхэмжлэл үүсгэх"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.misc_entry_rule
msgid "Create Miscellaneous Operations"
msgstr ""

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.vendor_bill_rule_financial
msgid "Create Vendor Bill"
msgstr ""

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.vendor_refund_rule
msgid "Create Vendor Refund"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_refund
msgid "Credit note"
msgstr "Буцаалт"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_invoice
msgid "Customer invoice"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_workflow_rule__display_journal_id
msgid "Display Journal"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__documents_account_settings
msgid "Documents Account Settings"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Санхүүгийн тайланг экспорт хийх формат"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Санхүүгийн тайланг экспорт хийх цонх"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_fs
msgid "Financial Statement"
msgstr "Санхүүгийн тайлан"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder"
msgstr "Хавтас"

#. module: documents_account
#: model:ir.model.fields,help:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder where to save the generated file"
msgstr ""

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/wizard/account_reports_export_wizard.py:0
msgid "Generated Documents"
msgstr "Үүсгэсэн баримтууд"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__id
msgid "ID"
msgstr "ID"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.bank_statement_rule
msgid "Import Bank Statement"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__journal_id
#: model:ir.model.fields,field_description:documents_account.field_documents_workflow_rule__journal_id
msgid "Journal"
msgstr "Журнал"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move
msgid "Journal Entry"
msgstr "Ажил гүйлгээ"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_account_folder_setting
msgid "Journal and Folder settings"
msgstr ""

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Journals"
msgstr "Журнал"

#. module: documents_account
#: model:ir.actions.act_window,name:documents_account.action_folder_settings_installer
msgid "Journals to synchronize"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter documents and structure your process.</i>"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's process these bills: turn them into vendor bills."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's process this document, coming from our scanner."
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your process, according to the workspace.</i>"
msgstr ""

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_entry
msgid "Miscellaneous Operations"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_workflow_rule__move_type
msgid "Move Type"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__suspense_statement_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_move__suspense_statement_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_payment__suspense_statement_line_id
msgid "Request document from a bank statement line"
msgstr ""

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_workflow_rule__suitable_journal_ids
msgid "Suitable Journal"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__tag_ids
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__tag_ids
msgid "Tags"
msgstr "Пайз"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_vat
msgid "Tax Statement"
msgstr "Татварын тайлан"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "This invoice has been initiated by a bank transaction."
msgstr ""

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_refund
msgid "Vendor Credit Note"
msgstr "Нийлүүлэгчийн буцаалт"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_invoice
msgid "Vendor bill"
msgstr "Нийлүүлэгчийн нэхэмжлэх"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__folder_id
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Workspace"
msgstr "Ажлын талбар"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is complete. Try uploading your own documents now."
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__account_folder
msgid "account default folder"
msgstr "Дансны үндсэн хавтас"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "to mark this invoice as paid."
msgstr "төлсөн гэж тэмдэглэх."
