<odoo>
    <!--Hr Department Inherit Kanban view-->
    <record id="hr_department_view_kanban" model="ir.ui.view">
        <field name="name">hr.department.kanban.inherit</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="hr.hr_department_view_kanban"/>
        <field name="arch" type="xml">
            <data>
                <xpath expr="//div[@name='kanban_primary_right']" position="inside">
                    <div groups="hr_appraisal.group_hr_appraisal_user" t-if="record.appraisals_to_process_count.raw_value > 0" class="row g-0 ml32">
                        <a name="%(hr_appraisal_action_from_department)d" class="col" type="action">
                            <field name="appraisals_to_process_count"/> Appraisals
                        </a>
                    </div>
                </xpath>

                <xpath expr="//div[hasclass('o_kanban_manage_reports')]" position="inside">
                    <div role="menuitem">
                        <a class="dropdown-item" name="%(action_appraisal_report_department)d" type="action">
                            Appraisals
                        </a>
                    </div>
                </xpath>
            </data>
        </field>
    </record>

    <record id="hr_department_view_form" model="ir.ui.view">
        <field name="name">hr.department.view.form</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="hr.view_department_form"/>
        <field name="arch" type="xml">
            <xpath expr="/form" position="attributes">
                <attribute name="class">o_appraisal_form</attribute>
            </xpath>

            <xpath expr="//field[@name='color']" position="after">
                <field name="custom_appraisal_template_id"/>
            </xpath>
        </field>
    </record>
</odoo>
