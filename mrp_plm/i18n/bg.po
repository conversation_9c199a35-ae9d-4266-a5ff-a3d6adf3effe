# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_plm
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <albena_v<PERSON><PERSON>@abv.bg>, 2024
# <PERSON> <igor.shelud<PERSON>@gmail.com>, 2024
# Ивайло <PERSON> <<EMAIL>>, 2024
# KeyVillage, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 15:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__document_count
msgid "# Attachments"
msgstr "# прикачени файлове"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__eco_count
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__eco_count
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__eco_count
msgid "# ECOs"
msgstr "# Поръчки за инженерни промени - ECO"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "%(approval_name)s %(approver_name)s %(approval_status)s this ECO"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" "
"title=\"Domain alias\"/>&amp;nbsp;"
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your ECO, that will be applied to the product later\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_plm_production_form_view
msgid "<span class=\"o_stat_text\">ECO(S)</span>"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "<span class=\"o_stat_text\">Revision</span>"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Речник на Python, който ще бъде оценен, за да предостави стойности по "
"подразбиране при създаването на нови записи за този псевдоним."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "A user cannot be assigned more than once to the same stage"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_needaction
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_needaction
msgid "Action Needed"
msgstr "Необходимо действие"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__active
msgid "Active"
msgstr "Активно"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_ids
msgid "Activities"
msgstr "Дейности"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr " Декорация за изключение на дейност"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_state
msgid "Activity State"
msgstr "Състояние на дейност"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_type_icon
msgid "Activity Type Icon"
msgstr "Икона за вид дейност"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__add
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__add
msgid "Add"
msgstr "Добави"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Add a description..."
msgstr "Добавете описание ..."

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_tag_action
msgid "Add a new tag"
msgstr "Добавете нов етикет"

#. module: mrp_plm
#: model:res.groups,name:mrp_plm.group_plm_manager
msgid "Administrator"
msgstr "Администратор"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_id
msgid "Alias"
msgstr "Псевдоним"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_contact
msgid "Alias Contact Security"
msgstr "Сигурност на контакт за псевдоним"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_domain_id
msgid "Alias Domain"
msgstr "Домейн за псевдоними"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_domain
msgid "Alias Domain Name"
msgstr "Псевдоним на име на домейн"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_full_name
msgid "Alias Email"
msgstr "Псевдоним имейл"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_name
msgid "Alias Name"
msgstr "Псевдоним име"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_status
msgid "Alias Status"
msgstr "Псевдоним статус"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Статус на псевдонима, оценен при последното получено съобщение."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_model_id
msgid "Aliased Model"
msgstr "Преименуван модел"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "All Validations"
msgstr "Всички потвърждения"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_change_kanban_state
msgid "Allow Change Kanban State"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_change_stage
msgid "Allow Change Stage"
msgstr "Позволете промяна на етапа"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__allow_apply_change
msgid "Allow to apply changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__allow_apply_change
msgid "Allow to apply changes from this stage."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_tree
msgid "Apply Changes"
msgstr "Приложете промени"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Apply Rebase"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__type
msgid "Apply on"
msgstr "Приложете върху"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__approval_date
msgid "Approval Date"
msgstr "Дата на одобрение"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__approval_roles
msgid "Approval Roles"
msgstr "Одобряващи длъжности"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__template_stage_id
msgid "Approval Stage"
msgstr "Етап на одобрение"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__approval_type
msgid "Approval Type"
msgstr "Вид одобрение"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__approval_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__approval_template_ids
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Approvals"
msgstr "Одобрения"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__approval_ids
msgid "Approvals by stage"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Approve"
msgstr "Одобри"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__done
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__approved
msgid "Approved"
msgstr "Одобрен"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__user_id
msgid "Approved by"
msgstr "Одобрен от"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__optional
msgid "Approves, but the approval is optional"
msgstr "Одобрения, но одобрението не е задължително"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Archived"
msgstr "Архивиран"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__effectivity__asap
msgid "As soon as possible"
msgstr "Колкото е възможно по-скоро"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__effectivity__date
msgid "At Date"
msgstr "На дата"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_attachment_count
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_attachment_count
msgid "Attachment Count"
msgstr "Брой прикачени файлове"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__document_ids
msgid "Attachments"
msgstr "Прикачени файлове"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__awaiting_my_validation
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Awaiting My Validation"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Awaiting Validation"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr ""

#. module: mrp_plm
#: model:mrp.eco.type,name:mrp_plm.ecotype_bom_update
msgid "BOM Updates"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_mode_batch
msgid "Batch count Change"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom
msgid "Bill of Material"
msgstr "Фактура за покупка на материали"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Ред на Списък с материали"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.actions.act_window,name:mrp_plm.mrp_bom_action_kanban
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_id
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_boms
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Bill of Materials"
msgstr "Спецификация на материали"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_bom_action_kanban
msgid ""
"Bills of materials allow you to define the list of required components\n"
"              used to make a finished product; through a manufacturing\n"
"              order or a pack of products."
msgstr ""
"Спецификациите на материалите Ви позволяват да дефинирате списъка с необходимите компоненти\n"
"              използвани за направата на завършен продукт; чрез производствена\n"
"              поръчка или пакет от продукти."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__blocked
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_validated
msgid "Blocked"
msgstr "Блокиран"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__is_blocking
msgid "Blocking Stage"
msgstr "Блокиращ етап"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__byproduct_id
msgid "BoM By-Product"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__bom_line_id
msgid "BoM Line"
msgstr "Ред на Списъка с материали"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_rebase_ids
msgid "BoM Rebase"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__new_bom_revision
msgid "BoM Revision"
msgstr "Ревизия на фактура за покупка на материали"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid "BoM Suggestions from %(mo_name)s"
msgstr ""

#. module: mrp_plm
#. odoo-javascript
#: code:addons/mrp_plm/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_plm.report_mrp_bom_inherit_mrp_plm
msgid "BoM Version"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "BoM:"
msgstr "Фактура за покупка на материали:"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "By-Product Changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom_byproduct
msgid "Byproduct"
msgstr "Вторичен продукт"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_can_approve
msgid "Can Approve"
msgstr "Може да одобри"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_can_reject
msgid "Can Reject"
msgstr "Мотете да отхвърлите"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid ""
"Cannot create an ECO if the Manufacturing Order doesn't use a Bill of "
"Materials"
msgstr ""

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_changes
msgid "Changes"
msgstr "Промени"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made in previous eco"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on old bill of materials"
msgstr "Промени, направени в стара спецификация на материали"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on the new revision bill of materials"
msgstr "Промени, направени в новата версия на спецификацията на материалите"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on the operation."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__color
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__color
msgid "Color"
msgstr "Цвят"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__color
msgid "Color Index"
msgstr "Цветови индекс"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__comment
msgid "Commented"
msgstr "Коментиран"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__comment
msgid "Comments only"
msgstr "Само коментари"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__company_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__company_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Company"
msgstr "Фирма"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Component Changes"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Component Rebase"
msgstr ""

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_configuration
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "Configuration"
msgstr "Конфигурация"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__conflict
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__conflict
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Conflict"
msgstr "Конфликт"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Conflict Resolved"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__operation_change
msgid "Consumed in Operation"
msgstr "Изразходвани в процеса на работа"

#. module: mrp_plm
#: model:ir.actions.server,name:mrp_plm.action_production_order_create_eco
msgid "Create ECO"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_stage_action
msgid "Create a new ECO stage"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_type_action_dashboard
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_type_action_form
msgid "Create a new ECO type"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__create_uid
msgid "Created by"
msgstr "Създаден от"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Персонализирано отвхвърлено съобщение"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__effectivity
msgid "Date on which the changes should be applied. For reference only."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_defaults
msgid "Default Values"
msgstr "Стойности по подразбиране"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Define the approval roles on the ECO stages."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Delete"
msgstr "Изтрий"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__description
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Description"
msgstr "Описание"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__description
msgid "Description and tooltips of the stage states."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Description of the change and its reason."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__bom_change_ids
msgid "Difference between old BoM and new BoM revision"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__routing_change_ids
msgid "Difference between old operation and new operation revision"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__displayed_image_id
msgid "Displayed Image"
msgstr "Показано изображение"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Documents"
msgstr "Документи"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__done
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Done"
msgstr "Извършен"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__eco_id
msgid "ECO"
msgstr "Поръчки за инженерни промени - ECO"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_graph
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_pivot
msgid "ECO Analysis"
msgstr "Анализ на ECO"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_approval
#: model:mail.activity.type,name:mrp_plm.mail_activity_eco_approval
msgid "ECO Approval"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_approval_template
msgid "ECO Approval Template"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids
msgid "ECO BoM Changes"
msgstr "Промени във фактурата за покупка на материали на ECO"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids_on_byproduct
msgid "ECO BoM Changes - By-Product"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids_on_line
msgid "ECO BoM Changes - Component"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_bom_change
msgid "ECO BoM changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__eco_rebase_id
msgid "ECO Rebase"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__routing_change_ids
msgid "ECO Routing Changes"
msgstr "Промени на маршрутизацията в ECO"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_stage
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__eco_stage_id
msgid "ECO Stage"
msgstr "Етап на ECO"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_stage_action
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_stages
msgid "ECO Stages"
msgstr "Етапи на ECO"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_tag_action
#: model:ir.model,name:mrp_plm.model_mrp_eco_tag
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_tag
msgid "ECO Tags"
msgstr "Маркери на ECO"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_type
msgid "ECO Type"
msgstr ""

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_type_action_form
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_types
msgid "ECO Types"
msgstr "Видове ECO"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_stage_action
msgid "ECO stages give the different stages for the Engineering Change Orders"
msgstr "Етапите на ECO различните етапи на Поръчките за инженерни промени"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__eco_ids
msgid "ECO to be applied"
msgstr "ECO, която трябва да се приложи"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_bom_view_form_inherit_plm
msgid "ECO(s)"
msgstr "Поръчка(и) за инженерни промени - ECO"

#. module: mrp_plm
#. odoo-javascript
#: code:addons/mrp_plm/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp_plm/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp_plm/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_ecos
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__eco_ids
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__eco_ids
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__eco_ids
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_report
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_calendar
#: model_terms:ir.ui.view,arch_db:mrp_plm.product_product_view_form_inherit_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.product_template_view_form_inherit_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.report_mrp_bom_inherit_mrp_plm
msgid "ECOs"
msgstr "Поръчки за инженерни промени - ECOs"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Eco"
msgstr "Поръчка за инженерни промени"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Eco BoM"
msgstr "Фактура за покупка на материали на ECO"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__eco_count
msgid "Eco Count"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_routing_change
msgid "Eco Routing changes"
msgstr "Промени в маршрутизацията на ECO"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "Edit Task"
msgstr "Редактирайте задача"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__effectivity
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,name:mrp_plm.ecostage_effective
msgid "Effective"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__effectivity_date
msgid "Effective Date"
msgstr "Дата на влизане в сила"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_email
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "Email Alias"
msgstr "Имейл псевдоним"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__email_cc
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Email cc"
msgstr "Имейл копие"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Имейл домейн напр. 'example.com' в '<EMAIL>'"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__eco_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__eco_id
msgid "Engineering Change"
msgstr "Инженерна промяна"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco
msgid "Engineering Change Order (ECO)"
msgstr ""

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_approval
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_approval_my
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_late
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_main
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_product_tmpl
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_report
msgid "Engineering Change Orders"
msgstr "Поръчки за инженерни промени"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "Engineering Changes"
msgstr "Инженерни промени"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Extra Info"
msgstr "Допълнителна информация"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Filters"
msgstr "Филтри"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__final_stage
msgid "Final Stage"
msgstr "Финален етап"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__folded
msgid "Folded in kanban view"
msgstr "Сгънато в канбан изглед"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_follower_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_follower_ids
msgid "Followers"
msgstr "Последователи"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_partner_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_partner_ids
msgid "Followers (Partners)"
msgstr "Последователи (партньори)"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr " Икона, примерно fa-tasks"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__effectivity_date
msgid "For reference only."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Future Activities"
msgstr "Бъдещи дейности"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_done
msgid "Green Kanban Label"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_normal
msgid "Grey Kanban Label"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Group by..."
msgstr "Групиране по..."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__has_message
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__has_message
msgid "Has Message"
msgstr "има съобщение"

#. module: mrp_plm
#: model:ir.module.category,description:mrp_plm.module_category_manufacturing_product_lifecycle_management_(plm)
msgid "Helps you manage your product's lifecycles."
msgstr "Помага Ви да управлявате жизнения цикъл на продукта."

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__priority__1
msgid "High"
msgstr "Високо"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__id
msgid "ID"
msgstr "ID"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID на родителския запис, притежаващ псевдонима (например: проект, притежаващ"
" псевдонима за създаване на задача)"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_exception_icon
msgid "Icon"
msgstr "Икона"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Икона за обозначаване на дейност по изключение."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_needaction
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ако е отметнато, новите съобщения ще изискват внимание."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_sms_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ако е отметнато, някои съобщения имат грешка при доставката."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Ако е зададено, това съдържание ще се изпраща автоматично на неупълномощени "
"потребители вместо съобщението по подразбиране."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"engineering change order without removing it."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__will_update_version
msgid ""
"If unchecked, the version of the product/BoM will remain unchanged once the "
"ECO is applied"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__image_128
msgid "Image 128"
msgstr "Изображение 128"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__normal
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_validated
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,name:mrp_plm.ecostage_progress
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "In Progress"
msgstr "В процес на изпълнение"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_approved
msgid "Is Approved"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_closed
msgid "Is Closed"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_is_follower
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_is_follower
msgid "Is Follower"
msgstr "е последовател"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_rejected
msgid "Is Rejected"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__mandatory
msgid "Is required to approve"
msgstr "Изисква се да одобри"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Обяснение за блокиран Канбан"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Обяснение за функциониращ Канбан"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__kanban_state
msgid "Kanban State"
msgstr "Kanban състояние"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__kanban_state_label
msgid "Kanban State Label"
msgstr "Етикет за състояние в Канбан"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_done
msgid "Kanban Valid Explanation"
msgstr "Валидно обяснение Канбан"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__write_uid
msgid "Last Updated by"
msgstr "Последно актуализирано от"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__write_date
msgid "Last Updated on"
msgstr "Последно актуализирано на"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Late Activities"
msgstr "Последни дейности"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__latest_bom_id
msgid "Latest Bom"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Откриване на входящи съобщения на база локалната част на имейла"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval_my
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_late
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_main
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_product_tmpl
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_report
msgid ""
"Manage your products and bills of materials changes with the ECO's.\n"
"              Gather the related documentation and receive the necessary approvals\n"
"              before applying your changes."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_cycle_manual
msgid "Manual Duration Change"
msgstr "Механична промяна на времетраенето"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_production
msgid "Manufacturing Order"
msgstr "Производствена поръчка "

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__production_id
msgid "Manufacturing Orders"
msgstr "Производствена поръчка "

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_master_data
msgid "Master Data"
msgstr "Основни данни"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_error
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_error
msgid "Message Delivery error"
msgstr "Грешка при доставяне на съобщението"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_ids
msgid "Messages"
msgstr "Syob]eniq"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_mode
msgid "Mode Change"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_tag_view_search
msgid "Mrp Eco Tags"
msgstr "Маркери на ECO за Планирането на производствено на ресурсите"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Краен срок за моята дейност"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "My Change Orders"
msgstr "Моите поръчки за промени"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "My Validations"
msgstr "Моите потвърждения"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__name
msgid "Name"
msgstr "Име"

#. module: mrp_plm
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,name:mrp_plm.ecostage_new
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "New"
msgstr "Нов"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__new_bom_id
msgid "New Bill of Materials"
msgstr "Нова спецификация на материали"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid "New BoM from %(mo_name)s"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__current_bom_id
msgid "New Bom"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_operation_id
msgid "New Consumed in Operation"
msgstr ""

#. module: mrp_plm
#: model:mrp.eco.type,name:mrp_plm.ecotype0
msgid "New Product Introduction"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_uom_id
msgid "New Product UoM"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_product_qty
msgid "New revision quantity"
msgstr "Ново количество за ревизиране"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Следващото събитие от календара на дейностите"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Краен срок на следващо действие"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_summary
msgid "Next Activity Summary"
msgstr "Обобщение на следваща дейност"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_type_id
msgid "Next Activity Type"
msgstr "Вид на следващо действие"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_bom_action_kanban
msgid "No bill of materials found. Let's create one!"
msgstr "Не е намерена спецификация на материалите. Нека да създадем една!"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval_my
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_late
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_main
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_product_tmpl
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_report
msgid "No engineering change order found"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__priority__0
msgid "Normal"
msgstr "Нормален"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__none
msgid "Not Yet"
msgstr "Не още"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__note
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Note"
msgstr "Забележка"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_needaction_counter
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_needaction_counter
msgid "Number of Actions"
msgstr "Брой действия"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_error_counter
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_error_counter
msgid "Number of errors"
msgstr "Брой грешки"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_needaction_counter
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Брой съобщения изискващи действие"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_error_counter
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Брой съобщения с грешка при доставка"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__final_stage
msgid "Once the changes are applied, the ECOs will be moved to this stage."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Open Component"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Open Operation"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__operation_name
msgid "Operation"
msgstr "Операция"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Operation Changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__operation_id
msgid "Operation Id"
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Operation not supported"
msgstr "Операцията не се поддържа"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Незадължителен идентификационен номер на нишка (запис), към която ще бъдат "
"прикрепени всички входящи съобщения, дори ако не са отговорили на него. Ако "
"е настроено, това ще забрани напълно създаването на нови записи."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_attachment_id
msgid "Origin Attachment"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_res_model
msgid "Origin Model"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_res_name
msgid "Origin Name"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_blocked
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection, when the ECO is in that stage."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_done
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection, when the ECO is in that stage."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_normal
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection, when the ECO is in that stage."
msgstr ""

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_dashboard
msgid "Overview"
msgstr "Общ преглед"

#. module: mrp_plm
#: model:ir.module.category,name:mrp_plm.module_category_manufacturing_product_lifecycle_management_(plm)
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_root
msgid "PLM"
msgstr "Управление жизнения цикъл на продукта - PLM"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_type_action_dashboard
msgid "PLM Overview"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_parent_model_id
msgid "Parent Model"
msgstr "Родителски модел "

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID на нишката на родителския запис"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Родителски модел, който притежава псевдонима. Моделът, който държи "
"референцията на псевдонима, не е непременно моделът, зададен от "
"alias_model_id (пример: проект (parent_model) и задача (model))."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Политика за публикуване на съобщение в документа с помощта на mailgateway.\n"
"- всеки: всеки може да публикува\n"
"- партньори: само автентифицирани партньори\n"
"- последователи: само последователи на свързания документ или членове на следващите канали\n"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__previous_bom_id
msgid "Previous BoM"
msgstr "Предишна фактура за покупка на материали"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_operation_id
msgid "Previous Consumed in Operation"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__previous_change_ids
msgid "Previous ECO Changes"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Previous Eco Component Changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_uom_id
msgid "Previous Product UoM"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_product_qty
msgid "Previous revision quantity"
msgstr "Количество при предишна ревизия"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__priority
msgid "Priority"
msgstr "Приоритет"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Produced in Operation"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_template
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__product_tmpl_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__product_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Product"
msgstr "Продукт"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_document
msgid "Product Document"
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Product Only"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_product
msgid "Product Variant"
msgstr "Продуктов вариант"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Production"
msgstr "Продукция"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__active
msgid "Production Ready"
msgstr ""

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_products
msgid "Products"
msgstr "Продукти"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__upd_product_qty
msgid "Quantity"
msgstr "Количество"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__rating_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__rating_ids
msgid "Ratings"
msgstr "Оценявания"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_validated
msgid "Ready"
msgstr "Готов"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__rebase_id
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__rebase
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Rebase"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Rebase new revision of BoM with previous eco bom and old bom changes."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID на запис на нишката"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_blocked
msgid "Red Kanban Label"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__name
msgid "Reference"
msgstr "Идентификатор"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Reject"
msgstr "Отхвърлете"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__rejected
msgid "Rejected"
msgstr "Отхвърлен"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__displayed_image_attachment_id
msgid "Related attachment"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__remove
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__remove
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Remove"
msgstr "Премахнете"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_reporting
msgid "Reporting"
msgstr "Отчитане"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__required_user_ids
msgid "Requested Users"
msgstr "Заявени потребители"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_id
msgid "Responsible"
msgstr "Отговорен"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_user_id
msgid "Responsible User"
msgstr "Отговорен потребител"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__name
msgid "Role"
msgstr "Длъжност"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Routing"
msgstr "Маршрутизиране"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_sms_error
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS грешка при доставка"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_search
msgid "Search"
msgstr "Tyrsene"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__sequence
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__sequence
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__sequence
msgid "Sequence"
msgstr "Последователност"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "Set Cover Image"
msgstr "Настройте изображение за корица"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_apply_change
msgid "Show Apply Change"
msgstr "Покажете приложена промяна"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Показване на всички записи, на които следващата дата на действие е преди "
"днес"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__stage_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__stage_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Stage"
msgstr "Стадий"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Описание на етап и изскачащи инструкции"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__stage_ids
msgid "Stages"
msgstr "Стадии"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Start Revision"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "State"
msgstr "Област"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__state
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__status
msgid "Status"
msgstr "Състояние"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус въз основа на дейности\n"
"Просрочени: Срокът вече е изтекъл\n"
"Днес: Датата на дейността е днес\n"
"Планирано: Бъдещи дейности."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Successfully Rebased!"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid ""
"System will automatically resolved the conflict(s) and apply changes. Do you"
" agree?"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__name
msgid "Tag Name"
msgstr "Име на маркер"

#. module: mrp_plm
#: model:ir.model.constraint,message:mrp_plm.constraint_mrp_eco_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Тагът вече съществува"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__tag_ids
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_tag_view_tree
msgid "Tags"
msgstr "Маркери"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__user_can_approve
msgid "Technical field to check if approval by current user is required"
msgstr ""
"Техническо поле, което да отметнете, ако се изисква одобрение от настоящ "
"потребител"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__user_can_reject
msgid "Technical field to check if reject by current user is possible"
msgstr ""
"Техническо поле, което да отметнете, ако е възможно отхвърляне от настоящ "
"потребител"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__approval_template_id
msgid "Template"
msgstr "Шаблон"

#. module: mrp_plm
#: model:res.groups,comment:mrp_plm.group_plm_manager
msgid "The PLM manager manages products lifecycle management"
msgstr ""
"Мениджър правление жизнения цикъл на продукт управлява жизнения цикъл на "
"продукт"

#. module: mrp_plm
#: model:res.groups,comment:mrp_plm.group_plm_user
msgid "The PLM user uses products lifecycle management"
msgstr ""
"Потребителят на управление жизнения цикъл на продукт използва управлението "
"на жизнен цикъл на продукт"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_product_product__version
#: model:ir.model.fields,help:mrp_plm.field_product_template__version
msgid "The current version of the product."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
" Моделът (Odoo Document Kind), към който съответства този псевдоним. Всеки "
"входящ имейл, който не отговаря на съществуващ запис, ще доведе до "
"създаването на нов запис на този модел (например задача за проекта)"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Името на имейла, напр. \"работни места\", ако искате да уловите имейли за "
"<<EMAIL>>"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_validation
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "To Apply"
msgstr "За прилагане"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "To Do"
msgstr " За извършване"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Today Activities"
msgstr "Дневни дейности"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__type_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__change_type
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__change_type
msgid "Type"
msgstr "Вид"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Вид на записаната дейност по изключение."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__type_ids
msgid "Types"
msgstr "Видове"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Undo"
msgstr "Отменете"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__uom_change
msgid "Unit of Measure"
msgstr "Мерна единица"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__update
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__update
msgid "Update"
msgstr "Актуализирайте"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__will_update_version
msgid "Update Version"
msgstr ""

#. module: mrp_plm
#: model:res.groups,name:mrp_plm.group_plm_user
msgid "User"
msgstr "Потребител"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__user_ids
msgid "Users"
msgstr "Потребители"

#. module: mrp_plm
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,name:mrp_plm.ecostage_validated
msgid "Validated"
msgstr "Валидиране"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Variant"
msgstr "Вариант"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__version
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__version
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__version
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_bom_view_form_inherit_plm
msgid "Version"
msgstr "Версия"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_approvals
msgid "Waiting Approvals"
msgstr "Чакащ одобрения"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_approvals_my
msgid "Waiting my Approvals"
msgstr "Чакащ моите одобрения"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__website_message_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__website_message_ids
msgid "Website Messages"
msgstr "Съобщения в уебсайт"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__website_message_ids
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__website_message_ids
msgid "Website communication history"
msgstr "История на комуникацията на уебсайт"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__workcenter_id
msgid "Work Center"
msgstr "Работен център"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Използване на работен център"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_workcenters
msgid "Work Centers"
msgstr "Работни центрове"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"Освен това можете да добавите описание, за да помогнете на колегите ви да "
"разберат значението и целта на етапа."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid ""
"You can define here labels that will be displayed for the state instead\n"
"                            of the default labels."
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "You cannot change the stage, as approvals are required in the process."
msgstr ""
"Не можете да промените този етап, тъй като в процеса се изискват одобрения."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "You cannot change the stage, as approvals are still required."
msgstr ""
"Не можете да промените този етап, тъй като все още се изискват одобрения."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "alias"
msgstr "псевдоним"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "e.g. Awesome Product 2.0"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "e.g. Engineering Department"
msgstr "напр. Инженерен отдел"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "e.g. mycompany.com"
msgstr "напр. mycompany.com"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "text-danger"
msgstr "text-danger"
