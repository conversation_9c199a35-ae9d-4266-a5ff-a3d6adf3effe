# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_plm
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 15:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__document_count
msgid "# Attachments"
msgstr "# Archivos adjuntos"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__eco_count
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__eco_count
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__eco_count
msgid "# ECOs"
msgstr "# Órdenes de cambio de ingeniería"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "%(approval_name)s %(approver_name)s %(approval_status)s this ECO"
msgstr ""
"%(approval_name)s %(approver_name)s %(approval_status)s esta orden de cambio"
" de ingeniería"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" "
"title=\"Domain alias\"/>&amp;nbsp;"
msgstr ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Seudónimo del "
"dominio\" title=\"Seudónimo del dominio\"/>&amp;nbsp;"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your ECO, that will be applied to the product later\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Suba archivos a su orden de cambio de ingeniería para aplicarlos al producto después.\n"
"                    </p><p>\n"
"                        Use esta función para almacenar cualquier archivo, como diagramas o especificaciones.\n"
"                    </p>"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_plm_production_form_view
msgid "<span class=\"o_stat_text\">ECO(S)</span>"
msgstr "<span class=\"o_stat_text\">Órdenes de cambio de ingeniería</span>"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "<span class=\"o_stat_text\">Revision</span>"
msgstr "<span class=\"o_stat_text\">Revisión</span>"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Un diccionario de Python que se evaluará con el fin de proporcionar valores "
"predeterminados cuando se creen nuevos registros para este seudónimo."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "A user cannot be assigned more than once to the same stage"
msgstr "No se puede asignar a un usuario más de una vez en la misma etapa."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_needaction
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_needaction
msgid "Action Needed"
msgstr "Se requiere una acción"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__active
msgid "Active"
msgstr "Activo"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de la actividad de excepción"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono del tipo de actividad"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__add
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__add
msgid "Add"
msgstr "Agregar"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Add a description..."
msgstr "Agregar una descripción..."

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_tag_action
msgid "Add a new tag"
msgstr "Agregar una nueva etiqueta"

#. module: mrp_plm
#: model:res.groups,name:mrp_plm.group_plm_manager
msgid "Administrator"
msgstr "Administrador"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_id
msgid "Alias"
msgstr "Seudónimo"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_contact
msgid "Alias Contact Security"
msgstr "Seudónimo del contacto de seguridad"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_domain_id
msgid "Alias Domain"
msgstr "Dominio del seudónimo"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_domain
msgid "Alias Domain Name"
msgstr "Nombre del dominio del seudónimo"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_full_name
msgid "Alias Email"
msgstr "Correo electrónico del seudónimo"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_name
msgid "Alias Name"
msgstr "Nombre del seudónimo"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_status
msgid "Alias Status"
msgstr "Estado del seudónimo"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Estado del seudónimo evaluado en el último mensaje recibido."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_model_id
msgid "Aliased Model"
msgstr "Modelo con seudónimo"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "All Validations"
msgstr "Todas las validaciones"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_change_kanban_state
msgid "Allow Change Kanban State"
msgstr "Permitir cambios en el estado de kanban"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_change_stage
msgid "Allow Change Stage"
msgstr "Permitir cambio de etapa"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__allow_apply_change
msgid "Allow to apply changes"
msgstr "Permitir aplicar cambios"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__allow_apply_change
msgid "Allow to apply changes from this stage."
msgstr "Permitir aplicar los cambios de esta etapa."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_tree
msgid "Apply Changes"
msgstr "Aplicar cambios"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Apply Rebase"
msgstr "Aplicar transferencia de base"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__type
msgid "Apply on"
msgstr "Aplicar en"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__approval_date
msgid "Approval Date"
msgstr "Fecha de aprobación"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__approval_roles
msgid "Approval Roles"
msgstr "Funciones de aprobación"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__template_stage_id
msgid "Approval Stage"
msgstr "Etapa de aprobación"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__approval_type
msgid "Approval Type"
msgstr "Tipo de aprobación"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__approval_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__approval_template_ids
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Approvals"
msgstr "Aprobaciones"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__approval_ids
msgid "Approvals by stage"
msgstr "Aprobaciones por etapa"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Approve"
msgstr "Aprobar"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__done
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__approved
msgid "Approved"
msgstr "Aprobado"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__user_id
msgid "Approved by"
msgstr "Aprobado por"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__optional
msgid "Approves, but the approval is optional"
msgstr "Aprueba, pero la aprobación es opcional"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Archived"
msgstr "Archivado"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__effectivity__asap
msgid "As soon as possible"
msgstr "Lo antes posible"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__effectivity__date
msgid "At Date"
msgstr "Desde una fecha específica"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_attachment_count
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__document_ids
msgid "Attachments"
msgstr "Archivos adjuntos"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__awaiting_my_validation
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Awaiting My Validation"
msgstr "En espera de mi validación"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Awaiting Validation"
msgstr "En espera de validación"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Reporte de resumen de la lista de materiales"

#. module: mrp_plm
#: model:mrp.eco.type,name:mrp_plm.ecotype_bom_update
msgid "BOM Updates"
msgstr "Actualizaciones de la lista de materiales"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_mode_batch
msgid "Batch count Change"
msgstr "Cambio en el conteo de lotes"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom
msgid "Bill of Material"
msgstr "Lista de materiales"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Línea de lista de materiales"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.actions.act_window,name:mrp_plm.mrp_bom_action_kanban
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_id
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_boms
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Bill of Materials"
msgstr "Lista de materiales"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_bom_action_kanban
msgid ""
"Bills of materials allow you to define the list of required components\n"
"              used to make a finished product; through a manufacturing\n"
"              order or a pack of products."
msgstr ""
"Las listas de materiales le permiten definir la lista de los componentes necesarios\n"
"              para fabricar un producto terminado mediante una orden\n"
"              de fabricación o de un paquete de productos."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__blocked
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_validated
msgid "Blocked"
msgstr "Bloqueado"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__is_blocking
msgid "Blocking Stage"
msgstr "Etapa de bloqueo"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__byproduct_id
msgid "BoM By-Product"
msgstr "Subproducto de la LdM"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__bom_line_id
msgid "BoM Line"
msgstr "Línea de la lista de materiales"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_rebase_ids
msgid "BoM Rebase"
msgstr "Transferencia de base de la LdM"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__new_bom_revision
msgid "BoM Revision"
msgstr "Revisión de la LdM"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid "BoM Suggestions from %(mo_name)s"
msgstr "Sugerencias de LdM de %(mo_name)s"

#. module: mrp_plm
#. odoo-javascript
#: code:addons/mrp_plm/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_plm.report_mrp_bom_inherit_mrp_plm
msgid "BoM Version"
msgstr "Versión de LdM"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "BoM:"
msgstr "LdM:"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "By-Product Changes"
msgstr "Cambios del subproducto"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom_byproduct
msgid "Byproduct"
msgstr "Subproducto"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_can_approve
msgid "Can Approve"
msgstr "Puede aprobar"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_can_reject
msgid "Can Reject"
msgstr "Puede rechazar"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid ""
"Cannot create an ECO if the Manufacturing Order doesn't use a Bill of "
"Materials"
msgstr ""
"No puede crear una orden de cambio de ingeniería si la orden de fabricación "
"no utiliza una lista de materiales"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_changes
msgid "Changes"
msgstr "Cambios"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made in previous eco"
msgstr "Cambios hechos en las órdenes de cambio de ingeniería previas"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on old bill of materials"
msgstr "Cambios hechos en la antigua lista de materiales"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on the new revision bill of materials"
msgstr "Cambios hechos en la nueva revisión de lista de materiales"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on the operation."
msgstr "Cambios realizados en la operación."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__color
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__color
msgid "Color"
msgstr "Color"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__color
msgid "Color Index"
msgstr "Índice de color"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__comment
msgid "Commented"
msgstr "Comentó"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__comment
msgid "Comments only"
msgstr "Solo comentarios"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__company_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__company_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Company"
msgstr "Empresa"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Component Changes"
msgstr "Cambios del componente"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Component Rebase"
msgstr "Transferencia de base del componente"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_configuration
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "Configuration"
msgstr "Configuración"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__conflict
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__conflict
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Conflict"
msgstr "Conflicto"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Conflict Resolved"
msgstr "Conflicto resuelto"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__operation_change
msgid "Consumed in Operation"
msgstr "Consumido en la operación"

#. module: mrp_plm
#: model:ir.actions.server,name:mrp_plm.action_production_order_create_eco
msgid "Create ECO"
msgstr "Crear una orden de cambio de ingeniería"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_stage_action
msgid "Create a new ECO stage"
msgstr "Crear una nueva etapa de orden de cambio de ingeniería"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_type_action_dashboard
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_type_action_form
msgid "Create a new ECO type"
msgstr "Crear un nuevo tipo de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Mensaje personalizado en caso de devolución"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__effectivity
msgid "Date on which the changes should be applied. For reference only."
msgstr "Fecha en la que deben aplicarse los cambios. Solo como referencia."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_defaults
msgid "Default Values"
msgstr "Valores predeterminados"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Define the approval roles on the ECO stages."
msgstr ""
"Defina las funciones de aprobación en las etapas de las órdenes de cambio de"
" ingeniería."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Delete"
msgstr "Eliminar"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__description
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Description"
msgstr "Descripción"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__description
msgid "Description and tooltips of the stage states."
msgstr "Descripción y herramientas de los estados de etapa."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Description of the change and its reason."
msgstr "Descripción del cambio y su motivo."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__bom_change_ids
msgid "Difference between old BoM and new BoM revision"
msgstr ""
"Diferencia entre la revisión de la lista de materiales antigua y de la nueva"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__routing_change_ids
msgid "Difference between old operation and new operation revision"
msgstr "Diferencia entre la revisión de la operación anterior y la nueva"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__displayed_image_id
msgid "Displayed Image"
msgstr "Imagen visualizada"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Documents"
msgstr "Documentos"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__done
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Done"
msgstr "Disponible"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__eco_id
msgid "ECO"
msgstr "Orden de cambio de ingeniería"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_graph
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_pivot
msgid "ECO Analysis"
msgstr "Análisis de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_approval
#: model:mail.activity.type,name:mrp_plm.mail_activity_eco_approval
msgid "ECO Approval"
msgstr "Aprobación de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_approval_template
msgid "ECO Approval Template"
msgstr "Plantilla de aprobación de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids
msgid "ECO BoM Changes"
msgstr "Cambios de la LdM de la orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids_on_byproduct
msgid "ECO BoM Changes - By-Product"
msgstr "Cambios a la LdM de la orden de cambio de ingeniería - subproducto"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids_on_line
msgid "ECO BoM Changes - Component"
msgstr "Cambios a la LdM de la orden de cambio de ingeniería - componente"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_bom_change
msgid "ECO BoM changes"
msgstr "Cambios de la LdM de la orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__eco_rebase_id
msgid "ECO Rebase"
msgstr "Transferencia de base de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__routing_change_ids
msgid "ECO Routing Changes"
msgstr "Cambios de enrutamiento de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_stage
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__eco_stage_id
msgid "ECO Stage"
msgstr "Etapa de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_stage_action
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_stages
msgid "ECO Stages"
msgstr "Etapas de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_tag_action
#: model:ir.model,name:mrp_plm.model_mrp_eco_tag
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_tag
msgid "ECO Tags"
msgstr "Etiquetas de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_type
msgid "ECO Type"
msgstr "Tipo de orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_type_action_form
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_types
msgid "ECO Types"
msgstr "Tipos de órdenes de cambio de ingeniería"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_stage_action
msgid "ECO stages give the different stages for the Engineering Change Orders"
msgstr ""
"Las etapas de la orden de cambio de ingeniería muestran los diferentes "
"estados para las órdenes de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__eco_ids
msgid "ECO to be applied"
msgstr "Orden de cambio de ingeniería que se aplicará"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_bom_view_form_inherit_plm
msgid "ECO(s)"
msgstr "Órdenes de cambio de ingeniería"

#. module: mrp_plm
#. odoo-javascript
#: code:addons/mrp_plm/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp_plm/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp_plm/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_ecos
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__eco_ids
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__eco_ids
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__eco_ids
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_report
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_calendar
#: model_terms:ir.ui.view,arch_db:mrp_plm.product_product_view_form_inherit_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.product_template_view_form_inherit_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.report_mrp_bom_inherit_mrp_plm
msgid "ECOs"
msgstr "Órdenes de cambio de ingeniería"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Eco"
msgstr "Orden de cambio de ingeniería"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Eco BoM"
msgstr "LdM de la orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__eco_count
msgid "Eco Count"
msgstr "Número de órdenes de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_routing_change
msgid "Eco Routing changes"
msgstr "Cambios en el enrutamiento de la orden de cambio de ingeniería"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "Edit Task"
msgstr "Editar tarea"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__effectivity
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,name:mrp_plm.ecostage_effective
msgid "Effective"
msgstr "Vigente"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__effectivity_date
msgid "Effective Date"
msgstr "Fecha efectiva"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_email
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "Email Alias"
msgstr "Seudónimo de correo electrónico"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__email_cc
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Email cc"
msgstr "CC del correo electrónico"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""
"Dominio del correo electrónico, por ejemplo, \"ejemplo.com\" en "
"\"<EMAIL>\""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__eco_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__eco_id
msgid "Engineering Change"
msgstr "Cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco
msgid "Engineering Change Order (ECO)"
msgstr "Orden de cambio de ingeniería (ECO)"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_approval
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_approval_my
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_late
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_main
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_product_tmpl
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_report
msgid "Engineering Change Orders"
msgstr "Órdenes de cambio de ingeniería"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "Engineering Changes"
msgstr "Cambios de ingeniería"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Extra Info"
msgstr "Información adicional"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Filters"
msgstr "Filtros"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__final_stage
msgid "Final Stage"
msgstr "Etapa final"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__folded
msgid "Folded in kanban view"
msgstr "Plegado en la vista de kanban"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_follower_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_partner_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (contactos)"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome, por ejemplo, fa-tasks"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__effectivity_date
msgid "For reference only."
msgstr "Solo como referencia."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_done
msgid "Green Kanban Label"
msgstr "Etiqueta kanban verde"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Etiqueta kanban gris"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Group by..."
msgstr "Agrupar por..."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__has_message
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: mrp_plm
#: model:ir.module.category,description:mrp_plm.module_category_manufacturing_product_lifecycle_management_(plm)
msgid "Helps you manage your product's lifecycles."
msgstr "Le ayuda a administrar los ciclos de vida de su producto."

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__priority__1
msgid "High"
msgstr "Alta"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__id
msgid "ID"
msgstr "ID"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID del registro principal que tiene el seudónimo (ejemplo: el proyecto que "
"contiene el seudónimo para la creación de tareas)"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono que indica una actividad de excepción."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_needaction
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_sms_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Si se establece, este contenido se enviará en automático a los usuarios no "
"autorizados en vez del mensaje predeterminado."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"engineering change order without removing it."
msgstr ""
"Si desmarca el campo activo podrá ocultar la orden de cambio de ingeniería "
"sin eliminarla."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__will_update_version
msgid ""
"If unchecked, the version of the product/BoM will remain unchanged once the "
"ECO is applied"
msgstr ""
"Si se deja sin marcar, la versión del producto o la lista de materiales no "
"cambiará una vez que se aplique la orden de cambio de ingeniería."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__normal
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_validated
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,name:mrp_plm.ecostage_progress
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "In Progress"
msgstr "En progreso"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_approved
msgid "Is Approved"
msgstr "Está aprobado"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_closed
msgid "Is Closed"
msgstr "Está cerrado"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_is_follower
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_rejected
msgid "Is Rejected"
msgstr "Está rechazado"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__mandatory
msgid "Is required to approve"
msgstr "Se requiere para aprobar"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Explicación del kanban bloqueado"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Explicación del kanban en curso"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__kanban_state
msgid "Kanban State"
msgstr "Estado de kanban"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__kanban_state_label
msgid "Kanban State Label"
msgstr "Etiqueta del estado de kanban"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_done
msgid "Kanban Valid Explanation"
msgstr "Explicación del kanban válida"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Late Activities"
msgstr "Actividades atrasadas"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__latest_bom_id
msgid "Latest Bom"
msgstr "Última lista de materiales"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Detección entrante basada en la parte local"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval_my
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_late
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_main
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_product_tmpl
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_report
msgid ""
"Manage your products and bills of materials changes with the ECO's.\n"
"              Gather the related documentation and receive the necessary approvals\n"
"              before applying your changes."
msgstr ""
"Gestione sus cambios en productos y en listas de materiales con las órdenes de cambio de ingeniería.\n"
"              Recopile los documentos relacionados y obtenga las aprobaciones necesarias\n"
"              antes de aplicar sus cambios."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_cycle_manual
msgid "Manual Duration Change"
msgstr "Cambio manual de duración"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_production
msgid "Manufacturing Order"
msgstr "Orden de fabricación"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__production_id
msgid "Manufacturing Orders"
msgstr "Órdenes de fabricación"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_master_data
msgid "Master Data"
msgstr "Datos maestros"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_error
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_error
msgid "Message Delivery error"
msgstr "Error al enviar el mensaje"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_mode
msgid "Mode Change"
msgstr "Cambio de modo"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_tag_view_search
msgid "Mrp Eco Tags"
msgstr ""
"Etiquetas de orden de cambio de ingeniería de planeación de recursos de "
"fabricación"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "My Change Orders"
msgstr "Mis órdenes de cambio"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "My Validations"
msgstr "Mis validaciones"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__name
msgid "Name"
msgstr "Nombre"

#. module: mrp_plm
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,name:mrp_plm.ecostage_new
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "New"
msgstr "Nuevo"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__new_bom_id
msgid "New Bill of Materials"
msgstr "Nueva lista de materiales"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid "New BoM from %(mo_name)s"
msgstr "Nueva LdM de %(mo_name)s"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__current_bom_id
msgid "New Bom"
msgstr "Nueva LdM"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_operation_id
msgid "New Consumed in Operation"
msgstr "Nuevo consumo en la operación"

#. module: mrp_plm
#: model:mrp.eco.type,name:mrp_plm.ecotype0
msgid "New Product Introduction"
msgstr "Introducción del nuevo producto"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_uom_id
msgid "New Product UoM"
msgstr "Nueva LdM de producto"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_product_qty
msgid "New revision quantity"
msgstr "Nueva cantidad de revisión"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Siguiente fecha límite de la actividad"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_bom_action_kanban
msgid "No bill of materials found. Let's create one!"
msgstr "No se encontró ninguna lista de materiales, creemos una"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval_my
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_late
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_main
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_product_tmpl
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_report
msgid "No engineering change order found"
msgstr "No se encontró ninguna orden de cambio de ingeniería"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__priority__0
msgid "Normal"
msgstr "Normal"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__none
msgid "Not Yet"
msgstr "Aún no"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__note
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Note"
msgstr "Nota"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_needaction_counter
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_error_counter
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_needaction_counter
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_error_counter
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__final_stage
msgid "Once the changes are applied, the ECOs will be moved to this stage."
msgstr ""
"Las órdenes de cambio de ingeniería se moverán a esta etapa después de "
"aplicar los cambios."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Open Component"
msgstr "Abrir componente"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Open Operation"
msgstr "Abrir operación"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__operation_name
msgid "Operation"
msgstr "Operación"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Operation Changes"
msgstr "Cambios de operación"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__operation_id
msgid "Operation Id"
msgstr "ID de la operación"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Operation not supported"
msgstr "Operación incompatible"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID opcional de un hilo (registro) al que se adjuntarán todos los mensajes "
"entrantes, incluso si no fueron respuestas del mismo. Si se establece, se "
"deshabilitará por completo la creación de nuevos registros."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_attachment_id
msgid "Origin Attachment"
msgstr "Archivo adjunto de origen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_res_model
msgid "Origin Model"
msgstr "Modelo de origen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_res_name
msgid "Origin Name"
msgstr "Nombre del origen"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_blocked
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection, when the ECO is in that stage."
msgstr ""
"Anule el valor predeterminado que aparece en el estado bloqueado de la vista"
" de kanban cuando la orden de cambio de ingeniería está en esa etapa."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_done
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection, when the ECO is in that stage."
msgstr ""
"Anule el valor predeterminado que aparece en el estado hecho de la vista de "
"kanban cuando la orden de cambio de ingeniería está en esa etapa."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_normal
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection, when the ECO is in that stage."
msgstr ""
"Anule el valor predeterminado que aparece en el estado normal de la vista de"
" kanban cuando la orden de cambio de ingeniería está en esa etapa."

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_dashboard
msgid "Overview"
msgstr "Información general"

#. module: mrp_plm
#: model:ir.module.category,name:mrp_plm.module_category_manufacturing_product_lifecycle_management_(plm)
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_root
msgid "PLM"
msgstr "PLM"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_type_action_dashboard
msgid "PLM Overview"
msgstr "Información general de gestión de ciclo de vida del producto"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_parent_model_id
msgid "Parent Model"
msgstr "Modelo principal"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID del hilo del registro principal"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modelo principal que posee el seudónimo. El modelo que contiene la "
"referencia del seudónimo no es necesariamente el modelo dado por "
"alias_model_id (por ejemplo: proyecto (parent_model) y tarea (model))"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"La política sobre cómo publicar un mensaje en el documento con el servidor de correo.\n"
"- Todos: todos pueden publicar\n"
"- Contactos: solo los contactos verificados\n"
"- Seguidores: solo los seguidores del documento relacionado o los miembros de los canales\n"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__previous_bom_id
msgid "Previous BoM"
msgstr "LdM anterior"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_operation_id
msgid "Previous Consumed in Operation"
msgstr "Consumo anterior en la operación"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__previous_change_ids
msgid "Previous ECO Changes"
msgstr "Cambios de orden de ingeniería anteriores"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Previous Eco Component Changes"
msgstr "Cambios de componentes de órdenes de cambio de ingeniería anteriores"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_uom_id
msgid "Previous Product UoM"
msgstr "UdM de producto anterior"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_product_qty
msgid "Previous revision quantity"
msgstr "Cantidad de revisión anterior"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__priority
msgid "Priority"
msgstr "Prioridad"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Produced in Operation"
msgstr "Producido en la operación"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_template
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__product_tmpl_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__product_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Product"
msgstr "Producto"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_document
msgid "Product Document"
msgstr "Documento del producto"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Product Only"
msgstr "Solo producto"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_product
msgid "Product Variant"
msgstr "Variante del producto"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Production"
msgstr "Producción"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__active
msgid "Production Ready"
msgstr "Listo para producción"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_products
msgid "Products"
msgstr "Productos"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__upd_product_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__rating_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_validated
msgid "Ready"
msgstr "Listo"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__rebase_id
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__rebase
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Rebase"
msgstr "Transferencia de base"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Rebase new revision of BoM with previous eco bom and old bom changes."
msgstr ""
"Modifique la nueva revisión de la LdM con la lista de la orden de cambio de "
"ingeniería anterior y los cambios pasados."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID del hilo de registro"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Etiqueta kanban roja"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__name
msgid "Reference"
msgstr "Referencia"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Reject"
msgstr "Rechazar"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__rejected
msgid "Rejected"
msgstr "Rechazado"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__displayed_image_attachment_id
msgid "Related attachment"
msgstr "Archivo adjunto relacionado"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__remove
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__remove
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Remove"
msgstr "Eliminar"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_reporting
msgid "Reporting"
msgstr "Reportes"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__required_user_ids
msgid "Requested Users"
msgstr "Usuarios solicitados"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_id
msgid "Responsible"
msgstr "Responsable"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__name
msgid "Role"
msgstr "Función"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Routing"
msgstr "Enrutamiento"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_sms_error
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error en el envío del SMS"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_search
msgid "Search"
msgstr "Búsqueda"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__sequence
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__sequence
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "Set Cover Image"
msgstr "Establecer imagen de portada"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_apply_change
msgid "Show Apply Change"
msgstr "Mostrar aplicar el cambio"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros cuya próxima fecha de acción es antes de la "
"fecha de hoy."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__stage_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__stage_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Stage"
msgstr "Etapa"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Descripción de la etapa e información sobre herramientas"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__stage_ids
msgid "Stages"
msgstr "Etapas"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Start Revision"
msgstr "Comenzar revisión"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "State"
msgstr "Estado"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__state
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__status
msgid "Status"
msgstr "Estado"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado según las actividades\n"
"Vencida: ya pasó la fecha límite\n"
"Hoy: hoy es la fecha de la actividad\n"
"Planeada: futuras actividades."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Successfully Rebased!"
msgstr "¡Se realizó la transferencia de base con éxito!"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid ""
"System will automatically resolved the conflict(s) and apply changes. Do you"
" agree?"
msgstr ""
"El sistema resolverá cualquier conflicto de forma automática y aplicará los "
"cambios. ¿Está de acuerdo?"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__name
msgid "Tag Name"
msgstr "Nombre de la etiqueta"

#. module: mrp_plm
#: model:ir.model.constraint,message:mrp_plm.constraint_mrp_eco_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Ya hay una etiqueta con este nombre."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__tag_ids
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_tag_view_tree
msgid "Tags"
msgstr "Etiquetas"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "Tarea en curso. Haga clic para bloquear o establecer como hecha."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""
"La tarea está bloqueada. Haga clic para desbloquear o configurar como hecha."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__user_can_approve
msgid "Technical field to check if approval by current user is required"
msgstr ""
"Campo técnico para verificar si la aprobación del usuario actual es "
"necesaria"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__user_can_reject
msgid "Technical field to check if reject by current user is possible"
msgstr ""
"Campo técnico para comprobar si el rechazo por el usuario actual es posible"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__approval_template_id
msgid "Template"
msgstr "Plantilla"

#. module: mrp_plm
#: model:res.groups,comment:mrp_plm.group_plm_manager
msgid "The PLM manager manages products lifecycle management"
msgstr ""
"El responsable de gestión del ciclo de vida del producto gestiona el ciclo "
"de vida de los productos"

#. module: mrp_plm
#: model:res.groups,comment:mrp_plm.group_plm_user
msgid "The PLM user uses products lifecycle management"
msgstr ""
"El usuario de gestión del ciclo de vida del producto utiliza la gestión del "
"ciclo de vida de los productos"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_product_product__version
#: model:ir.model.fields,help:mrp_plm.field_product_template__version
msgid "The current version of the product."
msgstr "La versión actual del producto."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"El modelo (tipo de documento de Odoo) al que corresponde este seudónimo. "
"Cualquier correo entrante que no sea respuesta a un registro existente "
"creará un nuevo registro de este modelo (por ejemplo, una tarea de "
"proyecto)."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El nombre del seudónimo de correo electrónico. Por ejemplo, \"trabajos\" si "
"desea recibir correos electrónicos en <<EMAIL>>."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr ""
"Este paso está hecho. Haga clic para bloquear o marcarlo como en progreso."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_validation
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "To Apply"
msgstr "Por aplicar"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "To Do"
msgstr "Por hacer"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__type_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__change_type
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__change_type
msgid "Type"
msgstr "Tipo"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de la actividad de excepción registrada."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__type_ids
msgid "Types"
msgstr "Tipos"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Undo"
msgstr "Deshacer"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__uom_change
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__update
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__update
msgid "Update"
msgstr "Actualizar"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__will_update_version
msgid "Update Version"
msgstr "Actualizar versión"

#. module: mrp_plm
#: model:res.groups,name:mrp_plm.group_plm_user
msgid "User"
msgstr "Usuario"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__user_ids
msgid "Users"
msgstr "Usuarios"

#. module: mrp_plm
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,name:mrp_plm.ecostage_validated
msgid "Validated"
msgstr "Validado"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Variant"
msgstr "Variante"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__version
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__version
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__version
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_bom_view_form_inherit_plm
msgid "Version"
msgstr "Versión"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_approvals
msgid "Waiting Approvals"
msgstr "En espera de aprobaciones"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_approvals_my
msgid "Waiting my Approvals"
msgstr "En espera de mis aprobaciones"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__website_message_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__website_message_ids
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__workcenter_id
msgid "Work Center"
msgstr "Centro de trabajo"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Uso del centro de producción"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_workcenters
msgid "Work Centers"
msgstr "Centros de trabajo"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"Incluso puede agregar una descripción para ayudar a sus compañeros de "
"trabajo a entender el significado y el propósito de esta etapa."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid ""
"You can define here labels that will be displayed for the state instead\n"
"                            of the default labels."
msgstr ""
"Aquí puede definir las etiquetas que aparecerán para el estado en lugar\n"
"                            de las etiquetas predeterminadas."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "You cannot change the stage, as approvals are required in the process."
msgstr "No puede cambiar la etapa, el proceso necesita aprobaciones."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "You cannot change the stage, as approvals are still required."
msgstr "No puede cambiar la etapa, las aprobaciones siguen siendo necesarias."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "alias"
msgstr "seudónimo"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "e.g. Awesome Product 2.0"
msgstr "Por ejemplo, Producto asombroso 2.0"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "e.g. Engineering Department"
msgstr "Por ejemplo, Departamento de Ingeniería"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "e.g. mycompany.com"
msgstr "Por ejemplo, miempresa.com"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "text-danger"
msgstr "text-danger"
