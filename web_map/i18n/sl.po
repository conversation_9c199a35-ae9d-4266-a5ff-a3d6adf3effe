# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_map
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid ""
".st0{opacity:0.3;enable-background:new;}\n"
"                .st1{fill:currentColor;stroke:#1A1919;stroke-width:3;stroke-miterlimit:10;}"
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                Sign up to MapBox to get a free token"
msgstr ""

#. module: web_map
#: model:ir.model,name:web_map.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Pogled okna za ukrep"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
#: model_terms:ir.ui.view,arch_db:web_map.view_res_partner_filter_inherit_map
msgid "Address"
msgstr "Naslov"

#. module: web_map
#: model:ir.model,name:web_map.model_base
msgid "Base"
msgstr "Osnova"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Close"
msgstr "Zaključi"

#. module: web_map
#: model:ir.model,name:web_map.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: web_map
#: model:ir.model,name:web_map.model_res_partner
msgid "Contact"
msgstr "Stik"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_partner__contact_address_complete
#: model:ir.model.fields,field_description:web_map.field_res_users__contact_address_complete
msgid "Contact Address Complete"
msgstr "Celotni kontaktni naslov"

#. module: web_map
#: model:ir.model,name:web_map.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP usmerjanje"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_view.js:0
msgid "Items"
msgstr "Artikli"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Locating new addresses..."
msgstr ""

#. module: web_map
#: model:ir.model.fields.selection,name:web_map.selection__ir_actions_act_window_view__view_mode__map
#: model:ir.model.fields.selection,name:web_map.selection__ir_ui_view__type__map
msgid "Map"
msgstr "Zemljevid"

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Map Routes"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "MapBox servers unreachable"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
msgid "Name"
msgstr "Naziv"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Navigate to"
msgstr ""

#. module: web_map
#: model:ir.model.fields,help:web_map.field_res_config_settings__map_box_token
msgid "Necessary for some functionalities in the map view"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "No"
msgstr "Ne"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "None"
msgstr "Brez"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Open"
msgstr "Odprto"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "OpenStreetMap's request limit exceeded, try again later."
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Set a MapBox account to activate routes and style"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Set up token"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Some routing points are too far apart"
msgstr ""

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
msgid "The MapBox server is unreachable"
msgstr ""

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
msgid "The token input is not valid"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid ""
"The view has switched to another provider but functionalities will be "
"limited"
msgstr ""

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
msgid "This referer is not authorized"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid ""
"To get routing on your map, you first need to set up your MapBox token. It's"
" free."
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Token"
msgstr ""

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_config_settings__map_box_token
msgid "Token Map Box"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Token invalid"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Too many requests, try again in a few minutes"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Too many routing points (maximum 25)"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Unauthorized connection"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Undefined"
msgstr "Nedoločeno"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "Unsuccessful routing request:"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_controller.js:0
msgid "Untitled"
msgstr "Brez naslova"

#. module: web_map
#: model:ir.model,name:web_map.model_ir_ui_view
msgid "View"
msgstr "Prikaz"

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_map.field_ir_ui_view__type
msgid "View Type"
msgstr "Vrsta prikaza"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
msgid "View in Google Maps"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
msgid "Yes"
msgstr "Da"

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/models.py:0
msgid "You need to set a Contact field on this model to use the Map View"
msgstr ""
