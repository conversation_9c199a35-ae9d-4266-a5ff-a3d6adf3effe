# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_mps
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "<span invisible=\"enable_batch_size\">No Batch Size</span>"
msgstr "<span invisible=\"enable_batch_size\">لا يوجد حجم محدد للدفعة</span> "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "<span invisible=\"enable_max_replenish\">No Maximum</span>"
msgstr "<span invisible=\"enable_max_replenish\">لا يوجد حد أقصى</span> "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_bom_form_view_inherit_mps
msgid "<span>Schedules</span>"
msgstr "<span>الجداول</span> "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "ATP"
msgstr "متاح للوعد (ATP) "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Actual"
msgstr "الفعلي "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand"
msgstr "الطلب الفعلي "

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid "Actual Demand %(product)s %(date)s (%(date_start)s - %(date_end)s)"
msgstr "الطلب الفعلي %(product)s %(date)s (%(date_start)s - %(date_end)s) "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand Y-1"
msgstr "الطلب الفعلي Y-1 "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand Y-2"
msgstr "الطلب الفعلي Y-2 "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__total_qty
msgid "Actual Replenishment"
msgstr "تجديد المخزون الفعلي "

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid ""
"Actual Replenishment %(product)s %(date)s (%(date_start)s - %(date_end)s)"
msgstr ""
"تجديد المخزون الفعلي %(product)s %(date)s (%(date_start)s - %(date_end)s) "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
#: model:ir.actions.act_window,name:mrp_mps.action_mrp_mps_form_view
msgid "Add a Product"
msgstr "إضافة منتج"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__allowed_route_ids
msgid "Allowed Route"
msgstr "المسار المسموح به "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Are you sure you want to delete these records?"
msgstr "هل أنت متأكد من أنك ترغب في حذف تلك السجلات؟ "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Are you sure you want to delete this record?"
msgstr "هل أنت متأكد من أنك ترغب في حذف هذا السجل؟ "

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_trigger__automated
msgid "Automatic"
msgstr "تلقائي "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Automatically Replenished"
msgstr "تجديد المخزون تلقائياً "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Available to Promise"
msgstr "متاح للوعد "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__batch_size
msgid "Batch Size"
msgstr "حجم الدفعة "

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_bom
msgid "Bill of Material"
msgstr "قائمة المواد"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__bom_id
msgid "Bill of Materials"
msgstr "قائمة المواد"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Cancel"
msgstr "إلغاء"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Close"
msgstr "إغلاق"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__company_id
msgid "Company"
msgstr "الشركة "

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Confirmation"
msgstr "التأكيد "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__day
msgid "Daily"
msgstr "يوميًا"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__date
msgid "Date"
msgstr "التاريخ"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Day"
msgstr "اليوم"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Default Time Range"
msgstr "النطاق الافتراضي للوقت "

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "وحدة القياس الافتراضية المستخدمة لكافة عمليات المخزون. "

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,help:mrp_mps.field_res_config_settings__manufacturing_period
msgid ""
"Default value for the time ranges in Master Production Schedule report."
msgstr "القيمة الافتراضية للنطاق الزمني في تقرير جدول الإنتاج الرئيسي."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Delete"
msgstr "حذف"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__forecast_qty
msgid "Demand Forecast"
msgstr "توقعات الطلب"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand
msgid "Display Actual Demand"
msgstr "عرض الطلب الفعلي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_2
msgid "Display Actual Demand Before Year"
msgstr "عرض الطلب الفعلي قبل سنة "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_1
msgid "Display Actual Demand Last Year"
msgstr "عرض الطلب الفعلي السنة الماضية "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_replenishment
msgid "Display Actual Replenishment"
msgstr "عرض تجديد المخزون الفعلي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_available_to_promise
msgid "Display Available to Promise"
msgstr "عرض المتاح للوعد "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_demand_forecast
msgid "Display Demand Forecast"
msgstr "عرض توقعات الطلب "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_indirect_demand
msgid "Display Indirect Demand"
msgstr "عرض الطلب غير المباشر "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_safety_stock
msgid "Display Safety Stock"
msgstr "عرض المخزون الاحتياطي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_starting_inventory
msgid "Display Starting Inventory"
msgstr "عرض المخزون المبدأي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_to_replenish
msgid "Display To Replenish"
msgstr "العرض لتجديد المخزون "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__enable_batch_size
msgid "Enable Batch Size"
msgstr "تمكين حجم الدفعة "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__enable_max_replenish
msgid "Enable Max Replenish"
msgstr "تمكين خاصية تجديد المخزون للحد الأقصى "

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__excessive_replenishment
msgid "Excessive Replenishment"
msgstr "تجديد المخزون فوق الحاجة "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Export"
msgstr "تصدير"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "External ID"
msgstr "معرف خارجي"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_mps_forecast_details
msgid "Forecast Demand Details"
msgstr "تفاصيل الطلب المتوقع "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Forecasted"
msgstr "المتوقعة "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Forecasted Demand"
msgstr "الطلب المتوقع "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Forecasted Stock"
msgstr "المخزون المتوقع "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_ids
msgid "Forecasted quantity at date"
msgstr "الكمية المتوقعة بتاريخ "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__id
msgid "ID"
msgstr "المُعرف"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__bom_id
msgid "If set, the bill of materials components will also be imported."
msgstr "إذا تم تعيينه، سيتم استيراد مكونات قائمة المواد أيضاً. "

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__batch_size
msgid ""
"If set, the generated manufacturing orders will be split in quantities of "
"this value at maximum."
msgstr ""
"إذا تم تعيينه، سيتم تقسيم أوامر التصنيع المُنشأة بكميات تبلغ هذه القيمة كحد "
"أقصى. "

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid "Import Template for Master Production Schedule"
msgstr "استيراد قالب لجدول الإنتاج الرئيسي "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Indirect Demand"
msgstr "الطلب غير المباشر "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Indirect Demand Forecast"
msgstr "توقع الطلب غير المباشر "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__is_indirect
msgid "Indirect demand product"
msgstr "منتج الطلب غير المباشر "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__is_manufacture_route
msgid "Is Manufacture Route"
msgstr "مسار تصنيع "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_menu_planning
msgid "MPS"
msgstr "جدول التخطيط الرئيسي "

#. module: mrp_mps
#: model:ir.actions.server,name:mrp_mps.ir_cron_replenish_automated_mps_ir_actions_server
msgid "MPS: replenish automated schedules"
msgstr "جدول التخطيط الرئيسي (MPS): التجديد حسب الجداول المؤتمتة "

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_trigger__manual
msgid "Manual"
msgstr "يدوي "

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__replenish_trigger
msgid ""
"Manual: Product to be replenished manually from MPS.\n"
"Automatic: Product replenished automatically via scheduled action.\n"
"Never: Product is not replenished from MPS."
msgstr ""
"يدوي: تجديد مخزون المنتج يدوياً من جدول التخطيط الرئيسي. \n"
"تلقائي: تجديد المنتج تلقائياً عن طريق إجراء مجدول. \n"
"مطلقاً: لا يتم تجديد مخزون المنتج من جدول التخطيط الرئيسي. "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Manually Replenished"
msgstr "يتم تجديد المخزون يدوياً "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__manufacture_string
msgid "Manufacture String"
msgstr "نَص التصنيع "

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
msgid "Manufacturing Order"
msgstr "أمر التصنيع "

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Manufacturing Orders"
msgstr "أوامر التصنيع"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period
msgid "Manufacturing Period"
msgstr "فترة التصنيع"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/res_company.py:0
msgid ""
"Manufacturing Settings: Your Master Production Schedule must always display "
"at least 1 period."
msgstr ""
"إعدادات التصنيع: يجب أن يعرض جدول الإنتاج الرئيسي الخاص بك دائماً فترة واحدة"
" على الأقل. "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
#: model:ir.actions.client,name:mrp_mps.action_mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_report_menu
#: model:ir.ui.menu,name:mrp_mps.stock_mrp_mps_report_menu
msgid "Master Production Schedule"
msgstr "جدول الإنتاج الرئيسي"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid "Maximum to Replenish"
msgstr "الحد الأقصى لتجديد المخزون "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid "Minimum to Replenish"
msgstr "الحد الأدنى لتجديد المخزون "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Month"
msgstr "الشهر"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__month
msgid "Monthly"
msgstr "شهرياً"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__move_ids
msgid "Move"
msgstr "حركة"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__moves_string
msgid "Moves String"
msgstr "نَص الحركات "

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_trigger__never
msgid "Never"
msgstr "مطلقاً "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid "No product yet. Add one to start scheduling."
msgstr "لا يوجد منتج بعد. قم بإضافة واحد لبدء الجدولة. "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Not Replenished from MPS"
msgstr "لا يتم تجديد المخزون من جدول التخطيط الرئيسي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_day
msgid "Number of Daily Manufacturing Period Columns"
msgstr "عدد أعمدة فترة التصنيع اليومي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_month
msgid "Number of Monthly Manufacturing Period Columns"
msgstr "عدد أعمدة فترة التصنيع الشهري "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Number of Periods"
msgstr "عدد الفترات "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_week
msgid "Number of Weekly Manufacturing Period Columns"
msgstr "عدد أعمدة فترة التصنيع الأسبوعي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_year
msgid "Number of Yearly Manufacturing Period Columns"
msgstr "عدد أعمدة فترة التصنيع السنوي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_day
msgid ""
"Number of columns for the daily period to display in Master Production "
"Schedule"
msgstr "عدد الأعمدة في الفترة اليومية ليتم عرضها في جدول الإنتاج الرئيسي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_month
msgid ""
"Number of columns for the monthly period to display in Master Production "
"Schedule"
msgstr "عدد الأعمدة في الفترة الشهرية ليتم عرضها في جدول الإنتاج الرئيسي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_week
msgid ""
"Number of columns for the weekly period to display in Master Production "
"Schedule"
msgstr "عدد الأعمدة في الفترة الأسبوعية ليتم عرضها في جدول الإنتاج الرئيسي "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_year
msgid ""
"Number of columns for the yearly period to display in Master Production "
"Schedule"
msgstr "عدد الأعمدة في الفترة السنوية ليتم عرضها في جدول الإنتاج الرئيسي "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/components/main.js:0
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid "Order"
msgstr "طلب "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__route_id
msgid "Preferred Route"
msgstr "المسار المفضل "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__procurement_launched
msgid "Procurement has been run for this forecast"
msgstr "لقد تم تشغيل الشراء لهذا التوقع "

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_template
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_id
msgid "Product"
msgstr "المنتج"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_category_id
msgid "Product Category"
msgstr "فئة المنتج"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_product_forecast
msgid "Product Forecast at Date"
msgstr "توقعات المنتج في تاريخه "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_tmpl_id
msgid "Product Template"
msgstr "قالب المنتج"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Product UoM"
msgstr "وحدة قياس المنتج"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__production_schedule_id
msgid "Production Schedule"
msgstr "جدول الإنتاج "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__warehouse_id
msgid "Production Warehouse"
msgstr "مستودع الإنتاج "

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_purchase_order
msgid "Purchase Order"
msgstr "أمر شراء"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__purchase_order_line_ids
msgid "Purchase Order Line"
msgstr "بند أمر الشراء"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__moves_qty
msgid "Quantity from Incoming Moves"
msgstr "الكمية من الحركات الواردة "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__manufacture_qty
msgid "Quantity from Manufacturing Order"
msgstr "الكمية من أمر التصنيع "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__rfq_qty
msgid "Quantity from RFQ"
msgstr "الكمية من طلب عرض السعر "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"Quantity predicted to be available for sale at the end of the period (= to "
"replenish - actual demand)."
msgstr ""
"الكمية المتوقع توافرها للبيع في نهاية الفترة (=لتجديد المخزون - الطلب "
"الفعلي). "

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
msgid "Receipt"
msgstr "إيصال "

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Receipts"
msgstr "الإيصالات"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Replenish"
msgstr "تجديد المخزون "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Replenish State"
msgstr "حالة تجديد المخزون "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty_updated
msgid "Replenish_qty has been manually updated"
msgstr "لقد تم تحديث Replenish_qty يدوياً "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too High"
msgstr "تم تجديد المخزون فوق الحاجة "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too Low"
msgstr "تم تجديد المخزون أقل من المطلوب "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__replenish_trigger
msgid "Replenishment Trigger"
msgstr "مشغّل تجديد المخزون "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Replenishment:"
msgstr "تجديد المخزون: "

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
msgid "Request for quotation"
msgstr "طلب عرض سعر "

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Requests for quotation"
msgstr "طلبات عرض السعر "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__rfq_string
msgid "Rfq String"
msgstr "نَص طلب عرض السعر "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Route"
msgstr "المسار "

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__route_id
msgid "Route to replenish your product."
msgstr "المسار لتجديد مخزون منتجك. "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Rows"
msgstr "الصفوف"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid "Safety Stock Target"
msgstr "هدف المخزون الاحتياطي "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Save"
msgstr "حفظ"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_production_schedule
msgid "Schedule the production of Product in a warehouse"
msgstr "جدولة إنتاج المنتج في المستودع "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_purchase_order__date_planned_mps
msgid "Scheduled Date"
msgstr "التاريخ المجدول"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_product_product__schedule_count
#: model:ir.model.fields,field_description:mrp_mps.field_product_template__schedule_count
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_normal_form_view_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_template_only_form_view_mps
msgid "Schedules"
msgstr "الجداول "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__mps_sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__show_bom
msgid "Show Bom"
msgstr "إظهار قائمة المواد "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__show_vendor
msgid "Show Vendor"
msgstr "إظهار المورّد "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Starting Inventory"
msgstr "المخزون المبدأي "

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_stock_rule
msgid "Stock Rule"
msgstr "قاعدة المخزون"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Technical field to support filtering by replenish state"
msgstr "حقل تقني لدعم التصفية بناءً على حالة تجديد المخزون "

#. module: mrp_mps
#: model:ir.model.constraint,message:mrp_mps.constraint_mrp_production_schedule_warehouse_product_ref_uniq
msgid "The combination of warehouse and product must be unique!"
msgstr "يجب أن يكون المزيج بين المستودع والمنتج فريدا! "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The confirmed demand, based on the confirmed sales orders."
msgstr "الطلب المؤكد بناءً على أوامر البيع المؤكدة. "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"The forecasted demand to fulfill the needs in components of the "
"Manufacturing Orders."
msgstr "الطلب المتوقع لاستيفاء احتياجات مكونات أوامر التصنيع. "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted demand. This value has to be entered manually."
msgstr "الطلب المتوقع. يجب إدخال هذه القيمة يدوياً. "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted quantity in stock at the beginning of the period."
msgstr "الكمية المتوقعة في المخزون في بداية الفترة. "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted quantity in stock at the end of the period."
msgstr "الكمية المتوقعة في المخزون في نهاية الفترة. "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid ""
"The master schedule translates your sales and demand forecasts into a production and purchase planning for each component.\n"
"                    It ensures everything gets scheduled on time, based on constraints such as: safety stock, production capacity, lead times.\n"
"                    It's the perfect tool to support your S&OP meetings."
msgstr ""
"يقوم الجدول الرئيسي بترجمة توقعات مبيعاتك وطلباتك إلى مخططات إنتاج وشراء لكل مكون.\n"
"                    يضمن ذلك بأن تتم جدولة كل شيء في الوقت المحدد، بناءً على التقييدات مثل: المخزون الاحتياطي وسعة الإنتاج والمهل المتاحة.\n"
"                    إنه الأداة المناسبة لدعم اجتماعات تخطيط المبيعات والعمليات الخاصة بك. "

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid ""
"The maximum replenishment you would like to launch for each period in the "
"MPS. This is only applied for the period defined in the settings. Note that "
"if the demand is higher than that amount, the remaining quantity will be "
"transferred to the next period automatically."
msgstr ""
"الحد الأقصى لتجديد المخزون الذي تود تنفيذه لكل فترة في جدول التخطيط الرئيسي."
" يتم تطبيق ذلك فقط على الفترات المحددة في الإعدادات. يرجى الملاحظة بأنه إذا "
"زاد الطلب عن الكمية، سيتم نقل الكمية المتبقية إلى الفترة التالية تلقائياً. "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"The quantity being replenished, based on the Requests for Quotation and the "
"Manufacturing Orders."
msgstr "الكمية التي يتم تجديدها بناءً على طلبات عروض الأسعار وأوامر التصنيع. "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"The quantity to replenish through Purchase Orders or Manufacturing Orders."
msgstr "الكمية لتجديدها من خلال أوامر الشراء أو أوامر التصنيع. "

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid ""
"This is the minimum free stock you want to keep for that product at all "
"times."
msgstr ""
"هذا هو الحد الأدنى للمخزون المتاح الذي يجب إبقاؤه لذلك المنتج في كل الأحيان."
" "

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "To Forecast"
msgstr "المتوقّع "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__to_replenish
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "To Replenish"
msgstr "للتجديد "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Toggle Indirect Demand"
msgstr "التبديل للطلب غير المباشر "

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__under_replenishment
msgid "Under Replenishment"
msgstr "يتم تجديد المخزون "

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid ""
"Unless the demand is 0, Odoo will always at least replenish this quantity."
msgstr ""
"سيقوم أودو بتجديد المخزون بإضافة هذه الكمية على الأقل دائماً، إلى كان كان "
"الطلب هو 0. "

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__supplier_id
msgid "Vendor"
msgstr "المورّد "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Week"
msgstr "الأسبوع"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/res_company.py:0
msgid "Week %(week_num)s (%(start_date)s-%(end_date)s/%(month)s)"
msgstr "الأسبوع %(week_num)s (%(start_date)s-%(end_date)s/%(month)s) "

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__week
msgid "Weekly"
msgstr "أسبوعيًا"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__is_indirect
msgid ""
"When checked, this product will not appear in the 'To Forecast' filter."
msgstr "عند تحديده، لن يظهر هذا المنتج في فلتر \"الكمية التي يجب توقعها\". "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Year"
msgstr "السنة"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__year
msgid "Yearly"
msgstr "سنويًا"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "by"
msgstr "بواسطة"
