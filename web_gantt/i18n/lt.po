# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_gantt
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <linaskrisiu<PERSON>@gmail.com>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"Language: lt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Are you sure to delete this record?"
msgstr "Ar tikrai norite ištrinti šį įrašą?"

#. module: web_gantt
#: model:ir.model,name:web_gantt.model_base
msgid "Base"
msgstr "Bazė"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.xml:0
msgid "Collapse rows"
msgstr "Sutraukti eilutes"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_cell_buttons.xml:0
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Create"
msgstr "Sukurti"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Day"
msgstr "Diena"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Edit"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.xml:0
msgid "Expand rows"
msgstr "Išskleisti eilutes"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_view.js:0
msgid "Gantt"
msgstr "Gantt"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Gantt View"
msgstr "Ganto vaizdas"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt child can only be field or template, got %s"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_start' attribute"
msgstr "Ganto diagrama privalo turėti pradžios ir pabaigos požymius"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_stop' attribute"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'dependency_inverted_field' attribute once the 'dependency_field' is specified"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt view can contain only one templates tag"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "Grouping by date is not supported"
msgstr "Grupavimas pagal datas nepalaikomas"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Insufficient fields for Gantt View!"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid attributes (%s) in gantt view. Attributes must be in (%s)"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid default_scale '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Month"
msgstr "Mėnuo"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Name"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.xml:0
msgid "New"
msgstr "Naujas"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.xml:0
msgid "Next"
msgstr "Kitas"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Open"
msgstr "Atidaryti"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Plan"
msgstr "Suplanuoti"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_cell_buttons.xml:0
msgid "Plan existing"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.xml:0
msgid "Previous"
msgstr "Ankstesnis"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Records that are in the past cannot be automatically rescheduled. They should be manually rescheduled instead."
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Start"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Stop"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.xml:0
msgid "Today"
msgstr "Šiandien"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Total"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "Undefined %s"
msgstr "Nežinoma %s"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
#: model:ir.model,name:web_gantt.model_ir_ui_view
msgid "View"
msgstr "Peržiūrėti"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Week"
msgstr "Savaitė"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Year"
msgstr "Metai"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot reschedule %s towards %s."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot reschedule tasks that do not follow a direct dependency path. Only the first task has been automatically rescheduled."
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "hours"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "minutes"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "months"
msgstr ""
