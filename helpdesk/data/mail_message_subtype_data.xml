<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">
    <!-- Ticket related subtypes for messaging / Chatter -->
    <record id="mt_ticket_new" model="mail.message.subtype">
        <field name="name">Ticket Created</field>
        <field name="sequence">0</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="False"/>
        <field name="hidden" eval="True"/>
        <field name="description">Ticket created</field>
    </record>
    <record id="mt_ticket_rated" model="mail.message.subtype">
        <field name="name">Ticket Rated</field>
        <field name="sequence">20</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="hidden" eval="False"/>
    </record>
    <record id="mt_ticket_stage" model="mail.message.subtype">
        <field name="name">Stage Changed</field>
        <field name="sequence">10</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="False"/>
        <field name="internal" eval="True"/>
        <field name="description">Stage Changed</field>
    </record>
    <record id="mt_ticket_refund_status" model="mail.message.subtype">
        <field name="name">Refund Status</field>
        <field name="sequence">11</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="False"/>
        <field name="internal" eval="True"/>
        <field name="description"></field>
    </record>
    <record id="mt_ticket_return_status" model="mail.message.subtype">
        <field name="name">Return Status</field>
        <field name="sequence">13</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="False"/>
        <field name="internal" eval="True"/>
        <field name="description"></field>
    </record>
    <record id="mt_ticket_repair_status" model="mail.message.subtype">
        <field name="name">Repair Status</field>
        <field name="sequence">15</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="False"/>
        <field name="internal" eval="True"/>
        <field name="description"></field>
    </record>

    <!-- Team related subtypes for messaging / Chatter -->
    <record id="mt_team_ticket_new" model="mail.message.subtype">
        <field name="name">Ticket Created</field>
        <field name="sequence">0</field>
        <field name="res_model">helpdesk.team</field>
        <field name="default" eval="True"/>
        <field name="parent_id" ref="mt_ticket_new"/>
        <field name="relation_field">team_id</field>
    </record>
    <record id="mt_team_ticket_rated" model="mail.message.subtype">
        <field name="name">Ticket Rated</field>
        <field name="sequence">15</field>
        <field name="res_model">helpdesk.team</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="parent_id" ref="mt_ticket_rated"/>
        <field name="relation_field">team_id</field>
    </record>
</data></odoo>
