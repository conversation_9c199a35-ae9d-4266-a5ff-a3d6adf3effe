<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="helpdesk_ticket_view_pivot_analysis" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.pivot</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <pivot string="Ticket Analysis" display_quantity="1" disable_linking="1" sample="1">
                <field name="team_id" type="row"/>
                <field name="ticket_open_hours" widget="float_time"/>
                <field name="ticket_assignation_hours" widget="float_time" type="measure"/>
                <field name="first_response_hours" widget="float_time" type="measure"/>
                <field name="avg_response_hours" widget="float_time" type="measure"/>
                <field name="ticket_close_hours" widget="float_time" type="measure"/>
                <field name="ticket_deadline_hours" widget="float_time"/>
                <field name="rating_avg" type="measure" invisible="1"/>
            </pivot>
        </field>
    </record>

    <record id="helpdesk_ticket_view_pivot_analysis_dashboard" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.pivot.dashboard</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_pivot_analysis"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='team_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_ticket_view_pivot_7days_analysis_inherit_dashboard" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.pivot.7days.analysis.inherit</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_pivot_analysis"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='team_id']" position="replace">
                <field name="close_date" interval="day" type="row"/>
            </xpath>
            <field name="ticket_close_hours" position="replace"/>
            <field name="ticket_open_hours" position="replace"/>
            <field name="ticket_assignation_hours" position="replace"/>
        </field>
    </record>

    <record id="helpdesk_ticket_view_pivot_analysis_success_inherit_dashboard" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.pivot.analysis.success.inherit</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="inherit_id" ref="helpdesk_ticket_view_pivot_7days_analysis_inherit_dashboard"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='close_date']" position="replace">
                <field name="ticket_id" type="row"/>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_ticket_view_pivot_analysis_7dayssuccess_inherit_dashboard" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.pivot.analysis.7dayssuccess.inherit</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="inherit_id" ref="helpdesk_ticket_view_pivot_7days_analysis_inherit_dashboard"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='close_date']" position="attributes">
                <attribute name="name">create_date</attribute>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_ticket_view_pivot_analysis_inherit_dashboard" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.pivot.analysis.inherit</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="inherit_id" ref="helpdesk_ticket_view_pivot_analysis_7dayssuccess_inherit_dashboard"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//pivot" position="attributes">
                <attribute name="display_quantity">1</attribute>
            </xpath>
            <field name="create_date" position="after">
                <field name="ticket_open_hours" widget="float_time" type="measure"/>
            </field>
        </field>
    </record>

    <record id="helpdesk_ticket_view_list_analysis" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.list</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <list string="Tickets" multi_edit="1" sample="1">
                <field name="ticket_id" string="Name" readonly="1"/>
                <field name="team_id" optional="show" readonly="1" column_invisible="context.get('default_team_id', False)"/>
                <field name="team_id" optional="hide" readonly="1" column_invisible="not context.get('default_team_id', False)"/>
                <field name="user_id" optional="show" widget="many2one_avatar_user"/>
                <field name="partner_id" optional="show"/>
                <field name="priority" optional="show" widget="priority"/>
                <field name="stage_id" optional="show" readonly="1"/>
                <field name="sla_deadline" optional="show" widget="remaining_days"/>
                <field name="company_id" groups="base.group_multi_company" optional="show" readonly="1" column_invisible="context.get('default_team_id', False)"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide" readonly="1" column_invisible="not context.get('default_team_id', False)"/>
            </list>
        </field>
    </record>

    <record id="helpdesk_ticket_view_graph_analysis" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.graph</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <graph string="Tickets Analysis" sample="1" disable_linking="1">
                <field name="team_id"/>
                <field name="stage_id"/>
                <field name="ticket_close_hours" widget="float_time"/>
                <field name="avg_response_hours" widget="float_time"/>
                <field name="ticket_assignation_hours" widget="float_time"/>
                <field name="first_response_hours" widget="float_time"/>
                <field name="ticket_deadline_hours" widget="float_time"/>
                <field name="ticket_open_hours" widget="float_time"/>
                <field name="rating_avg" type="measure" invisible="1"/>
            </graph>
        </field>
    </record>

    <record id="helpdesk_ticket_view_graph_analysis_dashboard" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.graph.analysis.dashboard</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_graph_analysis"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//graph" position="attributes">
                <attribute name="stacked">False</attribute>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_ticket_view_graph_analysis_inherit_dashboard" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.graph.analysis.inherit</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_graph_analysis"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//graph" position="attributes">
                <attribute name="stacked">False</attribute>
                <attribute name="order">DESC</attribute>
            </xpath>
            <xpath expr="//field[@name='team_id']" position="replace">
                <field name="create_date" interval="day"/>
            </xpath>
            <field name="stage_id" position="replace"/>
        </field>
    </record>

    <record id="helpdesk_ticket_report_view_cohort" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.cohort</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <cohort string="Tickets Analysis" date_start="create_date" date_stop="close_date" disable_linking="True" interval="day" sample="1"/>
        </field>
    </record>

    <record id="helpdesk_ticket_report_analysis_view_tree" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.list</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <list string="Tickets Analysis">
                <field name="ticket_id"/>
                <field name="priority" optional="show" widget="priority"/>
                <field name="team_id" optional="show"/>
                <field name="partner_id" widget="res_partner_many2one" optional="show"/>
                <field name="stage_id" optional="show"/>
            </list>
        </field>
    </record>

    <record id="helpdesk_ticket_report_analysis_view_search" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.search</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="helpdesk_tickets_view_search_base"/>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <search position="attributes"/>
        </field>
    </record>

    <!-- Action -->
    <record id="helpdesk_ticket_analysis_action" model="ir.actions.act_window">
       <field name="name">Tickets Analysis</field>
       <field name="res_model">helpdesk.ticket.report.analysis</field>
       <field name="path">tickets-analysis</field>
       <field name="view_mode">pivot,graph</field>
        <field name="search_view_id" ref="helpdesk_ticket_report_analysis_view_search"/>
        <field name="domain">['|', ('company_id', '=', False), ('company_id', 'in', allowed_company_ids)]</field>
        <field name="context">{
            'search_default_group_by_create_date': 1,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet!
            </p><p>
                Get statistics on your tickets and how long it takes to assign and resolve them.
            </p>
        </field>
    </record>

    <record id="action_helpdesk_ticket_analysis_graph" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="helpdesk_ticket_view_graph_analysis"/>
        <field name="act_window_id" ref="helpdesk_ticket_analysis_action"/>
    </record>

    <record id="action_helpdesk_ticket_analysis_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="helpdesk_ticket_view_pivot_analysis"/>
        <field name="act_window_id" ref="helpdesk_ticket_analysis_action"/>
    </record>

    <record id="action_helpdesk_ticket_analysis_cohort" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">cohort</field>
        <field name="view_id" ref="helpdesk_ticket_report_view_cohort"/>
        <field name="act_window_id" ref="helpdesk_ticket_analysis_action"/>
    </record>

    <record id="helpdesk_ticket_analysis_dashboard_action" model="ir.actions.act_window">
        <field name="name">Ticket Analysis</field>
        <field name="res_model">helpdesk.ticket.report.analysis</field>
        <field name="view_mode">pivot,graph,cohort</field>
        <field name="context">{
            'search_default_group_by_create_date': 1,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet!
            </p><p>
                Get statistics on your tickets and how long it takes to assign and resolve them.
            </p>
        </field>
    </record>

    <record id="action_helpdesk_ticket_analysis_dashboard_graph_view" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="helpdesk_ticket_view_graph_analysis_dashboard"/>
        <field name="act_window_id" ref="helpdesk_ticket_analysis_dashboard_action"/>
    </record>

    <record id="action_helpdesk_ticket_analysis_dashboard_pivot_view" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="helpdesk_ticket_view_pivot_analysis_dashboard"/>
        <field name="act_window_id" ref="helpdesk_ticket_analysis_dashboard_action"/>
    </record>

    <record id="helpdesk_ticket_action_7dayssuccess" model="ir.actions.act_window">
        <field name="name">Success Rate Analysis</field>
        <field name="res_model">helpdesk.ticket.report.analysis</field>
        <field name="view_mode">pivot,graph</field>
        <field name="domain" eval="[('close_date', '>=', (DateTime.today() - relativedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S'))]"/>
        <field name="search_view_id" ref="helpdesk.helpdesk_ticket_report_analysis_view_search"/>
        <field name="context">{
            'search_default_is_close': True,
            'search_default_my_ticket': True,
            'search_default_sla_success': True,
            'search_default_closed_on': 'custom_closed_on_last_7_days',
            'pivot_measures': ['__count__'],
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet!
            </p><p>
                Create tickets to get statistics.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_action_7dayssuccess_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="19"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="helpdesk.helpdesk_ticket_view_pivot_analysis_7dayssuccess_inherit_dashboard"/>
        <field name="act_window_id" ref="helpdesk_ticket_action_7dayssuccess"/>
    </record>

    <record id="helpdesk_ticket_action_7dayssuccess_graph" model="ir.actions.act_window.view">
        <field name="sequence" eval="29"/>
        <field name="view_mode">graph</field>
        <field name="act_window_id" ref="helpdesk_ticket_action_7dayssuccess"/>
    </record>

    <record id="helpdesk_ticket_action_dashboard" model="ir.actions.act_window">
        <field name="name">Ticket Analysis</field>
        <field name="res_model">helpdesk.ticket.report.analysis</field>
        <field name="view_mode">pivot,graph,cohort</field>
        <field name="search_view_id" ref="helpdesk.helpdesk_ticket_report_analysis_view_search"/>
        <field name="domain">[('stage_id.fold', '=', False)]</field>
        <field name="context">{
            'search_default_my_ticket': True,
            'search_default_is_open': True,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet!
            </p><p>
                Create tickets to get statistics.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_action_dashboard_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="16"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="helpdesk.helpdesk_ticket_view_pivot_analysis_inherit_dashboard"/>
        <field name="act_window_id" ref="helpdesk_ticket_action_dashboard"/>
    </record>

    <record id="helpdesk_ticket_action_dashboard_graph" model="ir.actions.act_window.view">
        <field name="sequence" eval="25"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="helpdesk.helpdesk_ticket_view_graph_analysis_inherit_dashboard"/>
        <field name="act_window_id" ref="helpdesk_ticket_action_dashboard"/>
    </record>

</odoo>
