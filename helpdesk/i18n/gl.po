# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * helpdesk
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2018-01-15 14:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Galician (https://www.transifex.com/odoo/teams/41243/gl/)\n"
"Language: gl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__answered_customer_message_count
msgid "# Exchanges"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__open_ticket_count
msgid "# Open Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_count
msgid "# Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sla_policy_count
msgid "# SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__urgent_ticket
msgid "# Urgent Ticket"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_sla.py:0
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "%s (copy)"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "(any of these tags)"
msgstr ""

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_1
msgid "2 days to start"
msgstr ""

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_2
msgid "7 days to finish"
msgstr ""

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_3
msgid "8 hours to finish"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "<b class=\"tip_title\">Tip: Create tickets from incoming emails</b>"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "<b>Drag &amp; drop</b> the card to change the stage of your ticket."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"<b>Log notes</b> for internal communications (you will only notify the persons you specifically tag). \n"
"    Use <b>@ mentions</b> to ping a colleague \n"
"    or <b># mentions</b> to contact a group of people."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "<center><strong><b>Good job!</b> You walked through all steps of this tour.</strong></center>"
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.rating_ticket_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"></t>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"></t>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the ticket \"<strong t-out=\"object.name or ''\">Table legs are unbalanced</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br><br>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <br><br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your ticket has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In Progress</b>.</span>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.solved_ticket_request_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.sudo().partner_id.name or 'Madam/Sir'\">Madam/Sir</t>,<br><br>\n"
"    This automatic message informs you that we have closed your ticket (reference <t t-out=\"object.id or ''\">15</t>).\n"
"    We hope that the services provided have met your expectations.\n"
"    If you have any more questions or comments, don't hesitate to reply to this e-mail to re-open your ticket.<br><br>\n"
"    Thank you for your cooperation.<br>\n"
"    Kind regards,<br><br>\n"
"    <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team.\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.new_ticket_request_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.sudo().partner_id.name or object.sudo().partner_name or 'Madam/Sir'\">Madam/Sir</t>,<br><br>\n"
"    Your request\n"
"    <t t-if=\"object.get_portal_url()\">\n"
"        <a t-attf-href=\"/my/ticket/{{ object.id }}/{{ object.access_token }}\" t-out=\"object.name or ''\">Table legs are unbalanced</a>\n"
"    </t>\n"
"    has been received and is being reviewed by our <t t-out=\"object.team_id.name or ''\">VIP Support</t> team.<br><br>\n"
"    The reference for your ticket is <strong><t t-out=\"object.id or ''\">15</t></strong>.<br><br>\n"
"\n"
"    To provide any additional information, simply reply to this email.<br><br>\n"
"    <t t-if=\"object.team_id.show_knowledge_base\">\n"
"        Don't hesitate to visit our <a t-attf-href=\"{{ object.team_id.get_knowledge_base_url() }}\">Help Center</a>. You might find the answer to your question.\n"
"        <br><br>\n"
"    </t>\n"
"    <t t-if=\"object.team_id.allow_portal_ticket_closing\">\n"
"        Feel free to close your ticket if our help is no longer needed. Thank you for your collaboration.<br><br>\n"
"    </t>\n"
"\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.get_portal_url()\" target=\"_blank\">View Ticket</a>\n"
"        <t t-if=\"object.team_id.allow_portal_ticket_closing\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"'/my/ticket/close/%s/%s' % (object.id, object.access_token)\" target=\"_blank\">Close Ticket</a>\n"
"        </t>\n"
"        <t t-if=\"object.team_id.use_website_helpdesk_forum or object.team_id.use_website_helpdesk_knowledge or object.team_id.use_website_helpdesk_slides\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.team_id.feature_form_url\" target=\"_blank\">Visit Help Center</a>\n"
"        </t><br><br>\n"
"    </div>\n"
"\n"
"    Best regards,<br><br>\n"
"    <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<i class=\"fa fa-envelope-o\" title=\"Domain alias\" role=\"img\" aria-label=\"Domain alias\"/>&amp;nbsp;"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" attrs=\"{'invisible': [('rating_avg', '&lt;', 3.66)]}\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" attrs=\"{'invisible': ['|', ('rating_avg', '&lt;', 2.33), ('rating_avg', '&gt;=', 3.66)]}\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" attrs=\"{'invisible': [('rating_avg', '&gt;=', 2.33)]}\" title=\"Dissatisfied\"/>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<i class=\"fa fa-lg fa-clock-o me-2 mt-1\" aria-label=\"Sla Deadline\" title=\"Sla Deadline\"/>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-lightbulb-o\" role=\"img\"/><span class=\"ms-2\">To use an email alias, the first step is to configure an Alias Domain. You can achieve this by navigating to the General Settings and configuring the corresponding field</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                                <span class=\"ms-2\">\n"
"                                    Type <b>/helpdesk</b> to create tickets<br/>\n"
"                                    Type <b>/helpdesk_search</b> to find tickets<br/>\n"
"                                    Type <b>:shortcut</b> to insert canned responses in your messages\n"
"                                </span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small class=\"text-end\">Stage:</small>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "<small>#</small>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" title=\"By saving this change, the customer phone number will also be updated.\" attrs=\"{'invisible': [('is_partner_phone_update', '=', False)]}\"/>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "<span class=\"o_field_widget o_readonly_modifier\">Working Hours</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text order-2\">Open</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text order-2\">Tickets</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Avg. Rating\n"
"                                </span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_partner_form_inherit_helpdesk
msgid "<span class=\"o_stat_text\"> Tickets</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text\">Customer Preview</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text\">Rating</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted\">Failed</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted\">Open</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted\">Unassigned</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted\">Urgent</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<span><b>Followers </b></span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Average Rating</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Reporting</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>SLA Success Rate</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Tickets Closed</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>View</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<span>Your ticket has successfully been closed. Thank you for your collaboration.</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<span>days of inactivity</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Managed by</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Reported on</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>After</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>Alias </strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong>Assigned to</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong>Customer</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong>Message and communication history</strong>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_defaults
msgid "A Python dictionary that will be evaluated to provide default values when creating new records for this alias."
msgstr ""

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_helpdesk_tag_name_uniq
msgid "A tag with the same name already exists."
msgstr ""

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_helpdesk_ticket_type_name_uniq
msgid "A type with the same name already exists."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__description
msgid "About Team"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Accept Emails From"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__access_instruction_message
msgid "Access Instruction Message"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_warning
msgid "Access warning"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction
msgid "Action Needed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__active
msgid "Active"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_ids
msgid "Activities"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_state
msgid "Activity State"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.mail_activity_type_action_config_helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config_activity_type
msgid "Activity Types"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Adapt your <b>pipeline</b> to your workflow by adding <b>stages</b> <i>(e.g. Awaiting Customer Feedback, etc.).</i>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid "Adapt your pipeline to your workflow and track the progress of your tickets."
msgstr ""

#. module: helpdesk
#: model:ir.actions.client,name:helpdesk.helpdesk_ticket_action_configure_properties_field
msgid "Add Properties"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Add a description to help your coworkers understand the meaning and purpose of the stage."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Add a description..."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Add your stage and place it at the right step of your workflow by dragging & dropping it."
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_manager
msgid "Administrator"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "After-Sales"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_id
msgid "Alias"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_email_from
msgid "Alias Email From"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_name
msgid "Alias Name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_status
msgid "Alias Status"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_domain
msgid "Alias domain"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__display_alias_name
msgid "Alias email"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "All"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_all
msgid "All Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__internal
msgid "All internal users (company)"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Allow your customers to close their own tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Archive Stages"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Archived"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Assign the ticket to a <b>member of your team</b>."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Assigned"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__user_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__user_id
msgid "Assigned To"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__user_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Assigned to"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Assignee"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__assign_method
msgid "Assignment Method"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid ""
"At each stage, employees can block tickets or mark them as ready for the next step.\n"
"                                You can customize the meaning of each state."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_attachment_count
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_auto_assignment
msgid "Auto Assigment"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Automate the assignment of new tickets to the right people, and make sure all tickets are being handled"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_assignment
msgid "Automatic Assignment"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_ticket
msgid "Automatic Closing"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Average"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__avg_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__avg_response_hours
msgid "Average Hours to Respond"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_avg
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Average Rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_avg_percentage
msgid "Average Rating (%)"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Dissatisfied"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Okay"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Satisfied"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Average rating for the last 7 days"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Avg 7 Days Customer Satisfaction"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Avg Last 7 days"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Avg Open Hours"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Bad"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Bill the time spent on your tickets to your customers"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_new
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_solved
msgid "Blocked"
msgstr ""

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_crm
msgid "CRM"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__campaign_id
msgid "Campaign"
msgstr ""

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_cancelled
msgid "Canceled"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Centralize, manage, share and grow your knowledge library"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Channels"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.xml:0
msgid "Click to set"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__close_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__close_date
msgid "Close date"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Close inactive tickets automatically"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close ticket"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Closed"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Closed Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_analysis
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_close_analysis
msgid "Closed Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__closed_by_partner
msgid "Closed by Partner"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Closed in Last 30 days"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Closed in Last 7 days"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
msgid "Closing Date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__allow_portal_ticket_closing
msgid "Closure by Customers"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__color
msgid "Color"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__color
msgid "Color Index"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
msgid "Comment"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_forum
msgid "Community Forum"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_company
msgid "Companies"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__company_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Company"
msgstr "Compañía"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config
msgid "Configuration"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Confirm"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/wizard/helpdesk_stage_delete.py:0
msgid "Confirmation"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "Congratulations!"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_partner
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Contact"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_coupons
msgid "Coupons"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Create Date"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.email_template_action_helpdesk
msgid "Create a new template"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid "Create teams to organize your tickets by expertise or geographical region, and define a different workflow for each team."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Create tickets by sending an email to an alias"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
msgid "Create tickets to get statistics."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__create_date
msgid "Created On"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__create_date
msgid "Created on"
msgstr "Creado o"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Creation Date"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Criteria"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Current stage of this ticket"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Customer"
msgstr "Cliente"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/models/res_company.py:0
#: model:helpdesk.team,name:helpdesk.helpdesk_team1
msgid "Customer Care"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_email
msgid "Customer Email"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_name
msgid "Customer Name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_phone
msgid "Customer Phone"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.rating_rating_action_helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_rating
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_ratings
msgid "Customer Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__partner_ids
msgid "Customers"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid "Customers will be added to the followers of their tickets."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Daily Target"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/views/helpdesk_team_kanban/helpdesk_team_kanban_view.js:0
msgid "Dashboard"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Datetime at which the SLA stage was reached for the first time"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_hours
msgid "Day to reach the stage of the SLA, without taking the working calendar into account"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__deadline
msgid "Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_defaults
msgid "Default Values"
msgstr ""

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.unlink_helpdesk_stage_action
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Delete"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Delete Stage"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Describe your team to your colleagues and customers..."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__description
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Description"
msgstr "Descrición"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Description of the policy..."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_digest_digest
msgid "Digest"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Discard"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__display_name
msgid "Display Name"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Dissatisfied"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Documentation"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__domain_user_ids
msgid "Domain User"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__balanced
msgid "Each user has an equal number of open tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__randomly
msgid "Each user is assigned an equal number of tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Edit"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_alias
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
msgid "Email Alias"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__template_id
msgid "Email Template"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__template_id
msgid ""
"Email automatically sent to the customer when the ticket reaches this stage.\n"
"By default, the email will be sent from the email alias of the helpdesk team.\n"
"Otherwise it will be sent from the company's email address, or from the catchall (as defined in the System Parameters)."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__email_cc
msgid "Email cc"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "Emails sent to"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "Emails sent to a Helpdesk Team alias generate tickets in your pipeline."
msgstr ""

#. module: helpdesk
#: model:mail.template,description:helpdesk.rating_ticket_request_email_template
msgid "Enable \"customer ratings\" feature on the helpdesk team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_slides
msgid "Enable eLearning"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Enter the <b>subject</b> of your ticket <br/><i>(e.g. Problem with my installation, Wrong order, etc.).</i>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__exceeded_hours
msgid "Exceeded Working Hours"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid "Excluding Stages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__failed
msgid "Failed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_fail
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_fail
msgid "Failed SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sla_failed
msgid "Failed SLA Ticket"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Failed Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_fsm
msgid "Field Service"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "First Assignment Date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__assign_date
msgid "First assignment date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__fold
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__fold
msgid "Folded in Kanban"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Follow All Team's Tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Followed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "Followed Teams"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_follower_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_follower_ids
msgid "Followers"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_partner_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Future Activities"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get in touch with your website visitors. Create and search tickets from your conversations. Answer in an instant by using canned responses."
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_dashboard_action
msgid "Get statistics on your tickets and how long it takes to assign and resolve them."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get tickets through an online form"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Grant discounts or free products"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid "Grant employees access to your helpdesk team or tickets by adding them as followers. Employees automatically get access to the tickets they are assigned to."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid "Grant portal users access to your helpdesk team or tickets by adding them as followers. Customers automatically get access to their tickets in their portal."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__done
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__done
msgid "Green"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_done
msgid "Green Kanban Label"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__normal
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__normal
msgid "Grey"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_normal
msgid "Grey Kanban Label"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: helpdesk
#: model:mail.activity.type,name:helpdesk.mail_act_helpdesk_handle
msgid "Handle Ticket"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Happy"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Happy face"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__has_external_mail_server
msgid "Has External Mail Server"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__has_message
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__has_message
msgid "Has Message"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_reached
msgid "Has SLA reached"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_reached_late
msgid "Has SLA reached late"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Help Center"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.menu_helpdesk_root
#: model_terms:ir.ui.view,arch_db:helpdesk.digest_digest_view_form
msgid "Helpdesk"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_dashboard_action_main
msgid "Helpdesk Overview"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage
msgid "Helpdesk Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage_delete_wizard
msgid "Helpdesk Stage Delete Wizard"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_tag
msgid "Helpdesk Tags"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__name
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Helpdesk Team"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Helpdesk Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_type
msgid "Helpdesk Ticket Type"
msgstr ""

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.ir_cron_auto_close_ticket_ir_actions_server
msgid "Helpdesk Ticket: Automatically close the tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Helpdesk Tickets"
msgstr ""

#. module: helpdesk
#: model:mail.template,name:helpdesk.new_ticket_request_email_template
msgid "Helpdesk: Request Acknowledgment"
msgstr ""

#. module: helpdesk
#: model:mail.template,name:helpdesk.solved_ticket_request_email_template
msgid "Helpdesk: Ticket Closed"
msgstr ""

#. module: helpdesk
#: model:mail.template,name:helpdesk.rating_ticket_request_email_template
msgid "Helpdesk: Ticket Rating Request"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "High Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__2
msgid "High priority"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "History"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_open_hours
msgid "Hours Open"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_assignation_hours
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Hours to Assign"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_close_hours
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Hours to Close"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__first_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__first_response_hours
msgid "Hours to First Response"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_deadline_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_deadline_hours
msgid "Hours to SLA Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "ID"
msgstr "ID"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid "ID of the parent record holding the alias (example: project holding the task creation alias)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__portal_show_rating
msgid ""
"If enabled, portal users will have access to your customer satisfaction statistics from the last 30 days in their portal.\n"
"They will only have access to the ratings themselves, and not to the written feedback if any was left. You can also manually hide ratings of your choosing."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_bounced_content
msgid "If set, this content will automatically be sent out to unauthorized users instead of the default message."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__time
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "In"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_normal:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_normal:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_normal:helpdesk.stage_new
#: model:helpdesk.stage,legend_normal:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_normal:helpdesk.stage_solved
#: model:helpdesk.stage,name:helpdesk.stage_in_progress
msgid "In Progress"
msgstr "En curso"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__from_stage_ids
msgid "In Stages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_day
msgid "Inactive Period(days)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__invited_internal
msgid "Invited internal users (private)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_is_follower
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_incident
msgid "Issue"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Issue credits notes"
msgstr ""

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_helpdesk_team_not_portal_show_rating_if_not_use_rating
msgid "It is necessary to enable customer ratings in the settings of your helpdesk team so that they can be displayed on the portal."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__kanban_state
msgid "Kanban State"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state_label
msgid "Kanban State Label"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_done
msgid "Kanban Valid Explanation"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_knowledge
msgid "Knowledge"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_digest_digest__kpi_helpdesk_tickets_closed_value
msgid "Kpi Helpdesk Tickets Closed Value"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 3 months"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
msgid "Last 30 Days"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 30 days"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
msgid "Last 7 Days"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 7 days"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__date_last_stage_update
msgid "Last Stage Update"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Late Activities"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Latest Ratings"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Let your customers answer each other's questions on a forum"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Let's create your first <b>ticket</b>."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Let's go back to the <b>kanban view</b> to get an overview of your next tickets."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Let's view your <b>team's tickets</b>."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live Chat"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Low Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__0
msgid "Low priority"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
msgid "Make sure tickets are handled in a timely manner by using SLA Policies.<br>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Make sure tickets are handled on time"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "Make sure tickets are handled on time by using SLA Policies.<br>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__time
msgid "Maximum number of working hours a ticket should take to reach the target stage, starting from the date it was created."
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid "Measure your customer satisfaction by sending rating requests when your tickets are solved."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__medium_id
msgid "Medium"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Medium Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__1
msgid "Medium priority"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_ir_ui_menu
msgid "Menu"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_mail_message
msgid "Message"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_ids
msgid "Messages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__priority
msgid "Minimum Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__stage_id
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__sla_stage_id
msgid "Minimum stage a ticket needs to reach in order to satisfy this SLA."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_ir_module_module
msgid "Module"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__to_stage_id
msgid "Move to Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "My Deadline"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "My Performance"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "My Teams"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_my
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "My Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__name
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_list_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Name"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Neutral face"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: model:helpdesk.stage,name:helpdesk.stage_new
msgid "New"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__assign_method
msgid "New tickets will automatically be assigned to the team members that are available, according to their working hours and their time off."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Newest"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "No Rating"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "No SLA policies found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.mail_activity_type_action_config_helpdesk
msgid "No activity types found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_dashboard_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_dashboard_action
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid "No data yet!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid "No stages found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "No tags found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid "No teams found"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "No teams found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
msgid "No tickets found"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid "No tickets found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "No types found. Let's create one!"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "None"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_failed
msgid "Number of SLA Failed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_successful
msgid "Number of SLA Success"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_ongoing
msgid "Number of SLA in Progress"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__ticket_count
msgid "Number of Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Number of open tickets with at least one SLA failed."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_open_ticket_count
msgid "Number of other open tickets from the same partner"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_count
msgid "Number of other tickets from the same partner"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Number of tickets closed in the past 7 days."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Okay"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__oldest_unanswered_customer_message_date
msgid "Oldest Unanswered Customer Message Date"
msgstr ""

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_on_hold
msgid "On Hold"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__ongoing
msgid "Ongoing"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Open"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Open Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__open_hours
msgid "Open Time (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid "Optional ID of a thread (record) to which all incoming messages will be attached, even if they did not reply to it. If set, this will disable the creation of new records completely."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_page
msgid "Our Customer Satisfaction"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
msgid "Our Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_team_dashboard
msgid "Overview"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_user_id
msgid "Owner"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid "Parent model holding the alias. The model holding the alias reference is not necessarily the model given by alias_model_id (example: project (parent_model) and task (model))"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__is_partner_email_update
msgid "Partner Email will Update"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__is_partner_phone_update
msgid "Partner Phone will Update"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_ids
msgid "Partner Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__privacy_visibility
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__team_privacy_visibility
msgid ""
"People to whom this helpdesk team and its tickets will be visible.\n"
"\n"
"- Invited internal users: internal users can access the team and the tickets they are following. This access can be modified on each ticket individually by adding or removing the user as follower.\n"
"A user with the helpdesk > administrator access right level can still access this team and its tickets, even if they are not explicitely part of the followers.\n"
"\n"
"- All internal users: all internal users can access the team and all of its tickets without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the team and all of its tickets without distinction.\n"
"Portal users can only access the tickets they are following. This access can be modified on each ticket individually by adding or removing the portal user as follower."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "People to whom this team and its tickets will be visible"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Percentage of tickets that were closed without failing any SLAs."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Percentage of tickets whose SLAs have successfully been reached on time over the total number of tickets closed within the past 7 days."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Performance"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_graph_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_pivot_analysis
msgid "Performance Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__auto_close_day
msgid "Period of inactivity after which tickets will be automatically closed."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Phone"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Plan onsite interventions"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a number."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a percentage below 100."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a positive value."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_url
msgid "Portal Access URL"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid "Portal users will be removed from the followers of the team and its tickets."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__priority
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__privacy_visibility_warning
msgid "Privacy Visibility Warning"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__properties
msgid "Properties"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Publish this team's ratings on your website"
msgstr ""

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_question
msgid "Question"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
msgid "Rated Tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__rating_last_value
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Rating (/5)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_avg_text
msgid "Rating Avg Text"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_feedback
msgid "Rating Last Feedback"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_image
msgid "Rating Last Image"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_value
msgid "Rating Last Value"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_text
msgid "Rating Text"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_count
msgid "Rating count"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_ids
msgid "Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__portal_show_rating
msgid "Ratings on Website"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
msgid "Reach Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__reached
msgid "Reached"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Reached Date"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_done:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_done:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_done:helpdesk.stage_new
#: model:helpdesk.stage,legend_done:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_done:helpdesk.stage_solved
msgid "Ready"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Receive notifications whenever tickets are created, rated or discussed on in this team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__blocked
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__blocked
msgid "Red"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_blocked
msgid "Red Kanban Label"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Reference"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_team_ticket_refund_cancel
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_refund_cancel
#: model:mail.message.subtype,name:helpdesk.mt_ticket_refund_cancel
msgid "Refund Canceled"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_team_ticket_refund_posted
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_refund_posted
#: model:mail.message.subtype,name:helpdesk.mt_ticket_refund_posted
msgid "Refund Posted"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_credit_notes
msgid "Refunds"
msgstr ""

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_repair
msgid "Repair"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_team_ticket_repair_cancel
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_repair_cancel
#: model:mail.message.subtype,name:helpdesk.mt_ticket_repair_cancel
msgid "Repair Canceled"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_team_ticket_repair_done
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_repair_done
#: model:mail.message.subtype,name:helpdesk.mt_ticket_repair_done
msgid "Repair Done"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_repairs
msgid "Repairs"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Reported on"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_main
msgid "Reporting"
msgstr "Reportaxe"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Restore"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_team_ticket_return_cancel
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_return_cancel
#: model:mail.message.subtype,name:helpdesk.mt_ticket_return_cancel
msgid "Return Canceled"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_team_ticket_return_done
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_return_done
#: model:mail.message.subtype,name:helpdesk.mt_ticket_return_done
msgid "Return Done"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Return faulty products"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_returns
msgid "Returns"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
msgid "SLA"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_deadline
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_deadline
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
msgid "SLA Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__failed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "SLA Failed"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action_main
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_users__sla_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_sla_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_partner__sla_ids
#: model:ir.model.fields,help:helpdesk.field_res_users__sla_ids
msgid "SLA Policies that will automatically apply to the tickets submitted by this customer."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__description
msgid "SLA Policy Description"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_reached_datetime
msgid "SLA Reached Date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_stage_id
msgid "SLA Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_status_ids
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
msgid "SLA Status"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_report_analysis_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_report_analysis_dashboard_action
#: model:ir.model,name:helpdesk.model_helpdesk_sla_report_analysis
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_sla_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "SLA Status Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_fail
msgid "SLA Status Failed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__reached
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "SLA Success"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "SLA Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__ongoing
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "SLA in Progress"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_ids
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "SLAs"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Sad face"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Sample"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Satisfied"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Save this ticket and the modifications you've made to it."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Schedule your <b>activity</b>."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
msgid "Search SLA Policies"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Assigned to"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Messages"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Reference"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_token
msgid "Security Token"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Select the <b>customer</b> of your ticket."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Self-Service"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.action_helpdesk_ticket_mass_mail
msgid "Send Email"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Send broken products for repair"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_service
msgid "Service"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set an Alias Domain"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set an Email Template on Stages"
msgstr ""

#. module: helpdesk
#: model:mail.template,description:helpdesk.new_ticket_request_email_template
#: model:mail.template,description:helpdesk.solved_ticket_request_email_template
msgid "Set this template on a project's stage to automate email when tasks reach stages"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Settings"
msgstr ""

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.model_helpdesk_ticket_action_share
msgid "Share"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Share presentations and videos, and organize them into courses"
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_rating
msgid "Show Customer Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__show_knowledge_base
msgid "Show Knowledge Base"
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_sla
msgid "Show SLA Policies"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__sla_id
msgid "Sla"
msgstr ""

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_solved
msgid "Solved"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__source_id
msgid "Source"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__stage_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Stage"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_stage
#: model:mail.message.subtype,name:helpdesk.mt_ticket_stage
msgid "Stage Changed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Description and States Meaning"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Search"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__stage_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_stage_menu
msgid "Stages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__stages_active
msgid "Stages Active"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__stage_ids
msgid "Stages the team will use. This team's tickets will only be able to be in these stages."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__status
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Status"
msgstr "Estado"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__name
msgid "Subject"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__success_rate
msgid "Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_success
msgid "Success Rate Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_success
msgid "Success SLA Policy"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_type_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_type_view_tree
msgid "Tag"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_tag_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__tag_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_tag_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_form
msgid "Tags"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Tags are perfect for organizing your tickets."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Target"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users__helpdesk_target_rating
msgid "Target Customer Rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__sla_stage_id
msgid "Target Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users__helpdesk_target_success
msgid "Target Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users__helpdesk_target_closed
msgid "Target Tickets to Close"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__team_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__member_ids
msgid "Team Members"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "Team Search"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__team_privacy_visibility
msgid "Team Visibility"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__team_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__team_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_menu
msgid "Teams"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.email_template_action_helpdesk
msgid "Templates"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_model_id
msgid "The model (Odoo Document Kind) to which this alias corresponds. Any incoming email that does not reply to an existing record will cause the creation of a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_name
msgid "The name of the email alias, e.g. 'jobs' if you want to catch emails for <<EMAIL>>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_user_id
msgid "The owner of records created upon receiving emails on this alias. If this field is not set the system will attempt to find the right owner based on the sender (From) address, or will use the Administrator account if no system user is found for that address."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "The team does not allow ticket closing through portal"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid "The time spent in these stages won't be taken into account in the calculation of the SLA."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid "The visibility of the team needs to be set as \"Invited portal users and all internal users\" in order to use the website form."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "There are currently no Ticket for your account."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_page
msgid "There are no ratings yet."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "This"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "This SLA Policy will apply to tickets matching ALL of the following criteria:"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__campaign_id
msgid "This is a name that helps you keep track of your different campaign efforts, e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__source_id
msgid "This is the source of the link, e.g. Search Engine, another domain, or name of email list"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
msgid "This will archive the stages and all of the tickets they contain from the following teams:"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.mail_activity_type_action_config_helpdesk
msgid "Those represent the different categories of things you have to do (e.g. \"Call\" or \"Send email\")."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Three stars, maximum score"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_activity
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_dashboard_action
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_report_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_analysis
msgid "Ticket Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_closed
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_closed
msgid "Ticket Closed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__ticket_count
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__ticket_count
msgid "Ticket Count"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__create_date
msgid "Ticket Create Date"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_new
#: model:mail.message.subtype,name:helpdesk.mt_ticket_new
msgid "Ticket Created"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_deadline
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_deadline
msgid "Ticket Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__ticket_ref
msgid "Ticket IDs Sequence"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_properties
msgid "Ticket Properties"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_rated
#: model:mail.message.subtype,name:helpdesk.mt_ticket_rated
msgid "Ticket Rated"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla_status
msgid "Ticket SLA Status"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_stage_id
msgid "Ticket Stage"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_stage
msgid "Ticket Stage Changed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.quick_create_ticket_form
msgid "Ticket Title"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_type_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_type_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Ticket Type"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Ticket closed by the customer"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_new
msgid "Ticket created"
msgstr ""

#. module: helpdesk
#. odoo-python
#. odoo-javascript
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_my_ticket_action_no_create
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_my
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_unassigned
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__ticket_count
#: model:ir.model.fields,field_description:helpdesk.field_res_users__ticket_count
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_list_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
msgid "Tickets"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_analysis
msgid "Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_digest_digest__kpi_helpdesk_tickets_closed
msgid "Tickets Closed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Tickets Search"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__fold
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__fold
msgid "Tickets in a folded stage are considered as closed."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_sale_timesheet
msgid "Time Billing"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_hours
msgid "Time to close (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_hours
msgid "Time to first assignment (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_timesheet
msgid "Timesheets"
msgstr ""

#. module: helpdesk
#: model:digest.tip,name:helpdesk.digest_tip_helpdesk_0
msgid "Tip: Create tickets from incoming emails"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid ""
"To get things done, plan activities and use the ticket status.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Today"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Today Activities"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Today Average Rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__total_response_hours
msgid "Total Exchange Time in Hours"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track &amp; Bill Time"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track customer satisfaction on tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_dashboard_action
msgid "Track the performance of your teams, the success rate of your tickets, and how quickly you reach your service level agreements (SLAs)."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track the time spent on tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_twitter
msgid "Twitter"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Two stars, with a maximum of three"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__ticket_type_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Type"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_type_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__ticket_type_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_type_menu
msgid "Types"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "Types are perfect for categorizing your tickets."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
msgid "Unarchive Tickets"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Unassigned"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__unassigned_tickets
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Unassigned Tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unread Messages"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__3
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Urgent"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Use <b>activities</b> to organize your daily work."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_coupons
msgid "Use Coupons"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_credit_notes
msgid "Use Credit Notes"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_rating
msgid "Use Customer Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_product_repairs
msgid "Use Repairs"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_product_returns
msgid "Use Returns"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your customers. \n"
"    Add new people to the followers' list to make them aware of the progress of this ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_users
#: model:res.groups,name:helpdesk.group_helpdesk_user
msgid "User"
msgstr ""

#. module: helpdesk
#: model:helpdesk.team,name:helpdesk.helpdesk_team3
msgid "VIP Support"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__privacy_visibility
msgid "Visibility"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Visibility &amp; Assignment"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Want to <b>boost your customer satisfaction</b>?<br/><i>Click Helpdesk to start.</i>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "We hope to have addressed your request satisfactorily. If you no longer need our assistance, please close this ticket. Thank you for your collaboration."
msgstr ""

#. module: helpdesk
#: model_terms:helpdesk.team,description:helpdesk.helpdesk_team1
#: model_terms:helpdesk.team,description:helpdesk.helpdesk_team3
msgid ""
"We provide 24/7 support, Monday through Friday. Ticket responses are usually provided within 2 working days.<br>\n"
"            Support is mainly provided in English. We can also assist in Spanish, French, and Dutch."
msgstr ""

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_website
msgid "Website"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_form
msgid "Website Form"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__website_message_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__website_message_ids
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working Hours"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_assignation_hours
msgid "Working Hours to Assign"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_close_hours
msgid "Working Hours to Close"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_hours
msgid "Working Hours to Reach SLA"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__exceeded_hours
msgid "Working hours exceeded for reached SLAs compared with deadline. Positive number means the SLA was reached after the deadline."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working hours used to determine the deadline of SLA Policies."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Would you like to unarchive all of the tickets contained in these stages as well?"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "You cannot delete stages containing tickets. You can either archive them or first delete all of their tickets."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "You cannot delete stages containing tickets. You should first delete all of their tickets."
msgstr ""

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_closed_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_rating_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_success_not_zero
msgid "You cannot have negative targets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "You completed all your tickets on time."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "e.g. Close urgent tickets within 36 hours"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. Customer Care"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.quick_create_ticket_form
msgid "e.g. Product arrived damaged"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_type_view_tree
msgid "e.g. Question"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "generate tickets in your pipeline."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "has been created from ticket:"
msgstr ""

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.helpdesk_ratings_server_action
msgid "helpdesk view rating"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "team search"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "tickets"
msgstr ""

#. module: helpdesk
#: model:mail.template,subject:helpdesk.rating_ticket_request_email_template
msgid "{{ object.company_id.name or object.user_id.company_id.name or 'Helpdesk' }}: Service Rating Request"
msgstr ""

#. module: helpdesk
#: model:mail.template,subject:helpdesk.new_ticket_request_email_template
#: model:mail.template,subject:helpdesk.solved_ticket_request_email_template
msgid "{{ object.display_name }}"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "{{rating.res_name if t['is_helpdesk_user'] else ''}}"
msgstr ""
