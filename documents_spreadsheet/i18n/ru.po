# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_spreadsheet
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-12 10:56+0000\n"
"PO-Revision-Date: 2024-01-30 15:14+0400\n"
"Last-Translator: \n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/wizard/save_spreadsheet_template.py:0
msgid "\"%s\" saved as template"
msgstr "\"%s\" сохраняется как шаблон"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.share_page
msgid "#{'Download '}#{document.name}"
msgstr "#{'Download'}#{document.name}"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
msgid "%s - Template"
msgstr "%s - Шаблон"

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_contributor_spreadsheet_user_unique
msgid "A combination of the spreadsheet and the user already exist"
msgstr "Комбинация электронной таблицы и пользователя уже существует"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid "Any user will be able to create a new spreadsheet based on this template."
msgstr "Любой пользователь сможет создать новую электронную таблицу на основе этого шаблона."

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Ask an admin to configure the workspace to be accessible to the users you want."
msgstr "Попросите администратора настроить рабочую область так, чтобы она была доступна нужным вам пользователям."

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
msgid "Blank spreadsheet"
msgstr "Пустая электронная таблица"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "By default, the spreadsheets in this workspace will only be seen and updated by their <strong>creator</strong>."
msgstr "По умолчанию электронные таблицы в этой рабочей области будут видны и обновляться только их <strong>создателем</strong>."

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid "Cancel"
msgstr "Отменить"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Centralize your spreadsheets"
msgstr "Централизация электронных таблиц"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid "Confirm"
msgstr "Подтвердить"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_contributor_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_contributor
msgid "Contributors"
msgstr "Авторы"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_shared_spreadsheet
msgid "Copy of a shared spreadsheet"
msgstr "Копия общей электронной таблицы"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
msgid "Create"
msgstr "Создать"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/spreadsheet_template_dialog.js:0
msgid "Create a Spreadsheet or select a Template"
msgstr "Создайте электронную таблицу или выберите шаблон"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_uid
msgid "Created by"
msgstr "Создано"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_date
msgid "Created on"
msgstr "Создано"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__current_revision_uuid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__current_revision_uuid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__current_revision_uuid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__current_revision_uuid
msgid "Current Revision Uuid"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
msgid "Discard"
msgstr "Отменить"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_document
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__document_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__document_id
msgid "Document"
msgstr "Документ"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_share
msgid "Documents Share"
msgstr "Документы Поделиться"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_company__documents_spreadsheet_folder_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_config_settings__documents_spreadsheet_folder_id
msgid "Documents Spreadsheet Folder"
msgstr "Папка с электронными таблицами документов"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "Edit"
msgstr "Редактировать"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Enjoy collaborative work on your spreadsheets."
msgstr "Наслаждайтесь совместной работой над своими электронными таблицами."

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__excel_export
msgid "Excel Export"
msgstr "Экспорт в Excel"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__file_name
msgid "File Name"
msgstr "Имя файла"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Format issue"
msgstr "Проблема с форматом"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_share__freezed_spreadsheet_ids
msgid "Freezed Spreadsheet"
msgstr "Замороженная электронная таблица"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__handler
msgid "Handler"
msgstr "Handler"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__id
msgid "ID"
msgstr "ID"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "If you want to work together on those spreadsheets :"
msgstr "Если вы хотите вместе работать над этими таблицами:"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__last_update_date
msgid "Last update date"
msgstr "Дата последнего обновления"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "Make a copy"
msgstr "Создать копию"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Manage and work with all the <strong>spreadsheets</strong> created in other applications."
msgstr "Управление и работа со всеми <strong>электронными таблицами</strong>, созданными в других приложениях."

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Move them to another workspace"
msgstr "Переместите их на другое рабочее пространство"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_search
msgid "My Templates"
msgstr "Мои шаблоны"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__name
msgid "Name"
msgstr "Имя"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.xml:0
msgid "New Spreadsheet"
msgstr "Новая электронная таблица"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/spreadsheet_template_dialog.js:0
msgid "New sheet saved in Documents"
msgstr "Новый лист сохраняется в папке Документы"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "New spreadsheet"
msgstr "Новая электронная таблица"

#. module: documents_spreadsheet
#. odoo-javascript
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
#: code:addons/documents_spreadsheet/static/src/spreadsheet_selector_dialog/document_selector_panel.js:0
msgid "New spreadsheet created in Documents"
msgstr "Новая электронная таблица создана в разделе Документы"

#. module: documents_spreadsheet
#. odoo-javascript
#. odoo-python
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_template/spreadsheet_template_action.js:0
msgid "New spreadsheet template created"
msgstr "Создан новый шаблон электронной таблицы"

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_documents_shared_spreadsheet__unique
msgid "Only one freezed spreadsheet per document share"
msgstr "Только одна замороженная электронная таблица в одном общем доступе к документу"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Open with Odoo Spreadsheet"
msgstr "Открыть с помощью Odoo Spreadsheet"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Restore"
msgstr "Восстановить"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Restore file?"
msgstr "Восстановить файл?"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.save_spreadsheet_template_action
msgid "Save as template"
msgstr "Сохранить как шаблон"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_clone_xlsx_dialog/spreadsheet_clone_xlsx_dialog.xml:0
msgid "Send source file to trash"
msgstr "Отправьте исходный файл в корзину"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__share_id
msgid "Share"
msgstr "Поделиться"

#. module: documents_spreadsheet
#: model:documents.folder,name:documents_spreadsheet.documents_spreadsheet_folder
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.document_view_search_spreadsheet
msgid "Spreadsheet"
msgstr "Таблица"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_contributor
msgid "Spreadsheet Contributor"
msgstr "Составитель электронных таблиц"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__spreadsheet_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_data
msgid "Spreadsheet Data"
msgstr "Данные электронной таблицы"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_document_view_kanban
msgid "Spreadsheet Preview"
msgstr "Предварительный просмотр электронных таблиц"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr "Пересмотр электронных таблиц"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_snapshot
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__spreadsheet_snapshot
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_snapshot
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr "Снимок электронной таблицы"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_template
msgid "Spreadsheet Template"
msgstr "Шаблон электронной таблицы"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_save_spreadsheet_template
msgid "Spreadsheet Template Save Wizard"
msgstr "Мастер сохранения шаблонов электронных таблиц"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_template_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_template
msgid "Spreadsheet Templates"
msgstr "Шаблоны электронных таблиц"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_binary_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__spreadsheet_binary_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_binary_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr "Файл электронной таблицы"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Spreadsheet files cannot be handled from the Trash. Would you like to restore this document?"
msgstr "Файлы электронных таблиц не могут быть обработаны из корзины. Вы хотите восстановить этот документ?"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_clone_xlsx_dialog/spreadsheet_clone_xlsx_dialog.xml:0
msgid "Spreadsheet files cannot be previewed or edited in Odoo. Opening your file with Odoo Spreadsheet will allow you to do so."
msgstr "Файлы электронных таблиц нельзя просматривать или редактировать в Odoo. Открытие файла в Odoo Spreadsheet позволит вам сделать это."

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Spreadsheets"
msgstr "Электронные таблицы"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/inspector/documents_inspector.js:0
msgid ""
"Spreadsheets mass download not yet supported.\n"
" Download spreadsheets individually instead."
msgstr ""
"Массовая загрузка электронных таблиц пока не поддерживается.\n"
" Вместо этого загружайте электронные таблицы по отдельности."

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__template_name
msgid "Template Name"
msgstr "Название шаблона"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The file is not a xlsx file"
msgstr "Файл не является файлом xlsx"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The spreadsheet you are trying to access does not exist."
msgstr "Электронная таблица, к которой вы пытаетесь получить доступ, не существует."

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The xlsx file is corrupted"
msgstr "Файл xlsx поврежден"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The xlsx file is too big"
msgstr "Файл xlsx слишком большой"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_shared_spreadsheet__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__thumbnail
msgid "Thumbnail"
msgstr "Миниатюра"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/components/control_panel/spreadsheet_breadcrumbs.xml:0
msgid "Toggle favorite"
msgstr "Переключить любимое"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "Untitled spreadsheet"
msgstr "Электронная таблица без названия"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_template/spreadsheet_template_action.js:0
msgid "Untitled spreadsheet template"
msgstr "Шаблон электронной таблицы без названия"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__user_id
msgid "User"
msgstr "Пользователь"

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Workspace"
msgstr "Рабочая область"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/controllers/documents.py:0
msgid "You cannot upload spreadsheets in a shared folder"
msgstr "Невозможно загрузить электронные таблицы в общую папку"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/controllers/share.py:0
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "You don't have access to this document"
msgstr "У вас нет доступа к этому документу"
