# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll_partena
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 10:47+0000\n"
"PO-Revision-Date: 2024-11-20 10:47+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_be_hr_payroll_ucm
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_ucm.res_config_settings_view_form
msgid "Allow to export Working Entries to your Social Secretariat"
msgstr "Autoriser l'export des prestations à votre secrétariat social"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__company_id
msgid "Company"
msgstr "Société"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__contract_ids
msgid "Contract"
msgstr "Contrat"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__create_date
msgid "Created on"
msgstr "Créé le"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__eligible_employee_line_ids
msgid "Eligible Employees"
msgstr "Employés Eligibles"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__eligible_employee_count
msgid "Eligible Employees Count"
msgstr "Nombre d'employés éligibles"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_hr_employee
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__employee_id
msgid "Employee"
msgstr "Employé"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__export_id
msgid "Export"
msgstr "Exporter"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__export_file
msgid "Export File"
msgstr "Exporter le fichier"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__export_filename
msgid "Export Filename"
msgstr "Nom du fichier d'export"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_l10n_be_hr_payroll_export_ucm
msgid "Export Payroll to UCM"
msgstr "Exporter la paie vers UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.ui.menu,name:l10n_be_hr_payroll_ucm.menu_l10n_be_export_work_entries_ucm
msgid "Export Work Entries to UCM"
msgstr "Exporter les prestations vers UCM"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/hr_payroll_export_ucm.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll_ucm.l10n_be_export_ucm_action
msgid "Export to UCM"
msgstr "Exporter vers UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Type de prestations RH"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__id
msgid "ID"
msgstr "ID"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__period_start
msgid "Period Start"
msgstr "Début de la période"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__period_stop
msgid "Period Stop"
msgstr "Fin de la période"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__reference_month
msgid "Reference Month"
msgstr "Mois de référence"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__reference_year
msgid "Reference Year"
msgstr "Année de référence"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/hr_work_entry_type.py:0
msgid "The code should have 2 or 3 characters!"
msgstr "Le code doit comporter 2 ou 3 caractères !"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/res_company.py:0
msgid "The code should have 5 characters!"
msgstr "Le code doit comporter 5 caractères !"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/res_company.py:0
msgid "The code should have 6 characters!"
msgstr "Le code doit comporter 6 caractères !"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/hr_payroll_export_ucm.py:0
msgid "The following employees do not have a ucm code: %(names)s"
msgstr "Les employés suivants n'ont pas de code UCM : %(names)s"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/hr_payroll_export_ucm.py:0
msgid "The work entry type %(we)s does not have a UCM code"
msgstr "Le type de prestation de travail %(we)s n'a pas de code UCM"

#. module: l10n_be_hr_payroll_ucm
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_ucm.res_config_settings_view_form
msgid "UCM"
msgstr "UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_res_company__ucm_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_res_config_settings__ucm_code
msgid "UCM Affiliation Number"
msgstr "Numéro d'affiliation UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_res_config_settings__ucm_company_code
msgid "UCM Company Code"
msgstr "Code d'entreprise UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_l10n_be_hr_payroll_export_ucm_employee
msgid "UCM Export Employee"
msgstr "Exporter employé UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_hr_employee__ucm_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_hr_work_entry_type__ucm_code
msgid "UCM code"
msgstr "Code UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_res_company__ucm_company_code
msgid "UCM folder Number"
msgstr "Numéro de dossier UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__work_entry_ids
msgid "Work Entry"
msgstr "Prestation"

#. module: l10n_be_hr_payroll_ucm
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_ucm.hr_employee_form_l10n_be_hr_payroll_ucm
msgid "ucm"
msgstr "UCM"
