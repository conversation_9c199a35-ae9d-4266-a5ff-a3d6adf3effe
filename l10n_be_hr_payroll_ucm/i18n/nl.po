# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll_ucm
# 
# Translators:
# <PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 10:47+0000\n"
"PO-Revision-Date: 2025-02-12 11:36+0000\n"
"Last-Translator: Man<PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_hr_payroll_ucm
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_ucm.res_config_settings_view_form
msgid "Allow to export Working Entries to your Social Secretariat"
msgstr "Werkboekingen exporteren naar je sociaal secretariaat toestaan"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__contract_ids
msgid "Contract"
msgstr "Contract"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__eligible_employee_line_ids
msgid "Eligible Employees"
msgstr "In aanmerking komende werknemers"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__eligible_employee_count
msgid "Eligible Employees Count"
msgstr "In aanmerking komende werknemers Tellen"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_hr_employee
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__employee_id
msgid "Employee"
msgstr "Werknemer"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__export_id
msgid "Export"
msgstr "Exporteren"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__export_file
msgid "Export File"
msgstr "Bestand exporteren"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__export_filename
msgid "Export Filename"
msgstr "Naam exportbestand"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_l10n_be_hr_payroll_export_ucm
msgid "Export Payroll to UCM"
msgstr "Loonadministratie exporteren naar UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.ui.menu,name:l10n_be_hr_payroll_ucm.menu_l10n_be_export_work_entries_ucm
msgid "Export Work Entries to UCM"
msgstr "Werkaantekeningen exporteren naar UCM"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/hr_payroll_export_ucm.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll_ucm.l10n_be_export_ucm_action
msgid "Export to UCM"
msgstr "Exporteren naar UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR werkboekingstype"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__id
msgid "ID"
msgstr "ID"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__period_start
msgid "Period Start"
msgstr "Begin periode"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__period_stop
msgid "Period Stop"
msgstr "Periode Stop"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__reference_month
msgid "Reference Month"
msgstr "Referentiemaand"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm__reference_year
msgid "Reference Year"
msgstr "Referentiejaar"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/hr_work_entry_type.py:0
msgid "The code should have 2 or 3 characters!"
msgstr "De code moet uit 2 of 3 tekens bestaan!"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/res_company.py:0
msgid "The code should have 5 characters!"
msgstr "De code moet uit 5 tekens bestaan!"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/res_company.py:0
msgid "The code should have 6 characters!"
msgstr "De code moet uit 6 tekens bestaan!"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/hr_payroll_export_ucm.py:0
msgid "The following employees do not have a ucm code: %(names)s"
msgstr "De volgende werknemers hebben geen UCM-code: %(names)s"

#. module: l10n_be_hr_payroll_ucm
#. odoo-python
#: code:addons/l10n_be_hr_payroll_ucm/models/hr_payroll_export_ucm.py:0
msgid "The work entry type %(we)s does not have a UCM code"
msgstr "Het werkboekingstype %(we)s heeft geen UCM-code"

#. module: l10n_be_hr_payroll_ucm
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_ucm.res_config_settings_view_form
msgid "UCM"
msgstr "UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_res_company__ucm_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_res_config_settings__ucm_code
msgid "UCM Affiliation Number"
msgstr "Aansluitingsnummer UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_res_config_settings__ucm_company_code
msgid "UCM Company Code"
msgstr "Bedrijfscode UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model,name:l10n_be_hr_payroll_ucm.model_l10n_be_hr_payroll_export_ucm_employee
msgid "UCM Export Employee"
msgstr "Exporteer werknemer UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_hr_employee__ucm_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_hr_work_entry_type__ucm_code
msgid "UCM code"
msgstr "UCM-code"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_res_company__ucm_company_code
msgid "UCM folder Number"
msgstr "Dossiernummer UCM"

#. module: l10n_be_hr_payroll_ucm
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_ucm.field_l10n_be_hr_payroll_export_ucm_employee__work_entry_ids
msgid "Work Entry"
msgstr "Werkboeking"

#. module: l10n_be_hr_payroll_ucm
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_ucm.hr_employee_form_l10n_be_hr_payroll_ucm
msgid "ucm"
msgstr "UCM"
