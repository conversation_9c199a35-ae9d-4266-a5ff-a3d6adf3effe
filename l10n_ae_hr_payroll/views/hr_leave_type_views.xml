<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_leave_type_view_form" model="ir.ui.view">
        <field name="name">hr.leave.type.view.form</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_work_entry_holidays.work_entry_type_leave_form_inherit"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='configuration']" position="inside">
                <field name="l10n_ae_is_annual_leave" invisible="country_code != 'AE' or not requires_allocation" nolabel="1"/>
                <label for="l10n_ae_is_annual_leave" invisible="country_code != 'AE' or not requires_allocation"/>
            </xpath>
        </field>
    </record>
</odoo>
