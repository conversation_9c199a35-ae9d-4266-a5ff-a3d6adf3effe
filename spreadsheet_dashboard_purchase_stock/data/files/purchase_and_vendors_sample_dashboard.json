{"version": "18.3.1", "sheets": [{"id": "c1dd5136-2d63-4eb2-8a48-42eae5ba2086", "name": "Main", "colNumber": 10, "rowNumber": 139, "rows": {"11": {"size": 45}, "28": {"size": 17}, "29": {"size": 46}, "48": {"size": 42}, "67": {"size": 41}, "86": {"size": 39}, "104": {"size": 43}, "105": {"size": 43}, "106": {"size": 27}, "107": {"size": 27}, "108": {"size": 27}, "109": {"size": 27}, "110": {"size": 27}, "111": {"size": 27}, "112": {"size": 27}, "113": {"size": 27}, "114": {"size": 27}, "115": {"size": 27}, "117": {"size": 42}, "118": {"size": 43}, "119": {"size": 27}, "120": {"size": 27}, "121": {"size": 27}, "122": {"size": 27}, "123": {"size": 27}, "124": {"size": 27}, "125": {"size": 27}, "126": {"size": 27}, "127": {"size": 27}, "128": {"size": 27}}, "cols": {"0": {"size": 308}, "1": {"size": 48}, "2": {"size": 113}, "3": {"size": 95}, "4": {"size": 98}, "5": {"size": 115}, "6": {"size": 23}, "7": {"size": 235}, "8": {"size": 107}, "9": {"size": 120}}, "merges": ["B106:C106"], "cells": {"A12": "=_t(\"Purchase Value by creation confirmation date\")", "A30": "=_t(\"% On time deliveries by vendor\")", "A49": "=_t(\"Top vendors by amount\")", "A68": "=_t(\"Top vendors by lead time in days\")", "A87": "=_t(\"Average product purchased cost by confirmation week\")", "A105": "=_t(\"Top purchase orders by value\")", "A106": "=_t(\"Order Reference\")", "A118": "=_t(\"Top purchased products\")", "A119": "=_t(\"Product\")", "B106": "=_t(\"Vendor\")", "B119": "=_t(\"Count\")", "C119": "=_t(\"Total\")", "D106": "=_t(\"Buyer\")", "D119": "=_t(\"Qty Ordered\")", "E106": "=_t(\"Order date\")", "E119": "=_t(\"Average Cost\")", "F106": "=_t(\"Total\")", "F119": "=_t(\"Days to Receive\")", "H105": "=_t(\"Top 10 late receipts\")", "H106": "=_t(\"Picking Name\")", "H118": "=_t(\"Purchase Order by buyer\")"}, "styles": {"A12": 1, "A30": 1, "A49": 1, "A68": 1, "A87": 1, "A105": 1, "A118": 1, "H105": 1, "H118": 1, "A119": 2, "A106:E106": 2, "H106": 2, "A107:F117": 3, "F106": 4, "B119:F119": 4}, "formats": {}, "borders": {"A105:F105": 1, "A118:F118": 1, "A12:J12": 1, "A30:J30": 1, "A49:J49": 1, "A68:J68": 1, "A87:J87": 1, "H105:J105": 1, "H118:J118": 1, "A106:F106": 2, "A13:J13": 2, "A31:J31": 2, "A50:J50": 2, "A69:J69": 2, "A88:J88": 2, "I106:J106": 2, "H119:J119": 2, "A119:F119": 3, "H106": 3, "A120:F120": 4, "H107": 4, "A121:F129": 5, "H108:I116": 5, "A130:F130": 6, "H117:I117": 6, "I107": 7}, "conditionalFormats": [], "dataValidationRules": [], "figures": [{"id": "c79871cd-6483-4d0f-9493-cca9725c3dd7", "width": 242, "height": 107, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": {"text": "Purchased value", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "baseline": "KPIs!D3", "baselineDescr": "since last period", "keyValue": "KPIs!D4", "humanize": true}, "offset": {"x": 0, "y": 11}, "col": 0, "row": 0}, {"id": "939104fa-dea7-4c61-9991-0654ab1dbf8d", "width": 242, "height": 107, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": {"text": "Number of orders", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#EFF6FF", "baseline": "KPIs!C3", "baselineDescr": "since last period", "keyValue": "KPIs!C4", "humanize": false}, "offset": {"x": 499, "y": 11}, "col": 0, "row": 0}, {"id": "3607d85d-8efd-4432-b4fd-8157b834e4c4", "width": 242, "height": 107, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": {"text": "Average order value", "align": "left", "color": "#434343", "bold": true}, "type": "scorecard", "background": "#FFF7ED", "baseline": "KPIs!I3", "baselineDescr": "since last period", "keyValue": "KPIs!I4", "humanize": true}, "offset": {"x": 250, "y": 11}, "col": 0, "row": 0}, {"id": "45e7f600-9d45-49d4-865a-5f0ad24104f9", "width": 242, "height": 107, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": {"text": "Days to Receive", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FEF2F2", "baseline": "KPIs!H3", "baselineDescr": "last period", "keyValue": "KPIs!H4", "humanize": true}, "offset": {"x": 0, "y": 125}, "col": 0, "row": 0}, {"id": "e0ddc454-8376-4e21-b7fe-262d4a1c057e", "width": 242, "height": 107, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": {"text": "Quantity ordered", "align": "left", "color": "#434343", "bold": true}, "type": "scorecard", "background": "#EFF6FF", "baseline": "KPIs!E3", "baselineDescr": "since last period", "keyValue": "KPIs!E4", "humanize": true}, "offset": {"x": 748, "y": 11}, "col": 0, "row": 0}, {"id": "de913d05-7245-41a9-9939-0e1808def088", "width": 242, "height": 107, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": {"text": "Days to Confirm", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FEF2F2", "baseline": "KPIs!G3", "baselineDescr": "last period", "keyValue": "KPIs!G4", "humanize": true}, "offset": {"x": 250, "y": 125}, "col": 0, "row": 0}, {"id": "7d77e1dc-020d-4c44-b379-9a59b7c4d46d", "width": 242, "height": 107, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": {"text": "Supplier service level", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#ECFDF5", "baseline": "KPIs!D10", "baselineDescr": "since last period", "keyValue": "KPIs!D11", "humanize": false}, "offset": {"x": 499, "y": 125}, "col": 0, "row": 0}, {"id": "b90dc169-577a-467b-b8b5-d5ad079efcc5", "width": 242, "height": 107, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": {"text": "On time deliveries", "align": "left", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#ECFDF5", "baseline": "KPIs!C17", "baselineDescr": "since last period", "keyValue": "KPIs!B17", "humanize": false}, "offset": {"x": 748, "y": 125}, "col": 0, "row": 0}, {"id": "626ed32a-17ab-44f0-927f-43e50b6bb6ba", "width": 1261, "height": 386, "tag": "chart", "data": {"type": "line", "dataSetsHaveTitle": false, "dataSets": [{"dataRange": "KPIs!C30:C48", "yAxisId": "y"}], "legendPosition": "none", "labelRange": "KPIs!A30:A48", "title": {}, "labelsAsText": true, "stacked": false, "aggregated": false, "cumulative": true, "fillArea": true}, "offset": {"x": 0, "y": 298}, "col": 0, "row": 0}, {"id": "372f956c-b703-4129-bde9-f7b11f3f5291", "width": 1263, "height": 416, "tag": "chart", "data": {"type": "bar", "dataSetsHaveTitle": false, "dataSets": [{"dataRange": "KPIs!B51:B55", "yAxisId": "y"}], "legendPosition": "none", "labelRange": "KPIs!A51:A55", "title": {}, "stacked": false, "aggregated": false}, "offset": {"x": 0, "y": 729}, "col": 0, "row": 0}, {"id": "06685da9-e904-4464-84e9-278cf3ee266d", "width": 1257, "height": 413, "tag": "chart", "data": {"type": "combo", "dataSetsHaveTitle": true, "dataSets": [{"dataRange": "KPIs!B58:B77", "yAxisId": "y", "type": "bar"}, {"dataRange": "KPIs!C58:C77", "type": "line"}, {"dataRange": "KPIs!D58:D77", "type": "line"}, {"dataRange": "KPIs!E58:E77", "type": "line"}], "legendPosition": "top", "labelRange": "KPIs!A58:A77", "title": {}, "aggregated": false}, "offset": {"x": 0, "y": 1185}, "col": 0, "row": 0}, {"id": "54db544f-2499-441a-8a54-3812d4b4bb9a", "width": 462, "height": 335, "tag": "chart", "data": {"type": "pie", "dataSetsHaveTitle": false, "dataSets": [{"dataRange": "KPIs!B80:B81"}], "legendPosition": "top", "labelRange": "KPIs!A80:A81", "title": {}, "aggregated": false, "isDoughnut": false}, "offset": {"x": 800, "y": 2905}, "col": 0, "row": 0}, {"id": "c5352dad-6a5d-4681-b87e-e22a0df16f1a", "width": 1257, "height": 413, "tag": "chart", "data": {"type": "bar", "dataSetsHaveTitle": false, "dataSets": [{"dataRange": "KPIs!B84:B88", "yAxisId": "y"}], "legendPosition": "none", "labelRange": "KPIs!A84:A88", "title": {}, "stacked": false, "aggregated": false}, "offset": {"x": 0, "y": 1640}, "col": 0, "row": 0}, {"id": "bde57359-c4e1-49b8-8f48-814b90f95621", "width": 1259, "height": 392, "tag": "chart", "data": {"type": "line", "dataSetsHaveTitle": false, "dataSets": [{"dataRange": "KPIs!B93:B101", "yAxisId": "y"}], "legendPosition": "none", "labelRange": "KPIs!A93:A101", "title": {}, "labelsAsText": false, "stacked": false, "aggregated": false, "cumulative": true, "fillArea": true}, "offset": {"x": 0, "y": 2093}, "col": 0, "row": 0}], "tables": [{"range": "A105:F107", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "None"}}, {"range": "A117:F119", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": true, "bandedColumns": false, "automaticAutofill": true, "styleId": "None"}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}, {"id": "sheet1", "name": "KPIs", "colNumber": 26, "rowNumber": 183, "rows": {}, "cols": {}, "merges": [], "cells": {"A3": "=_t(\"Previous period\")", "A4": "=_t(\"Selected period\")", "A8": "=_t(\"Orders with reception picking\")", "A10": "=_t(\"Previous period\")", "A11": "=_t(\"Selected period\")", "A17": "=_t(\"Total\")", "A30": "=TODAY() - B30", "A31": "=TODAY() - B31", "A32": "=TODAY() - B32", "A33": "=TODAY() - B33", "A34": "=TODAY() - B34", "A35": "=TODAY() - B35", "A36": "=TODAY() - B36", "A37": "=TODAY() - B37", "A38": "=TODAY() - B38", "A39": "=TODAY() - B39", "A40": "=TODAY() - B40", "A41": "=TODAY() - B41", "A42": "=TODAY() - B42", "A43": "=TODAY() - B43", "A44": "=TODAY() - B44", "A45": "=TODAY() - B45", "A46": "=TODAY() - B46", "A47": "=TODAY() - B47", "A48": "=TODAY() - B48", "A51": "=_t(\"Azure Interior\")", "A52": "=_t(\"Swiss Vendor\")", "A53": "=_t(\"Wood Corner\")", "A54": "=_t(\"Ready Mat\")", "A55": "=_t(\"<PERSON>\")", "A59": "=TODAY() - F59", "A60": "=TODAY() - F60", "A61": "=TODAY() - F61", "A62": "=TODAY() - F62", "A63": "=TODAY() - F63", "A64": "=TODAY() - F64", "A65": "=TODAY() - F65", "A66": "=TODAY() - F66", "A67": "=TODAY() - F67", "A68": "=TODAY() - F68", "A69": "=TODAY() - F69", "A70": "=TODAY() - F70", "A71": "=TODAY() - F71", "A72": "=TODAY() - F72", "A73": "=TODAY() - F73", "A74": "=TODAY() - F74", "A75": "=TODAY() - F75", "A76": "=TODAY() - F76", "A77": "=TODAY() - F77", "A80": "=_t(\"John Stones\")", "A81": "=_t(\"Elisabeth Wild\")", "A84": "=_t(\"Azure Interior\")", "A85": "=_t(\"Swiss Vendor\")", "A86": "=_t(\"Wood Corner\")", "A87": "=_t(\"Ready Mat\")", "A88": "=_t(\"<PERSON>\")", "A93": "=_t(\"Whiteboard Pen\")", "A94": "=_t(\"Large Desk\")", "A95": "=_t(\"Test Batch Product\")", "A96": "=_t(\"Acoustic Bloc Screens\")", "A97": "=_t(\"Drawer Black\")", "A98": "=_t(\"Flipover\")", "A99": "=_t(\"Office Chair Black\")", "A100": "=_t(\"Corner Desk Right Sit\")", "A101": "=_t(\"Corner Desk Left Sit\")", "B2": "=_t(\"Count\")", "B3": "164", "B4": "116", "B9": "=_t(\"Qty Ordered\")", "B14": "=_t(\"On time deliveries\")", "B15": "=_t(\"Selected period\")", "B16": "=_t(\"On-Time Delivery Rate\")", "B17": "0.49630765422508083", "B30": "18", "B31": "17", "B32": "16", "B33": "15", "B34": "14", "B35": "13", "B36": "12", "B37": "11", "B38": "10", "B39": "9", "B40": "8", "B41": "7", "B42": "6", "B43": "5", "B44": "4", "B45": "3", "B46": "2", "B47": "1", "B48": "0", "B51": "0.9046755786150218", "B52": "0.5892210810559773", "B53": "0.7908984102387622", "B54": "0.05559889347379188", "B55": "0.3671340466243489", "B58": "=_t(\"Swiss Vendor\")", "B59": "115429", "B60": "441261", "B61": "399285", "B62": "251550", "B63": "261399", "B64": "420886", "B65": "359623", "B66": "469715", "B67": "347630", "B68": "282382", "B69": "424034", "B70": "326986", "B71": "329495", "B72": "288564", "B73": "482253", "B74": "441891", "B75": "193022", "B76": "474619", "B77": "220350", "B80": "459", "B81": "345", "B84": "21", "B85": "28", "B86": "19", "B87": "34", "B88": "26", "B93": "376360", "B94": "336420", "B95": "150703", "B96": "213300", "B97": "304853", "B98": "401930", "B99": "487360", "B100": "176647", "B101": "411081", "C2": "=_t(\"Order\")", "C3": "148", "C4": "187", "C6": "=ODOO.FILTER.VALUE(\"Order date\")", "C9": "=_t(\"Qty Received\")", "C15": "=_t(\"Previous period\")", "C16": "=_t(\"On-Time Delivery Rate\")", "C17": "0.18501508250948984", "C30": "373714", "C31": "226738", "C32": "417699", "C33": "285460", "C34": "288850", "C35": "227848", "C36": "222024", "C37": "297395", "C38": "440877", "C39": "265150", "C40": "451897", "C41": "352767", "C42": "117300", "C43": "324438", "C44": "380424", "C45": "194799", "C46": "306722", "C47": "275560", "C48": "155636", "C58": "=_t(\"Wood Corner\")", "C59": "254289", "C60": "261292", "C61": "278376", "C62": "411665", "C63": "417147", "C64": "386528", "C65": "460587", "C66": "460382", "C67": "318980", "C68": "120907", "C69": "477067", "C70": "325249", "C71": "254320", "C72": "276038", "C73": "317756", "C74": "351094", "C75": "182354", "C76": "347249", "C77": "411618", "D2": "=_t(\"Total\")", "D3": "98567", "D4": "171563", "D9": "=_t(\"Fill rate\")", "D10": "0.05", "D11": "0.0874", "D58": "=_t(\"Ready Mat\")", "D59": "343491", "D60": "341531", "D61": "316875", "D62": "446336", "D63": "481247", "D64": "355248", "D65": "106559", "D66": "212560", "D67": "364003", "D68": "287561", "D69": "499937", "D70": "348764", "D71": "158689", "D72": "277289", "D73": "400793", "D74": "453366", "D75": "417131", "D76": "200739", "D77": "316205", "E2": "=_t(\"Qty Ordered\")", "E3": "160", "E4": "41", "E58": "=_t(\"<PERSON>\")", "E59": "114472", "E60": "272993", "E61": "308643", "E62": "401558", "E63": "104660", "E64": "421054", "E65": "232261", "E66": "223125", "E67": "203022", "E68": "371186", "E69": "263942", "E70": "470107", "E71": "478158", "E72": "465797", "E73": "396133", "E74": "309348", "E75": "362066", "E76": "334418", "E77": "473663", "F2": "=_t(\"Qty Received\")", "F3": "350", "F4": "102", "F59": "18", "F60": "17", "F61": "16", "F62": "15", "F63": "14", "F64": "13", "F65": "12", "F66": "11", "F67": "10", "F68": "9", "F69": "8", "F70": "7", "F71": "6", "F72": "5", "F73": "4", "F74": "3", "F75": "2", "F76": "1", "F77": "0", "G2": "=_t(\"Days to Confirm\")", "G3": "421", "G4": "410", "H2": "=_t(\"Days to Receive\")", "H3": "63", "H4": "99", "I2": "=_t(\"Average order\")", "I3": "4360", "I4": "2610", "J2": "=_t(\"Fill rate\")", "J3": "410", "J4": "31"}, "styles": {"B14:B15": 5, "C15": 5}, "formats": {"B51:B55": 1, "B17:C17": 1, "D10:D11": 1, "B93:B101": 2, "C30:C48": 2, "D3:D4": 2, "B59:E77": 2, "I3:I4": 2}, "borders": {"A16:A26": 8, "A51:A55": 8, "A84:A88": 8, "D16:D27": 8, "A27": 9, "A28:C28": 10, "B27": 11, "C16:C26": 12, "C27": 13}, "conditionalFormats": [], "dataValidationRules": [], "figures": [], "tables": [{"range": "A2:J4", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": false, "bandedColumns": false, "automaticAutofill": true, "styleId": "TableStyleLight5"}}, {"range": "A8:D11", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 2, "bandedRows": false, "bandedColumns": false, "automaticAutofill": true, "styleId": "TableStyleLight5"}}, {"range": "A14:C27", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 2, "bandedRows": false, "bandedColumns": false, "automaticAutofill": true, "styleId": "TableStyleLight5"}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "comments": {}}], "styles": {"1": {"fontSize": 16, "bold": true, "textColor": "#01636B"}, "2": {"textColor": "#434343", "bold": true, "fontSize": 11}, "3": {"fillColor": "#FFFFFF"}, "4": {"textColor": "#434343", "bold": true, "fontSize": 11, "align": "center"}, "5": {"bold": true}}, "formats": {"1": "0.00%", "2": "[$$]#,##0"}, "borders": {"1": {"bottom": {"style": "thin", "color": "#CCCCCC"}}, "2": {"top": {"style": "thin", "color": "#CCCCCC"}}, "3": {"top": {"style": "thin", "color": "#CCCCCC"}, "bottom": {"style": "thin", "color": "#FFFFFF"}}, "4": {"top": {"style": "thin", "color": "#FFFFFF"}, "bottom": {"style": "thick", "color": "#FFFFFF"}}, "5": {"top": {"style": "thick", "color": "#FFFFFF"}, "bottom": {"style": "thick", "color": "#FFFFFF"}}, "6": {"top": {"style": "thick", "color": "#FFFFFF"}}, "7": {"bottom": {"style": "thick", "color": "#FFFFFF"}}, "8": {"left": {"style": "thin", "color": "#000000"}}, "9": {"left": {"style": "thin", "color": "#000000"}, "bottom": {"style": "thin", "color": "#000000"}}, "10": {"top": {"style": "thin", "color": "#000000"}}, "11": {"bottom": {"style": "thin", "color": "#000000"}}, "12": {"right": {"style": "thin", "color": "#000000"}}, "13": {"right": {"style": "thin", "color": "#000000"}, "bottom": {"style": "thin", "color": "#000000"}}}, "revisionId": "START_REVISION", "uniqueFigureIds": true, "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ",", "weekStart": 7}}, "pivots": {}, "pivotNextId": 14, "customTableStyles": {}, "globalFilters": [], "lists": {}, "listNextId": 6, "chartOdooMenusReferences": {}}