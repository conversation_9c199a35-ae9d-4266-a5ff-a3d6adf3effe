<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">

    <t t-name="planning.PlanningGanttView.Buttons" t-inherit="web_gantt.GanttView.Buttons">
        <xpath expr="//div" position="inside">
            <div class="btn-group px-1">
                <div class="btn-group d-block">
                    <button t-if="model.metaData.canEdit"
                        class="o_gantt_button_send_all btn btn-primary"
                        title="Send schedule"
                        data-hotkey="w"
                        t-on-click="() => this.planningControllerActions.publish()"
                    >
                        Publish
                    </button>
                    <button t-if="env.isSmall and model.metaData.canCreate and model.metaData.scale.id === 'week' and model.metaData.rangeId === 'week'"
                        class="o_gantt_button_copy_previous_week btn btn-link dropdown-item"
                        title="Copy previous week"
                        data-hotkey="c"
                        t-on-click="() => this.planningControllerActions.copyPrevious()"
                    >
                        Copy previous week
                    </button>
                    <button t-if="env.isSmall and model.metaData.canCreate"
                        class="o_gantt_button_auto_plan btn btn-link dropdown-item"
                        title="Automatically plan open shifts and sales orders"
                        data-hotkey="k"
                        t-on-click="() => this.planningControllerActions.autoPlan()"
                    >
                        Auto Plan
                    </button>
                </div>
                <Dropdown t-if="!env.isSmall and model.metaData.canCreate">
                    <button class="btn btn-primary" data-hotkey="o">
                        <i class="fa fa-caret-down"></i>
                    </button>
                    <t t-set-slot="content">
                        <DropdownItem t-if="model.metaData.scale.id === 'week' and model.metaData.rangeId === 'week'"
                            class="'o_gantt_button_copy_previous_week btn btn-link dropdown-item'"
                            onSelected="() => this.planningControllerActions.copyPrevious()"
                            attrs="{ 'data-hotkey': 'c', 'title': 'Copy previous week' }"
                        >
                            Copy previous week
                        </DropdownItem>
                        <DropdownItem
                            class="'o_gantt_button_auto_plan btn btn-link dropdown-item'"
                            onSelected="() => this.planningControllerActions.autoPlan()"
                            attrs="{ 'data-hotkey': 'k', 'title': 'Automatically plan open shifts and sales orders' }"
                        >
                            Auto Plan
                        </DropdownItem>
                    </t>
                </Dropdown>
            </div>
        </xpath>
    </t>

</templates>
