# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* timer
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__id
msgid "ID"
msgstr "ID"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: timer
#: model:ir.model.constraint,message:timer.constraint_timer_timer_unique_timer
msgid "Only one timer occurrence by model, record and user"
msgstr ""
"Tylko jedno wystąpienie timera dla danego modelu, rekordu i użytkownika"

#. module: timer
#. odoo-python
#: code:addons/timer/models/timer_mixin.py:0
msgid "Operation not supported"
msgstr "Operacja nie jest wspierana"

#. module: timer
#. odoo-javascript
#: code:addons/timer/static/src/component/timer_toggle_button/timer_toggle_button.js:0
msgid "Start"
msgstr "Uruchom"

#. module: timer
#. odoo-javascript
#: code:addons/timer/static/src/component/timer_toggle_button/timer_toggle_button.js:0
msgid "Stop"
msgstr "Zatrzymaj"

#. module: timer
#: model:ir.model,name:timer.model_timer_mixin
msgid "Timer Mixin"
msgstr "Timer mixin"

#. module: timer
#: model:ir.model,name:timer.model_timer_timer
msgid "Timer Module"
msgstr "Moduł timera"
