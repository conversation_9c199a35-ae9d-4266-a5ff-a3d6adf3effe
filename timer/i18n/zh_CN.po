# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* timer
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_uid
msgid "Created by"
msgstr "创建人"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_date
msgid "Created on"
msgstr "创建日期"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__id
msgid "ID"
msgstr "ID"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: timer
#: model:ir.model.constraint,message:timer.constraint_timer_timer_unique_timer
msgid "Only one timer occurrence by model, record and user"
msgstr "模型、记录和用户只出现一个计时器"

#. module: timer
#. odoo-python
#: code:addons/timer/models/timer_mixin.py:0
msgid "Operation not supported"
msgstr "不支持的操作"

#. module: timer
#. odoo-javascript
#: code:addons/timer/static/src/component/timer_toggle_button/timer_toggle_button.js:0
msgid "Start"
msgstr "开始"

#. module: timer
#. odoo-javascript
#: code:addons/timer/static/src/component/timer_toggle_button/timer_toggle_button.js:0
msgid "Stop"
msgstr "停止"

#. module: timer
#: model:ir.model,name:timer.model_timer_mixin
msgid "Timer Mixin"
msgstr "计时器混合器"

#. module: timer
#: model:ir.model,name:timer.model_timer_timer
msgid "Timer Module"
msgstr "计时器模块"
