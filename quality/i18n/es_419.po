# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alert_count
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_count
msgid "# Quality Alerts"
msgstr "Nº de alertas de calidad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__check_count
msgid "# Quality Checks"
msgstr "Nº de controles de calidad"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Un diccionario de Python que se evaluará con el fin de proporcionar valores "
"predeterminados cuando se creen nuevos registros para este seudónimo."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction
msgid "Action Needed"
msgstr "Se requiere una acción"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_2
msgid "Action Proposed"
msgstr "Acción propuesta"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__active
msgid "Active"
msgstr "Activo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de la actividad de excepción"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_state
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono del tipo de actividad"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_check_view_activity
msgid "Activity view"
msgstr "Vista de actividad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__additional_note
msgid "Additional Note"
msgstr "Nota adicional"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_check__additional_note
msgid "Additional remarks concerning this check."
msgstr "Notas adicionales sobre esta revisión."

#. module: quality
#: model:res.groups,name:quality.group_quality_manager
msgid "Administrator"
msgstr "Administrador"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__done
msgid "Alert Processed"
msgstr "Alerta procesada"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_ids
msgid "Alerts"
msgstr "Alertas"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_id
msgid "Alias"
msgstr "Seudónimo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_contact
msgid "Alias Contact Security"
msgstr "Seudónimo del contacto de seguridad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_domain_id
msgid "Alias Domain"
msgstr "Dominio del seudónimo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_domain
msgid "Alias Domain Name"
msgstr "Nombre del dominio del seudónimo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_full_name
msgid "Alias Email"
msgstr "Correo electrónico del seudónimo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_name
msgid "Alias Name"
msgstr "Nombre del seudónimo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_status
msgid "Alias Status"
msgstr "Estado del seudónimo"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Estado del seudónimo evaluado en el último mensaje recibido."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_model_id
msgid "Aliased Model"
msgstr "Modelo con seudónimo"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Archived"
msgstr "Archivado"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_check__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_point__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__reason
msgid "Cause"
msgstr "Causa"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__check_id
#: model:ir.model.fields,field_description:quality.field_quality_point__check_ids
msgid "Check"
msgstr "Cheque"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__check_count
msgid "Check Count"
msgstr "Comprobar recuento"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Close"
msgstr "Cerrar"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__color
msgid "Color"
msgstr "Color"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__color
msgid "Color Index"
msgstr "Índice de color"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__company_id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__company_id
#: model:ir.model.fields,field_description:quality.field_quality_check__company_id
#: model:ir.model.fields,field_description:quality.field_quality_point__company_id
msgid "Company"
msgstr "Empresa"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_1
msgid "Confirmed"
msgstr "Confirmado"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__control_date
msgid "Control Date"
msgstr "Fecha de control"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__point_id
msgid "Control Point"
msgstr "Punto de control"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_corrective
msgid "Corrective Action"
msgstr "Acción correctiva"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_date
#: model:ir.model.fields,field_description:quality.field_quality_check__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_date
msgid "Created on"
msgstr "Creado el"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Creation Date"
msgstr "Fecha de creación"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Mensaje personalizado en caso de devolución"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_assign
msgid "Date Assigned"
msgstr "Fecha asignada"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_close
msgid "Date Closed"
msgstr "Fecha de cierre"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_defaults
msgid "Default Values"
msgstr "Valores predeterminados"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr "Define el tipo de punto de control de calidad."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe the quality check to do..."
msgstr "Describa el control de calidad a realizar..."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe why you need to perform this quality check..."
msgstr "Describa por qué necesita llevar a cabo este control de calidad..."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__description
msgid "Description"
msgstr "Descripción"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__display_name
#: model:ir.model.fields,field_description:quality.field_quality_check__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__display_name
#: model:ir.model.fields,field_description:quality.field_quality_reason__display_name
#: model:ir.model.fields,field_description:quality.field_quality_tag__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Done"
msgstr "Listo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_email
msgid "Email Alias"
msgstr "Seudónimo de correo electrónico"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__email_cc
msgid "Email cc"
msgstr "CC del correo electrónico"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""
"Dominio del correo electrónico, por ejemplo, \"ejemplo.com\" en "
"\"<EMAIL>\""

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__fail
msgid "Failed"
msgstr "Fallido"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__folded
msgid "Folded"
msgstr "Plegado"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (contactos)"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome, por ejemplo, fa-tasks"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__has_message
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__has_message
#: model:ir.model.fields,field_description:quality.field_quality_check__has_message
#: model:ir.model.fields,field_description:quality.field_quality_point__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__2
msgid "High"
msgstr "Alta"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__id
#: model:ir.model.fields,field_description:quality.field_quality_check__id
#: model:ir.model.fields,field_description:quality.field_quality_point__id
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__id
#: model:ir.model.fields,field_description:quality.field_quality_reason__id
#: model:ir.model.fields,field_description:quality.field_quality_tag__id
msgid "ID"
msgstr "ID"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID del registro principal que tiene el seudónimo (ejemplo: el proyecto que "
"contiene el seudónimo para la creación de tareas)"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono que indica una actividad de excepción."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Si se establece, este contenido se enviará en automático a los usuarios no "
"autorizados en vez del mensaje predeterminado."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "In Progress"
msgstr "En progreso"

#. module: quality
#: model:quality.point.test_type,name:quality.test_type_instructions
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Instructions"
msgstr "Instrucciones"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_check__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_point__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_date
#: model:ir.model.fields,field_description:quality.field_quality_check__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Late Activities"
msgstr "Actividades atrasadas"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Detección entrante basada en la parte local"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__lot_id
msgid "Lot"
msgstr "Lote"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__lot_id
msgid "Lot/Serial"
msgstr "Número de serie/lote"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__1
msgid "Low"
msgstr "Baja"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error
msgid "Message Delivery error"
msgstr "Error al enviar el mensaje"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__my_activity_date_deadline
#: model:ir.model.fields,field_description:quality.field_quality_check__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "My Alerts"
msgstr "Mis alertas"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__name
#: model:ir.model.fields,field_description:quality.field_quality_reason__name
msgid "Name"
msgstr "Nombre"

#. module: quality
#. odoo-python
#: code:addons/quality/models/quality.py:0
#: model:quality.alert.stage,name:quality.quality_alert_stage_0
msgid "New"
msgstr "Nuevo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_calendar_event_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_date_deadline
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Siguiente fecha límite de la actividad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_summary
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: quality
#. odoo-python
#: code:addons/quality/models/quality.py:0
msgid ""
"No quality team found for this company.\n"
"Please go to configuration and create one first."
msgstr ""
"No se ha encontrado un equipo de control de calidad para esta empresa.\n"
"Primero debe ir a configuración y crear uno."

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__0
msgid "Normal"
msgstr "Normal"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__note
#: model:ir.model.fields,field_description:quality.field_quality_point__note
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Note"
msgstr "Nota"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Notes"
msgstr "Notas"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__picking_type_ids
msgid "Operation Types"
msgstr "Tipos de operación"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Operations"
msgstr "Operaciones"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID opcional de un hilo (registro) al que se adjuntarán todos los mensajes "
"entrantes, incluso si no fueron respuestas del mismo. Si se establece, se "
"deshabilitará completamente la creación de nuevos registros."

#. module: quality
#: model:quality.reason,name:quality.reason_other
msgid "Others"
msgstr "Otros"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_model_id
msgid "Parent Model"
msgstr "Modelo principal"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID del hilo del registro principal"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modelo principal que posee el seudónimo. El modelo que contiene la "
"referencia del seudónimo no es necesariamente el modelo dado por "
"alias_model_id (por ejemplo: proyecto (parent_model) y tarea (model))"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: quality
#: model:quality.reason,name:quality.reason_parts
msgid "Parts Quality"
msgstr "Calidad de las piezas"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__pass
msgid "Passed"
msgstr "Aprobado"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__picking_id
#: model:ir.model.fields,field_description:quality.field_quality_check__picking_id
msgid "Picking"
msgstr "Recolección"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__picture
msgid "Picture"
msgstr "Foto"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"La política sobre cómo publicar un mensaje en el documento con el servidor de correo.\n"
"- Todos: todos pueden publicar\n"
"- Contactos: solo los contactos verificados\n"
"- Seguidores: solo los seguidores del documento relacionado o los miembros de los canales\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_preventive
msgid "Preventive Action"
msgstr "Acción preventiva"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Preview"
msgstr "Previsualizar"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__priority
msgid "Priority"
msgstr "Prioridad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_tmpl_id
#: model:ir.model.fields,field_description:quality.field_quality_check__product_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Product"
msgstr "Producto"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__product_category_ids
msgid "Product Categories"
msgstr "Categorías de productos"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_id
msgid "Product Variant"
msgstr "Variante del producto"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__product_ids
msgid "Products"
msgstr "Productos"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Alert"
msgstr "Alerta de calidad"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_stage
msgid "Quality Alert Stage"
msgstr "Fase de alertas de calidad"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_team
msgid "Quality Alert Team"
msgstr "Equipo de alertas de calidad"

#. module: quality
#: model:ir.model,name:quality.model_quality_check
msgid "Quality Check"
msgstr "Control de calidad"

#. module: quality
#: model:ir.model,name:quality.model_quality_point
msgid "Quality Control Point"
msgstr "Punto de control de calidad"

#. module: quality
#: model:ir.model,name:quality.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr "Tipo de prueba de control de calidad"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__product_category_ids
msgid ""
"Quality Point will apply to every Products in the selected Product "
"Categories."
msgstr ""
"El punto de calidad se aplicará a todos los productos seleccionados en las "
"categorías de producto."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__product_ids
msgid "Quality Point will apply to every selected Products."
msgstr "El punto de calidad se aplicará a todos los productos seleccionados."

#. module: quality
#: model:ir.model,name:quality.model_quality_tag
msgid "Quality Tag"
msgstr "Etiqueta de calidad"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Team"
msgstr "Equipo de calidad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID del hilo de registro"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__name
#: model:ir.model.fields,field_description:quality.field_quality_point__name
msgid "Reference"
msgstr "Referencia"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__user_id
#: model:ir.model.fields,field_description:quality.field_quality_point__user_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Responsible"
msgstr "Responsable"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__reason_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Root Cause"
msgstr "Causa raíz"

#. module: quality
#: model:ir.model,name:quality.model_quality_reason
msgid "Root Cause for Quality Failure"
msgstr "Causa raíz por la que no pasó el control de calidad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error en el envío del SMS"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__sequence
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__sequence
#: model:ir.model.fields,field_description:quality.field_quality_point__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros cuya próxima fecha de acción es antes de la "
"fecha de hoy."

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_3
msgid "Solved"
msgstr "Resuelto"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__stage_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Stage"
msgstr "Etapa"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__quality_state
msgid "Status"
msgstr "Estado"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_state
#: model:ir.model.fields,help:quality.field_quality_check__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado según las actividades\n"
"Vencida: ya pasó la fecha límite\n"
"Hoy: hoy es la fecha de la actividad\n"
"Planeada: futuras actividades."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__name
msgid "Tag Name"
msgstr "Nombre de la etiqueta"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__tag_ids
msgid "Tags"
msgstr "Etiquetas"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
#: model:quality.point.test_type,name:quality.test_type_picture
msgid "Take a Picture"
msgstr "Tomar una foto"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__team_id
#: model:ir.model.fields,field_description:quality.field_quality_check__team_id
#: model:ir.model.fields,field_description:quality.field_quality_point__team_id
msgid "Team"
msgstr "Equipo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__team_ids
msgid "Teams"
msgstr "Equipos"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__technical_name
msgid "Technical name"
msgstr "Nombre técnico"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type_id
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type_id
msgid "Test Type"
msgstr "Tipo de prueba"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"El modelo (tipo de documento de Odoo) al que corresponde este seudónimo. "
"Cualquier correo entrante que no sea respuesta a un registro existente "
"creará un nuevo registro de este modelo (por ejemplo, una tarea de "
"proyecto)."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El nombre del seudónimo de correo electrónico. Por ejemplo, \"trabajos\" si "
"desea recibir correos electrónicos en <<EMAIL>>."

#. module: quality
#: model:res.groups,comment:quality.group_quality_manager
msgid "The quality manager manages the quality process"
msgstr "El jefe de calidad gestiona el proceso de control calidad"

#. module: quality
#: model:res.groups,comment:quality.group_quality_user
msgid "The quality user uses the quality process"
msgstr "El usuario de calidad hace uso del proceso de calidad"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__title
#: model:ir.model.fields,field_description:quality.field_quality_point__title
msgid "Title"
msgstr "Título"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__none
msgid "To do"
msgstr "Por realizar"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Type"
msgstr "Tipo"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de la actividad de excepción registrada."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_tag__color
msgid "Used in the kanban view"
msgstr "Se usa en la vista de kanban"

#. module: quality
#: model:res.groups,name:quality.group_quality_user
msgid "User"
msgstr "Usuario"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__partner_id
msgid "Vendor"
msgstr "Proveedor"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__3
msgid "Very High"
msgstr "Muy alta"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Viewer"
msgstr "Visor"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_point__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: quality
#: model:quality.reason,name:quality.reason_wo
msgid "Work Operation"
msgstr "Operación de trabajo"

#. module: quality
#: model:quality.reason,name:quality.reason_workcenter
msgid "Workcenter Failure"
msgstr "Falla en el centro de trabajo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__active
msgid "active"
msgstr "activa"
