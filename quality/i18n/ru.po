# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alert_count
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_count
msgid "# Quality Alerts"
msgstr "# Уведомления о качестве"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__check_count
msgid "# Quality Checks"
msgstr "# Проверка качества"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Словарь Python, который будет оцениваться для получения значений по "
"умолчанию при создании новых записей для этого псевдонима."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction
msgid "Action Needed"
msgstr "Требуются действия"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_2
msgid "Action Proposed"
msgstr "Предлагаемое действие"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__active
msgid "Active"
msgstr "Активный"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_ids
msgid "Activities"
msgstr "Активность"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_state
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_state
msgid "Activity State"
msgstr "Состояние активности"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_icon
msgid "Activity Type Icon"
msgstr "Значок типа активности"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_check_view_activity
msgid "Activity view"
msgstr "Вид деятельности"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__additional_note
msgid "Additional Note"
msgstr "Дополнительное примечание"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_check__additional_note
msgid "Additional remarks concerning this check."
msgstr "Дополнительные замечания к этому чеку."

#. module: quality
#: model:res.groups,name:quality.group_quality_manager
msgid "Administrator"
msgstr "Администратор"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__done
msgid "Alert Processed"
msgstr "Обработано оповещений"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_ids
msgid "Alerts"
msgstr "Оповещения"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_id
msgid "Alias"
msgstr "Алиас"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_contact
msgid "Alias Contact Security"
msgstr "Безопасность контакта с псевдонимом"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_domain_id
msgid "Alias Domain"
msgstr "Псевдоним Домен"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_domain
msgid "Alias Domain Name"
msgstr "Псевдоним Доменное имя"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_full_name
msgid "Alias Email"
msgstr "Псевдоним электронной почты"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_name
msgid "Alias Name"
msgstr "Псевдоним"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_status
msgid "Alias Status"
msgstr "Статус псевдонима"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Статус псевдонима оценивается по последнему полученному сообщению."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_model_id
msgid "Aliased Model"
msgstr "Модель с псевдонимом"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Archived"
msgstr "Архивировано"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_check__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_point__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__reason
msgid "Cause"
msgstr "Дело"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__check_id
#: model:ir.model.fields,field_description:quality.field_quality_point__check_ids
msgid "Check"
msgstr "Проверить"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__check_count
msgid "Check Count"
msgstr "Контрольный подсчет"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Close"
msgstr "Закрыть"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__color
msgid "Color"
msgstr "Цвет"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__color
msgid "Color Index"
msgstr "Цветовой индекс"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__company_id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__company_id
#: model:ir.model.fields,field_description:quality.field_quality_check__company_id
#: model:ir.model.fields,field_description:quality.field_quality_point__company_id
msgid "Company"
msgstr "Компания"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_1
msgid "Confirmed"
msgstr "Подтверждена"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__control_date
msgid "Control Date"
msgstr "Контрольная дата"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__point_id
msgid "Control Point"
msgstr "Контрольная точка"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_corrective
msgid "Corrective Action"
msgstr "Корректирующие действия"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_uid
msgid "Created by"
msgstr "Создано"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_date
#: model:ir.model.fields,field_description:quality.field_quality_check__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_date
msgid "Created on"
msgstr "Создано"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Creation Date"
msgstr "Дата создания"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Пользовательское сообщение об отказе"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_assign
msgid "Date Assigned"
msgstr "Назначенная дата"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_close
msgid "Date Closed"
msgstr "Дата закрытия"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_defaults
msgid "Default Values"
msgstr "Значения по умолчанию"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr "Определяет тип точки контроля качества."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe the quality check to do..."
msgstr "Опишите проверку качества..."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe why you need to perform this quality check..."
msgstr "Опишите, почему вам необходимо выполнить проверку качества ..."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__description
msgid "Description"
msgstr "Описание"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__display_name
#: model:ir.model.fields,field_description:quality.field_quality_check__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__display_name
#: model:ir.model.fields,field_description:quality.field_quality_reason__display_name
#: model:ir.model.fields,field_description:quality.field_quality_tag__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Done"
msgstr "Готово"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_email
msgid "Email Alias"
msgstr "Псевдоним почты"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__email_cc
msgid "Email cc"
msgstr "Электронная почта cc"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Домен электронной почты, например, 'example.com' в '<EMAIL>'"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__fail
msgid "Failed"
msgstr "Ошибка"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__folded
msgid "Folded"
msgstr "Сложенный"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Шрифт, отличный значок, например. к.-а.-задачи"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Future Activities"
msgstr "Планируемые действия"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Group By"
msgstr "Группировать по"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__has_message
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__has_message
#: model:ir.model.fields,field_description:quality.field_quality_check__has_message
#: model:ir.model.fields,field_description:quality.field_quality_point__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__2
msgid "High"
msgstr "Высокий"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__id
#: model:ir.model.fields,field_description:quality.field_quality_check__id
#: model:ir.model.fields,field_description:quality.field_quality_point__id
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__id
#: model:ir.model.fields,field_description:quality.field_quality_reason__id
#: model:ir.model.fields,field_description:quality.field_quality_tag__id
msgid "ID"
msgstr "ID"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID записи родителя, содержащей алиас (например, проект содержит псевдоним "
"для создания записи)"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий исключение Дела."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Если флажок установлен, значит, новые сообщения требуют вашего внимания."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено, некоторые сообщения имеют ошибку доставки."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Если этот параметр установлен, то вместо сообщения по умолчанию "
"неавторизованным пользователям будет автоматически рассылаться это "
"содержимое."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "In Progress"
msgstr "В процессе"

#. module: quality
#: model:quality.point.test_type,name:quality.test_type_instructions
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Instructions"
msgstr "Инструкции"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_check__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_point__message_is_follower
msgid "Is Follower"
msgstr "Является подписчиком"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_date
#: model:ir.model.fields,field_description:quality.field_quality_check__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Late Activities"
msgstr "Поздние Мероприятия"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Обнаружение входящих на основе локальных частей"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__lot_id
msgid "Lot"
msgstr "Лот"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__lot_id
msgid "Lot/Serial"
msgstr "Партия/серия"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__1
msgid "Low"
msgstr "Низкий"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__my_activity_date_deadline
#: model:ir.model.fields,field_description:quality.field_quality_check__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моей активности"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "My Alerts"
msgstr "Мои предупреждения"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__name
#: model:ir.model.fields,field_description:quality.field_quality_reason__name
msgid "Name"
msgstr "Имя"

#. module: quality
#. odoo-python
#: code:addons/quality/models/quality.py:0
#: model:quality.alert.stage,name:quality.quality_alert_stage_0
msgid "New"
msgstr "Новый"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_calendar_event_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Следующее событие календаря активности"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_date_deadline
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Следующий срок мероприятия"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_summary
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_summary
msgid "Next Activity Summary"
msgstr "Резюме следующего действия"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_id
msgid "Next Activity Type"
msgstr "Следующий Тип Мероприятия"

#. module: quality
#. odoo-python
#: code:addons/quality/models/quality.py:0
msgid ""
"No quality team found for this company.\n"
"Please go to configuration and create one first."
msgstr ""
"Для этой компании не найдено ни одной команды качества.\n"
"Пожалуйста, перейдите к конфигурации и создайте ее."

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__0
msgid "Normal"
msgstr "Нормальный"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__note
#: model:ir.model.fields,field_description:quality.field_quality_point__note
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Note"
msgstr "Заметка"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Notes"
msgstr "Заметки"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction_counter
msgid "Number of Actions"
msgstr "Число действий"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error_counter
msgid "Number of errors"
msgstr "Число ошибок"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих принятия мер"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество недоставленных сообщений"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__picking_type_ids
msgid "Operation Types"
msgstr "Типы операций"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Operations"
msgstr "Операции"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Необязательный идентификатор потока (записи), к которому будут прикрепляться"
" все входящие сообщения, даже если на них не было ответа. Если установить "
"это значение, то создание новых записей будет полностью запрещено."

#. module: quality
#: model:quality.reason,name:quality.reason_other
msgid "Others"
msgstr "Другие"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_model_id
msgid "Parent Model"
msgstr "Основная модель"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Номер родительской цепочки записей"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Родительская модель, на которую ссылается псевдоним. Модель, на которую "
"ссылается псевдоним, не обязательно является моделью, заданной "
"alias_model_id (пример: проект (родительская_модель) и задача (модель))"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__partner_id
msgid "Partner"
msgstr "Партнер"

#. module: quality
#: model:quality.reason,name:quality.reason_parts
msgid "Parts Quality"
msgstr "Качество деталей"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__pass
msgid "Passed"
msgstr "Пройдено"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__picking_id
#: model:ir.model.fields,field_description:quality.field_quality_check__picking_id
msgid "Picking"
msgstr "Выбор"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__picture
msgid "Picture"
msgstr "Изображение"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Политика публикации сообщения в документе с помощью mailgateway.\n"
"- все: все могут отправлять сообщения\n"
"- партнеры: только аутентифицированные партнеры\n"
"- последователи: только последователи связанного документа или участники следующих каналов\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_preventive
msgid "Preventive Action"
msgstr "Превентивные меры"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Preview"
msgstr "Предпросмотр"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__priority
msgid "Priority"
msgstr "Приоритет"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_tmpl_id
#: model:ir.model.fields,field_description:quality.field_quality_check__product_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Product"
msgstr "Товар"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__product_category_ids
msgid "Product Categories"
msgstr "Категории товаров"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_id
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__product_ids
msgid "Products"
msgstr "Товары"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Alert"
msgstr "Предупреждение о качестве"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_stage
msgid "Quality Alert Stage"
msgstr "Этап оповещения о качестве"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_team
msgid "Quality Alert Team"
msgstr "Команда контроля качества"

#. module: quality
#: model:ir.model,name:quality.model_quality_check
msgid "Quality Check"
msgstr "Проверка качества"

#. module: quality
#: model:ir.model,name:quality.model_quality_point
msgid "Quality Control Point"
msgstr "Пункт контроля качества"

#. module: quality
#: model:ir.model,name:quality.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr "Контроль качества Тип испытания"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__product_category_ids
msgid ""
"Quality Point will apply to every Products in the selected Product "
"Categories."
msgstr ""
"Баллы качества будут применяться к каждому товару в выбранных категориях "
"товаров."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__product_ids
msgid "Quality Point will apply to every selected Products."
msgstr "Quality Point будет применяться к каждому выбранному товару."

#. module: quality
#: model:ir.model,name:quality.model_quality_tag
msgid "Quality Tag"
msgstr "Тег качества"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Team"
msgstr "Команда качества"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__rating_ids
msgid "Ratings"
msgstr "Рейтинги"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Номер цепочки записей"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__name
#: model:ir.model.fields,field_description:quality.field_quality_point__name
msgid "Reference"
msgstr "Справка"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__user_id
#: model:ir.model.fields,field_description:quality.field_quality_point__user_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Responsible"
msgstr "Ответственный"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_user_id
msgid "Responsible User"
msgstr "Ответственный пользователь"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__reason_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Root Cause"
msgstr "Коренная причина"

#. module: quality
#: model:ir.model,name:quality.model_quality_reason
msgid "Root Cause for Quality Failure"
msgstr "Коренная причина нарушения качества"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__sequence
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__sequence
#: model:ir.model.fields,field_description:quality.field_quality_point__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Показать все записи, у которых дата следующего действия наступает до "
"сегодняшнего дня"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_3
msgid "Solved"
msgstr "Решено"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__stage_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Stage"
msgstr "Этап"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__quality_state
msgid "Status"
msgstr "Статус"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_state
#: model:ir.model.fields,help:quality.field_quality_check__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус, основанный на мероприятии\n"
"Просроченная: Дата, уже прошла\n"
"Сегодня: Дата мероприятия сегодня\n"
"Запланировано: будущая деятельность."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__name
msgid "Tag Name"
msgstr "Название тега"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__tag_ids
msgid "Tags"
msgstr "Теги"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
#: model:quality.point.test_type,name:quality.test_type_picture
msgid "Take a Picture"
msgstr "Сделайте снимок"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__team_id
#: model:ir.model.fields,field_description:quality.field_quality_check__team_id
#: model:ir.model.fields,field_description:quality.field_quality_point__team_id
msgid "Team"
msgstr "Команда"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__team_ids
msgid "Teams"
msgstr "Команды"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__technical_name
msgid "Technical name"
msgstr "Техническое название"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type_id
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type_id
msgid "Test Type"
msgstr "Тип теста"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Модель (Вид Документа), которому соответствует этот псевдоним. Любая "
"входящая электронная почта, которая не отвечает существующей записи, вызовет"
" создание новогой записи этой модели (например, Задача Проекта)"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Имя Алиаса Эл.Почты, например, \"jobs\" если вы хотите перехватывать почту "
"для <<EMAIL>>"

#. module: quality
#: model:res.groups,comment:quality.group_quality_manager
msgid "The quality manager manages the quality process"
msgstr "Менеджеры качества управляют процессом качества"

#. module: quality
#: model:res.groups,comment:quality.group_quality_user
msgid "The quality user uses the quality process"
msgstr "Качественный пользователь использует процесс качества"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__title
#: model:ir.model.fields,field_description:quality.field_quality_point__title
msgid "Title"
msgstr "Заголовок"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__none
msgid "To do"
msgstr "Сделать"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Today Activities"
msgstr "Сегодняшние Дела"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Type"
msgstr "Тип"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип Дела для исключения в записи."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_tag__color
msgid "Used in the kanban view"
msgstr "Используется в виде канбан"

#. module: quality
#: model:res.groups,name:quality.group_quality_user
msgid "User"
msgstr "Пользователь"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__partner_id
msgid "Vendor"
msgstr "Продавец"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__3
msgid "Very High"
msgstr "Очень высоко"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Viewer"
msgstr "Просмотрщик"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__website_message_ids
msgid "Website Messages"
msgstr "Веб-сайт сообщения"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_point__website_message_ids
msgid "Website communication history"
msgstr "История общений с сайта"

#. module: quality
#: model:quality.reason,name:quality.reason_wo
msgid "Work Operation"
msgstr "Работа Операция"

#. module: quality
#: model:quality.reason,name:quality.reason_workcenter
msgid "Workcenter Failure"
msgstr "Отказ рабочего центра"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__active
msgid "active"
msgstr "активный"
