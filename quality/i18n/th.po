# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alert_count
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_count
msgid "# Quality Alerts"
msgstr "# การแจ้งเตือน การจัดการคุณภาพ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__check_count
msgid "# Quality Checks"
msgstr "# การตรวจสอบคุณภาพ"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"พจนานุกรม Python "
"ที่จะถูกประเมินเพื่อให้เป็นค่าเริ่มต้นเมื่อสร้างการบันทึกใหม่สำหรับนามแฝงนี้"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_2
msgid "Action Proposed"
msgstr "การดำเนินการที่เสนอ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_state
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_check_view_activity
msgid "Activity view"
msgstr "มุมมองกิจกรรม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__additional_note
msgid "Additional Note"
msgstr "บันทึกภายในเพิ่มเติม"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_check__additional_note
msgid "Additional remarks concerning this check."
msgstr "ข้อสังเกตเพิ่มเติมเกี่ยวกับการตรวจสอบนี้"

#. module: quality
#: model:res.groups,name:quality.group_quality_manager
msgid "Administrator"
msgstr "ผู้ดูแลระบบ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__done
msgid "Alert Processed"
msgstr "ดำเนินการแจ้งเตือนแล้ว"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_ids
msgid "Alerts"
msgstr "การเตือน"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_id
msgid "Alias"
msgstr "นามแฝง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_contact
msgid "Alias Contact Security"
msgstr "นามแฝงสำหรับติดต่อเพื่อความปลอดภัย"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_domain_id
msgid "Alias Domain"
msgstr "ชื่อโดเมน"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_domain
msgid "Alias Domain Name"
msgstr "ชื่อโดเมนนามแฝง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_full_name
msgid "Alias Email"
msgstr "อีเมลนามแฝง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_name
msgid "Alias Name"
msgstr "นามแฝง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_status
msgid "Alias Status"
msgstr "สถานะนามแฝง"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr "ประเมินสถานะนามแฝงตามข้อความล่าสุดที่ได้รับ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_model_id
msgid "Aliased Model"
msgstr "โมเดลนามแฝง"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_check__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_point__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__reason
msgid "Cause"
msgstr "สาเหตุ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__check_id
#: model:ir.model.fields,field_description:quality.field_quality_point__check_ids
msgid "Check"
msgstr "ตรวจสอบ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__check_count
msgid "Check Count"
msgstr "จำนวนที่ตรวจสอบ"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Close"
msgstr "ปิด"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__color
msgid "Color"
msgstr "สี"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__color
msgid "Color Index"
msgstr "ดัชนีสี"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__company_id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__company_id
#: model:ir.model.fields,field_description:quality.field_quality_check__company_id
#: model:ir.model.fields,field_description:quality.field_quality_point__company_id
msgid "Company"
msgstr "บริษัท"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_1
msgid "Confirmed"
msgstr "ยืนยันแล้ว"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__control_date
msgid "Control Date"
msgstr "วันที่ควบคุม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__point_id
msgid "Control Point"
msgstr "จุดควบคุม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_corrective
msgid "Corrective Action"
msgstr "การดำเนินการแก้ไข"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_date
#: model:ir.model.fields,field_description:quality.field_quality_check__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Creation Date"
msgstr "วันที่สร้าง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "ข้อความตีกลับที่กำหนดเอง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_assign
msgid "Date Assigned"
msgstr "วันที่ได้รับมอบหมาย"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_close
msgid "Date Closed"
msgstr "วันที่ปิด"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_defaults
msgid "Default Values"
msgstr "ค่าเริ่มต้น"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr "กำหนดประเภทของจุดควบคุมคุณภาพ"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe the quality check to do..."
msgstr "อธิบายการตรวจสอบคุณภาพที่ต้องทำ..."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe why you need to perform this quality check..."
msgstr "อธิบายว่าเหตุใดคุณจึงต้องดำเนินการตรวจสอบคุณภาพนี้..."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__description
msgid "Description"
msgstr "คำอธิบาย"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__display_name
#: model:ir.model.fields,field_description:quality.field_quality_check__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__display_name
#: model:ir.model.fields,field_description:quality.field_quality_reason__display_name
#: model:ir.model.fields,field_description:quality.field_quality_tag__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_email
msgid "Email Alias"
msgstr "นามแฝงอีเมล"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__email_cc
msgid "Email cc"
msgstr "cc อีเมล"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "โดเมนอีเมล เช่น 'example.com' ใน '<EMAIL>'"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__fail
msgid "Failed"
msgstr "ล้มเหลว"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__folded
msgid "Folded"
msgstr "พับ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบฟอนต์ที่ยอดเยี่ยมเช่น fa-tasks"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Future Activities"
msgstr "กิจกรรมในอนาคต"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__has_message
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__has_message
#: model:ir.model.fields,field_description:quality.field_quality_check__has_message
#: model:ir.model.fields,field_description:quality.field_quality_point__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__2
msgid "High"
msgstr "สูง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__id
#: model:ir.model.fields,field_description:quality.field_quality_check__id
#: model:ir.model.fields,field_description:quality.field_quality_point__id
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__id
#: model:ir.model.fields,field_description:quality.field_quality_reason__id
#: model:ir.model.fields,field_description:quality.field_quality_tag__id
msgid "ID"
msgstr "ไอดี"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ไอดีของบันทึกหลักที่มีนามแฝง (ตัวอย่าง: โปรเจ็กต์ที่มีนามแฝงในการสร้างงาน)"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุการยกเว้นกิจกรรม"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"หากตั้งค่าไว้ "
"เนื้อหานี้จะถูกส่งไปยังผู้ใช้ที่ไม่ได้รับอนุญาตโดยอัตโนมัติแทนข้อความเริ่มต้น"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "In Progress"
msgstr "กำลังดำเนินการ"

#. module: quality
#: model:quality.point.test_type,name:quality.test_type_instructions
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Instructions"
msgstr "คำแนะนำ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_check__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_point__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_date
#: model:ir.model.fields,field_description:quality.field_quality_check__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Late Activities"
msgstr "กิจกรรมล่าสุด"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "การตรวจจับขาเข้าตามชิ้นส่วนในพื้นที่"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__lot_id
msgid "Lot"
msgstr "ล็อต"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__lot_id
msgid "Lot/Serial"
msgstr "ล็อต/ซีเรียล"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__1
msgid "Low"
msgstr "ต่ำ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__my_activity_date_deadline
#: model:ir.model.fields,field_description:quality.field_quality_check__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "My Alerts"
msgstr "การแจ้งเตือนของฉัน"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__name
#: model:ir.model.fields,field_description:quality.field_quality_reason__name
msgid "Name"
msgstr "ชื่อ"

#. module: quality
#. odoo-python
#: code:addons/quality/models/quality.py:0
#: model:quality.alert.stage,name:quality.quality_alert_stage_0
msgid "New"
msgstr "ใหม่"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_calendar_event_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_date_deadline
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_summary
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: quality
#. odoo-python
#: code:addons/quality/models/quality.py:0
msgid ""
"No quality team found for this company.\n"
"Please go to configuration and create one first."
msgstr ""
"ไม่พบทีมงานตรวจสอบคุณภาพสำหรับบริษัทนี้\n"
"โปรดไปที่การกำหนดค่าและสร้างขึ้นมาก่อน"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__0
msgid "Normal"
msgstr "ปกติ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__note
#: model:ir.model.fields,field_description:quality.field_quality_point__note
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Note"
msgstr "โน้ต"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Notes"
msgstr "โน้ต"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__picking_type_ids
msgid "Operation Types"
msgstr "ประเภทการปฏิบัติการ"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Operations"
msgstr "การปฏิบัติการ"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ไอดีทางเลือกของเธรด (บันทึก) ที่จะแนบข้อความขาเข้าทั้งหมด "
"แม้ว่าพวกเขาจะไม่ตอบกลับก็ตาม หากตั้งค่าไว้ "
"การดำเนินการนี้จะปิดใช้งานการสร้างการบันทึกใหม่ทั้งหมด"

#. module: quality
#: model:quality.reason,name:quality.reason_other
msgid "Others"
msgstr "อื่นๆ "

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_model_id
msgid "Parent Model"
msgstr "โมเดลหลัก"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ไอดีเธรดการบันทึกหลัก"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"โมเดลหลักที่ถือนามแฝง "
"โมเดลที่มีการอ้างอิงนามแฝงไม่จำเป็นต้องเป็นโมเดลที่กำหนดโดย alias_model_id "
"(ตัวอย่าง: project (parent_model) and task (model))"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__partner_id
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: quality
#: model:quality.reason,name:quality.reason_parts
msgid "Parts Quality"
msgstr "การจัดการคุณภาพของชิ้นส่วน"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__pass
msgid "Passed"
msgstr "ผ่าน"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__picking_id
#: model:ir.model.fields,field_description:quality.field_quality_check__picking_id
msgid "Picking"
msgstr "การรับ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__picture
msgid "Picture"
msgstr "รูปภาพ"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"นโยบายการโพสต์ข้อความบนเอกสารโดยใช้เมลล์เกตเวย์\n"
"- ทุกคน: ทุกคนโพสต์ได้\n"
"- พาร์ทเนอร์: เฉพาะพาร์ทเนอร์ที่ได้รับการรับรองเท่านั้น\n"
"- ผู้ติดตาม: เฉพาะผู้ติดตามเอกสารที่เกี่ยวข้องหรือสมาชิกของช่องดังต่อไปนี้\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_preventive
msgid "Preventive Action"
msgstr "การดำเนินการป้องกัน"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Preview"
msgstr "ตัวอย่าง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__priority
msgid "Priority"
msgstr "ระดับความสำคัญ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_tmpl_id
#: model:ir.model.fields,field_description:quality.field_quality_check__product_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Product"
msgstr "สินค้า"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__product_category_ids
msgid "Product Categories"
msgstr "หมวดหมู่สินค้า"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_id
msgid "Product Variant"
msgstr "ตัวเลือกสินค้า"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__product_ids
msgid "Products"
msgstr "สินค้า"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Alert"
msgstr "การแจ้งเตือนคุณภาพ"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_stage
msgid "Quality Alert Stage"
msgstr "ขั้นตอนการแจ้งเตือนการจัดการคุณภาพ"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_team
msgid "Quality Alert Team"
msgstr "ทีมแจ้งเตือนการจัดการคุณภาพ"

#. module: quality
#: model:ir.model,name:quality.model_quality_check
msgid "Quality Check"
msgstr "การตรวจสอบคุณภาพ"

#. module: quality
#: model:ir.model,name:quality.model_quality_point
msgid "Quality Control Point"
msgstr "จุดควบคุมคุณภาพ"

#. module: quality
#: model:ir.model,name:quality.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr "ประเภทการทดสอบการควบคุมคุณภาพ"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__product_category_ids
msgid ""
"Quality Point will apply to every Products in the selected Product "
"Categories."
msgstr "คะแนนการจัดการคุณภาพจะนำไปใช้กับทุกสินค้าในหมวดหมู่สินค้าที่เลือก"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__product_ids
msgid "Quality Point will apply to every selected Products."
msgstr "จุดคุณภาพจะนำไปใช้กับทุกสินค้าที่เลือก"

#. module: quality
#: model:ir.model,name:quality.model_quality_tag
msgid "Quality Tag"
msgstr "แท็กการจัดการคุณภาพ"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Team"
msgstr "ทีมการจัดการคุณภาพ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__rating_ids
msgid "Ratings"
msgstr "การให้คะแนน"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "บันทึกเธรด ID"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__name
#: model:ir.model.fields,field_description:quality.field_quality_point__name
msgid "Reference"
msgstr "การอ้างอิง"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__user_id
#: model:ir.model.fields,field_description:quality.field_quality_point__user_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__reason_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Root Cause"
msgstr "สาเหตุที่แท้จริง"

#. module: quality
#: model:ir.model,name:quality.model_quality_reason
msgid "Root Cause for Quality Failure"
msgstr "สาเหตุที่แท้จริงของความล้มเหลวด้านคุณภาพ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__sequence
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__sequence
#: model:ir.model.fields,field_description:quality.field_quality_point__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Show all records which has next action date is before today"
msgstr "แสดงระเบียนทั้งหมดที่มีวันที่ดำเนินการถัดไปคือก่อนวันนี้"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_3
msgid "Solved"
msgstr "แก้แล้ว"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__stage_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Stage"
msgstr "ขั้นตอน"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__quality_state
msgid "Status"
msgstr "สถานะ"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_state
#: model:ir.model.fields,help:quality.field_quality_check__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__name
msgid "Tag Name"
msgstr "ชื่อแท็ก"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__tag_ids
msgid "Tags"
msgstr "แท็ก"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
#: model:quality.point.test_type,name:quality.test_type_picture
msgid "Take a Picture"
msgstr "ถ่ายภาพ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__team_id
#: model:ir.model.fields,field_description:quality.field_quality_check__team_id
#: model:ir.model.fields,field_description:quality.field_quality_point__team_id
msgid "Team"
msgstr "ทีม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__team_ids
msgid "Teams"
msgstr "ทีม"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__technical_name
msgid "Technical name"
msgstr "ชื่อทางเทคนิค"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type_id
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type_id
msgid "Test Type"
msgstr "ประเภทการทดสอบ"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"โมเดล (ชนิดเอกสาร Odoo) ซึ่งสอดคล้องกับนามแฝงนี้ "
"อีเมลขาเข้าที่ไม่ตอบกลับบันทึกที่มีอยู่จะทำให้เกิดการสร้างบันทึกใหม่ของโมเดลนี้"
" (เช่น โปรเจ็กต์งาน)"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"ชื่อของนามแฝงอีเมล เช่น 'งาน' หากคุณต้องการรับอีเมลสำหรับ "
"<<EMAIL>>"

#. module: quality
#: model:res.groups,comment:quality.group_quality_manager
msgid "The quality manager manages the quality process"
msgstr "ผู้จัดการคุณภาพจะจัดการขั้นตอนการตรวจสอบคุณภาพ"

#. module: quality
#: model:res.groups,comment:quality.group_quality_user
msgid "The quality user uses the quality process"
msgstr "ผู้ใช้ที่มีคุณภาพจะใช้กระบวนการที่มีคุณภาพ"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__title
#: model:ir.model.fields,field_description:quality.field_quality_point__title
msgid "Title"
msgstr "คำนำหน้าชื่อ"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__none
msgid "To do"
msgstr "To do"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Today Activities"
msgstr "กิจกรรมวันนี้"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Type"
msgstr "ประเภท"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "ประเภทกิจกรรมข้อยกเว้นบนบันทึก"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_tag__color
msgid "Used in the kanban view"
msgstr "ใช้ในมุมมองคัมบัง"

#. module: quality
#: model:res.groups,name:quality.group_quality_user
msgid "User"
msgstr "ผู้ใช้"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__partner_id
msgid "Vendor"
msgstr "ผู้ขาย"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__3
msgid "Very High"
msgstr "สูงมาก"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Viewer"
msgstr "ผู้ดู"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_point__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: quality
#: model:quality.reason,name:quality.reason_wo
msgid "Work Operation"
msgstr "การดำเนินงาน"

#. module: quality
#: model:quality.reason,name:quality.reason_workcenter
msgid "Workcenter Failure"
msgstr "ศูนย์งานล้มเหลว"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__active
msgid "active"
msgstr "ที่ใช้งานอยู่"
