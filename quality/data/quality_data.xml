<odoo>
    <data noupdate="1">

        <record forcecreate="True" id="decimal_quality" model="decimal.precision">
            <field name="name">Quality Tests</field>
            <field name="digits">2</field>
        </record>

        <record id="quality_alert_team0" model="quality.alert.team">
            <field name="name">Main Quality Team</field>
            <field name="alias_id" ref="mail_alias_quality_alert"/>
        </record>
        <record id="quality_alert_stage_0" model="quality.alert.stage">
            <field name="name">New</field>
        </record>
        <record id="quality_alert_stage_1" model="quality.alert.stage">
            <field name="name">Confirmed</field>
        </record>
        <record id="quality_alert_stage_2" model="quality.alert.stage">
            <field name="name">Action Proposed</field>
        </record>
        <record id="quality_alert_stage_3" model="quality.alert.stage">
            <field name="name">Solved</field>
            <field name="folded">True</field>
            <field name="done">True</field>
        </record>
        <record id="sequence_quality_point" model="ir.sequence">
            <field name="name">Quality Point</field>
            <field name="code">quality.point</field>
            <field name="prefix">QCP</field>
            <field name="padding">5</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="company_id" eval="False"/>
        </record>
        <record id="sequence_quality_check" model="ir.sequence">
            <field name="name">Quality Check</field>
            <field name="code">quality.check</field>
            <field name="prefix">QC</field>
            <field name="padding">5</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="company_id" eval="False"/>
        </record>
        <record id="sequence_quality_alert" model="ir.sequence">
            <field name="name">Quality Alert</field>
            <field name="code">quality.alert</field>
            <field name="prefix">QA</field>
            <field name="padding">5</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="company_id" eval="False"/>
        </record>



        <record id="reason_workcenter" model="quality.reason">
            <field name="name">Workcenter Failure</field>
        </record>
        <record id="reason_parts" model="quality.reason">
            <field name="name">Parts Quality</field>
        </record>
        <record id="reason_wo" model="quality.reason">
            <field name="name">Work Operation</field>
        </record>
        <record id="reason_other" model="quality.reason">
            <field name="name">Others</field>
        </record>
        <record id="quality.test_type_instructions" model="quality.point.test_type">
            <field name="name">Instructions</field>
            <field name="technical_name">instructions</field>
        </record>
        <record id="quality.test_type_picture" model="quality.point.test_type">
            <field name="name">Take a Picture</field>
            <field name="technical_name">picture</field>
        </record>
    </data>
</odoo>
