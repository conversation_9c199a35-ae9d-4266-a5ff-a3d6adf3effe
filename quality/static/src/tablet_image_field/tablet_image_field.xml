<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="quality.TabletImageField" t-inherit="web.ImageField" t-inherit-mode="primary">
        <xpath expr="//button[hasclass('o_select_file_button')]" position="replace">
            <div class="position-absolute start-100 ms-2" id="picture_button">
                <button class="btn btn-primary o_select_file_button" barcode_trigger="measure">Take a Picture</button>
            </div>
        </xpath>
        <xpath expr="//img" position="attributes">
        <attribute name="t-on-click">openModal</attribute>
      </xpath>
        <xpath expr="//div[@t-attf-class]" position="attributes">
            <attribute name="t-attf-class">position-absolute d-flex justify-content-between w-100 {{isMobile ? 'o_mobile_controls' : ''}}</attribute>
        </xpath>
    </t>

    <t t-name="quality.ImagePreviewDialog">
        <Dialog title.translate="Preview" size="'lg'">
            <div class="o_viewer_img_wrapper">
                    <div class="o_viewer_zoomer">
                        <img alt="Viewer" class="o_viewer_img" t-att-src="props.src"/>
                    </div>
                </div>
            <t t-set-slot="footer">
                <button class="btn btn-primary" t-on-click.stop="props.close">Close</button>
            </t>
        </Dialog>
    </t>
</templates>
