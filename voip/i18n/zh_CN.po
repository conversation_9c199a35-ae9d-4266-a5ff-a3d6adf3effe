# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Odoo哥 <<EMAIL>>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "%(minutes)s min %(seconds)s sec"
msgstr "%(minutes)s 分 %(seconds)s 秒"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "%(minutes)s minutes"
msgstr "%(minutes)s 分钟"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "%(number)s missed calls"
msgstr "%(number)s 未接来电"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "%(seconds)s seconds"
msgstr "%(seconds)s 秒"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "1 minute"
msgstr "1分钟"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "1 missed call"
msgstr "1个未接电话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "1 second"
msgstr "1秒"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "2 minutes"
msgstr "2分钟"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "2 missed calls"
msgstr "2个未接电话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "2 seconds"
msgstr "2秒"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"A hardware error has occurred while trying to access the audio recording "
"device. Please ensure that your drivers are up to date and try again."
msgstr "在尝试访问录音设备时发生了硬件错误。请确保您的驱动程序是最新的，然后再试一次。"

#. module: voip
#: model:ir.model,name:voip.model_voip_call
msgid "A phone call handled using the VoIP application"
msgstr "使用网络电话应用程序处理的电话呼叫"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__aborted
msgid "Aborted"
msgstr "中止"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Aborted call to %(phone_number)s"
msgstr "呼叫 %(phone_number)s 中止"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#: model:ir.model,name:voip.model_mail_activity
msgid "Activity"
msgstr "活动"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__activity_name
msgid "Activity Name"
msgstr "活动名称"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/call_queue_switch.js:0
msgid "Add to Call Queue"
msgstr "添加到呼叫队列"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"An error occured involving the audio recording device (%(errorName)s):\n"
"%(errorMessage)s"
msgstr ""
"录音设备（%(errorName)s）发生错误：\n"
"%(errorMessage)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"An error occurred during the instantiation of the User Agent:\n"
"\n"
"%(error)s"
msgstr ""
"用户代理实例化过程中发生错误：\n"
"\n"
"%(error)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"An error occurred trying to invite the following number: %(phoneNumber)s\n"
"\n"
"Error: %(error)s"
msgstr ""
"尝试邀请以下号码时发生错误：%(phoneNumber)s\n"
"\n"
"错误：%(error)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid ""
"Are you sure you want to delete this activity? It will be lost forever, "
"which is quite a long time 😔"
msgstr "您确定要删除此活动吗？它将永远丢失，而丢失的时间是相当长的😔。"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users__how_to_call_on_mobile__ask
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__ask
msgid "Ask"
msgstr "提问"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/numpad.xml:0
msgid "Backspace"
msgstr "退格键"

#. module: voip
#. odoo-javascript
#. odoo-python
#: code:addons/voip/models/voip_queue_mixin.py:0
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Call"
msgstr "电话"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call from %(correspondent)s"
msgstr "来自 %(correspondent)s 的呼叫"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call from %(phone_number)s"
msgstr "来自 %(phone_number)s 的呼叫"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_call_from_another_device
msgid "Call from another device"
msgstr "来自其他设备的呼叫"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Call rejected (reason: “%(reasonPhrase)s”)"
msgstr "呼叫被拒绝（原因：\"%(reasonPhrase)s\"）。"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call to %(correspondent)s"
msgstr "呼叫 %(correspondent)s"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call to %(phone_number)s"
msgstr "呼叫 %(phone_number)s"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__calling
msgid "Calling"
msgstr "连接中"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Calling %(phone number)s"
msgstr "拨打 %(phone number)s"

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_call_view
#: model_terms:ir.ui.view,arch_db:voip.voip_call_tree_view
msgid "Calls"
msgstr "电话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/devise_selection_dialog.xml:0
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Cancel"
msgstr "取消"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.xml:0
msgid "Cancel the activity"
msgstr "取消活动"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Cannot access audio recording device. If you have denied access to your "
"microphone, please allow it and try again. Otherwise, make sure that this "
"website is running over HTTPS and that your browser is not set to deny "
"access to media devices."
msgstr "无法访问录音设备。如果您拒绝访问麦克风，请允许后再试一次。否则，请确保本网站通过 HTTPS 运行，且您的浏览器未设置为拒绝访问媒体设备。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Change input device"
msgstr "更改输入设备"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/devise_selection_dialog.xml:0
msgid "Choose a device:"
msgstr "选择设备："

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__how_to_call_on_mobile
msgid ""
"Choose the method to be used to place a call when using the mobile application:\n"
"            • VoIP: Always use the Odoo softphone\n"
"            • Device's phone: Always use the device's phone\n"
"            • Ask: Always ask whether the softphone or the device's phone must be used\n"
"        "
msgstr ""
"在使用移动端应用程序时，选择用于拨打电话的方法。\n"
"            • VoIP：始终使用Odoo软电话\n"
"            • 设备的电话：总是使用设备的电话\n"
"            • 询问：总是询问是否必须使用软电话或设备的电话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.xml:0
msgid "Close"
msgstr "关闭"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Close Numpad"
msgstr "关闭 Numpad"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/voip_systray_item.js:0
msgid "Close Softphone"
msgstr "关闭软电话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Close details"
msgstr "关闭细节"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__company_id
msgid "Company"
msgstr "公司"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
#: code:addons/voip/static/src/mobile/devise_selection_dialog.xml:0
msgid "Confirm"
msgstr "确认"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Connecting…"
msgstr "正在连接..."

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_call__partner_id
msgid "Contact"
msgstr "联系人"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Contacts"
msgstr "联系人"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/contacts_tab.xml:0
msgid "Contacts with a phone number will be shown here."
msgstr "带有电话号码的联系人将显示在此处。"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_provider__create_uid
msgid "Created by"
msgstr "创建人"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__create_date
#: model:ir.model.fields,field_description:voip.field_voip_provider__create_date
msgid "Created on"
msgstr "创建日期"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
msgid "Credentials"
msgstr "授权认证"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Customer"
msgstr "客户"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_provider__mode__demo
msgid "Demo"
msgstr "演示"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users__how_to_call_on_mobile__phone
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__phone
msgid "Device's phone"
msgstr "设备的电话"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__direction
msgid "Direction"
msgstr "方向"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__display_name
#: model:ir.model.fields,field_description:voip.field_voip_provider__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Documents"
msgstr "文档"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Edit"
msgstr "编辑"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "End Call"
msgstr "结束通话"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__end_date
msgid "End Date"
msgstr "结束日期"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/transfer_popover.xml:0
msgid "Enter number or name"
msgstr "输入号码或名称"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/numpad.xml:0
msgid "Enter the number…"
msgstr "输入数字..."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__external_device_number
msgid "External Device Number"
msgstr "外部设备号码"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__external_device_number
msgid "External device number"
msgstr "外部设备号码"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Failed to load the SIP.js library:\n"
"\n"
"%(error)s"
msgstr ""
"加载 SIP.js 库失败：\n"
"\n"
"%(error)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Failed to put the call on hold/unhold."
msgstr "保留/取消通话失败。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.xml:0
msgid "Hang up but keep call in queue"
msgstr "挂断电话，但将电话留在队列中"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Hold"
msgstr "保留通话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid "Hold on!"
msgstr "稍等！"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__how_to_call_on_mobile
msgid "How To Call On Mobile"
msgstr "如何在移动端拨打电话"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__how_to_call_on_mobile
msgid "How to place calls on mobile"
msgstr "如何在移动端拨打电话"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__id
#: model:ir.model.fields,field_description:voip.field_voip_provider__id
msgid "ID"
msgstr "ID"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "If enabled, incoming calls will be automatically declined in Odoo."
msgstr "如果启用，来电将在Odoo中被自动拒绝。"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__should_call_from_another_device
msgid ""
"If enabled, placing a call in Odoo will transfer the call to the \"External "
"device number\". Use this option to place the call in Odoo but handle it "
"from another device - e.g. your desk phone."
msgstr "如果启用，在Odoo中拨打电话将把电话转到 \"外部设备号码\"。使用该选项在Odoo中拨打电话，但从另一个设备--例如你的桌面电话--处理它。"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__external_device_number
msgid ""
"If the \"Call from another device\" option is enabled, calls placed in Odoo "
"will be transfered to this phone number."
msgstr "如果 “从另一设备拨打电话 ”选项被启用，在Odoo中拨打的电话将被转接到这个电话号码。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "In call for: %(minutes)s:%(seconds)s"
msgstr "正在呼叫：%(minutes)s:%(seconds)s"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__direction__incoming
msgid "Incoming"
msgstr "进货"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/call_invitation.xml:0
msgid "Incoming call from…"
msgstr "来自..."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/device_selection_dialog.js:0
msgid "Input device selection"
msgstr "输入设备选择"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_res_users__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__has_call_in_queue
msgid "Is in the Call Queue"
msgstr "在呼叫队列"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__last_seen_phone_call
msgid "Last Seen Phone Call"
msgstr "最后出现的电话"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_provider__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__write_date
#: model:ir.model.fields,field_description:voip.field_voip_provider__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Manage Providers"
msgstr "管理提供商"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Mark as done"
msgstr "标记为完成"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid "Missclicked, sorry."
msgstr "没打中，抱歉。"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__missed
msgid "Missed"
msgstr "错过了"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Missed call from %(phone_number)s"
msgstr "来自 %(phone_number)s 的未接来电"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__mobile
msgid "Mobile"
msgstr "手机"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/activity/activity_patch.xml:0
msgid "Mobile:"
msgstr "手机："

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Mute"
msgstr "静音"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__name
msgid "Name"
msgstr "名称"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Next Activities"
msgstr "下一活动"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"No audio recording device available. The application requires a microphone "
"in order to be used."
msgstr "没有可用的录音设备。该应用程序需要一个麦克风才能使用。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.xml:0
msgid "No calls scheduled for today 😎"
msgstr "今天没有安排通话 😎"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.xml:0
#: code:addons/voip/static/src/softphone/contacts_tab.xml:0
#: code:addons/voip/static/src/softphone/recent_tab.xml:0
msgid "No search results 💔"
msgstr "没有搜索结果 💔"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "On Hold"
msgstr "暂停"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__ongoing
msgid "Ongoing"
msgstr "持续"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Open Numpad"
msgstr "打开 Numpad"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/voip_systray_item.js:0
msgid "Open Softphone"
msgstr "开放式软电话"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__direction__outgoing
msgid "Outgoing"
msgstr "发信"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__pbx_ip
msgid "PBX Server IP"
msgstr "PBX服务器IP"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "PBX or Websocket address is missing. Please check your settings."
msgstr "PBX或Websocket地址丢失。请检查您的设置。"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__phone
msgid "Phone"
msgstr "电话"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__phone_number
msgid "Phone Number"
msgstr "电话号码"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/activity/activity_patch.xml:0
msgid "Phone:"
msgstr "电话:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Please accept the use of the microphone."
msgstr "请同意使用麦克风。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid ""
"Please try again later. If the problem persists, you may want to ask an "
"administrator to check the configuration."
msgstr "请稍后再试。如果问题仍然存在，您可能要请管理员检查一下配置。"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
msgid "Preferences"
msgstr "偏好"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_provider__mode__prod
msgid "Production"
msgstr "生产"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Recent"
msgstr "最近"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid "Registration rejected: %(statusCode)s %(reasonPhrase)s."
msgstr "注册被拒绝：%(statusCode)s%(reasonPhrase)s。"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "Reject incoming calls"
msgstr "拒绝来电"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__rejected
msgid "Rejected"
msgstr "已拒绝"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Rejected call from %(phone_number)s"
msgstr "拒接来自 %(phone_number)s 的呼叫"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Rejected call to %(phone_number)s"
msgstr "拒绝调用 %(phone_number)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Remember?"
msgstr "记得吗？"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/call_queue_switch.js:0
msgid "Remove from Call Queue"
msgstr "从呼叫队列中移除"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__user_id
msgid "Responsible"
msgstr "负责人"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Resume"
msgstr "恢复"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Ringing…"
msgstr "铃声..."

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule & make calls from your database"
msgstr "计划并从数据库中拨打电话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Schedule Activity"
msgstr "安排活动"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.xml:0
msgid "Search"
msgstr "搜索"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.js:0
msgid "Select a call method"
msgstr "选择呼叫方法"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Send e-mail"
msgstr "发送电子邮件"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_auto_reject_incoming_calls
msgid "Should Auto Reject Incoming Calls"
msgstr "应自动拒绝来电"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_call_from_another_device
msgid "Should Call From Another Device"
msgstr "应从其他设备拨打电话"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_queue_mixin.py:0
msgid ""
"Some documents cannot be added to the call queue as they do not have a phone"
" number set: %(record_names)s"
msgstr "某些文档无法添加到呼叫队列，因为它们没有设置电话号码：%(record_names)s"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__start_date
msgid "Start Date"
msgstr "开始日期"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__state
msgid "State"
msgstr "状态"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Tell us how to make the call:"
msgstr "告诉我们如何拨打电话："

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__terminated
msgid "Terminated"
msgstr "终止"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_provider__pbx_ip
msgid "The IP address of your PBX Server"
msgstr "您的PBX服务器的IP地址"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_provider__ws_server
msgid "The URL of your WebSocket"
msgstr "你的WebSocket的网址"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "The WebSocket connection was lost and couldn't be reestablished."
msgstr "WebSocket 连接丢失，无法重新建立。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid ""
"The error may come from the transport layer. Please have an administrator "
"verify the websocket server URL in the General Settings. If the problem "
"persists, this is probably an issue with the server."
msgstr "该错误可能来自传输层。请让管理员验证 \"常规设置 \"中的websocket服务器URL。如果问题持续存在，这可能是服务器的问题。"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_call__activity_name
msgid "The name of the activity related to this phone call, if any."
msgstr "与此电话有关的活动名称（如果有）。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"The number is incorrect, the user credentials could be wrong or the connection cannot be made. Please check your configuration.\n"
"(Reason received: %(reasonPhrase)s)"
msgstr ""
"号码不正确、用户凭证可能有误或无法连接。请检查您的配置。\n"
"(收到的原因：%(reasonPhrase)s)"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_secret
msgid "The password that will be used to register with the PBX server."
msgstr "用于向 PBX 服务器注册的密码。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "The person you try to contact is currently unavailable."
msgstr "你试图联系的人目前不在。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid ""
"The server failed to authenticate you. Please have an administrator verify "
"that you are reaching the right server (PBX server IP in the General "
"Settings) and that the credentials in your user preferences are correct."
msgstr "服务器无法对您进行身份验证。 请让管理员验证您正在访问正确的服务器（常规设置中的PBX服务器IP）以及您的用户偏好中的凭据是否正确。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"The user agent could not be started. The websocket server URL may be "
"incorrect. Please have an administrator check the websocket server URL in "
"the General Settings."
msgstr "用户代理无法启动。websocket 服务器 URL 可能不正确。请让管理员检查常规设置中的 websocket 服务器 URL。"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_username
msgid ""
"The username (typically the extension number) that will be used to register "
"with the PBX server."
msgstr "将用于向PBX服务器注册的用户名 (通常是分机号)。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"The websocket connection to the server has been lost. Attempting to "
"reestablish the connection…"
msgstr "与服务器的 websocket 连接已丢失。尝试重新建立连接⋯"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/voip_service.js:0
msgid ""
"There is still a call in progress, are you sure you want to leave the page?"
msgstr "仍在通话中，您确定要离开页面吗？"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#: code:addons/voip/static/src/softphone/transfer_popover.xml:0
msgid "Transfer"
msgstr "转账"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/transfer_popover.xml:0
msgid "Transfer to"
msgstr "交易费"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/voip_systray_item.js:0
msgid "Unfold Softphone"
msgstr "展开软电话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Unmute"
msgstr "频道"

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "User"
msgstr "用户"

#. module: voip
#: model:ir.model,name:voip.model_res_users_settings
msgid "User Settings"
msgstr "用户设置"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Using VoIP"
msgstr "使用网络电话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Using device's phone"
msgstr "使用设备的电话"

#. module: voip
#: model:ir.model,name:voip.model_voip_queue_mixin
msgid "VOIP Queue support"
msgstr "VOIP队列支持"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
#: model:ir.model.fields.selection,name:voip.selection__res_users__how_to_call_on_mobile__voip
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
msgid "VoIP"
msgstr "VoIP"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.voip_res_users_settings_view_form
msgid "VoIP Configuration"
msgstr "VoIP配置"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__mode
msgid "VoIP Environment"
msgstr "VoIP 环境"

#. module: voip
#: model:ir.model,name:voip.model_voip_provider
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_provider_id
msgid "VoIP Provider"
msgstr "VoIP 提供商"

#. module: voip
#: model:ir.actions.act_window,name:voip.action_voip_provider_view
#: model:ir.ui.menu,name:voip.voip_provider_view_menu
#: model_terms:ir.ui.view,arch_db:voip.voip_provider_tree_view
msgid "VoIP Providers"
msgstr "VoIP 提供商"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_secret
msgid "VoIP secret"
msgstr "VoIP密钥"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_username
msgid "VoIP username / Extension number"
msgstr "VoIP用户名 / 分机号"

#. module: voip
#: model:ir.ui.menu,name:voip.menu_voip_call_view
msgid "Voip / Calls"
msgstr "网络电话/通话"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_provider_id
msgid "Voip Provider"
msgstr "VoIP 提供商"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_secret
msgid "Voip Secret"
msgstr "Voip 密钥"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_username
msgid "Voip Username"
msgstr "Voip 用户名"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__ws_server
msgid "WebSocket"
msgstr "WebSocket"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid "Yes, do it."
msgstr "是的，就这么做。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Your browser does not support some of the features required for VoIP to "
"work. Please try updating your browser or using a different one."
msgstr "您的浏览器不支持网络电话运行所需的某些功能。请尝试更新浏览器或使用其他浏览器。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/recent_tab.xml:0
msgid "Your call history is empty! Make a call now and have it listed here 💡"
msgstr "您的通话记录是空的！现在拨打电话，并在此列出 💡"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Your login details are not set correctly. Please contact your administrator."
msgstr "您的登录信息设置不正确。请联系您的管理员。"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "less than a second"
msgstr "不到一秒钟"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "phone"
msgstr "电话"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "voip"
msgstr "语音电话"
