# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "%(minutes)s min %(seconds)s sec"
msgstr "%(minutes)s min %(seconds)s sec"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "%(minutes)s minutes"
msgstr "%(minutes)s minuti"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "%(number)s missed calls"
msgstr "%(number)s chiamate perse"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "%(seconds)s seconds"
msgstr "%(seconds)s secondi"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "1 minute"
msgstr "1 minuto"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "1 missed call"
msgstr "1 chiamata persa"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "1 second"
msgstr "1 secondo"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "2 minutes"
msgstr "2 minuti"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "2 missed calls"
msgstr "2 chiamate perse"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "2 seconds"
msgstr "2 secondi"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"A hardware error has occurred while trying to access the audio recording "
"device. Please ensure that your drivers are up to date and try again."
msgstr ""
"Si è verificato un errore di hardware durante il tentativo di accesso al "
"dispositivo di registrazione audio. Assicurati che i driver siano aggiornati"
" e riprova."

#. module: voip
#: model:ir.model,name:voip.model_voip_call
msgid "A phone call handled using the VoIP application"
msgstr "Una chiamata telefonica gestita con l'applicazione VoIP"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__aborted
msgid "Aborted"
msgstr "Interrotta"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Aborted call to %(phone_number)s"
msgstr "Chiamata interrotta per %(phone_number)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#: model:ir.model,name:voip.model_mail_activity
msgid "Activity"
msgstr "Attività"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__activity_name
msgid "Activity Name"
msgstr "Nome attività"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/call_queue_switch.js:0
msgid "Add to Call Queue"
msgstr "Aggiungi alla lista di chiamate da fare"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"An error occured involving the audio recording device (%(errorName)s):\n"
"%(errorMessage)s"
msgstr ""
"Si è verificato un errore che coinvolge il dispositivo di registrazione audio (%(errorName)s):\n"
"%(errorMessage)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"An error occurred during the instantiation of the User Agent:\n"
"\n"
"%(error)s"
msgstr ""
"Si è verificato un errore durante la creazione di un'istanza dell'agente utente:\n"
"\n"
"%(error)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"An error occurred trying to invite the following number: %(phoneNumber)s\n"
"\n"
"Error: %(error)s"
msgstr ""
"Si è verificato un errore durante la chiamata con il seguente numero: %(phoneNumber)s\n"
"\n"
"Errore: %(error)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid ""
"Are you sure you want to delete this activity? It will be lost forever, "
"which is quite a long time 😔"
msgstr ""
"Sei sicuro di voler eliminare quest'attività? Andrà persa per sempre e non "
"per pochi giorni 😔"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users__how_to_call_on_mobile__ask
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__ask
msgid "Ask"
msgstr "Chiedi"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/numpad.xml:0
msgid "Backspace"
msgstr "Backspace"

#. module: voip
#. odoo-javascript
#. odoo-python
#: code:addons/voip/models/voip_queue_mixin.py:0
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Call"
msgstr "Chiamata"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call from %(correspondent)s"
msgstr "Chiamata in arrivo da %(correspondent)s"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call from %(phone_number)s"
msgstr "Chiamata in arrivo da %(phone_number)s"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_call_from_another_device
msgid "Call from another device"
msgstr "Chiama da un altro dispositivo"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Call rejected (reason: “%(reasonPhrase)s”)"
msgstr "Chiamata rifiutata (motivo: \"%(reasonPhrase)s\")"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call to %(correspondent)s"
msgstr "Chiama %(correspondent)s"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call to %(phone_number)s"
msgstr "Chiama %(phone_number)s"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__calling
msgid "Calling"
msgstr "Chiamata in corso"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Calling %(phone number)s"
msgstr "Chiamando %(numero di telefono)s"

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_call_view
#: model_terms:ir.ui.view,arch_db:voip.voip_call_tree_view
msgid "Calls"
msgstr "Chiamate"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/devise_selection_dialog.xml:0
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Cancel"
msgstr "Annulla"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.xml:0
msgid "Cancel the activity"
msgstr "Annullare l'attività "

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Cannot access audio recording device. If you have denied access to your "
"microphone, please allow it and try again. Otherwise, make sure that this "
"website is running over HTTPS and that your browser is not set to deny "
"access to media devices."
msgstr ""
"Impossibile accedere al dispositivo di registrazione audio. Se hai negato "
"l'accesso al microfono, concedi l'autorizzazione e prova di nuovo. "
"Assicurati che il sito web funzioni su HTTPS e che il tuo browser conceda "
"l'accesso ai dispositivi multimediali."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Change input device"
msgstr "Modifica dispositivo di input"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/devise_selection_dialog.xml:0
msgid "Choose a device:"
msgstr "Scegli un dispositivo:"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__how_to_call_on_mobile
msgid ""
"Choose the method to be used to place a call when using the mobile application:\n"
"            • VoIP: Always use the Odoo softphone\n"
"            • Device's phone: Always use the device's phone\n"
"            • Ask: Always ask whether the softphone or the device's phone must be used\n"
"        "
msgstr ""
"Scegli il metodo da utilizzare per effettuare una chiamata quando usi l'app mobile:\n"
"            •VoIP: utilizza sempre il softphone Odoo\n"
"            •Telefono della periferica: utilizza sempre il telefono della periferica\n"
"            •Chiedi: chiedi sempre se deve essere utilizzato il softphone o il telefono della periferica\n"
"        "

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.xml:0
msgid "Close"
msgstr "Chiudi"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Close Numpad"
msgstr "Chiudi tastierino numerico"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/voip_systray_item.js:0
msgid "Close Softphone"
msgstr "Chiudi softphone"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Close details"
msgstr "Chiudi dettagli"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__company_id
msgid "Company"
msgstr "Azienda"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
#: code:addons/voip/static/src/mobile/devise_selection_dialog.xml:0
msgid "Confirm"
msgstr "Conferma"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Connecting…"
msgstr "Collegamento in corso..."

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_call__partner_id
msgid "Contact"
msgstr "Contatto"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Contacts"
msgstr "Contatti"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/contacts_tab.xml:0
msgid "Contacts with a phone number will be shown here."
msgstr "Contatti con un numero di telefono che verrà mostrato qui."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_provider__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__create_date
#: model:ir.model.fields,field_description:voip.field_voip_provider__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
msgid "Credentials"
msgstr "Credenziali"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Customer"
msgstr "Cliente"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_provider__mode__demo
msgid "Demo"
msgstr "Demo"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users__how_to_call_on_mobile__phone
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__phone
msgid "Device's phone"
msgstr "Telefono della periferica"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__direction
msgid "Direction"
msgstr "Direzione"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__display_name
#: model:ir.model.fields,field_description:voip.field_voip_provider__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Documents"
msgstr "Documenti"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Edit"
msgstr "Modifica"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "End Call"
msgstr "Fine della chiamata"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__end_date
msgid "End Date"
msgstr "Data fine"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/transfer_popover.xml:0
msgid "Enter number or name"
msgstr "Inserisci il numero o il nome"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/numpad.xml:0
msgid "Enter the number…"
msgstr "Inserisci il numero..."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__external_device_number
msgid "External Device Number"
msgstr "Numero dispositivo esterno"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__external_device_number
msgid "External device number"
msgstr "Numero dispositivo esterno"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Failed to load the SIP.js library:\n"
"\n"
"%(error)s"
msgstr ""
"Caricamento dell'archivio SIP.js non riuscito:\n"
"\n"
"%(error)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Failed to put the call on hold/unhold."
msgstr "Impossibile mettere la chiamata in attesa o meno."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.xml:0
msgid "Hang up but keep call in queue"
msgstr "Riagganciare ma mantenere la chiamata in coda"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Hold"
msgstr "Metti in attesa"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid "Hold on!"
msgstr "Aspetta!"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__how_to_call_on_mobile
msgid "How To Call On Mobile"
msgstr "Come effettuare chiamate sul cellulare"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__how_to_call_on_mobile
msgid "How to place calls on mobile"
msgstr "Come effettuare chiamate sul cellulare"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__id
#: model:ir.model.fields,field_description:voip.field_voip_provider__id
msgid "ID"
msgstr "ID"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "If enabled, incoming calls will be automatically declined in Odoo."
msgstr ""
"Se selezionato, le chiamate in arrivo verranno rifiutato automaticamente in "
"Odoo."

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__should_call_from_another_device
msgid ""
"If enabled, placing a call in Odoo will transfer the call to the \"External "
"device number\". Use this option to place the call in Odoo but handle it "
"from another device - e.g. your desk phone."
msgstr ""
"Se selezionato, la chiamata effettuata in Odoo verrà trasferita al \"Numero "
"del dispositivo esterno\". Utilizza questa opzione per effettura una "
"chiamata in Odoo ma gestirla da un altro dispositivo, ad es. il telefono "
"fisso."

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__external_device_number
msgid ""
"If the \"Call from another device\" option is enabled, calls placed in Odoo "
"will be transfered to this phone number."
msgstr ""
"Se l'opzione \"Chiama da un altro dispositivo\" è attiva, le chiamate in "
"Odoo verranno trasferite al numero di telefono indicato."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "In call for: %(minutes)s:%(seconds)s"
msgstr "In chiamata per: %(minutes)s:%(seconds)s"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__direction__incoming
msgid "Incoming"
msgstr "In entrata"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/call_invitation.xml:0
msgid "Incoming call from…"
msgstr "Chiamata in arrivo da..."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/device_selection_dialog.js:0
msgid "Input device selection"
msgstr "Scelta dispositivo di input"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_res_users__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__has_call_in_queue
msgid "Is in the Call Queue"
msgstr "È nella coda delle chiamate da fare"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__last_seen_phone_call
msgid "Last Seen Phone Call"
msgstr "Ultima chiamata vista"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_provider__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__write_date
#: model:ir.model.fields,field_description:voip.field_voip_provider__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Manage Providers"
msgstr "Gestisci fornitori"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Mark as done"
msgstr "Imposta a completata"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid "Missclicked, sorry."
msgstr "Ho cliccato male, scusa."

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__missed
msgid "Missed"
msgstr "Fallito"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Missed call from %(phone_number)s"
msgstr "Chiamata persa da %(phone_number)s"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__mobile
msgid "Mobile"
msgstr "Dispositivo mobile"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/activity/activity_patch.xml:0
msgid "Mobile:"
msgstr "Cellulare:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Mute"
msgstr "Silenzia"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__name
msgid "Name"
msgstr "Nome"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Next Activities"
msgstr "Prossime attività"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"No audio recording device available. The application requires a microphone "
"in order to be used."
msgstr ""
"Nessun dispositivo di registrazione audio disponibile. L'applicazione "
"richiede un microfono per essere utilizzata."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.xml:0
msgid "No calls scheduled for today 😎"
msgstr "Nessuna chiamata programmata per oggi 😎"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.xml:0
#: code:addons/voip/static/src/softphone/contacts_tab.xml:0
#: code:addons/voip/static/src/softphone/recent_tab.xml:0
msgid "No search results 💔"
msgstr "Nessun risultato di ricerca 💔"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "On Hold"
msgstr "In attesa"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__ongoing
msgid "Ongoing"
msgstr "In corso"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Open Numpad"
msgstr "Apri tastierino numerico"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/voip_systray_item.js:0
msgid "Open Softphone"
msgstr "Apri softphone"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__direction__outgoing
msgid "Outgoing"
msgstr "In uscita"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__pbx_ip
msgid "PBX Server IP"
msgstr "IP Server PBX"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "PBX or Websocket address is missing. Please check your settings."
msgstr "Indirizzo PBX o websocket mancante, controllare le impostazioni."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__phone
msgid "Phone"
msgstr "Telefono"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__phone_number
msgid "Phone Number"
msgstr "Numero di telefono"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/activity/activity_patch.xml:0
msgid "Phone:"
msgstr "Telefono:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Please accept the use of the microphone."
msgstr "Accettare l'utilizzo del microfono."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid ""
"Please try again later. If the problem persists, you may want to ask an "
"administrator to check the configuration."
msgstr ""
"Per favore, riprova più tardi. Se il problema persiste, puoi chiedere ad un "
"amministratore di controllare la configurazione."

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
msgid "Preferences"
msgstr "Preferenze"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_provider__mode__prod
msgid "Production"
msgstr "Produzione"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Recent"
msgstr "Recente"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid "Registration rejected: %(statusCode)s %(reasonPhrase)s."
msgstr "Registrazione rifiutata: %(statusCode)s %(reasonPhrase)s"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "Reject incoming calls"
msgstr "Rifiuta chiamate in entrata"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__rejected
msgid "Rejected"
msgstr "Rifiutato"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Rejected call from %(phone_number)s"
msgstr "Chiamata rifiutata da %(phone_number)s"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Rejected call to %(phone_number)s"
msgstr "Chiamata rifiutata da %(phone_number)s"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Remember?"
msgstr "Ricordi?"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/call_queue_switch.js:0
msgid "Remove from Call Queue"
msgstr "Rimuovi dalla coda delle chiamate da fare"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__user_id
msgid "Responsible"
msgstr "Responsabile"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Resume"
msgstr "Riprendi"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Ringing…"
msgstr "Sta squillando..."

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule & make calls from your database"
msgstr "Programma ed effettua chiamate dal tuo database"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Schedule Activity"
msgstr "Pianifica Attività"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.xml:0
msgid "Search"
msgstr "Ricerca"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.js:0
msgid "Select a call method"
msgstr "Seleziona un metodo per effettuare la chiamata"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Send e-mail"
msgstr "Invia e-mail"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_auto_reject_incoming_calls
msgid "Should Auto Reject Incoming Calls"
msgstr "Dovrebbe rifiutare automaticamente chiamate in entrata"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_call_from_another_device
msgid "Should Call From Another Device"
msgstr "Dovrebbe chiamare da un altro dispositivo"

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_queue_mixin.py:0
msgid ""
"Some documents cannot be added to the call queue as they do not have a phone"
" number set: %(record_names)s"
msgstr ""
"Alcuni documenti non possono essere aggiunti alla coda delle chiamate perché"
" non hanno un numero di telefono configurato: %(record_names)s"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__start_date
msgid "Start Date"
msgstr "Data inizio"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__state
msgid "State"
msgstr "Stato"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Tell us how to make the call:"
msgstr "Spiegaci come effetuare la chiamata:"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__terminated
msgid "Terminated"
msgstr "Terminata"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_provider__pbx_ip
msgid "The IP address of your PBX Server"
msgstr "Indirizzo IP del server PBX"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_provider__ws_server
msgid "The URL of your WebSocket"
msgstr "L'URL del tuo WebSocket"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "The WebSocket connection was lost and couldn't be reestablished."
msgstr ""
"La connessione WebSocket è stata persa e non è stato possibile ristabilirla."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid ""
"The error may come from the transport layer. Please have an administrator "
"verify the websocket server URL in the General Settings. If the problem "
"persists, this is probably an issue with the server."
msgstr ""
"L'errore potrebbe provenire dal livello di trasporto. Chiedi "
"all'amministratore di verificare l'URL del server websocket dalle "
"Impostazioni. Se il problema persiste si tratta probabilmente di un problema"
" del server."

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_call__activity_name
msgid "The name of the activity related to this phone call, if any."
msgstr ""
"Il nome dell'attività collegata a questa chiamata telefonica, se presente."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"The number is incorrect, the user credentials could be wrong or the connection cannot be made. Please check your configuration.\n"
"(Reason received: %(reasonPhrase)s)"
msgstr ""
"Il numero non è corretto, le credenziali dell'utente potrebbero essere errate o non è possibile stabilire una connessione. Controlla la configurazione.\n"
"(Motivo ricezione: %(reasonPhrase)s)"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_secret
msgid "The password that will be used to register with the PBX server."
msgstr ""
"La password che verrà utilizzata per la registrazione con il server PBX."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "The person you try to contact is currently unavailable."
msgstr "La persona che cerchi di contattare non disponibile al momento."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid ""
"The server failed to authenticate you. Please have an administrator verify "
"that you are reaching the right server (PBX server IP in the General "
"Settings) and that the credentials in your user preferences are correct."
msgstr ""
"Il server non è riuscito ad autenticarti. Chiedi ad un amministratore di "
"verificare che stai cercando di raggiungere il server giusto (IP server PBX "
"nelle Impostazioni) e che le credenziali nelle preferenze utente siano "
"corrette."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"The user agent could not be started. The websocket server URL may be "
"incorrect. Please have an administrator check the websocket server URL in "
"the General Settings."
msgstr ""
"Non è stato possibile avviare l'agente utente. L'URL del server websocket è "
"sbagliato. Chiedi ad un amministratore di verificare l'URL del server "
"websocket nelle Impostazioni."

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_username
msgid ""
"The username (typically the extension number) that will be used to register "
"with the PBX server."
msgstr ""
"Il nome utente (tipicamente il numero di estensione) che verrà utilizzato "
"per la registrazione con il server PBX."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"The websocket connection to the server has been lost. Attempting to "
"reestablish the connection…"
msgstr ""
"La connessione websocket al server è stata persa. Tentativo di ristabilire "
"la connessione..."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/voip_service.js:0
msgid ""
"There is still a call in progress, are you sure you want to leave the page?"
msgstr ""
"Sei sicuro di voler lasciare la pagina con una chiamata ancora in corso?"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#: code:addons/voip/static/src/softphone/transfer_popover.xml:0
msgid "Transfer"
msgstr "Trasferisci"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/transfer_popover.xml:0
msgid "Transfer to"
msgstr "Trasferire"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/voip_systray_item.js:0
msgid "Unfold Softphone"
msgstr "Espandi softphone"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Unmute"
msgstr "Riattiva audio"

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "User"
msgstr "Utente"

#. module: voip
#: model:ir.model,name:voip.model_res_users_settings
msgid "User Settings"
msgstr "Impostazioni utente"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Using VoIP"
msgstr "Utilizzano VoIP"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Using device's phone"
msgstr "Utilizzando telefono dispositivo"

#. module: voip
#: model:ir.model,name:voip.model_voip_queue_mixin
msgid "VOIP Queue support"
msgstr "Assistenza coda VOIP"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
#: model:ir.model.fields.selection,name:voip.selection__res_users__how_to_call_on_mobile__voip
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
msgid "VoIP"
msgstr "VOIP"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.voip_res_users_settings_view_form
msgid "VoIP Configuration"
msgstr "Configurazione VOIP"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__mode
msgid "VoIP Environment"
msgstr "Ambiente VoIP"

#. module: voip
#: model:ir.model,name:voip.model_voip_provider
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_provider_id
msgid "VoIP Provider"
msgstr "Fornitore VoIP"

#. module: voip
#: model:ir.actions.act_window,name:voip.action_voip_provider_view
#: model:ir.ui.menu,name:voip.voip_provider_view_menu
#: model_terms:ir.ui.view,arch_db:voip.voip_provider_tree_view
msgid "VoIP Providers"
msgstr "Fornitori VoIP"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_secret
msgid "VoIP secret"
msgstr "Chiave privata VOIP"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_username
msgid "VoIP username / Extension number"
msgstr "Nome utente/numero di estensione VOIP "

#. module: voip
#: model:ir.ui.menu,name:voip.menu_voip_call_view
msgid "Voip / Calls"
msgstr "Voip/chiamate"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_provider_id
msgid "Voip Provider"
msgstr "Fornitore Voip"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_secret
msgid "Voip Secret"
msgstr "Secret Voip"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_username
msgid "Voip Username"
msgstr "Nome utente Voip"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__ws_server
msgid "WebSocket"
msgstr "WebSocket"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid "Yes, do it."
msgstr "Sì, fallo."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Your browser does not support some of the features required for VoIP to "
"work. Please try updating your browser or using a different one."
msgstr ""
"Il tuo browser non supporta alcune delle funzionalità necessarie per il "
"VoIP. Prova ad aggiornare il browser o utilizzane uno diverso."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/recent_tab.xml:0
msgid "Your call history is empty! Make a call now and have it listed here 💡"
msgstr ""
"La cronologia delle chiamate è vuota! Effettua una chiamata ora e conservala"
" nell'elenco qui 💡"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Your login details are not set correctly. Please contact your administrator."
msgstr ""
"Le credenziali non sono state impostate correttamente, contatta "
"l'amministratore."

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "less than a second"
msgstr "meno di un secondo"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "phone"
msgstr "telefono"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "voip"
msgstr "voip"
