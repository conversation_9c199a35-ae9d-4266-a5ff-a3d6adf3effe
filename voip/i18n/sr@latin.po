# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <dragan.vuk<PERSON><PERSON><PERSON>@gmail.com>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-03 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON> <dragan.vukosavl<PERSON><EMAIL>>, "
"2022\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "%(minutes)s min %(seconds)s sec"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "%(minutes)s minutes"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "%(number)s missed calls"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "%(seconds)s seconds"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "1 minute"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "1 missed call"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "1 second"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "2 minutes"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "2 missed calls"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "2 seconds"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"A hardware error has occurred while trying to access the audio recording "
"device. Please ensure that your drivers are up to date and try again."
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_call
msgid "A phone call handled using the VoIP application"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__aborted
msgid "Aborted"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Aborted call to %(phone_number)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#: model:ir.model,name:voip.model_mail_activity
msgid "Activity"
msgstr "Aktivnost"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__activity_name
#, fuzzy
msgid "Activity Name"
msgstr "Aktivnost"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/call_queue_switch.js:0
msgid "Add to Call Queue"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"An error occured involving the audio recording device (%(errorName)s):\n"
"%(errorMessage)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"An error occurred during the instantiation of the User Agent:\n"
"\n"
"%(error)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"An error occurred trying to invite the following number: %(phoneNumber)s\n"
"\n"
"Error: %(error)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid ""
"Are you sure you want to delete this activity? It will be lost forever, "
"which is quite a long time 😔"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users__how_to_call_on_mobile__ask
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__ask
msgid "Ask"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/numpad.xml:0
msgid "Backspace"
msgstr ""

#. module: voip
#. odoo-javascript
#. odoo-python
#: code:addons/voip/models/voip_queue_mixin.py:0
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Call"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call from %(correspondent)s"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call from %(phone_number)s"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_call_from_another_device
msgid "Call from another device"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Call rejected (reason: “%(reasonPhrase)s”)"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call to %(correspondent)s"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Call to %(phone_number)s"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__calling
msgid "Calling"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Calling %(phone number)s"
msgstr ""

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_call_view
#: model_terms:ir.ui.view,arch_db:voip.voip_call_tree_view
msgid "Calls"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/devise_selection_dialog.xml:0
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Cancel"
msgstr "Otkaži"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.xml:0
msgid "Cancel the activity"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Cannot access audio recording device. If you have denied access to your "
"microphone, please allow it and try again. Otherwise, make sure that this "
"website is running over HTTPS and that your browser is not set to deny "
"access to media devices."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Change input device"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/devise_selection_dialog.xml:0
msgid "Choose a device:"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__how_to_call_on_mobile
msgid ""
"Choose the method to be used to place a call when using the mobile "
"application:\n"
"            • VoIP: Always use the Odoo softphone\n"
"            • Device's phone: Always use the device's phone\n"
"            • Ask: Always ask whether the softphone or the device's phone "
"must be used\n"
"        "
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.xml:0
msgid "Close"
msgstr "Zatvori"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Close Numpad"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/voip_systray_item.js:0
msgid "Close Softphone"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Close details"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__company_id
msgid "Company"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
#: code:addons/voip/static/src/mobile/devise_selection_dialog.xml:0
msgid "Confirm"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Connecting…"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_call__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Contacts"
msgstr "Kontakti"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/contacts_tab.xml:0
msgid "Contacts with a phone number will be shown here."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_provider__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__create_date
#: model:ir.model.fields,field_description:voip.field_voip_provider__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
msgid "Credentials"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Customer"
msgstr "Kupac"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_provider__mode__demo
msgid "Demo"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users__how_to_call_on_mobile__phone
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__phone
msgid "Device's phone"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__direction
msgid "Direction"
msgstr "Smer"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__display_name
#: model:ir.model.fields,field_description:voip.field_voip_provider__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#, fuzzy
msgid "Documents"
msgstr "Dokument"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Edit"
msgstr "Uredi"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "End Call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__end_date
#, fuzzy
msgid "End Date"
msgstr "Datum"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/transfer_popover.xml:0
msgid "Enter number or name"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/numpad.xml:0
msgid "Enter the number…"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__external_device_number
msgid "External Device Number"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__external_device_number
msgid "External device number"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Failed to load the SIP.js library:\n"
"\n"
"%(error)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Failed to put the call on hold/unhold."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.xml:0
msgid "Hang up but keep call in queue"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Hold"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid "Hold on!"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__how_to_call_on_mobile
msgid "How To Call On Mobile"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__how_to_call_on_mobile
msgid "How to place calls on mobile"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__id
#: model:ir.model.fields,field_description:voip.field_voip_provider__id
msgid "ID"
msgstr "ID"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "If enabled, incoming calls will be automatically declined in Odoo."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__should_call_from_another_device
msgid ""
"If enabled, placing a call in Odoo will transfer the call to the \"External "
"device number\". Use this option to place the call in Odoo but handle it "
"from another device - e.g. your desk phone."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__external_device_number
msgid ""
"If the \"Call from another device\" option is enabled, calls placed in Odoo "
"will be transfered to this phone number."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "In call for: %(minutes)s:%(seconds)s"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__direction__incoming
msgid "Incoming"
msgstr "Dolazni"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/call_invitation.xml:0
msgid "Incoming call from…"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/device_selection_dialog.js:0
msgid "Input device selection"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_res_users__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__has_call_in_queue
msgid "Is in the Call Queue"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__last_seen_phone_call
msgid "Last Seen Phone Call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_provider__write_uid
msgid "Last Updated by"
msgstr "Poslednje izmenio/la"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__write_date
#: model:ir.model.fields,field_description:voip.field_voip_provider__write_date
msgid "Last Updated on"
msgstr "Poslednje ažuriranje dana"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Manage Providers"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Mark as done"
msgstr "Označi kao završeno"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid "Missclicked, sorry."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__missed
msgid "Missed"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Missed call from %(phone_number)s"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__mobile
msgid "Mobile"
msgstr "Mobilni"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/activity/activity_patch.xml:0
msgid "Mobile:"
msgstr "Mobilni:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Mute"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__name
msgid "Name"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
#, fuzzy
msgid "Next Activities"
msgstr "Aktivnost"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"No audio recording device available. The application requires a microphone "
"in order to be used."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.xml:0
msgid "No calls scheduled for today 😎"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.xml:0
#: code:addons/voip/static/src/softphone/contacts_tab.xml:0
#: code:addons/voip/static/src/softphone/recent_tab.xml:0
msgid "No search results 💔"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "On Hold"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__ongoing
#, fuzzy
msgid "Ongoing"
msgstr "Izlazno"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Open Numpad"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/voip_systray_item.js:0
msgid "Open Softphone"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__direction__outgoing
msgid "Outgoing"
msgstr "Izlazno"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__pbx_ip
msgid "PBX Server IP"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "PBX or Websocket address is missing. Please check your settings."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__phone
msgid "Phone"
msgstr "Telefon"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__phone_number
msgid "Phone Number"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/activity/activity_patch.xml:0
msgid "Phone:"
msgstr "Telefon:"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "Please accept the use of the microphone."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid ""
"Please try again later. If the problem persists, you may want to ask an "
"administrator to check the configuration."
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
msgid "Preferences"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_provider__mode__prod
msgid "Production"
msgstr "Proizvodnja"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
msgid "Recent"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid "Registration rejected: %(statusCode)s %(reasonPhrase)s."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__should_auto_reject_incoming_calls
msgid "Reject incoming calls"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__rejected
msgid "Rejected"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Rejected call from %(phone_number)s"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_call.py:0
msgid "Rejected call to %(phone_number)s"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Remember?"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/call_queue_switch.js:0
msgid "Remove from Call Queue"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__user_id
msgid "Responsible"
msgstr "Odgovorno lice"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Resume"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Ringing…"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule & make calls from your database"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
msgid "Schedule Activity"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.xml:0
msgid "Search"
msgstr "Pronađi"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.js:0
msgid "Select a call method"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#, fuzzy
msgid "Send e-mail"
msgstr "Pošalji mail"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_auto_reject_incoming_calls
msgid "Should Auto Reject Incoming Calls"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__should_call_from_another_device
msgid "Should Call From Another Device"
msgstr ""

#. module: voip
#. odoo-python
#: code:addons/voip/models/voip_queue_mixin.py:0
msgid ""
"Some documents cannot be added to the call queue as they do not have a phone "
"number set: %(record_names)s"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__start_date
#, fuzzy
msgid "Start Date"
msgstr "Datum Kreiranja"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_call__state
#, fuzzy
msgid "State"
msgstr "Status"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Tell us how to make the call:"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_call__state__terminated
msgid "Terminated"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_provider__pbx_ip
msgid "The IP address of your PBX Server"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_provider__ws_server
msgid "The URL of your WebSocket"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "The WebSocket connection was lost and couldn't be reestablished."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid ""
"The error may come from the transport layer. Please have an administrator "
"verify the websocket server URL in the General Settings. If the problem "
"persists, this is probably an issue with the server."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_call__activity_name
msgid "The name of the activity related to this phone call, if any."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"The number is incorrect, the user credentials could be wrong or the "
"connection cannot be made. Please check your configuration.\n"
"(Reason received: %(reasonPhrase)s)"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_secret
msgid "The password that will be used to register with the PBX server."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid "The person you try to contact is currently unavailable."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/registerer.js:0
msgid ""
"The server failed to authenticate you. Please have an administrator verify "
"that you are reaching the right server (PBX server IP in the General "
"Settings) and that the credentials in your user preferences are correct."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"The user agent could not be started. The websocket server URL may be "
"incorrect. Please have an administrator check the websocket server URL in "
"the General Settings."
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users_settings__voip_username
msgid ""
"The username (typically the extension number) that will be used to register "
"with the PBX server."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"The websocket connection to the server has been lost. Attempting to "
"reestablish the connection…"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/voip_service.js:0
msgid ""
"There is still a call in progress, are you sure you want to leave the page?"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.xml:0
#: code:addons/voip/static/src/softphone/transfer_popover.xml:0
msgid "Transfer"
msgstr "Prenos"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/transfer_popover.xml:0
#, fuzzy
msgid "Transfer to"
msgstr "Prenos"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/web/voip_systray_item.js:0
msgid "Unfold Softphone"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/correspondence_details.js:0
msgid "Unmute"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "User"
msgstr "Korisnik"

#. module: voip
#: model:ir.model,name:voip.model_res_users_settings
msgid "User Settings"
msgstr "Podešavanja korisnika"

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Using VoIP"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/mobile/call_method_selection_dialog.xml:0
msgid "Using device's phone"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_queue_mixin
msgid "VOIP Queue support"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/softphone.js:0
#: model:ir.model.fields.selection,name:voip.selection__res_users__how_to_call_on_mobile__voip
#: model:ir.model.fields.selection,name:voip.selection__res_users_settings__how_to_call_on_mobile__voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
#: model_terms:ir.ui.view,arch_db:voip.res_users_view_form_preferences
msgid "VoIP"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.voip_res_users_settings_view_form
msgid "VoIP Configuration"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__mode
msgid "VoIP Environment"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_provider
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_provider_id
msgid "VoIP Provider"
msgstr ""

#. module: voip
#: model:ir.actions.act_window,name:voip.action_voip_provider_view
#: model:ir.ui.menu,name:voip.voip_provider_view_menu
#: model_terms:ir.ui.view,arch_db:voip.voip_provider_tree_view
msgid "VoIP Providers"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_secret
msgid "VoIP secret"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users_settings__voip_username
msgid "VoIP username / Extension number"
msgstr ""

#. module: voip
#: model:ir.ui.menu,name:voip.menu_voip_call_view
msgid "Voip / Calls"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_provider_id
msgid "Voip Provider"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_secret
msgid "Voip Secret"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__voip_username
msgid "Voip Username"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_provider__ws_server
msgid "WebSocket"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/activities_tab.js:0
msgid "Yes, do it."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Your browser does not support some of the features required for VoIP to "
"work. Please try updating your browser or using a different one."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/softphone/recent_tab.xml:0
msgid "Your call history is empty! Make a call now and have it listed here 💡"
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/user_agent_service.js:0
msgid ""
"Your login details are not set correctly. Please contact your administrator."
msgstr ""

#. module: voip
#. odoo-javascript
#: code:addons/voip/static/src/core/call_model.js:0
msgid "less than a second"
msgstr ""

#~ msgid "# of Cases"
#~ msgstr "# Slucajeva"

#~ msgid "Accept"
#~ msgstr "Prihvati"

#~ msgid "Add to call queue"
#~ msgstr "Dodaj u red za pozivanje"

#~ msgid "Avatar"
#~ msgstr "Avatar"

#~ msgid "Cancelled"
#~ msgstr "Otkazano"

#~ msgid "Config Settings"
#~ msgstr "Podešavanje konfiguracije"

#~ msgid "Due Date"
#~ msgstr "Datum valute"

#~ msgid "Duration"
#~ msgstr "Trajanje"

#~ msgid "Group By"
#~ msgstr "Grupiši po"

#~ msgid "Held"
#~ msgstr "Odrzan"

#~ msgid "Not Held"
#~ msgstr "Ne Odrzivo"

#~ msgid "Note"
#~ msgstr "Beleška"

#~ msgid "Pending"
#~ msgstr "U toku"

#~ msgid "Phonecalls"
#~ msgstr "TelPozivi"

#~ msgid "Search Phonecalls"
#~ msgstr "Pretrazi Telefonske pozive"

#~ msgid "Sequence"
#~ msgstr "Niz"

#~ msgid "Summary"
#~ msgstr "Pregled"

#~ msgid "To Do"
#~ msgstr "Za Uraditi"

#~ msgid "Tomorrow"
#~ msgstr "Sutra"

#~ msgid "Unassigned"
#~ msgstr "Nedodeljeno"

#~ msgid "min"
#~ msgstr "min"
