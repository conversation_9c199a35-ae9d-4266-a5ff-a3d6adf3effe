<?xml version="1.0"?>
<templates>

    <t t-name="voip.Activity" t-inherit="mail.Activity" t-inherit-mode="extension">
        <xpath expr="//*[hasclass('o-mail-Activity-info')]" position="after">
            <t t-if="props.activity.phone">
                <div class="o-mail-Activity-voip-landline-number">
                    <t t-if="props.activity.mobile">
                        Phone:
                    </t>
                    <a href="#" t-on-click="(ev) => this.onClickPhoneNumber(ev, props.activity.phone)">
                        <t t-esc="props.activity.phone"/>
                    </a>
                </div>
            </t>
            <t t-if="props.activity.mobile">
                <div class="o-mail-Activity-voip-mobile-number">
                    <t t-if="props.activity.phone">
                        Mobile:
                    </t>
                    <a href="#" t-on-click="(ev) => this.onClickPhoneNumber(ev, props.activity.mobile)">
                        <t t-esc="props.activity.mobile"/>
                    </a>
                </div>
            </t>
        </xpath>
    </t>

</templates>
