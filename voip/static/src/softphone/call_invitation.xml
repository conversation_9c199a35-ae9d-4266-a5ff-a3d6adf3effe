<?xml version="1.0"?>
<templates>
    <t t-name="voip.CallInvitation">
        <div t-attf-class="o-voip-CallInvitation {{ props.extraClass }}">
            <div class="d-flex flex-column align-items-center pt-5 bg-success bg-opacity-25">
                <div>Incoming call from…</div>
                <img class="o-voip-CallInvitation-avatar mt-3 rounded" t-att-src="props.correspondence.avatarUrl"/>
                <div class="m-2 fw-bold" t-esc="partnerName"/>
                <div class="mb-4" t-esc="props.correspondence.phoneNumber"/>
            </div>
            <div class="d-flex justify-content-around mt-5">
                <button class="btn btn-danger rounded-circle" t-on-click="onClickReject">
                    <i class="o-voip-CallInvitation-reject fa fa-2x fa-phone p-2" role="img"/>
                </button>
                <button class="btn btn-success rounded-circle" t-on-click="onClickAccept">
                    <i class="fa fa-2x fa-phone p-2" role="img"/>
                </button>
            </div>
        </div>
    </t>
</templates>
